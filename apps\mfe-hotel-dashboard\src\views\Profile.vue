<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Profil</h1>
      <p class="text-gray-600 dark:text-gray-300"><PERSON><PERSON><PERSON><PERSON><PERSON> profili ve hesap bilgileri</p>
    </div>

    <!-- Placeholder Content -->
    <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card p-6">
      <div class="text-center py-12">
        <div class="w-16 h-16 bg-gray-100 dark:bg-navy-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Profil <PERSON>fası Yakında Gelecek</h3>
        <p class="text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
          Kullanıcı profili ve hesap yönetimi özellikleri bu bölümde yer alacak.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Profile placeholder component
</script> 