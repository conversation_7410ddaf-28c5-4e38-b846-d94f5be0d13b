<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-4 pb-8">
    <!-- Header -->
    <div class="px-4 mb-6">
      <div class="flex items-center gap-4 mb-2">
        <button 
          @click="$router.go(-1)"
          class="p-2 text-gray-600 hover:text-gray-800 hover:bg-white rounded-lg transition-all"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 class="text-2xl font-bold text-navy-700">Servis <PERSON></h1>
      </div>
      <p class="text-gray-600">Kat hizmetleri, bakım ve diğer taleplerin için buradan talepte bulunabilirsin.</p>
    </div>

    <!-- Error Display -->
    <div v-if="serviceRequestStore.error" class="mx-4 mb-4">
      <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <p class="text-red-700 text-sm">{{ serviceRequestStore.error }}</p>
          </div>
          <button 
            @click="serviceRequestStore.clearError()"
            class="text-red-400 hover:text-red-600"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccessMessage" class="mx-4 mb-4">
      <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg">
        <div class="flex items-center">
          <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <p class="text-green-700 text-sm">Servis talebiniz başarıyla oluşturuldu!</p>
        </div>
      </div>
    </div>

    <div class="space-y-6 px-4">
      <!-- Service Request Form -->
      <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-xl font-semibold text-navy-700 mb-6">Yeni Servis Talebi</h2>
        
        <form @submit.prevent="handleSubmitRequest" class="space-y-6">
          <!-- Request Type -->
          <div>
            <label for="requestType" class="block text-sm font-medium text-gray-700 mb-2">
              Talep Türü *
            </label>
            <select 
              id="requestType"
              v-model="formData.request_type"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 transition-colors"
            >
              <option value="">Talep türünü seçin</option>
              <option value="Housekeeping">Kat Hizmetleri</option>
              <option value="Maintenance">Bakım & Tamir</option>
              <option value="Amenities">Olanaklar & Minibar</option>
              <option value="Other">Diğer</option>
            </select>
          </div>

          <!-- Description -->
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Açıklama *
            </label>
            <textarea 
              id="description"
              v-model="formData.description"
              required
              rows="4"
              placeholder="Lütfen talebinizi detaylı olarak açıklayın..."
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 transition-colors resize-none"
            ></textarea>
            <p class="text-sm text-gray-500 mt-1">En az 10 karakter gerekli</p>
          </div>

          <!-- Submit Button -->
          <button 
            type="submit"
            :disabled="!isFormValid || serviceRequestStore.isLoading"
            class="w-full bg-brand-500 text-white py-3 px-6 rounded-lg font-medium 
                   hover:bg-brand-600 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2
                   disabled:bg-gray-300 disabled:cursor-not-allowed
                   transition-all duration-200 flex items-center justify-center"
          >
            <svg 
              v-if="serviceRequestStore.isLoading" 
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24"
            >
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ serviceRequestStore.isLoading ? 'Gönderiliyor...' : 'Talep Gönder' }}
          </button>
        </form>
      </div>

      <!-- Request History -->
      <RequestHistory />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useServiceRequestStore } from '../stores/serviceRequestStore'
import { useGuestAuthStore } from '../stores/guestAuthStore'
import RequestHistory from '../components/RequestHistory.vue'

const serviceRequestStore = useServiceRequestStore()
const guestAuthStore = useGuestAuthStore()

// Form data
const formData = ref({
  request_type: '',
  description: ''
})

// Success message state
const showSuccessMessage = ref(false)

// Form validation
const isFormValid = computed(() => {
  return formData.value.request_type && 
         formData.value.description && 
         formData.value.description.length >= 10
})

// Handle form submission
async function handleSubmitRequest() {
  if (!isFormValid.value || !guestAuthStore.currentGuest) {
    return
  }

  const guest = guestAuthStore.currentGuest
  
  const requestData = {
    reservation_id: guest.id,
    hotel_id: guest.hotel_id,
    room_number: guest.room_number,
    request_type: formData.value.request_type as any,
    description: formData.value.description
  }

  const result = await serviceRequestStore.createRequest(requestData)
  
  if (result.success) {
    // Reset form
    formData.value = {
      request_type: '',
      description: ''
    }
    
    // Show success message
    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 5000)
    
    // Refresh the request history (RequestHistory component will handle this)
  }
}

// Load requests on mount
onMounted(async () => {
  if (guestAuthStore.currentGuest) {
    await serviceRequestStore.fetchRequests(guestAuthStore.currentGuest.id)
  }
})
</script> 