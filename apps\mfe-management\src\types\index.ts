// User types
export interface User {
  id: string
  email: string
  role: 'SUPER_ADMIN' | 'HOTEL_ADMIN' | 'HOTEL_STAFF'
  firstName?: string
  lastName?: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

// Hotel types
export interface Hotel {
  id: string
  name: string
  slug: string
  description?: string
  address?: string
  city?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  logo?: string
  status: 'active' | 'inactive' | 'suspended'
  subscriptionPlan?: string
  subscriptionStatus?: 'active' | 'cancelled' | 'past_due'
  mfeConfig?: MfeConfig
  createdAt: string
  updatedAt: string
}

// MFE Configuration types
export interface MfeConfig {
  hotelDashboard: {
    enabled: boolean
    url?: string
    customDomain?: string
    features: string[]
  }
  customerPortal: {
    enabled: boolean
    url?: string
    customDomain?: string
    features: string[]
  }
  theme?: {
    primaryColor?: string
    secondaryColor?: string
    logo?: string
    favicon?: string
  }
}

// Subscription types
export interface SubscriptionPlan {
  id: string
  name: string
  description?: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
  maxHotels?: number
  maxUsers?: number
  isActive: boolean
}

// Analytics types
export interface AnalyticsData {
  totalHotels: number
  activeHotels: number
  totalUsers: number
  activeUsers: number
  revenue: {
    monthly: number
    yearly: number
  }
  growth: {
    hotels: number
    users: number
    revenue: number
  }
}

// System Health types
export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  services: {
    database: ServiceStatus
    api: ServiceStatus
    storage: ServiceStatus
    cdn: ServiceStatus
  }
  metrics: {
    uptime: number
    responseTime: number
    errorRate: number
  }
}

export interface ServiceStatus {
  status: 'up' | 'down' | 'degraded'
  responseTime?: number
  lastCheck: string
}

// Announcement types
export interface Announcement {
  id: string
  title: string
  content: string
  type: 'info' | 'warning' | 'success' | 'error'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  targetAudience: 'all' | 'hotel_admins' | 'hotel_staff'
  isActive: boolean
  scheduledAt?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
}

// Audit Log types
export interface AuditLogEntry {
  id: string
  userId: string
  userEmail: string
  action: string
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: string
}

// API Response types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types
export interface HotelFormData {
  name: string
  slug: string
  description?: string
  address?: string
  city?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  subscriptionPlan?: string
}

export interface UserFormData {
  email: string
  firstName?: string
  lastName?: string
  role: User['role']
  hotelId?: string
}

// Error types
export interface ApiError {
  message: string
  code?: string
  details?: Record<string, any>
}
