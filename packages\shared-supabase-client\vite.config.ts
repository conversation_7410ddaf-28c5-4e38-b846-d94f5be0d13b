import { defineConfig } from 'vite'
import path from 'path'

export default defineConfig({
  build: {
    lib: {
      entry: path.resolve(__dirname, 'src/index.ts'),
      name: 'SharedSupabaseClient',
      fileName: (format) => `index.${format}.js`,
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: ['@supabase/supabase-js', 'vue'],
      output: {
        globals: {
          '@supabase/supabase-js': 'Supabase',
          'vue': 'Vue'
        }
      }
    },
    target: 'esnext',
    sourcemap: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
}) 