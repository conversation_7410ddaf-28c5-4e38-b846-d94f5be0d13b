<template>
  <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <!-- Icon and Trend -->
    <div class="flex items-center justify-between mb-4">
      <div v-if="icon" class="flex-shrink-0">
        <div :class="[
          'w-12 h-12 rounded-xl flex items-center justify-center',
          iconBgColor || 'bg-blue-100 dark:bg-blue-900/30'
        ]">
          <component :is="icon" :class="[
            'w-6 h-6',
            iconColor || 'text-blue-600 dark:text-blue-400'
          ]" />
        </div>
      </div>
      <div v-if="trend !== undefined" class="text-right">
        <span :class="[
          'text-sm font-bold',
          trend >= 0 ? 'text-green-500' : 'text-red-500'
        ]">
          {{ trend >= 0 ? '+' : '' }}{{ formatTrend(trend) }}
        </span>
      </div>
    </div>

    <!-- Value and Label -->
    <div class="space-y-1">
      <h3 class="text-2xl font-bold text-navy-700 dark:text-white">
        {{ formatValue(value) }}
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">{{ label }}</p>
      <p v-if="description" class="text-xs text-gray-500 dark:text-gray-500">{{ description }}</p>
    </div>

    <!-- Additional Info -->
    <div v-if="additionalInfo" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-600 dark:text-gray-400">{{ additionalInfo.label }}</span>
        <span class="font-medium text-navy-700 dark:text-white">{{ additionalInfo.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'

interface AdditionalInfo {
  label: string
  value: string | number
}

interface Props {
  label: string
  value: string | number
  icon?: Component
  iconColor?: string
  iconBgColor?: string
  trend?: number
  description?: string
  additionalInfo?: AdditionalInfo
  format?: 'number' | 'currency' | 'percentage' | 'time'
}

const props = defineProps<Props>()

const formatValue = (value: string | number): string => {
  if (typeof value === 'string') return value
  
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY'
      }).format(value)
    case 'percentage':
      return `${value}%`
    case 'number':
      return new Intl.NumberFormat('tr-TR').format(value)
    case 'time':
      return `${value}dk`
    default:
      return value.toString()
  }
}

const formatTrend = (trend: number): string => {
  if (props.format === 'percentage') {
    return `${trend}%`
  }
  return trend.toString()
}
</script>

<style scoped>
/* Management metric card specific styles */
</style>
