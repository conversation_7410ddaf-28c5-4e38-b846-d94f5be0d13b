import {
  crossMFERealtimeService,
  type CrossMFEEvent
} from './CrossMFERealtimeService'
import { supabase } from '../index'

export interface SyncConfiguration {
  /**
   * Tables to synchronize across MFEs
   */
  tables: string[]
  
  /**
   * Hotel ID for filtering data
   */
  hotelId: string
  
  /**
   * User ID for context
   */
  userId: string
  
  /**
   * Sync interval in milliseconds
   */
  syncInterval?: number
  
  /**
   * Enable conflict resolution
   */
  enableConflictResolution?: boolean
  
  /**
   * Maximum retry attempts for failed syncs
   */
  maxRetries?: number
}

export interface SyncStatus {
  isActive: boolean
  lastSyncTime: number | null
  syncCount: number
  errorCount: number
  conflictCount: number
  activeTables: string[]
}

export class CrossMFEDataSynchronizer {
  private static instance: CrossMFEDataSynchronizer | null = null
  
  private config: SyncConfiguration | null = null
  private syncTimer: NodeJS.Timeout | null = null
  private isActive = false
  private syncStatus: SyncStatus = {
    isActive: false,
    lastSyncTime: null,
    syncCount: 0,
    errorCount: 0,
    conflictCount: 0,
    activeTables: []
  }
  
  private tableVersions: Map<string, number> = new Map()
  private syncQueue: Array<{ table: string; operation: 'sync' | 'validate'; priority: number }> = []
  private isProcessingQueue = false

  /**
   * Get singleton instance
   */
  public static getInstance(): CrossMFEDataSynchronizer {
    if (!CrossMFEDataSynchronizer.instance) {
      CrossMFEDataSynchronizer.instance = new CrossMFEDataSynchronizer()
    }
    return CrossMFEDataSynchronizer.instance
  }

  /**
   * Initialize the synchronizer
   */
  public async initialize(config: SyncConfiguration): Promise<void> {
    this.config = {
      syncInterval: 2 * 60 * 1000, // 2 minutes default
      enableConflictResolution: true,
      maxRetries: 3,
      ...config
    }

    console.log('Initializing CrossMFEDataSynchronizer', this.config)

    // Initialize table versions
    for (const table of this.config.tables) {
      this.tableVersions.set(table, 1)
    }

    // Subscribe to cross-MFE events
    this.setupEventHandlers()

    // Start periodic sync
    this.startPeriodicSync()

    this.isActive = true
    this.syncStatus.isActive = true
    this.syncStatus.activeTables = [...this.config.tables]

    console.log('CrossMFEDataSynchronizer initialized successfully')
  }

  /**
   * Setup event handlers for cross-MFE synchronization
   */
  private setupEventHandlers(): void {
    // Handle specific table updates
    const tableEvents = [
      'hotel_data_change',
      'room_status_change',
      'service_request_change',
      'task_status_change',
      'user_profile_change',
      'notification_change'
    ]

    tableEvents.forEach(eventType => {
      crossMFERealtimeService.subscribe(eventType, this.handleTableUpdate.bind(this))
    })
  }



  /**
   * Handle table-specific updates
   */
  private handleTableUpdate(event: CrossMFEEvent): void {
    const table = this.getTableFromEvent(event.type as any)
    if (table && this.config?.tables.includes(table)) {
      console.log(`Table update received for ${table}:`, event.payload)
      
      // Increment version and sync
      const currentVersion = this.tableVersions.get(table) || 1
      this.tableVersions.set(table, currentVersion + 1)
      
      // Add to sync queue
      this.addToSyncQueue(table, 'sync', 1)
    }
  }

  /**
   * Get table name from event type
   */
  private getTableFromEvent(eventType: string): string | null {
    const eventTableMap: Record<string, string> = {
      'hotel_data_change': 'hotels',
      'room_status_change': 'rooms',
      'service_request_change': 'room_service_orders',
      'task_status_change': 'maintenance_tasks',
      'user_profile_change': 'user_profiles',
      'notification_change': 'notifications'
    }
    
    return eventTableMap[eventType] || null
  }

  /**
   * Add item to sync queue
   */
  private addToSyncQueue(table: string, operation: 'sync' | 'validate', priority: number): void {
    // Remove existing entries for the same table
    this.syncQueue = this.syncQueue.filter(item => item.table !== table)
    
    // Add new entry
    this.syncQueue.push({ table, operation, priority })
    
    // Sort by priority (higher priority first)
    this.syncQueue.sort((a, b) => b.priority - a.priority)
    
    // Process queue
    this.processQueue()
  }

  /**
   * Process sync queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.syncQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    try {
      while (this.syncQueue.length > 0) {
        const item = this.syncQueue.shift()!
        
        try {
          if (item.operation === 'sync') {
            await this.syncTable(item.table)
          } else if (item.operation === 'validate') {
            await this.validateTable(item.table)
          }
        } catch (error) {
          console.error(`Failed to ${item.operation} table ${item.table}:`, error)
          this.syncStatus.errorCount++
        }
        
        // Small delay between operations
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    } finally {
      this.isProcessingQueue = false
    }
  }

  /**
   * Sync specific table data
   */
  private async syncTable(table: string): Promise<void> {
    if (!this.config) return

    console.log(`Syncing table: ${table}`)

    try {
      // Fetch latest data from Supabase
      let query = supabase.from(table as any).select('*')

      // Add hotel_id filter if the table has this column
      if (['rooms', 'room_service_orders', 'service_requests', 'maintenance_tasks'].includes(table)) {
        query = query.eq('hotel_id', this.config.hotelId)
      }

      // Add ordering if the table has updated_at column
      if (['rooms', 'room_service_orders', 'service_requests', 'maintenance_tasks', 'hotels'].includes(table)) {
        query = query.order('updated_at', { ascending: false })
      }

      const { data, error } = await query

      if (error) {
        throw error
      }

      // Data synced successfully

      this.syncStatus.syncCount++
      this.syncStatus.lastSyncTime = Date.now()

      console.log(`Successfully synced ${table}: ${data?.length || 0} records`)
    } catch (error) {
      console.error(`Failed to sync table ${table}:`, error)
      this.syncStatus.errorCount++
      throw error
    }
  }

  /**
   * Validate table data consistency
   */
  private async validateTable(table: string): Promise<void> {
    console.log(`Validating table: ${table}`)

    try {
      // Perform sync to validate data
      await this.syncTable(table)
    } catch (error) {
      console.error(`Failed to validate table ${table}:`, error)
      this.syncStatus.errorCount++
    }
  }

  /**
   * Start periodic synchronization
   */
  private startPeriodicSync(): void {
    if (!this.config || this.syncTimer) return

    this.syncTimer = setInterval(() => {
      console.log('Performing periodic sync...')
      
      // Add all tables to sync queue with low priority
      this.config!.tables.forEach(table => {
        this.addToSyncQueue(table, 'validate', 0)
      })
    }, this.config.syncInterval)

    console.log(`Started periodic sync with interval: ${this.config.syncInterval}ms`)
  }

  /**
   * Stop periodic synchronization
   */
  private stopPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
      console.log('Stopped periodic sync')
    }
  }

  /**
   * Force sync all tables
   */
  public async forceSyncAll(): Promise<void> {
    if (!this.config) {
      throw new Error('Synchronizer not initialized')
    }

    console.log('Force syncing all tables...')

    for (const table of this.config.tables) {
      this.addToSyncQueue(table, 'sync', 3) // High priority
    }

    // Wait for queue to process
    while (this.syncQueue.length > 0 || this.isProcessingQueue) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    console.log('Force sync completed')
  }

  /**
   * Get synchronization status
   */
  public getStatus(): SyncStatus {
    return { ...this.syncStatus }
  }

  /**
   * Reset synchronization statistics
   */
  public resetStats(): void {
    this.syncStatus.syncCount = 0
    this.syncStatus.errorCount = 0
    this.syncStatus.conflictCount = 0
    this.syncStatus.lastSyncTime = null
  }

  /**
   * Stop synchronization
   */
  public stop(): void {
    this.stopPeriodicSync()
    this.isActive = false
    this.syncStatus.isActive = false
    this.syncQueue = []
    
    console.log('CrossMFEDataSynchronizer stopped')
  }
}

// Export singleton instance
export const crossMFEDataSynchronizer = CrossMFEDataSynchronizer.getInstance()
