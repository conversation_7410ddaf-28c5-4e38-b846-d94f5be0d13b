import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '../stores/authStore'
// import HomeView from '../views/HomeView.vue' // Unused import
import LoginView from '../views/LoginView.vue'

// Simple promotional website routes
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/HomePage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: LoginView
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/AboutPage.vue'),
    meta: {
      title: 'Hakkımızda - Hotelexia'
    }
  },
  {
    path: '/features',
    name: 'features',
    component: () => import('../views/FeaturesPage.vue'),
    meta: {
      title: 'Özellikler - Hotelexia'
    }
  },
  {
    path: '/contact',
    name: 'contact',
    component: () => import('../views/ContactPage.vue'),
    meta: {
      title: 'İletişim - Hotelexia'
    }
  },

  // Customer Portal Routes (customer.hotelexia.com)
  {
    path: '/guest',
    redirect: '/guest/login'
  },

  {
    path: '/guest/onboarding',
    name: 'guest-onboarding',
    component: () => import('mfeCustomerPortal/OnboardingView'),
    meta: {
      title: 'Hoş Geldiniz - Misafir Portalı',
      domain: 'customer'
    }
  },
  {
    path: '/guest/login',
    name: 'guest-login',
    component: () => import('mfeCustomerPortal/GuestLoginView'),
    meta: {
      title: 'Giriş - Misafir Portalı',
      domain: 'customer'
    }
  },
  {
    path: '/guest/dashboard',
    name: 'guest-dashboard',
    component: () => import('mfeCustomerPortal/DashboardView'),
    meta: {
      title: 'Ana Sayfa - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/room-service',
    name: 'guest-room-service',
    component: () => import('mfeCustomerPortal/RoomServiceMenuView'),
    meta: {
      title: 'Oda Servisi - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/room-service/:category',
    name: 'guest-room-service-category',
    component: () => import('mfeCustomerPortal/RoomServiceCategoryView'),
    meta: {
      title: 'Oda Servisi Kategorisi - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/activities',
    name: 'guest-activities',
    component: () => import('mfeCustomerPortal/ActivitiesView'),
    meta: {
      title: 'Günün Programı - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/activity-detail/:id',
    name: 'guest-activity-detail',
    component: () => import('mfeCustomerPortal/ActivityDetailView'),
    meta: {
      title: 'Aktivite Detayı - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/services',
    name: 'guest-services',
    component: () => import('mfeCustomerPortal/ServicesView'),
    meta: {
      title: 'Hizmetler - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/notifications',
    name: 'guest-notifications',
    component: () => import('mfeCustomerPortal/NotificationsView'),
    meta: {
      title: 'Bildirimler - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/cart',
    name: 'guest-cart',
    component: () => import('mfeCustomerPortal/CartView'),
    meta: {
      title: 'Sepetim - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/order-success',
    name: 'guest-order-success',
    component: () => import('mfeCustomerPortal/OrderSuccessView'),
    meta: {
      title: 'Sipariş Başarılı - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },
  {
    path: '/guest/order-failed',
    name: 'guest-order-failed',
    component: () => import('mfeCustomerPortal/OrderFailedView'),
    meta: {
      title: 'Sipariş Başarısız - Misafir Portalı',
      requiresGuestAuth: true,
      domain: 'customer'
    }
  },

  // Universal Login Routes for Role-Based Authentication
  {
    path: '/guest/auth/login',
    name: 'guest-auth-login',
    component: () => import('../views/LoginView.vue'),
    meta: {
      title: 'Misafir Girişi - Hotelexia',
      requiresGuest: true,
      domain: 'customer'
    }
  },
  {
    path: '/hotel/auth/login',
    name: 'hotel-auth-login',
    component: () => import('mfeHotelDashboard/LoginView'),
    meta: {
      title: 'Otel Personeli Girişi - Hotelexia',
      requiresGuest: true,
      domain: 'hotel'
    }
  },
  {
    path: '/management/auth/login',
    name: 'management-auth-login',
    component: () => import('../views/LoginView.vue'),
    meta: {
      title: 'Süper Admin Girişi - Hotelexia',
      requiresGuest: true,
      domain: 'management'
    }
  },

  // Hotel Dashboard Routes (hotel.hotelexia.com)
  {
    path: '/hotel',
    redirect: '/hotel/dashboard'
  },
  {
    path: '/hotel/dashboard',
    name: 'hotel-dashboard',
    component: () => import('hotelDashboard/Dashboard'),
    meta: {
      title: 'Ana Sayfa - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/operations/overview',
    name: 'hotel-operations-overview',
    component: () => import('hotelDashboard/OperationsOverview'),
    meta: {
      title: 'Operasyon Özeti - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/operations/housekeeping/overview',
    name: 'hotel-housekeeping-overview',
    component: () => import('hotelDashboard/HousekeepingOverview'),
    meta: {
      title: 'Kat Hizmetleri Özeti - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/operations/housekeeping/task-management',
    name: 'hotel-housekeeping-tasks',
    component: () => import('hotelDashboard/TaskManagement'),
    meta: {
      title: 'Görev Yönetimi - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/operations/maintenance/overview',
    name: 'hotel-maintenance-overview',
    component: () => import('hotelDashboard/MaintenanceOverview'),
    meta: {
      title: 'Bakım Onarım Özeti - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/content/overview',
    name: 'hotel-content-overview',
    component: () => import('hotelDashboard/ContentOverview'),
    meta: {
      title: 'İçerik Yönetimi Özeti - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/content/room-service/management',
    name: 'hotel-room-service-management',
    component: () => import('hotelDashboard/RoomServiceManagement'),
    meta: {
      title: 'Oda Servisi Yönetimi - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/content/activities/management',
    name: 'hotel-activities-management',
    component: () => import('hotelDashboard/ActivityManagement'),
    meta: {
      title: 'Aktivite Yönetimi - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/notifications',
    name: 'hotel-notifications',
    component: () => import('hotelDashboard/NotificationCenter'),
    meta: {
      title: 'Bildirim Merkezi - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/reports/customer',
    name: 'hotel-reports-customer',
    component: () => import('hotelDashboard/CustomerReports'),
    meta: {
      title: 'Müşteri Raporları - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/reports/staff',
    name: 'hotel-reports-staff',
    component: () => import('hotelDashboard/StaffReports'),
    meta: {
      title: 'Personel Raporları - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/reports/operational',
    name: 'hotel-reports-operational',
    component: () => import('hotelDashboard/OperationalReports'),
    meta: {
      title: 'Operasyonel Raporlar - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/settings',
    name: 'hotel-settings',
    component: () => import('hotelDashboard/Settings'),
    meta: {
      title: 'Ayarlar - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },
  {
    path: '/hotel/profile',
    name: 'hotel-profile',
    component: () => import('hotelDashboard/Profile'),
    meta: {
      title: 'Profil - Otel Yönetimi',
      requiresAuth: true,
      domain: 'hotel'
    }
  },

  // Management Portal Routes (management.hotelexia.com)
  {
    path: '/management',
    redirect: '/management/dashboard'
  },
  {
    path: '/management/dashboard',
    name: 'management-dashboard',
    component: () => import('mfeManagement/Dashboard'),
    meta: {
      title: 'Dashboard - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/hotels',
    name: 'management-hotels',
    component: () => import('mfeManagement/HotelList'),
    meta: {
      title: 'Oteller - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/hotels/new',
    name: 'management-hotel-new',
    component: () => import('mfeManagement/HotelSetupWizard'),
    meta: {
      title: 'Yeni Otel Kurulumu - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/hotels/:id/edit',
    name: 'management-hotel-edit',
    component: () => import('mfeManagement/HotelEdit'),
    meta: {
      title: 'Otel Düzenle - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/hotels/:id/mfes',
    name: 'management-hotel-mfes',
    component: () => import('mfeManagement/HotelMfeConfig'),
    meta: {
      title: 'MFE Yönetimi - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/settings',
    name: 'management-settings',
    component: () => import('mfeManagement/Settings'),
    meta: {
      title: 'Platform Ayarları - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/billing',
    name: 'management-billing',
    component: () => import('mfeManagement/Billing'),
    meta: {
      title: 'Abonelik Yönetimi - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/billing/configure',
    name: 'management-billing-configure',
    component: () => import('mfeManagement/ConfigurePlansWizard'),
    meta: {
      title: 'Abonelik Planlarını Yapılandır - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/analytics',
    name: 'management-analytics',
    component: () => import('mfeManagement/Analytics'),
    meta: {
      title: 'Platform Analitikleri - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/system-health',
    name: 'management-system-health',
    component: () => import('mfeManagement/SystemHealth'),
    meta: {
      title: 'Sistem Sağlığı - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/announcements',
    name: 'management-announcements',
    component: () => import('mfeManagement/Announcements'),
    meta: {
      title: 'Global Duyurular - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/management/audit-log',
    name: 'management-audit-log',
    component: () => import('mfeManagement/AuditLog'),
    meta: {
      title: 'Denetim Günlüğü - Süper Admin',
      requiresAuth: true,
      domain: 'management'
    }
  },
  {
    path: '/booking/:hotelId',
    name: 'Booking',
    component: () => import('../views/NotFoundPage.vue'), // Placeholder until MFE is ready
    meta: { requiresAuth: true }
  },
  {
    path: '/my-reservations',
    name: 'MyReservations',
    component: () => import('../views/NotFoundPage.vue'), // Placeholder until MFE is ready
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    component: () => import('../views/NotFoundPage.vue'),
    meta: {
      title: 'Sayfa Bulunamadı - Hotelexia'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Authentication and authorization guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Update page title
  document.title = to.meta.title as string || 'Hotelexia'

  // Wait for auth initialization with timeout to prevent infinite loading
  let authWaitCount = 0
  const maxAuthWait = 50 // 5 seconds max wait

  while (authStore.isLoading && authWaitCount < maxAuthWait) {
    await new Promise(resolve => setTimeout(resolve, 100))
    authWaitCount++
  }

  // If auth is still loading after timeout, log warning but continue
  if (authStore.isLoading) {
    console.warn('Auth initialization timeout - proceeding with navigation')
  }

  // Check if route requires authentication
  const requiresAuth = to.meta.requiresAuth
  const requiresGuest = to.meta.requiresGuest
  const requiresGuestAuth = to.meta.requiresGuestAuth
  const domain = to.meta.domain

  // If user is not authenticated and route requires auth
  if (requiresAuth && !authStore.isAuthenticated) {
    // Store the intended destination for post-login redirect
    if (to.path !== '/') {
      sessionStorage.setItem('hotelexia_redirect_after_login', to.fullPath)
    }
    
    // Redirect to appropriate login based on domain
    switch (domain) {
      case 'hotel':
        return next({ name: 'hotel-auth-login' })
      case 'management':
        return next({ name: 'management-auth-login' })
      case 'customer':
        return next({ name: 'guest-auth-login' })
      default:
        return next('/')
    }
  }

  // If user is authenticated and trying to access guest-only routes
  if (requiresGuest && authStore.isAuthenticated) {
    // Check for stored redirect path
    const redirectPath = sessionStorage.getItem('hotelexia_redirect_after_login')
    if (redirectPath) {
      sessionStorage.removeItem('hotelexia_redirect_after_login')
      return next(redirectPath)
    }
    
    authStore.redirectToDashboard()
    return
  }

  // Special handling for customer portal guest auth
  if (requiresGuestAuth && domain === 'customer') {
    // This will be handled by the customer portal's own auth system
    return next()
  }

  // Validate portal access for authenticated users
  if (authStore.isAuthenticated && domain) {
    let hasAccess = false
    
    switch (domain) {
      case 'customer':
        hasAccess = authStore.validatePortalAccess('guest')
        break
      case 'hotel':
        hasAccess = authStore.validatePortalAccess('hotel')
        break
      case 'management':
        hasAccess = authStore.validatePortalAccess('management')
        break
      default:
        hasAccess = true
    }

    if (!hasAccess) {
      // Show error message and redirect to appropriate dashboard
      console.warn(`🚫 Access denied to ${domain} portal for role: ${authStore.userRole}`)
      
      if (authStore.userRole) {
        authStore.redirectToDashboard()
      } else {
        next('/')
      }
      return
    }
  }

  // If we're coming from a successful login, clear any pending redirects
  if (authStore.isAuthenticated && from.path.includes('/auth/login')) {
    const redirectPath = sessionStorage.getItem('hotelexia_redirect_after_login')
    if (redirectPath && redirectPath !== to.fullPath) {
      sessionStorage.removeItem('hotelexia_redirect_after_login')
      return next(redirectPath)
    }
  }

  next()
})

export { routes }
export default router 