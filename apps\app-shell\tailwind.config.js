/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "../shared-components/src/**/*.{vue,js,ts,jsx,tsx}",
    "../customer-portal/src/**/*.{vue,js,ts,jsx,tsx}",
    "../hotel-portal/src/**/*.{vue,js,ts,jsx,tsx}",
    "../manager-portal/src/**/*.{vue,js,ts,jsx,tsx}",
    "../../packages/mfe-auth/src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['DM Sans', 'Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      },
      colors: {
        // Horizon UI Renk Sistemi - Brand Colors
        brand: {
          50: '#f0f9ff',
          100: '#E9E3FF',
          200: '#422AFB',
          300: '#422AFB',
          400: '#7551FF',
          500: '#422AFB',
          600: '#3311DB',
          700: '#02044A',
          800: '#190793',
          900: '#11047A',
        },
        // Secondary Gray - Horizon UI özel griler
        secondaryGray: {
          100: '#E0E5F2',
          200: '#E1E9F8',
          300: '#F4F7FE',
          400: '#E9EDF7',
          500: '#8F9BBA',
          600: '#A3AED0',
          700: '#707EAE',
          800: '#707EAE',
          900: '#1B2559',
        },
        // Navy - Horizon UI ana koyu renk paleti
        navy: {
          50: '#d0dcfb',
          100: '#aac0fe',
          200: '#a3b9f8',
          300: '#728fea',
          400: '#3652ba',
          500: '#1b3bbb',
          600: '#24388a',
          700: '#1B254B',
          800: '#111c44',
          900: '#0b1437',
        },
        // Status Renkleri - Horizon UI uyumlu
        red: {
          50: '#fef2f2',
          100: '#FEEFEE',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#EE5D50',
          600: '#E31A1A',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        blue: {
          50: '#EFF4FB',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3965FF',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        orange: {
          50: '#fffbeb',
          100: '#FFF6DA',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#FFB547',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        green: {
          50: '#f0fdf4',
          100: '#E6FAF5',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#01B574',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        // Primary alias to brand
        primary: {
          50: '#f0f9ff',
          100: '#E9E3FF',
          200: '#422AFB',
          300: '#422AFB',
          400: '#7551FF',
          500: '#422AFB',
          600: '#3311DB',
          700: '#02044A',
          800: '#190793',
          900: '#11047A',
        },
        // Overriding gray with Horizon UI compatible grays
        gray: {
          50: '#f9fafb',
          100: '#FAFCFE',
          200: '#E1E9F8',
          300: '#F4F7FE',
          400: '#A3AED0',
          500: '#8F9BBA',
          600: '#707EAE',
          700: '#1B2559',
          800: '#111c44',
          900: '#0b1437',
        },
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      boxShadow: {
        // Horizon UI uyumlu shadow sistemi
        'xs': '0 0 0 1px rgba(0, 0, 0, 0.05)',
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        // Horizon UI özel shadow'ları
        'soft': '0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'medium': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'hard': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'brand': '0 0 40px rgba(66, 42, 251, 0.15)',
        'card': '0px 18px 40px rgba(112, 144, 176, 0.12)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'fade-out': 'fadeOut 0.3s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'bounce-in': 'bounceIn 0.6s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float': 'float 3s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideIn: {
          '0%': { 
            opacity: '0',
            transform: 'translateY(-10px)',
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideUp: {
          '0%': { 
            opacity: '0',
            transform: 'translateY(10px)',
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideDown: {
          '0%': { 
            opacity: '0',
            transform: 'translateY(-10px)',
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        bounceIn: {
          '0%': {
            opacity: '0',
            transform: 'scale(0.3)',
          },
          '50%': {
            transform: 'scale(1.05)',
          },
          '70%': {
            transform: 'scale(0.9)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 20px rgba(66, 42, 251, 0.2)' },
          '100%': { boxShadow: '0 0 30px rgba(66, 42, 251, 0.4)' },
        },
      },
      screens: {
        'xs': '475px',
        '2sm': '380px',
        '3xl': '1920px',
      },
    },
  },
  plugins: [],
  // Dark mode configuration
  darkMode: 'class',
} 