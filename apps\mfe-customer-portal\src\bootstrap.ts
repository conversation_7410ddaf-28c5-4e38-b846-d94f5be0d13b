import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './style.css'

// Bootstrap function for MFE integration
export function bootstrap(container?: HTMLElement) {
  // Mount to provided container or default #app
  const mountElement = container || document.getElementById('app')

  if (!mountElement) {
    console.error('Customer Portal MFE: No mount element found')
    return null
  }

  // Check if already mounted to prevent double mounting
  if (mountElement.hasAttribute('data-vue-app-mounted')) {
    console.warn('Customer Portal MFE: Already mounted, skipping...')
    return (window as any).__CUSTOMER_PORTAL_APP__
  }

  const app = createApp(App)

  app.use(createPinia())
  app.use(router)

  try {
    app.mount(mountElement)

    // Mark as mounted and store reference
    mountElement.setAttribute('data-vue-app-mounted', 'true')
    ;(window as any).__CUSTOMER_PORTAL_APP__ = app

    console.log('Customer Portal MFE bootstrapped successfully')
  } catch (error) {
    console.error('Customer Portal MFE: Mount failed', error)
    return null
  }

  return app
}

// Unmount function for cleanup
export function unmount(container?: HTMLElement) {
  const mountElement = container || document.getElementById('app')
  const app = (window as any).__CUSTOMER_PORTAL_APP__

  if (app && mountElement) {
    try {
      app.unmount()
      mountElement.removeAttribute('data-vue-app-mounted')
      delete (window as any).__CUSTOMER_PORTAL_APP__
      console.log('Customer Portal MFE unmounted successfully')
    } catch (error) {
      console.error('Customer Portal MFE: Unmount failed', error)
    }
  }
}

// Auto-bootstrap in standalone mode
if (process.env.NODE_ENV === 'development' || !(window as any).__POWERED_BY_QIANKUN__) {
  bootstrap()
}

export default bootstrap
