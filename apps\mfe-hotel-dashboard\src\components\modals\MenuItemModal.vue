<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" @click="closeModal">
    <!-- Modal Content -->
    <div class="bg-white dark:bg-navy-800 rounded-lg shadow-xl max-w-2xl w-full m-4 max-h-[90vh] overflow-y-auto" @click.stop>
      <!-- <PERSON><PERSON> Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-navy-700 dark:text-white">
          {{ isEditing ? 'Menü Öğesini Düzenle' : '<PERSON>ni Menü Öğesi Olu<PERSON>tur' }}
        </h3>
      </div>

      <!-- Modal Body -->
      <form @submit.prevent="handleSubmit" class="px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Left Column -->
          <div class="space-y-4">
            <!-- Name -->
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ürün Adı *
              </label>
              <input
                id="name"
                v-model="formData.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="örn. Klasik Cheeseburger"
              >
            </div>

            <!-- Category -->
            <div>
              <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Kategori *
              </label>
              <select
                id="category"
                v-model="formData.category_id"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              >
                <option value="">Bir kategori seçin</option>
                <option 
                  v-for="category in roomServiceCategories" 
                  :key="category.id" 
                  :value="category.id"
                >
                  {{ category.name }}
                </option>
              </select>
            </div>

            <!-- Item Code -->
            <div>
              <label for="item_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ürün Kodu *
              </label>
              <input
                id="item_code"
                v-model="formData.item_code"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="örn. ET-001, SAL-001"
              >
            </div>

            <!-- Price -->
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Fiyat (₺) *
              </label>
              <input
                id="price"
                v-model.number="formData.price"
                type="number"
                min="0"
                step="0.01"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="0.00"
              >
            </div>

            <!-- Display Order -->
            <div>
              <label for="display_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Sıralama
              </label>
              <input
                id="display_order"
                v-model.number="formData.display_order"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="0"
              >
            </div>

            <!-- Preparation Time -->
            <div>
              <label for="preparation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Hazırlık Süresi (dakika)
              </label>
              <input
                id="preparation_time"
                v-model.number="formData.preparation_time_minutes"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="15"
              >
            </div>
          </div>

          <!-- Right Column -->
          <div class="space-y-4">
            <!-- Description -->
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Açıklama
              </label>
              <textarea
                id="description"
                v-model="formData.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="Ürün hakkında kısa açıklama"
              ></textarea>
            </div>

            <!-- Tags -->
            <div>
              <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Etiketler (virgül ile ayırın)
              </label>
              <input
                id="tags"
                v-model="tagsText"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="örn. acılı, lezzetli, popüler"
              >
            </div>

            <!-- Dietary Preferences -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Beslenme Tercihleri
              </label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input
                    v-model="formData.is_vegetarian"
                    type="checkbox"
                    class="rounded border-gray-300 text-brand-600 shadow-sm focus:border-brand-300 focus:ring focus:ring-brand-200 focus:ring-opacity-50"
                  >
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Vejetaryen</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="formData.is_vegan"
                    type="checkbox"
                    class="rounded border-gray-300 text-brand-600 shadow-sm focus:border-brand-300 focus:ring focus:ring-brand-200 focus:ring-opacity-50"
                  >
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Vegan</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="formData.is_gluten_free"
                    type="checkbox"
                    class="rounded border-gray-300 text-brand-600 shadow-sm focus:border-brand-300 focus:ring focus:ring-brand-200 focus:ring-opacity-50"
                  >
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Glutensiz</span>
                </label>
              </div>
            </div>

            <!-- Image Upload Placeholder -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Görsel
              </label>
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center">
                <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <p class="mt-2 text-xs text-gray-600 dark:text-gray-400">
                  Görsel yükleme (Supabase Storage ile bağlanacak)
                </p>
              </div>
            </div>

            <!-- Available Toggle -->
            <div>
              <label class="flex items-center">
                <input
                  v-model="formData.isAvailable"
                  type="checkbox"
                  class="sr-only"
                >
                <div class="relative">
                  <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
                  <div 
                    class="absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition transform"
                    :class="formData.isAvailable ? 'translate-x-6 bg-brand-500' : ''"
                  ></div>
                </div>
                <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Sipariş için uygun
                </span>
              </label>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-600 transition-colors"
          >
            İptal
          </button>
          <button
            type="submit"
            :disabled="!isFormValid"
            class="px-4 py-2 text-sm font-medium text-white bg-brand-500 hover:bg-brand-600 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-lg transition-colors"
          >
            {{ isEditing ? 'Güncelle' : 'Oluştur' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import type { MenuItem, CreateMenuItemData } from '@/types/management'

interface Props {
  item?: MenuItem | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: CreateMenuItemData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const managementStore = useManagementStore()

const isEditing = computed(() => !!props.item)

// Get room service categories for dropdown
const roomServiceCategories = computed(() => 
  managementStore.categories.filter(category => category.type === 'ROOM_SERVICE')
)

const formData = ref<CreateMenuItemData>({
  name: '',
  category: '',
  category_id: '',
  description: '',
  price: 0,
  isAvailable: true,
  display_order: 0,
  preparation_time_minutes: null,
  item_code: '',
  is_vegetarian: false,
  is_vegan: false,
  is_gluten_free: false,
  tags: []
})

// Tags as text for easier editing
const tagsText = ref('')

// Form validation
const isFormValid = computed(() => {
  return formData.value.name.trim() !== '' && 
         formData.value.category_id !== '' && 
         formData.value.item_code.trim() !== '' &&
         formData.value.price > 0
})

// Watch for tags text changes and convert to array
watch(tagsText, (newValue) => {
  formData.value.tags = newValue.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
})

// Watch for category_id changes to update category name
watch(() => formData.value.category_id, (newCategoryId) => {
  const category = roomServiceCategories.value.find(cat => cat.id === newCategoryId)
  if (category) {
    formData.value.category = category.name
  }
})

const closeModal = () => {
  emit('close')
}

const handleSubmit = () => {
  if (isFormValid.value) {
    emit('save', { ...formData.value })
  }
}

onMounted(() => {
  if (props.item) {
    formData.value = {
      name: props.item.name,
      category: props.item.category,
      category_id: props.item.category_id,
      description: props.item.description,
      price: props.item.price,
      isAvailable: props.item.isAvailable,
      display_order: props.item.display_order,
      preparation_time_minutes: props.item.preparation_time_minutes,
      item_code: props.item.item_code,
      is_vegetarian: props.item.is_vegetarian,
      is_vegan: props.item.is_vegan,
      is_gluten_free: props.item.is_gluten_free,
      tags: [...props.item.tags]
    }
    tagsText.value = props.item.tags.join(', ')
  }
})
</script>

<style scoped>
/* Custom checkbox styling */
input[type="checkbox"]:checked + div {
  background-color: #6366f1;
}
</style> 