<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Performans Analizi</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">Temizlik departmanı personel performans raporları</p>
        </div>
        
        <!-- Filters -->
        <div class="flex items-center space-x-4">
          <select 
            v-model="selectedPeriod" 
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          >
            <option value="today">Bugün</option>
            <option value="week">Bu Hafta</option>
            <option value="month">Bu Ay</option>
            <option value="quarter">Bu Çeyrek</option>
          </select>
          
          <select 
            v-model="selectedDepartment" 
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          >
            <option value="all">Tüm Departmanlar</option>
            <option value="housekeeping">Temizlik</option>
            <option value="maintenance">Bakım</option>
            <option value="guest_services">Misafir Hizmetleri</option>
          </select>
          
          <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Raporu İndir
          </button>
        </div>
      </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <KpiCard
        title="Ortalama Görev Süresi"
        :value="averageTaskTime"
        format="time"
        color="blue"
        icon="ClockIcon"
        :trend="12"
        :additional-info="{ label: 'Ek Bilgi', value: 'dakika' }"
      />
      <KpiCard
        title="Tamamlanan Görevler"
        :value="completedTasks"
        format="number"
        color="green"
        icon="CheckCircleIcon"
        :trend="8"
        :additional-info="{ label: 'Ek Bilgi', value: 'bugün' }"
      />
      <KpiCard
        title="Ortalama Kalite Puanı"
        :value="averageQualityScore"
        format="percentage"
        color="purple"
        icon="StarIcon"
        :trend="15"
        :additional-info="{ label: 'Ek Bilgi', value: '/100' }"
      />
      <KpiCard
        title="Personel Verimliliği"
        :value="staffEfficiency"
        format="percentage"
        color="yellow"
        icon="TrendingUpIcon"
        :trend="5"
        :additional-info="{ label: 'Ek Bilgi', value: 'bu hafta' }"
      />
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Daily Performance Chart -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Günlük Performans Trendi</h3>
          <div class="flex items-center space-x-2">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Tamamlanan</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Kalite Puanı</span>
            </div>
          </div>
        </div>
        
        <!-- Simplified Chart Visualization -->
        <div class="h-64 flex items-end justify-between space-x-2">
          <div 
            v-for="(day, index) in weeklyData" 
            :key="index"
            class="flex-1 flex flex-col items-center space-y-2"
          >
            <div class="w-full bg-gray-100 dark:bg-gray-800 rounded-t-lg relative overflow-hidden" style="height: 200px;">
              <div 
                class="absolute bottom-0 w-full bg-blue-500 rounded-t-lg transition-all duration-500"
                :style="{ height: `${(day.completed / 20) * 100}%` }"
              ></div>
              <div 
                class="absolute bottom-0 w-full bg-green-500 opacity-60 rounded-t-lg transition-all duration-500"
                :style="{ height: `${day.quality}%` }"
              ></div>
            </div>
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800 dark:text-white">{{ day.day }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ day.completed }} görev</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Task Distribution Chart -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-6">Görev Dağılımı</h3>
        
        <!-- Pie Chart Simulation -->
        <div class="flex items-center justify-center mb-6">
          <div class="relative w-48 h-48">
            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              <!-- Background circle -->
              <circle cx="50" cy="50" r="40" fill="none" stroke="#f3f4f6" stroke-width="10" class="dark:stroke-gray-700"/>
              
              <!-- Oda Temizliği (45%) -->
              <circle 
                cx="50" cy="50" r="40" 
                fill="none" 
                stroke="#3b82f6" 
                stroke-width="10"
                stroke-dasharray="113.1 251.3"
                stroke-dashoffset="0"
              />
              
              <!-- Banyo Temizliği (25%) -->
              <circle 
                cx="50" cy="50" r="40" 
                fill="none" 
                stroke="#10b981" 
                stroke-width="10"
                stroke-dasharray="62.8 251.3"
                stroke-dashoffset="-113.1"
              />
              
              <!-- Yatak Düzenleme (20%) -->
              <circle 
                cx="50" cy="50" r="40" 
                fill="none" 
                stroke="#f59e0b" 
                stroke-width="10"
                stroke-dasharray="50.3 251.3"
                stroke-dashoffset="-175.9"
              />
              
              <!-- Diğer (10%) -->
              <circle 
                cx="50" cy="50" r="40" 
                fill="none" 
                stroke="#ef4444" 
                stroke-width="10"
                stroke-dasharray="25.1 251.3"
                stroke-dashoffset="-226.2"
              />
            </svg>
            
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-800 dark:text-white">{{ totalTasks }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Toplam Görev</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Legend -->
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <div>
              <div class="text-sm font-medium text-gray-800 dark:text-white">Oda Temizliği</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">45% ({{ Math.round(totalTasks * 0.45) }})</div>
            </div>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <div>
              <div class="text-sm font-medium text-gray-800 dark:text-white">Banyo Temizliği</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">25% ({{ Math.round(totalTasks * 0.25) }})</div>
            </div>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
            <div>
              <div class="text-sm font-medium text-gray-800 dark:text-white">Yatak Düzenleme</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">20% ({{ Math.round(totalTasks * 0.20) }})</div>
            </div>
          </div>
          <div class="flex items-center">
            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <div>
              <div class="text-sm font-medium text-gray-800 dark:text-white">Diğer</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">10% ({{ Math.round(totalTasks * 0.10) }})</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Leaderboards Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Top Performers -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">En İyi Performans</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">Bu hafta</span>
        </div>
        
        <div class="space-y-4">
          <div 
            v-for="(performer, index) in topPerformers" 
            :key="performer.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div 
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm',
                    index === 0 ? 'bg-yellow-500' : 
                    index === 1 ? 'bg-gray-400' : 
                    index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                  ]"
                >
                  {{ index + 1 }}
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <img :src="performer.avatar" :alt="performer.name" class="w-10 h-10 rounded-full object-cover">
                <div>
                  <div class="font-medium text-gray-800 dark:text-white">{{ performer.name }} {{ performer.surname }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ performer.department }}</div>
                </div>
              </div>
            </div>
            
            <div class="text-right">
              <div class="font-semibold text-gray-800 dark:text-white">{{ performer.score }}%</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ performer.completedTasks }} görev</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quality Scores -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Kalite Puanları</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">Son 30 gün</span>
        </div>
        
        <div class="space-y-4">
          <div 
            v-for="staff in qualityScores" 
            :key="staff.id"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <img :src="staff.avatar" :alt="staff.name" class="w-8 h-8 rounded-full object-cover">
              <div>
                <div class="font-medium text-gray-800 dark:text-white text-sm">{{ staff.name }} {{ staff.surname }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ staff.department }}</div>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 w-24">
                <div 
                  class="h-2 rounded-full transition-all duration-500"
                  :class="getQualityBarColor(staff.qualityScore)"
                  :style="{ width: `${staff.qualityScore}%` }"
                ></div>
              </div>
              <div class="text-sm font-semibold text-gray-800 dark:text-white w-12 text-right">
                {{ staff.qualityScore }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Performance Table -->
    <div class="mt-8 bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Detaylı Performans Tablosu</h3>
          <div class="flex items-center space-x-4">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Personel ara..."
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
            />
            <select 
              v-model="sortBy" 
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
            >
              <option value="name">İsim</option>
              <option value="completedTasks">Tamamlanan Görev</option>
              <option value="qualityScore">Kalite Puanı</option>
              <option value="efficiency">Verimlilik</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Personel</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Departman</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tamamlanan</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Kalite Puanı</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ortalama Süre</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Verimlilik</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Durum</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr 
              v-for="staff in filteredStaffPerformance" 
              :key="staff.id"
              class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img :src="staff.avatar" :alt="staff.name" class="w-10 h-10 rounded-full object-cover mr-3">
                  <div>
                    <div class="text-sm font-medium text-gray-800 dark:text-white">{{ staff.name }} {{ staff.surname }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">ID: {{ staff.id.substring(0, 8) }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                  {{ staff.department }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white font-medium">
                {{ staff.completedTasks }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 w-16 mr-3">
                    <div 
                      class="h-2 rounded-full transition-all duration-500"
                      :class="getQualityBarColor(staff.qualityScore)"
                      :style="{ width: `${staff.qualityScore}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-medium text-gray-800 dark:text-white">{{ staff.qualityScore }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">
                {{ staff.averageTime }} dk
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    staff.efficiency >= 90 ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                    staff.efficiency >= 75 ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                    'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                  ]"
                >
                  {{ staff.efficiency }}%
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    staff.status === 'AVAILABLE' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                    staff.status === 'BUSY' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
                    staff.status === 'ON_BREAK' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                    'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'
                  ]"
                >
                  {{ getStatusText(staff.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import KpiCard from './KpiCard.vue'
import type { StaffStatus } from '@/types/housekeeping'

// Store
const housekeepingStore = useHousekeepingStore()

// State
const selectedPeriod = ref('week')
const selectedDepartment = ref('all')
const searchQuery = ref('')
const sortBy = ref('name')

// Mock data for charts and analytics
const weeklyData = ref([
  { day: 'Pzt', completed: 18, quality: 92 },
  { day: 'Sal', completed: 15, quality: 88 },
  { day: 'Çar', completed: 20, quality: 95 },
  { day: 'Per', completed: 17, quality: 90 },
  { day: 'Cum', completed: 19, quality: 93 },
  { day: 'Cmt', completed: 14, quality: 87 },
  { day: 'Paz', completed: 12, quality: 89 }
])

// Computed
const averageTaskTime = computed(() => 42) // minutes
const completedTasks = computed(() => 127)
const averageQualityScore = computed(() => 91)
const staffEfficiency = computed(() => 88)
const totalTasks = computed(() => 156)

const topPerformers = computed(() => {
  return housekeepingStore.staff
    .map(staff => ({
      ...staff,
      score: Math.floor(Math.random() * 20) + 80, // 80-100 range
      completedTasks: Math.floor(Math.random() * 15) + 10 // 10-25 range
    }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 5)
})

const qualityScores = computed(() => {
  return housekeepingStore.staff.map(staff => ({
    ...staff,
    qualityScore: Math.floor(Math.random() * 25) + 75 // 75-100 range
  }))
})

const filteredStaffPerformance = computed(() => {
  let filtered = housekeepingStore.staff.map(staff => ({
    ...staff,
    completedTasks: Math.floor(Math.random() * 20) + 8,
    qualityScore: Math.floor(Math.random() * 25) + 75,
    averageTime: Math.floor(Math.random() * 20) + 30,
    efficiency: Math.floor(Math.random() * 30) + 70
  }))

  // Filter by search query
  if (searchQuery.value) {
    filtered = filtered.filter(staff =>
      `${staff.name} ${staff.surname}`.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // Sort by selected criteria
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'completedTasks':
        return b.completedTasks - a.completedTasks
      case 'qualityScore':
        return b.qualityScore - a.qualityScore
      case 'efficiency':
        return b.efficiency - a.efficiency
      default:
        return 0
    }
  })

  return filtered
})

// Methods
const getQualityBarColor = (score: number) => {
  if (score >= 90) return 'bg-green-500'
  if (score >= 80) return 'bg-yellow-500'
  if (score >= 70) return 'bg-orange-500'
  return 'bg-red-500'
}

const getStatusText = (status: StaffStatus) => {
  const texts = {
    'AVAILABLE': 'Müsait',
    'BUSY': 'Meşgul',
    'BREAK': 'Molada',
    'OFF_DUTY': 'Mesai Dışı'
  }
  return texts[status] || status
}

// Lifecycle
onMounted(() => {
  // Initialize any required data
})
</script> 