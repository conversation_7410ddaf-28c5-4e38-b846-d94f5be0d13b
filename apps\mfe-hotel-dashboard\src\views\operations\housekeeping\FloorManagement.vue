<template>
  <div class="p-6 bg-gray-50 dark:bg-navy-900 min-h-screen">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h1 class="text-2xl font-bold text-navy-700 dark:text-white"><PERSON></h1>
          <p class="text-gray-600 dark:text-gray-400">Otel katlarını ve oda durumlarını görü<PERSON>üleyin</p>
        </div>
        <div class="flex gap-3">
          <button
            @click="refreshFloorData"
            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            🔄 Yenile
          </button>
          <button
            @click="showFloorModal = true"
            class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            + <PERSON>
          </button>
        </div>
      </div>

      <!-- Overview Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Toplam Kat</p>
              <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ floors.length }}</p>
            </div>
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <BuildingOfficeIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Toplam Oda</p>
              <p class="text-2xl font-bold text-teal-600 dark:text-teal-400">{{ totalRooms }}</p>
            </div>
            <div class="w-10 h-10 bg-teal-100 dark:bg-teal-900/20 rounded-lg flex items-center justify-center">
              <HomeIcon class="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Müsait Odalar</p>
              <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ availableRooms }}</p>
            </div>
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <CheckCircleIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Temizlik Bekleyen</p>
              <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ roomsNeedingCleaning }}</p>
            </div>
            <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
              <ClockIcon class="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Floor Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="floor in floors"
        :key="floor.id"
        class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 overflow-hidden"
      >
        <!-- Floor Header -->
        <div class="bg-gradient-to-r from-brand-500 to-brand-600 p-4 text-white">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="text-lg font-semibold">{{ floor.name }}</h3>
              <p class="text-brand-100 text-sm">{{ floor.rooms.length }} oda</p>
            </div>
            <div class="text-right">
              <div class="text-sm text-brand-100">Supervisor</div>
              <div class="font-medium">{{ floor.supervisor }}</div>
            </div>
          </div>
        </div>

        <!-- Floor Stats -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <div class="grid grid-cols-4 gap-2 text-center">
            <div>
              <div class="text-lg font-bold text-green-600">{{ getFloorRoomsByStatus(floor, 'AVAILABLE').length }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400">Müsait</div>
            </div>
            <div>
              <div class="text-lg font-bold text-blue-600">{{ getFloorRoomsByStatus(floor, 'OCCUPIED').length }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400">Dolu</div>
            </div>
            <div>
              <div class="text-lg font-bold text-orange-600">{{ getFloorRoomsByStatus(floor, 'CLEANING').length }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400">Temizlik</div>
            </div>
            <div>
              <div class="text-lg font-bold text-red-600">{{ getFloorRoomsByStatus(floor, 'MAINTENANCE').length }}</div>
              <div class="text-xs text-gray-600 dark:text-gray-400">Bakım</div>
            </div>
          </div>
        </div>

        <!-- Room Grid -->
        <div class="p-4">
          <div class="grid grid-cols-6 gap-2">
            <div
              v-for="room in floor.rooms"
              :key="room.id"
              @click="selectRoom(room)"
              :class="getRoomStatusClass(room.status)"
              class="aspect-square rounded-lg flex items-center justify-center text-xs font-medium cursor-pointer transition-all hover:scale-105 hover:shadow-md"
            >
              {{ room.number }}
            </div>
          </div>
        </div>

        <!-- Floor Actions -->
        <div class="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600">
          <div class="flex gap-2">
            <button
              @click="viewFloorDetails(floor)"
              class="flex-1 px-3 py-2 text-sm bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Detayları Görüntüle
            </button>
            <button
              @click="editFloor(floor)"
              class="px-3 py-2 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Düzenle
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Room Status Legend -->
    <div class="mt-6 bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
      <h3 class="text-sm font-medium text-gray-700 dark:text-white mb-3">Oda Durumu Açıklaması</h3>
      <div class="flex flex-wrap gap-4">
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-green-500 rounded"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Müsait</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-blue-500 rounded"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Dolu</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-orange-500 rounded"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Temizlik</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-red-500 rounded"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Bakım</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-yellow-500 rounded"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Check-out</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-4 h-4 bg-purple-500 rounded"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Rezerve</span>
        </div>
      </div>
    </div>

    <!-- Floor Creation Modal -->
    <div v-if="showFloorModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Yeni Kat Ekle</h3>
        
        <form @submit.prevent="createFloor" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Kat Adı</label>
            <input
              v-model="newFloor.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg"
              placeholder="Örn: 1. Kat, Zemin Kat..."
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Supervisor</label>
            <select
              v-model="newFloor.supervisorId"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg"
            >
              <option value="">Supervisor seçin...</option>
              <option v-for="member in supervisors" :key="member.id" :value="member.id">
                {{ member.name }} {{ member.surname }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Oda Sayısı</label>
            <input
              v-model.number="newFloor.roomCount"
              type="number"
              min="1"
              max="50"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg"
              placeholder="Oda sayısı..."
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Başlangıç Oda Numarası</label>
            <input
              v-model.number="newFloor.startRoomNumber"
              type="number"
              min="1"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white rounded-lg"
              placeholder="Örn: 101, 201..."
            />
          </div>

          <div class="flex gap-3 pt-4">
            <button
              type="button"
              @click="showFloorModal = false"
              class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              class="flex-1 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Oluştur
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Room Details Modal -->
    <div v-if="selectedRoom" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-lg mx-4">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Oda {{ selectedRoom.number }} Detayları</h3>
          <button
            @click="selectedRoom = null"
            class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Oda Numarası</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.number }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Oda Tipi</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.type }}</p>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Durum</label>
            <span :class="getRoomStatusBadgeClass(selectedRoom.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
              {{ getRoomStatusText(selectedRoom.status) }}
            </span>
          </div>

          <div v-if="selectedRoom.guestName">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Misafir</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.guestName }}</p>
          </div>

          <div v-if="selectedRoom.checkIn">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Check-in</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.checkIn) }}</p>
          </div>

          <div v-if="selectedRoom.checkOut">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Check-out</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.checkOut) }}</p>
          </div>

          <div v-if="selectedRoom.lastCleaned">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Son Temizlik</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.lastCleaned) }}</p>
          </div>

          <div v-if="selectedRoom.assignedStaff">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Atanan Personel</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.assignedStaff }}</p>
          </div>

          <div v-if="selectedRoom.notes">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notlar</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.notes }}</p>
          </div>
        </div>

        <div class="flex gap-3 pt-6">
          <button
            v-if="selectedRoom.status === 'CLEANING'"
            @click="markRoomClean(selectedRoom.id)"
            class="flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            Temizlik Tamamlandı
          </button>
          <button
            v-if="selectedRoom.status === 'AVAILABLE'"
            @click="markRoomForCleaning(selectedRoom.id)"
            class="flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            Temizlik Gerekli
          </button>
          <button
            @click="selectedRoom = null"
            class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          >
            Kapat
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { RoomStatus, Floor, Room } from '@/types/housekeeping'
import {
  BuildingOfficeIcon,
  HomeIcon,
  CheckCircleIcon,
  ClockIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const housekeepingStore = useHousekeepingStore()

// Reactive data
const showFloorModal = ref(false)
const selectedRoom = ref<Room | null>(null)

// New floor form data
const newFloor = ref({
  name: '',
  supervisorId: '',
  roomCount: 10,
  startRoomNumber: 101
})

// Computed properties
const { floors, staff } = housekeepingStore

const supervisors = computed(() => {
  return staff.filter(member => member.position?.includes('Supervisor') || member.position?.includes('Müdür'))
})

const totalRooms = computed(() => {
  return floors.reduce((total, floor) => total + floor.rooms.length, 0)
})

const availableRooms = computed(() => {
  return floors.reduce((total, floor) => {
    return total + floor.rooms.filter(room => room.status === 'AVAILABLE').length
  }, 0)
})

const roomsNeedingCleaning = computed(() => {
  return floors.reduce((total, floor) => {
    return total + floor.rooms.filter(room => room.status === 'CLEANING').length
  }, 0)
})

// Methods
const refreshFloorData = () => {
  // Simulate data refresh
  console.log('Floor data refreshed')
}

const getFloorRoomsByStatus = (floor: Floor, status: RoomStatus) => {
  return floor.rooms.filter(room => room.status === status)
}

const getRoomStatusClass = (status: RoomStatus) => {
  switch (status) {
    case 'AVAILABLE':
      return 'bg-green-500 text-white'
    case 'OCCUPIED':
      return 'bg-blue-500 text-white'
    case 'CLEANING':
      return 'bg-orange-500 text-white'
    case 'MAINTENANCE':
      return 'bg-red-500 text-white'
    case 'CHECKOUT':
      return 'bg-yellow-500 text-white'
    case 'RESERVED':
      return 'bg-purple-500 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

const getRoomStatusBadgeClass = (status: RoomStatus) => {
  switch (status) {
    case 'AVAILABLE':
      return 'bg-green-100 text-green-800'
    case 'OCCUPIED':
      return 'bg-blue-100 text-blue-800'
    case 'CLEANING':
      return 'bg-orange-100 text-orange-800'
    case 'MAINTENANCE':
      return 'bg-red-100 text-red-800'
    case 'CHECKOUT':
      return 'bg-yellow-100 text-yellow-800'
    case 'RESERVED':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getRoomStatusText = (status: RoomStatus) => {
  switch (status) {
    case 'AVAILABLE':
      return 'Müsait'
    case 'OCCUPIED':
      return 'Dolu'
    case 'CLEANING':
      return 'Temizlik'
    case 'MAINTENANCE':
      return 'Bakım'
    case 'CHECKOUT':
      return 'Check-out'
    case 'RESERVED':
      return 'Rezerve'
    default:
      return 'Bilinmiyor'
  }
}

const selectRoom = (room: Room) => {
  selectedRoom.value = room
}

const viewFloorDetails = (floor: Floor) => {
  console.log('Viewing floor details:', floor.name)
}

const editFloor = (floor: Floor) => {
  console.log('Editing floor:', floor.name)
}

const createFloor = () => {
  const selectedSupervisor = supervisors.value.find(s => s.id === newFloor.value.supervisorId)
  if (selectedSupervisor) {
    const rooms: Room[] = []
    
    // Generate rooms for the floor
    for (let i = 0; i < newFloor.value.roomCount; i++) {
      const roomNumber = newFloor.value.startRoomNumber + i
      rooms.push({
        id: `room-${Date.now()}-${i}`,
        number: roomNumber.toString(),
        floorId: `floor-${Date.now()}`,
        type: i % 3 === 0 ? 'Suite' : i % 2 === 0 ? 'Deluxe' : 'Standard',
        floor: floors.length + 1,
        status: 'AVAILABLE' as RoomStatus,
        lastCleaned: new Date().toISOString(),
        nextInspection: new Date(Date.now() + 24*60*60*1000).toISOString()
      })
    }

    const floor: Floor = {
      id: `floor-${Date.now()}`,
      name: newFloor.value.name,
      number: floors.length + 1,
      supervisor: `${selectedSupervisor.name} ${selectedSupervisor.surname}`,
      supervisorId: selectedSupervisor.id,
      rooms,
      totalRooms: rooms.length,
      cleanRooms: rooms.filter(r => r.status === 'CLEAN').length,
      dirtyRooms: rooms.filter(r => r.status === 'DIRTY').length,
      maintenanceRooms: rooms.filter(r => r.status === 'MAINTENANCE').length
    }
    
    floors.push(floor)
    
    // Reset form
    newFloor.value = {
      name: '',
      supervisorId: '',
      roomCount: 10,
      startRoomNumber: 101
    }
    
    showFloorModal.value = false
  }
}

const markRoomClean = (roomId: string) => {
  housekeepingStore.updateRoomStatus(roomId, 'AVAILABLE')
  selectedRoom.value = null
}

const markRoomForCleaning = (roomId: string) => {
  housekeepingStore.updateRoomStatus(roomId, 'CLEANING')
  selectedRoom.value = null
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script> 