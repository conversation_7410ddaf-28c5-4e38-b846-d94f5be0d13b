import { describe, it, expect, vi } from 'vitest'

// Simple helper utilities for customer portal
export function formatRoomNumber(roomNumber: string | number): string {
  return `Oda ${roomNumber}`
}

export function calculateServiceTotal(items: Array<{ price: number; quantity: number }>): number {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0)
}

export function getServiceStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'Beklemede',
    'in-progress': 'Hazırlanıyor',
    'completed': 'Tamamlandı',
    'cancelled': 'İptal Edildi'
  }
  return statusMap[status] || 'Bilinmeyen'
}

export function isServiceAvailable(startTime: string, endTime: string): boolean {
  const now = new Date()
  const currentTime = now.getHours() * 60 + now.getMinutes()
  
  const [startHour, startMin] = startTime.split(':').map(Number)
  const [endHour, endMin] = endTime.split(':').map(Number)
  
  const serviceStart = startHour * 60 + startMin
  const serviceEnd = endHour * 60 + endMin
  
  return currentTime >= serviceStart && currentTime <= serviceEnd
}

describe('Customer Portal Helpers', () => {
  describe('formatRoomNumber', () => {
    it('formats room number with string input', () => {
      expect(formatRoomNumber('101')).toBe('Oda 101')
      expect(formatRoomNumber('A205')).toBe('Oda A205')
    })

    it('formats room number with number input', () => {
      expect(formatRoomNumber(101)).toBe('Oda 101')
      expect(formatRoomNumber(205)).toBe('Oda 205')
    })
  })

  describe('calculateServiceTotal', () => {
    it('calculates total for single item', () => {
      const items = [{ price: 50, quantity: 2 }]
      expect(calculateServiceTotal(items)).toBe(100)
    })

    it('calculates total for multiple items', () => {
      const items = [
        { price: 25, quantity: 2 },
        { price: 15, quantity: 3 },
        { price: 10, quantity: 1 }
      ]
      expect(calculateServiceTotal(items)).toBe(105) // 50 + 45 + 10
    })

    it('handles empty array', () => {
      expect(calculateServiceTotal([])).toBe(0)
    })

    it('handles zero quantities', () => {
      const items = [
        { price: 25, quantity: 0 },
        { price: 15, quantity: 2 }
      ]
      expect(calculateServiceTotal(items)).toBe(30)
    })
  })

  describe('getServiceStatusText', () => {
    it('returns correct Turkish text for known statuses', () => {
      expect(getServiceStatusText('pending')).toBe('Beklemede')
      expect(getServiceStatusText('in-progress')).toBe('Hazırlanıyor')
      expect(getServiceStatusText('completed')).toBe('Tamamlandı')
      expect(getServiceStatusText('cancelled')).toBe('İptal Edildi')
    })

    it('returns default text for unknown status', () => {
      expect(getServiceStatusText('unknown')).toBe('Bilinmeyen')
      expect(getServiceStatusText('')).toBe('Bilinmeyen')
    })
  })

  describe('isServiceAvailable', () => {
    it('returns true when current time is within service hours', () => {
      // Mock current time to 14:30 (2:30 PM)
      const mockDate = new Date()
      mockDate.setHours(14, 30, 0, 0)
      vi.setSystemTime(mockDate)
      
      expect(isServiceAvailable('09:00', '18:00')).toBe(true)
      expect(isServiceAvailable('14:00', '15:00')).toBe(true)
    })

    it('returns false when current time is outside service hours', () => {
      // Mock current time to 20:30 (8:30 PM)
      const mockDate = new Date()
      mockDate.setHours(20, 30, 0, 0)
      vi.setSystemTime(mockDate)
      
      expect(isServiceAvailable('09:00', '18:00')).toBe(false)
      expect(isServiceAvailable('21:00', '23:00')).toBe(false)
    })

    it('handles edge cases at exact start and end times', () => {
      // Mock current time to 09:00
      const mockDate = new Date()
      mockDate.setHours(9, 0, 0, 0)
      vi.setSystemTime(mockDate)
      
      expect(isServiceAvailable('09:00', '18:00')).toBe(true)
      
      // Mock current time to 18:00
      mockDate.setHours(18, 0, 0, 0)
      vi.setSystemTime(mockDate)
      
      expect(isServiceAvailable('09:00', '18:00')).toBe(true)
    })
  })
})
