<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-6">
        <h1 class="text-2xl font-bold text-textDark">My Orders</h1>
      </div>
    </div>

    <!-- Content -->
    <div class="px-4 py-6">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
        <span class="ml-3 text-textMedium">Loading orders...</span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
          <span class="text-red-700 font-medium">{{ error }}</span>
        </div>
        <button 
          @click="clearError()" 
          class="mt-3 text-red-600 hover:text-red-700 text-sm underline"
        >
          Clear Error
        </button>
      </div>

      <!-- Orders List -->
      <div v-if="!isLoading && !error && orders.length > 0" class="space-y-4">
        <div 
          v-for="order in orders" 
          :key="order.id"
          class="mobile-card"
        >
          <OrderCard 
            :order="order as Order" 
            :is-cancelling="cancelling === order.id"
            :show-cancel="['pending', 'confirmed', 'in_progress', 'preparing'].includes(order.status || '')"
          />
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!isLoading && !error && orders.length === 0" class="text-center py-16">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-textLight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-textDark mb-2">No orders yet</h3>
        <p class="text-textMedium mb-6">Start by placing your first room service order</p>
        <button 
          @click="$router.push('/guest/room-service')"
          class="btn-primary max-w-xs mx-auto"
        >
          Order Now
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useRoomServiceStore } from '@/stores/roomServiceStore'
import { useGuestAuthStore } from '@/stores/guestAuthStore'
import OrderCard from '@/components/OrderCard.vue'
import type { Order } from '@hotelexia/shared-supabase-client'

// Router and Stores
const router = useRouter()
const roomServiceStore = useRoomServiceStore()
const guestAuthStore = useGuestAuthStore()
const { orders, isLoading, error, cancelling } = storeToRefs(roomServiceStore)
const { currentGuest } = storeToRefs(guestAuthStore)
const { fetchOrders, clearError } = roomServiceStore

// Lifecycle
onMounted(async () => {
  // Check if guest is authenticated and get reservation ID
  const hasSession = await guestAuthStore.checkExistingSession()
  
  if (hasSession && currentGuest.value) {
    await fetchOrders(currentGuest.value.id)
  } else {
    // Redirect to login if not authenticated
    await router.push('/')
  }
})
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style> 