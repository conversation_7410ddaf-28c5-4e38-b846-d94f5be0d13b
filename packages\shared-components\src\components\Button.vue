<template>
  <component
    :is="as"
    :class="buttonClasses"
    :disabled="disabled"
    v-bind="$attrs"
    @click="handleClick"
  >
    <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
    <slot v-if="!loading" name="icon" />
    <span v-if="!loading || $slots.default">
      <slot />
    </span>
  </component>
</template>

<script setup lang="ts">
import { computed, type Component } from 'vue'
import { cva, type VariantProps } from 'class-variance-authority'
import { Loader2 } from 'lucide-vue-next'
import { cn } from '../lib/utils'

const buttonVariants = cva(
  // Base styles
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "bg-gradient-to-r from-brand-500 to-purple-600 text-white shadow-brand hover:shadow-lg hover:scale-105 focus-visible:ring-brand-500",
        secondary: "bg-white text-navy-700 border border-gray-200 shadow-sm hover:bg-gray-50 hover:border-gray-300 focus-visible:ring-navy-500",
        ghost: "text-navy-700 hover:bg-gray-100 hover:text-brand-500 focus-visible:ring-navy-500",
        outline: "border border-brand-500 text-brand-500 bg-transparent hover:bg-brand-50 focus-visible:ring-brand-500",
        destructive: "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl hover:scale-105 focus-visible:ring-red-500",
        success: "bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:shadow-xl hover:scale-105 focus-visible:ring-green-500",
        link: "text-brand-500 underline-offset-4 hover:underline focus-visible:ring-brand-500",
      },
      size: {
        sm: "h-8 px-3 text-xs",
        md: "h-10 px-4 py-2",
        lg: "h-11 px-6 py-3",
        xl: "h-12 px-8 py-3 text-base",
        icon: "h-10 w-10",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
      fullWidth: false,
    },
  }
)

interface ButtonProps extends /* @vue-ignore */ VariantProps<typeof buttonVariants> {
  as?: string | Component
  disabled?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<ButtonProps>(), {
  as: 'button',
  disabled: false,
  loading: false,
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => 
  cn(
    buttonVariants({ 
      variant: props.variant, 
      size: props.size, 
      fullWidth: props.fullWidth 
    })
  )
)

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script> 