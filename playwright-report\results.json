{"config": {"configFile": "C:\\hotel-yedek\\hotelexia\\playwright.config.ts", "rootDir": "C:/hotel-yedek/hotelexia/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 9}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "playwright-report/results.json"}], ["junit", {"outputFile": "playwright-report/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/hotel-yedek/hotelexia/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 9}, "id": "chromium", "name": "chromium", "testDir": "C:/hotel-yedek/hotelexia/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/hotel-yedek/hotelexia/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 9}, "id": "firefox", "name": "firefox", "testDir": "C:/hotel-yedek/hotelexia/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/hotel-yedek/hotelexia/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 9}, "id": "webkit", "name": "webkit", "testDir": "C:/hotel-yedek/hotelexia/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/hotel-yedek/hotelexia/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 9}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/hotel-yedek/hotelexia/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/hotel-yedek/hotelexia/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 9}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/hotel-yedek/hotelexia/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 10, "webServer": null}, "suites": [{"title": "supabase-data-integration.spec.ts", "file": "supabase-data-integration.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Supabase Data Integration - Cross-MFE Tests", "file": "supabase-data-integration.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display consistent hotel data across Management Portal and Hotel Dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 12224, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('table tbody tr') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('table tbody tr') to be visible\u001b[22m\n\n    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:10:16", "location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m\n \u001b[90m  9 |\u001b[39m     \u001b[90m// Wait for hotels to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'table tbody tr'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// Get first hotel data from Management Portal\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mconst\u001b[39m firstHotelRow \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'table tbody tr'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m"}, "errors": [{"location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 16, "line": 10}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('table tbody tr') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m\n \u001b[90m  9 |\u001b[39m     \u001b[90m// Wait for hotels to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'table tbody tr'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// Get first hotel data from Management Portal\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mconst\u001b[39m firstHotelRow \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'table tbody tr'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[0m\n\u001b[2m    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.924Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "5fe19a02e80b542bb67a-c7f1e387d8587a89c7b1", "file": "supabase-data-integration.spec.ts", "line": 5, "column": 3}, {"title": "should fetch and display live hotel data from Supabase", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 17120, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('table tbody tr') to be visible\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('table tbody tr') to be visible\u001b[22m\n\n    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:37:16", "location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 16, "line": 37}, "snippet": "\u001b[0m \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[90m// Wait for data to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'table tbody tr'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m\n \u001b[90m 39 |\u001b[39m     \u001b[90m// Check that hotels are loaded from Supabase (not dummy data)\u001b[39m\n \u001b[90m 40 |\u001b[39m     \u001b[36mconst\u001b[39m hotelRows \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'table tbody tr'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 16, "line": 37}, "message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('table tbody tr') to be visible\u001b[22m\n\n\n\u001b[0m \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[90m// Wait for data to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 37 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'table tbody tr'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 38 |\u001b[39m\n \u001b[90m 39 |\u001b[39m     \u001b[90m// Check that hotels are loaded from Supabase (not dummy data)\u001b[39m\n \u001b[90m 40 |\u001b[39m     \u001b[36mconst\u001b[39m hotelRows \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'table tbody tr'\u001b[39m)\u001b[0m\n\u001b[2m    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:37:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.935Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 16, "line": 37}}], "status": "unexpected"}], "id": "5fe19a02e80b542bb67a-998b517ff4199121c0b9", "file": "supabase-data-integration.spec.ts", "line": 33, "column": 3}, {"title": "should display live room data in Hotel Dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 5024, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:75:40", "location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 40, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m       \u001b[90m// If no rooms, check for empty state or loading\u001b[39m\n \u001b[90m 74 |\u001b[39m       \u001b[36mconst\u001b[39m emptyState \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.empty-state, .no-data, .loading'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m       expect(\u001b[36mawait\u001b[39m emptyState\u001b[33m.\u001b[39mcount())\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\n \u001b[90m    |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     }\n \u001b[90m 77 |\u001b[39m   })\n \u001b[90m 78 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 40, "line": 75}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 73 |\u001b[39m       \u001b[90m// If no rooms, check for empty state or loading\u001b[39m\n \u001b[90m 74 |\u001b[39m       \u001b[36mconst\u001b[39m emptyState \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.empty-state, .no-data, .loading'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m       expect(\u001b[36mawait\u001b[39m emptyState\u001b[33m.\u001b[39mcount())\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\n \u001b[90m    |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m     }\n \u001b[90m 77 |\u001b[39m   })\n \u001b[90m 78 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:75:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.923Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--447bc-oom-data-in-Hotel-Dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--447bc-oom-data-in-Hotel-Dashboard-chromium\\video.webm"}], "errorLocation": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 40, "line": 75}}], "status": "unexpected"}], "id": "5fe19a02e80b542bb67a-a365bf75e11b0ed7949f", "file": "supabase-data-integration.spec.ts", "line": 54, "column": 3}, {"title": "should display live user data in Management Portal", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 5128, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:99:40", "location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 40, "line": 99}, "snippet": "\u001b[0m \u001b[90m  97 |\u001b[39m       \u001b[90m// Check for empty state\u001b[39m\n \u001b[90m  98 |\u001b[39m       \u001b[36mconst\u001b[39m emptyState \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.empty-state, .no-data, .loading'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  99 |\u001b[39m       expect(\u001b[36mawait\u001b[39m emptyState\u001b[33m.\u001b[39mcount())\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 100 |\u001b[39m     }\n \u001b[90m 101 |\u001b[39m   })\n \u001b[90m 102 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 40, "line": 99}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m  97 |\u001b[39m       \u001b[90m// Check for empty state\u001b[39m\n \u001b[90m  98 |\u001b[39m       \u001b[36mconst\u001b[39m emptyState \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.empty-state, .no-data, .loading'\u001b[39m)\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  99 |\u001b[39m       expect(\u001b[36mawait\u001b[39m emptyState\u001b[33m.\u001b[39mcount())\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 100 |\u001b[39m     }\n \u001b[90m 101 |\u001b[39m   })\n \u001b[90m 102 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:99:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.986Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 40, "line": 99}}], "status": "unexpected"}], "id": "5fe19a02e80b542bb67a-4923f0536bd253b792c3", "file": "supabase-data-integration.spec.ts", "line": 79, "column": 3}, {"title": "should display live reservation data in Customer Portal", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 2000, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:121:42", "location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 42, "line": 121}, "snippet": "\u001b[0m \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m hasHotels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m hotelElements\u001b[33m.\u001b[39mcount() \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m\n \u001b[90m 120 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m     expect(hasReservations \u001b[33m||\u001b[39m hasHotels)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\n \u001b[90m     |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m     \n \u001b[90m 123 |\u001b[39m     \u001b[36mif\u001b[39m (hasReservations) {\n \u001b[90m 124 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'Reservation data found'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 42, "line": 121}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 119 |\u001b[39m     \u001b[36mconst\u001b[39m hasHotels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m hotelElements\u001b[33m.\u001b[39mcount() \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m\n \u001b[90m 120 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 121 |\u001b[39m     expect(hasReservations \u001b[33m||\u001b[39m hasHotels)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\n \u001b[90m     |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 122 |\u001b[39m     \n \u001b[90m 123 |\u001b[39m     \u001b[36mif\u001b[39m (hasReservations) {\n \u001b[90m 124 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'Reservation data found'\u001b[39m)\u001b[0m\n\u001b[2m    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:121:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.987Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 42, "line": 121}}], "status": "unexpected"}], "id": "5fe19a02e80b542bb67a-fb15e7ff6b41915945c3", "file": "supabase-data-integration.spec.ts", "line": 103, "column": 3}, {"title": "should handle data loading states properly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 7004, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:147:38", "location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 38, "line": 147}, "snippet": "\u001b[0m \u001b[90m 145 |\u001b[39m     \u001b[36mconst\u001b[39m hasEmptyState \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.empty-state, .no-data'\u001b[39m)\u001b[33m.\u001b[39mcount() \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m\n \u001b[90m 146 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 147 |\u001b[39m     expect(hasData \u001b[33m||\u001b[39m hasEmptyState)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\n \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 148 |\u001b[39m   })\n \u001b[90m 149 |\u001b[39m\n \u001b[90m 150 |\u001b[39m   test(\u001b[32m'should handle error states gracefully'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 38, "line": 147}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 145 |\u001b[39m     \u001b[36mconst\u001b[39m hasEmptyState \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'.empty-state, .no-data'\u001b[39m)\u001b[33m.\u001b[39mcount() \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m\n \u001b[90m 146 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 147 |\u001b[39m     expect(hasData \u001b[33m||\u001b[39m hasEmptyState)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\n \u001b[90m     |\u001b[39m                                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 148 |\u001b[39m   })\n \u001b[90m 149 |\u001b[39m\n \u001b[90m 150 |\u001b[39m   test(\u001b[32m'should handle error states gracefully'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts:147:38\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.987Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--95156-ata-loading-states-properly-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--95156-ata-loading-states-properly-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\hotel-yedek\\hotelexia\\test-results\\supabase-data-integration--95156-ata-loading-states-properly-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\hotel-yedek\\hotelexia\\tests\\e2e\\supabase-data-integration.spec.ts", "column": 38, "line": 147}}], "status": "unexpected"}], "id": "5fe19a02e80b542bb67a-208c57f4d469cc53e8e4", "file": "supabase-data-integration.spec.ts", "line": 130, "column": 3}, {"title": "should handle error states gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 11413, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.984Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5fe19a02e80b542bb67a-fce98c5a9f5fd19b58f0", "file": "supabase-data-integration.spec.ts", "line": 150, "column": 3}, {"title": "should maintain data consistency during navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 8348, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.968Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5fe19a02e80b542bb67a-2767db300aff3ca0b738", "file": "supabase-data-integration.spec.ts", "line": 187, "column": 3}, {"title": "should display real-time data updates", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 8, "status": "passed", "duration": 10564, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-02T20:56:25.987Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "5fe19a02e80b542bb67a-aec6f34feb1d6c3cf575", "file": "supabase-data-integration.spec.ts", "line": 207, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-02T20:56:19.852Z", "duration": 24904.824, "expected": 3, "skipped": 0, "unexpected": 6, "flaky": 0}}