<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h1 class="text-2xl font-bold text-navy-700 dark:text-white">Personel Yönetimi</h1>
          <p class="text-gray-600 dark:text-gray-400">Housekeeping personelini yönetin ve performanslarını takip edin</p>
        </div>
        <div class="flex gap-3">
          <button
            @click="showPerformanceModal = true"
            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            📊 Performans Raporu
          </button>
          <button
            @click="showStaffModal = true"
            class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            + <PERSON><PERSON>
          </button>
        </div>
      </div>

      <!-- Team Stats -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Toplam Personel</p>
              <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ staff.length }}</p>
            </div>
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <UserGroupIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Aktif Çalışan</p>
              <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ activeStaff.length }}</p>
            </div>
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <CheckCircleIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">İzinli</p>
              <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ onLeaveStaff.length }}</p>
            </div>
            <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
              <ClockIcon class="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Ortalama Performans</p>
              <p class="text-2xl font-bold text-teal-600 dark:text-teal-400">{{ averagePerformance }}%</p>
            </div>
            <div class="w-10 h-10 bg-teal-100 dark:bg-teal-900/30 rounded-lg flex items-center justify-center">
              <ChartBarIcon class="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Aktif Görevler</p>
              <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ activeTasks }}</p>
            </div>
            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <ClipboardDocumentListIcon class="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="flex flex-wrap gap-4 mb-6">
        <select
          v-model="selectedDepartment"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
        >
          <option value="">Tüm Departmanlar</option>
          <option value="Housekeeping">Housekeeping</option>
          <option value="Maintenance">Maintenance</option>
          <option value="Front Office">Front Office</option>
        </select>

        <select
          v-model="selectedStatus"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
        >
          <option value="">Tüm Durumlar</option>
          <option value="ACTIVE">Aktif</option>
          <option value="ON_LEAVE">İzinli</option>
          <option value="SICK">Hasta</option>
          <option value="INACTIVE">Pasif</option>
        </select>

        <select
          v-model="selectedShift"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
        >
          <option value="">Tüm Vardiyalar</option>
          <option value="Sabah">Sabah (06:00-14:00)</option>
          <option value="Öğle">Öğle (14:00-22:00)</option>
          <option value="Gece">Gece (22:00-06:00)</option>
        </select>

        <input
          v-model="searchQuery"
          type="text"
          placeholder="Personel ara..."
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
        />

        <button
          @click="clearFilters"
          class="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm"
        >
          Filtreleri Temizle
        </button>
      </div>
    </div>

    <!-- Staff Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div
        v-for="member in filteredStaff"
        :key="member.id"
        class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 overflow-hidden hover:shadow-md transition-all"
      >
        <!-- Staff Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <div class="flex items-center gap-3">
            <div class="w-12 h-12 bg-gradient-to-br from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white font-semibold">
              {{ member.name.charAt(0) }}{{ member.surname.charAt(0) }}
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900 dark:text-white">{{ member.name }} {{ member.surname }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ member.position }}</p>
            </div>
            <span :class="getStatusBadgeClass(member.status)" class="px-2 py-1 rounded-full text-xs font-medium">
              {{ getStatusText(member.status) }}
            </span>
          </div>
        </div>

        <!-- Staff Info -->
        <div class="p-4 space-y-3">
          <div class="flex items-center gap-2 text-sm">
            <ClockIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-400">{{ member.shift }}</span>
          </div>

          <div class="flex items-center gap-2 text-sm">
            <PhoneIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-400">{{ member.phone }}</span>
          </div>

          <div class="flex items-center gap-2 text-sm">
            <CalendarIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-400">{{ formatDate(member.hireDate) }}</span>
          </div>

          <!-- Performance Score -->
          <div class="mt-3">
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm text-gray-600 dark:text-gray-400">Performans</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ member.performanceScore }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                :class="getPerformanceBarClass(member.performanceScore)"
                class="h-2 rounded-full transition-all"
                :style="{ width: `${member.performanceScore}%` }"
              ></div>
            </div>
          </div>

          <!-- Current Tasks -->
          <div v-if="member.currentTasks && member.currentTasks.length > 0" class="mt-3">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Aktif Görevler ({{ member.currentTasks.length }})</p>
            <div class="space-y-1">
              <div
                v-for="task in member.currentTasks.slice(0, 2)"
                :key="task"
                class="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded"
              >
                {{ task }}
              </div>
              <div
                v-if="member.currentTasks.length > 2"
                class="text-xs text-gray-500"
              >
                +{{ member.currentTasks.length - 2 }} daha...
              </div>
            </div>
          </div>
        </div>

        <!-- Staff Actions -->
        <div class="p-4 bg-gray-50 dark:bg-navy-800 border-t border-gray-200 dark:border-gray-600">
          <div class="flex gap-2">
            <button
              @click="assignTask(member)"
              class="flex-1 px-3 py-2 text-sm bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Görev Ata
            </button>
            <button
              @click="viewStaffDetails(member)"
              class="px-3 py-2 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Detay
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Staff Details Modal -->
    <div v-if="selectedStaff" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ selectedStaff.name }} {{ selectedStaff.surname }} - Detaylar</h3>
          <button
            @click="selectedStaff = null"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Personal Info -->
          <div class="space-y-4">
            <h4 class="font-medium text-gray-900 dark:text-white">Kişisel Bilgiler</h4>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Ad Soyad</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.name }} {{ selectedStaff.surname }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Pozisyon</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.position }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Telefon</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.phone }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">E-posta</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.email }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">İşe Başlama</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedStaff.hireDate) }}</p>
            </div>
          </div>

          <!-- Work Info -->
          <div class="space-y-4">
            <h4 class="font-medium text-gray-900 dark:text-white">Çalışma Bilgileri</h4>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Durum</label>
              <span :class="getStatusBadgeClass(selectedStaff.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getStatusText(selectedStaff.status) }}
              </span>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Vardiya</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.shift }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Performans Skoru</label>
              <div class="flex items-center gap-2">
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    :class="getPerformanceBarClass(selectedStaff.performanceScore)"
                    class="h-2 rounded-full"
                    :style="{ width: `${selectedStaff.performanceScore}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ selectedStaff.performanceScore }}%</span>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Tamamlanan Görevler</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.completedTasks || 0 }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400">Ortalama Tamamlama Süresi</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedStaff.averageTaskTime || 'N/A' }}</p>
            </div>
          </div>
        </div>

        <!-- Current Tasks -->
        <div v-if="selectedStaff.currentTasks && selectedStaff.currentTasks.length > 0" class="mt-6">
          <h4 class="font-medium text-gray-900 dark:text-white mb-3">Aktif Görevler</h4>
          <div class="space-y-2">
            <div
              v-for="task in selectedStaff.currentTasks"
              :key="task"
              class="bg-blue-50 text-blue-700 px-3 py-2 rounded-lg text-sm"
            >
              {{ task }}
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3 pt-6 border-t border-gray-200 mt-6">
          <button
            @click="assignTask(selectedStaff)"
            class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
          >
            Görev Ata
          </button>
          <button
            @click="editStaff(selectedStaff)"
            class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Düzenle
          </button>
          <button
            @click="selectedStaff = null"
            class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors ml-auto"
          >
            Kapat
          </button>
        </div>
      </div>
    </div>

    <!-- Task Assignment Modal -->
    <div v-if="showTaskModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Görev Ata - {{ taskAssignment.staffName }}</h3>
        
        <form @submit.prevent="submitTaskAssignment" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Görev Türü</label>
            <select
              v-model="taskAssignment.type"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg"
            >
              <option value="">Görev türü seçin...</option>
              <option value="Oda Temizliği">Oda Temizliği</option>
              <option value="Banyo Temizliği">Banyo Temizliği</option>
              <option value="Yatak Değişimi">Yatak Değişimi</option>
              <option value="Minibar Kontrolü">Minibar Kontrolü</option>
              <option value="Genel Temizlik">Genel Temizlik</option>
              <option value="Özel Görev">Özel Görev</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Oda/Alan</label>
            <input
              v-model="taskAssignment.location"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg"
              placeholder="Örn: 101, Lobi, 3. Kat..."
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Açıklama</label>
            <textarea
              v-model="taskAssignment.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg"
              placeholder="Görev detayları..."
            ></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Öncelik</label>
            <select
              v-model="taskAssignment.priority"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg"
            >
              <option value="LOW">Düşük</option>
              <option value="MEDIUM">Orta</option>
              <option value="HIGH">Yüksek</option>
              <option value="URGENT">Acil</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Tahmini Süre (dakika)</label>
            <input
              v-model.number="taskAssignment.estimatedTime"
              type="number"
              min="5"
              max="480"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg"
              placeholder="30"
            />
          </div>

          <div class="flex gap-3 pt-4">
            <button
              type="button"
              @click="showTaskModal = false"
              class="flex-1 px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              class="flex-1 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Ata
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Add Staff Modal -->
    <div v-if="showStaffModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Yeni Personel Ekle</h3>
        
        <form @submit.prevent="addNewStaff" class="space-y-4">
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Ad</label>
              <input
                v-model="newStaff.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Soyad</label>
              <input
                v-model="newStaff.surname"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Pozisyon</label>
            <select
              v-model="newStaff.position"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            >
              <option value="">Pozisyon seçin...</option>
              <option value="Housekeeping Personeli">Housekeeping Personeli</option>
              <option value="Housekeeping Supervisor">Housekeeping Supervisor</option>
              <option value="Kat Görevlisi">Kat Görevlisi</option>
              <option value="Temizlik Personeli">Temizlik Personeli</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Telefon</label>
            <input
              v-model="newStaff.phone"
              type="tel"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">E-posta</label>
            <input
              v-model="newStaff.email"
              type="email"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-400 mb-1">Vardiya</label>
            <select
              v-model="newStaff.shift"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            >
              <option value="">Vardiya seçin...</option>
              <option value="Sabah">Sabah (06:00-14:00)</option>
              <option value="Öğle">Öğle (14:00-22:00)</option>
              <option value="Gece">Gece (22:00-06:00)</option>
            </select>
          </div>

          <div class="flex gap-3 pt-4">
            <button
              type="button"
              @click="showStaffModal = false"
              class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              class="flex-1 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Ekle
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Performance Modal -->
    <div v-if="showPerformanceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performans Raporu</h3>
          <button
            @click="showPerformanceModal = false"
            class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        
        <!-- Performance Summary -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 class="font-medium text-green-800 dark:text-green-400">Yüksek Performans</h4>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ highPerformers.length }}</p>
            <p class="text-sm text-green-600 dark:text-green-400">85%+ performans</p>
          </div>
          <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <h4 class="font-medium text-yellow-800 dark:text-yellow-400">Orta Performans</h4>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ mediumPerformers.length }}</p>
            <p class="text-sm text-yellow-600 dark:text-yellow-400">70-84% performans</p>
          </div>
          <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
            <h4 class="font-medium text-red-800 dark:text-red-400">Düşük Performans</h4>
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ lowPerformers.length }}</p>
            <p class="text-sm text-red-600 dark:text-red-400">70%'den düşük</p>
          </div>
        </div>

        <!-- Performance List -->
        <div class="space-y-3">
          <div
            v-for="member in sortedByPerformance"
            :key="member.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-navy-800 rounded-lg"
          >
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-gradient-to-br from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                {{ member.name.charAt(0) }}{{ member.surname.charAt(0) }}
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{{ member.name }} {{ member.surname }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ member.position }}</p>
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div class="text-right">
                <p class="font-medium text-gray-900 dark:text-white">{{ member.performanceScore }}%</p>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ member.completedTasks || 0 }} görev</p>
              </div>
              <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div
                  :class="getPerformanceBarClass(member.performanceScore)"
                  class="h-2 rounded-full"
                  :style="{ width: `${member.performanceScore}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { StaffMember, StaffStatus, TaskPriority } from '@/types/housekeeping'
import {
  UserGroupIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  CalendarIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const housekeepingStore = useHousekeepingStore()

// Reactive data
const selectedDepartment = ref('')
const selectedStatus = ref('')
const selectedShift = ref('')
const searchQuery = ref('')
const selectedStaff = ref<StaffMember | null>(null)
const showTaskModal = ref(false)
const showStaffModal = ref(false)
const showPerformanceModal = ref(false)

// Task assignment form
const taskAssignment = ref({
  staffId: '',
  staffName: '',
  type: '',
  location: '',
  description: '',
  priority: 'MEDIUM' as TaskPriority,
  estimatedTime: 30
})

// New staff form
const newStaff = ref({
  name: '',
  surname: '',
  position: '',
  phone: '',
  email: '',
  shift: ''
})

// Computed properties
const { staff, tasks } = housekeepingStore

const activeStaff = computed(() => {
  return staff.filter(member => member.status === 'ACTIVE')
})

const onLeaveStaff = computed(() => {
  return staff.filter(member => member.status === 'ON_LEAVE')
})

const averagePerformance = computed(() => {
  if (staff.length === 0) return 0
  const total = staff.reduce((sum, member) => sum + member.performanceScore, 0)
  return Math.round(total / staff.length)
})

const activeTasks = computed(() => {
  return tasks.filter(task => task.status === 'IN_PROGRESS' || task.status === 'ASSIGNED').length
})

const filteredStaff = computed(() => {
  return staff.filter(member => {
    const matchesDepartment = !selectedDepartment.value || member.position?.includes(selectedDepartment.value)
    const matchesStatus = !selectedStatus.value || member.status === selectedStatus.value
    const matchesShift = !selectedShift.value || member.shift === selectedShift.value
    const matchesSearch = !searchQuery.value || 
      member.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      member.surname.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      member.position?.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    return matchesDepartment && matchesStatus && matchesShift && matchesSearch
  })
})

const highPerformers = computed(() => {
  return staff.filter(member => member.performanceScore >= 85)
})

const mediumPerformers = computed(() => {
  return staff.filter(member => member.performanceScore >= 70 && member.performanceScore < 85)
})

const lowPerformers = computed(() => {
  return staff.filter(member => member.performanceScore < 70)
})

const sortedByPerformance = computed(() => {
  return [...staff].sort((a, b) => b.performanceScore - a.performanceScore)
})

// Methods
const clearFilters = () => {
  selectedDepartment.value = ''
  selectedStatus.value = ''
  selectedShift.value = ''
  searchQuery.value = ''
}

const viewStaffDetails = (member: StaffMember) => {
  selectedStaff.value = member
}

const assignTask = (member: StaffMember) => {
  taskAssignment.value.staffId = member.id
  taskAssignment.value.staffName = `${member.name} ${member.surname}`
  showTaskModal.value = true
}

const editStaff = (member: StaffMember) => {
  console.log('Editing staff:', member.name)
  // Implement edit functionality
}

const submitTaskAssignment = () => {
  // Create new task
  const newTask = {
    id: Date.now().toString(),
    title: `${taskAssignment.value.type} - ${taskAssignment.value.location}`,
    description: taskAssignment.value.description || `${taskAssignment.value.type} görevi`,
    roomNumber: '101', // Default room number
    assignedTo: taskAssignment.value.staffId,
    assignedToName: taskAssignment.value.staffName,
    status: 'ASSIGNED' as const,
    priority: taskAssignment.value.priority,
    location: taskAssignment.value.location,
    estimatedTime: taskAssignment.value.estimatedTime,
    createdAt: new Date().toISOString(),
    deadline: new Date(Date.now() + taskAssignment.value.estimatedTime * 60000).toISOString()
  }
  
  tasks.push(newTask)
  
  // Add task to staff member's current tasks
  const staffMember = staff.find(s => s.id === taskAssignment.value.staffId)
  if (staffMember) {
    if (!staffMember.currentTasks) {
      staffMember.currentTasks = []
    }
    staffMember.currentTasks.push(newTask.title)
  }
  
  // Reset form
  taskAssignment.value = {
    staffId: '',
    staffName: '',
    type: '',
    location: '',
    description: '',
    priority: 'MEDIUM' as TaskPriority,
    estimatedTime: 30
  }
  
  showTaskModal.value = false
}

const addNewStaff = () => {
  const newMember: StaffMember = {
    id: Date.now().toString(),
    name: newStaff.value.name,
    surname: newStaff.value.surname,
    role: newStaff.value.position,
    position: newStaff.value.position,
    phone: newStaff.value.phone,
    email: newStaff.value.email,
    shift: newStaff.value.shift,
    status: 'ACTIVE' as StaffStatus,
    avatar: '/placeholder-avatar.jpg',
    hireDate: new Date().toISOString(),
    startDate: new Date().toISOString(),
    performanceScore: 75, // Default starting score
    tasksCompleted: 0,
    averageTime: 30,
    currentTasks: []
  }
  
  staff.push(newMember)
  
  // Reset form
  newStaff.value = {
    name: '',
    surname: '',
    position: '',
    phone: '',
    email: '',
    shift: ''
  }
  
  showStaffModal.value = false
}

// Helper functions
const getStatusBadgeClass = (status: StaffStatus) => {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800'
    case 'ON_LEAVE':
      return 'bg-orange-100 text-orange-800'
    case 'SICK':
      return 'bg-red-100 text-red-800'
    case 'INACTIVE':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: StaffStatus) => {
  switch (status) {
    case 'ACTIVE':
      return 'Aktif'
    case 'ON_LEAVE':
      return 'İzinli'
    case 'SICK':
      return 'Hasta'
    case 'INACTIVE':
      return 'Pasif'
    default:
      return 'Bilinmiyor'
  }
}

const getPerformanceBarClass = (score: number) => {
  if (score >= 85) return 'bg-green-500'
  if (score >= 70) return 'bg-yellow-500'
  return 'bg-red-500'
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'Belirtilmemiş'
  const date = new Date(dateString)
  return date.toLocaleDateString('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}
</script> 