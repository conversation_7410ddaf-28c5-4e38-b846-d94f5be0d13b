import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    dts({
      insertTypesEntry: true,
      rollupTypes: true,
      exclude: ['node_modules/**', 'dist/**']
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'HotelexiaSharedComponents',
      fileName: 'index',
      formats: ['es']
    },
    rollupOptions: {
      external: [
        'vue',
        'vue-router',
        'vue-i18n',
        '@hotelexia/shared-supabase-client',
        '@heroicons/vue/24/outline',
        '@heroicons/vue/24/solid',
        'radix-vue',
        '@ark-ui/vue',
        'lucide-vue-next',
        'class-variance-authority',
        'clsx',
        'tailwind-merge'
      ],
      output: {
        globals: {
          vue: 'Vue',
          'vue-router': 'VueRouter',
          'vue-i18n': 'VueI18n'
        },
      },
    },
    target: 'esnext',
    minify: false
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
}) 