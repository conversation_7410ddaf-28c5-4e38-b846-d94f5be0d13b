<template>
  <div class="min-h-screen bg-backgroundLight flex flex-col">
    <!-- Back Button -->
    <div class="pt-12 px-6">
      <button 
        @click="$router.go(-1)"
        class="flex items-center justify-center w-10 h-10 rounded-lg bg-white shadow-sm border border-gray-200"
      >
        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex items-center justify-center px-6 py-8">
      <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
          <h1 class="text-4xl font-bold text-textDark mb-2">
            <PERSON><PERSON><PERSON> Geldiniz!
          </h1>
          <p class="text-textMedium text-lg">
            <PERSON><PERSON> etmek için giri<PERSON> yapın
          </p>
        </div>

        <!-- Login Form -->
        <form @submit.prevent="handleGuestLogin" class="space-y-6">
          <!-- Reservation Number Field -->
          <div>
            <div class="relative">
              <div class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-gradient-to-r from-primary to-teal-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <input
                id="reservationNumber"
                v-model="form.reservationNumber"
                type="text"
                required
                class="w-full h-16 pl-20 pr-16 bg-white rounded-2xl border border-gray-200 text-textDark placeholder-textLight text-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Rezervasyon Numarası"
                :class="{ 'border-red-500': errors.reservationNumber }"
                @input="clearFieldError('reservationNumber')"
              />
              <div v-if="form.reservationNumber" class="absolute right-4 top-1/2 transform -translate-y-1/2">
                <svg class="w-6 h-6 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
            </div>
            <p v-if="errors.reservationNumber" class="mt-2 text-sm text-red-500">
              {{ errors.reservationNumber }}
            </p>
          </div>

          <!-- Last Name Field -->
          <div>
            <div class="relative">
              <div class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-gradient-to-r from-primary to-teal-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <input
                id="lastName"
                v-model="form.lastName"
                type="text"
                required
                class="w-full h-16 pl-20 pr-16 bg-white rounded-2xl border border-gray-200 text-textDark placeholder-textLight text-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Soyadı"
                :class="{ 'border-red-500': errors.lastName }"
                @input="clearFieldError('lastName')"
              />
              <button
                type="button"
                class="absolute right-4 top-1/2 transform -translate-y-1/2 p-2"
                @click="toggleLastNameVisibility"
              >
                <svg v-if="showLastName" class="w-5 h-5 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg v-else class="w-5 h-5 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
              </button>
            </div>
            <p v-if="errors.lastName" class="mt-2 text-sm text-red-500">
              {{ errors.lastName }}
            </p>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="flex items-center justify-between">
            <label class="flex items-center">
              <input 
                v-model="rememberMe"
                type="checkbox" 
                class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2"
              >
              <span class="ml-2 text-sm text-textMedium">Beni hatırla</span>
            </label>
            
            <button
              type="button"
              @click="showForgotPassword"
              class="text-sm text-primary hover:text-teal-600 font-medium"
            >
              Rezervasyonu unuttunuz mu?
            </button>
          </div>

          <!-- Error Message -->
          <div v-if="guestAuthStore.error" class="p-4 bg-red-50 border border-red-200 rounded-2xl">
            <p class="text-sm text-red-600 text-center">{{ guestAuthStore.error }}</p>
          </div>

          <!-- Sign In Button -->
          <button
            type="submit"
            :disabled="guestAuthStore.isLoading || !isFormValid"
            class="w-full h-16 bg-gradient-to-r from-primary to-teal-600 hover:from-teal-600 hover:to-primary text-white text-lg font-semibold rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span v-if="guestAuthStore.isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Giriş yapılıyor...
            </span>
                          <span v-else>Giriş Yap</span>
          </button>

          <!-- Don't have account -->
          <p class="text-center text-textMedium">
            Hesabınız yok mu? 
            <button
              type="button"
              @click="showQRScanner = true"
              class="text-primary hover:text-teal-600 font-medium ml-1"
            >
              QR Kod Taratın.
            </button>
          </p>
        </form>



        <!-- Test Credentials Helper (Development only) -->
        <div v-if="isDevelopment" class="text-center p-4 bg-blue-50 border border-blue-200 rounded-2xl">
          <p class="text-sm text-blue-800 font-medium mb-2">Test Bilgileri:</p>
          <p class="text-xs text-blue-600">
            Rezervasyon: HTL2024001<br>
            Soyad: Smith
          </p>
          <button
            type="button"
            @click="fillTestCredentials"
            class="mt-2 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg"
          >
            Test bilgilerini kullan
          </button>
        </div>
      </div>
    </div>

    <!-- QR Scanner Modal -->
    <div
      v-if="showQRScanner"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click="showQRScanner = false"
    >
      <div class="bg-white rounded-2xl p-6 max-w-sm w-full text-center" @click.stop>
        <h3 class="text-xl font-semibold text-textDark mb-4">QR Kod Tarayıcısı</h3>
        
        <!-- Camera/Scanner Placeholder -->
        <div class="bg-gray-100 h-64 rounded-2xl flex flex-col items-center justify-center mb-6 relative overflow-hidden">
          <div class="absolute inset-4 border-2 border-primary rounded-2xl"></div>
          <div class="text-center z-10">
            <svg class="w-16 h-16 text-primary mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M6 8h4m-6 8h2m4-4h.01M16 8h2m0 0h2m-6 4h2"/>
            </svg>
            <p class="text-textMedium">QR kodu çerçeve içine konumlandırın</p>
            <p class="text-sm text-textLight mt-1">Kamera otomatik olarak tarayacak</p>
          </div>
          
          <!-- Scanning animation -->
          <div class="absolute top-4 left-4 right-4 h-1 bg-primary opacity-75 animate-pulse"></div>
        </div>
        
        <div class="space-y-3">
          <button
            @click="mockQRScan"
            class="w-full bg-primary hover:bg-teal-600 text-white py-3 rounded-xl font-medium transition-colors"
          >
            QR Tarama Simülasyonu (Demo)
          </button>
          
          <button
            @click="showQRScanner = false"
            class="w-full bg-gray-100 hover:bg-gray-200 text-textDark py-3 rounded-xl font-medium transition-colors"
          >
            İptal
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGuestAuthStore } from '../stores/guestAuthStore'

const router = useRouter()
const guestAuthStore = useGuestAuthStore()

// Form state
const form = ref({
  reservationNumber: '',
  lastName: ''
})

const errors = ref({
  reservationNumber: '',
  lastName: ''
})

const showQRScanner = ref(false)
const showLastName = ref(false)
const rememberMe = ref(false)

// Development mode check
const isDevelopment = computed(() => {
  return import.meta.env.DEV
})

// Form validation
const isFormValid = computed(() => {
  return form.value.reservationNumber.trim().length > 0 && 
         form.value.lastName.trim().length > 0
})

// Toggle last name visibility (for privacy)
const toggleLastNameVisibility = () => {
  showLastName.value = !showLastName.value
}

// Clear field-specific errors
const clearFieldError = (field: string) => {
  errors.value[field as keyof typeof errors.value] = ''
  guestAuthStore.clearError()
}

// Fill test credentials (development only)
const fillTestCredentials = () => {
  form.value.reservationNumber = 'HTL2024001'
  form.value.lastName = 'Smith'
}

// Show forgot password help
const showForgotPassword = () => {
  alert('Rezervasyon bilgileriniz için lütfen resepsiyonla iletişime geçin.')
}

// Mock QR scan for demo
const mockQRScan = () => {
  // Simulate QR code data containing reservation info
  const qrData = {
    reservationNumber: 'HTL2024001',
    lastName: 'Smith'
  }
  
  form.value.reservationNumber = qrData.reservationNumber
  form.value.lastName = qrData.lastName
  showQRScanner.value = false
  
  // Auto-submit after QR scan
  setTimeout(() => {
    handleGuestLogin()
  }, 500)
}

// Get default hotel ID
const getDefaultHotelId = () => {
  return import.meta.env.VITE_DEFAULT_HOTEL_ID || '660e8400-e29b-41d4-a716-446655440000'
}

// Guest login handler
const handleGuestLogin = async () => {
  // Clear previous errors
  errors.value = {
    reservationNumber: '',
    lastName: ''
  }
  guestAuthStore.clearError()
  
  // Basic validation
  if (!form.value.reservationNumber.trim()) {
    errors.value.reservationNumber = 'Rezervasyon numarası gereklidir'
    return
  }
  
  if (!form.value.lastName.trim()) {
    errors.value.lastName = 'Soyad gereklidir'
    return
  }

  try {
    const hotelId = getDefaultHotelId()
    
    const result = await guestAuthStore.loginWithReservation(
      form.value.reservationNumber.trim(),
      form.value.lastName.trim(),
      hotelId
    )
    
    if (result.success) {
      console.log('Guest login successful, redirecting to dashboard...')

      // Store remember me preference
      if (rememberMe.value) {
        localStorage.setItem('hotelexia_remember_guest', 'true')
      }

      // Schedule automatic data cleanup
      guestAuthStore.scheduleDataCleanup()

      // Check for stored redirect path
      const redirectPath = sessionStorage.getItem('hotelexia_guest_redirect_after_login')
      if (redirectPath) {
        sessionStorage.removeItem('hotelexia_guest_redirect_after_login')
        await router.push(redirectPath)
      } else {
        await router.push('/dashboard')
      }
    }
    
  } catch (error) {
    console.error('Guest login error:', error)
  }
}

// Check for existing session on mount
onMounted(async () => {
  const hasSession = await guestAuthStore.checkExistingSession()
  if (hasSession) {
    console.log('Existing session found, redirecting to dashboard...')
    await router.push('/dashboard')
  }
})
</script>

<style scoped>
/* Custom animations and focus styles */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Focus styles for better accessibility */
input:focus {
  outline: none;
}

/* Touch-friendly button styles */
button {
  touch-action: manipulation;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style> 