---
description: MFE: Hotel Dashboard Integration Summary
globs: 
alwaysApply: false
---
# MFE: Hotel Dashboard Integration Summary
**Latest Plan Reference: `backend-tasklist.mdc` - Phase 3**

## 1. Core Purpose
This MFE serves as the operational hub for hotel staff (`hotel_manager`, `hotel_staff`). It provides the tools to manage day-to-day activities, from room status and maintenance to activity scheduling, all scoped to the user's assigned hotel.

## 2. Key Backend Integration Tasks (Phase 3)

### 2.1. Hotel-Scoped Data Structures
- **Primary Tables:** `rooms`, `maintenance_tasks`, `hotel_activities`.
- **Foreign Keys:** All tables are linked to `public.hotels` to ensure data is associated with a specific hotel.
- **Permissions:** Strict Row Level Security (RLS) is the cornerstone of this MFE's security. All policies MUST validate that the authenticated user's `hotel_id` (from their `profiles` entry) matches the `hotel_id` of the data they are attempting to access.
- **Role Differentiation:** RLS policies will differentiate between `hotel_manager` (broader permissions, e.g., create/delete) and `hotel_staff` (e.g., update/read-only access).

### 2.2. Seeding & Dummy Data
- The backend integration plan includes specific tasks to **seed the database with dummy data** sourced directly from the current static frontend components. This includes:
    - Seeding the `rooms` table.
    - Seeding the `maintenance_tasks` table and assigning them to dummy staff members.
    - Seeding the `hotel_activities` table.
- This ensures a seamless transition from static mockups to a live, data-driven interface.

### 2.3. View & Component Integration
- **The integration will proceed view-by-view** to ensure a controlled and testable rollout.
- **Maintenance Task Management (`maintenance/TaskManagement.vue`):**
    - **Action:** Replace the static task list with a live `select` query on the `maintenance_tasks` table, filtered by RLS.
    - **Action:** Connect the "Create Task" form to `insert` new records into the table.
- **Room Management (`maintenance/RoomManagement.vue`):**
    - **Action:** Replace the static room list with a live `select` query on the `rooms` table, filtered by RLS.
- **Other Views:** This process will be repeated for all other management views, connecting them to their respective backend tables (`hotel_activities`, etc.).

## 3. Security & Access
- **Authentication:** Relies entirely on the centralized `authStore` from the App Shell. The dashboard is only accessible to users with `hotel_manager` or `hotel_staff` roles.
- **Authorization:** Every single database query is implicitly and automatically protected by Supabase RLS policies. There is no need for manual `where hotel_id = ...` clauses in the frontend code.

This summary provides a focused overview of the integration tasks for the Hotel Dashboard MFE as detailed in the main backend plan.

## 4. Exposed Components (`vite.config.ts`)
- `HotelDashboard.vue` (as `./HotelDashboard`) - Main app container
- `Dashboard.vue` (as `./Dashboard`) - Main dashboard view
- `ContentOverview.vue` (as `./ContentOverview`)
- `RoomServiceManagement.vue` (as `./RoomServiceManagement`)
- `GuestServiceManagement.vue` (as `./GuestServiceManagement`)
- `ActivityManagement.vue` (as `./ActivityManagement`)
- `ActivityCategoryManagement.vue` (as `./ActivityCategoryManagement`)
- `HousekeepingOverview.vue` (as `./HousekeepingOverview`)
- `TaskManagement.vue` (as `./TaskManagement`)
- `MaintenanceOverview.vue` (as `./MaintenanceOverview`)
- `FloorManagement.vue` (as `./FloorManagement`)
- `RoomManagement.vue` (as `./RoomManagement`)
- `StaffManagement.vue` (as `./StaffManagement`)
- `StaffCommunication.vue` (as `./StaffCommunication`)
- `PerformanceAnalysis.vue` (as `./PerformanceAnalysis`)
- `CustomerReports.vue` (as `./CustomerReports`)
- `StaffReports.vue` (as `./StaffReports`)
- `OperationalReports.vue` (as `./OperationalReports`)
- `NotificationCenter.vue` (as `./NotificationCenter`)
- `Settings.vue` (as `./Settings`)
- `Profile.vue` (as `./Profile`)

## 5. Authentication & Authorization

### **Portal Access Routes**
All hotel dashboard routes are protected by authentication guards in the App Shell:
- **Login Route:** `/hotel/auth/login` - Role-based login for hotel staff
- **Access Requirements:** Users must have `hotel_manager` or `hotel_staff` role
- **Auto-redirect:** Authenticated users with incorrect roles are redirected to appropriate dashboards

### **User Roles & Permissions**
- **`hotel_manager`:** Full CRUD access to all hotel operations, reports, and settings
- **`hotel_staff`:** Limited access to operational tasks, basic reports, and assigned duties
- **Scope:** All operations are automatically scoped to the user's assigned `hotel_id` via RLS policies

## 6. State Management

### **Authentication Integration**
- **Consumed:** Integrates with App Shell's `useAuthStore()` for centralized authentication
- **Profile Data:** Accesses `userRole`, `userHotelId`, `userDisplayName` from auth store
- **Permission Checks:** Components use `hasHotelAccess`, `isHotelManager`, `isHotelStaff` computed properties

### **Local Stores**
- **`dashboardStore.ts`:** Dashboard-specific data and KPIs
- **`housekeepingStore.ts`:** Housekeeping tasks and room status management
- **`managementStore.ts`:** Hotel management and configuration data

## 7. API & Backend Integration

### **Supabase Integration**
- **Client:** Uses shared `@hotelexia/shared-supabase-client` package
- **Authentication:** Leverages Supabase Auth for JWT-based session management
- **Database:** All queries automatically filtered by user's `hotel_id` via RLS policies

### **Core Database Tables & Operations**

#### **Hotel Operations**
```sql
-- hotel_activities: Activity management and scheduling
-- room_service_items: Menu and pricing management
-- room_service_categories: Service categorization
-- maintenance_tasks: Facility maintenance tracking
-- housekeeping_tasks: Cleaning and room status management
```

#### **Room & Facility Management**
```sql
-- rooms: Room inventory and status
-- hotel_rooms: Housekeeping status and floor management
-- reservations: Guest booking and check-in/out status
```

#### **Order & Service Management**
```sql
-- room_service_orders: Guest service requests
-- order_items: Detailed order tracking
-- service_requests: Maintenance and housekeeping requests
```

### **Real-time Features**
- **Live Dashboard Updates:** Real-time KPIs for occupancy, revenue, and service requests
- **Task Notifications:** Instant updates for new maintenance and housekeeping assignments
- **Order Tracking:** Live status updates for room service and guest requests
- **Staff Communication:** Real-time messaging and announcement system

### **Key API Operations (RLS-Protected)**

#### **Dashboard Data**
```typescript
// Aggregated dashboard statistics
- getHotelKPIs(hotel_id): occupancy, revenue, active_requests
- getRecentActivities(hotel_id): latest bookings, service requests
- getPendingTasks(hotel_id): maintenance, housekeeping assignments
```

#### **Content Management**
```typescript
// Room service and activity management
- getRoomServiceMenu(hotel_id): categories, items, pricing
- getHotelActivities(hotel_id): events, schedules, capacity
- updateMenuPricing(hotel_id, item_id, price): price updates
- createActivity(hotel_id, activity_data): new activity scheduling
```

#### **Operations Management**
```typescript
// Housekeeping and maintenance
- getHousekeepingTasks(hotel_id): room status, cleaning assignments
- getMaintenanceTasks(hotel_id): facility issues, repair schedules
- updateRoomStatus(hotel_id, room_id, status): room cleaning status
- assignTask(hotel_id, task_id, staff_id): task assignment
```

#### **Reports & Analytics**
```typescript
// Business intelligence and reporting
- getCustomerReports(hotel_id, date_range): guest satisfaction, bookings
- getStaffReports(hotel_id, date_range): performance, task completion
- getOperationalReports(hotel_id, date_range): revenue, efficiency metrics
- exportReport(hotel_id, report_type, format): PDF/Excel export
```

## 8. Security & Performance

### **Row Level Security (RLS)**
All database operations are protected by Supabase RLS policies that automatically filter data by the authenticated user's `hotel_id`, ensuring complete data isolation between hotels.

### **Performance Optimizations**
- **Lazy Loading:** Components are dynamically imported via Module Federation
- **Data Caching:** Frequent queries are cached with automatic invalidation
- **Real-time Subscriptions:** Selective subscriptions to minimize bandwidth usage
- **Pagination:** Large datasets (tasks, reports) are paginated for performance

### **Error Handling**
- **Network Resilience:** Automatic retry logic for failed API calls
- **Offline Support:** Essential data cached for limited offline functionality
- **User Feedback:** Toast notifications for all CRUD operations
- **Error Boundaries:** Graceful error handling at component level

## 9. Integration Points

### **App Shell Communication**
- **Authentication:** Shares auth state via Pinia store across MFE boundaries
- **Navigation:** Integrates with App Shell router for seamless transitions
- **Notifications:** Receives global notifications and system announcements

### **Cross-MFE Data Sharing**
- **Guest Profiles:** Shares guest data with Customer Portal for unified experience
- **Activity Updates:** Synchronizes activity schedules with Customer Portal
- **Service Requests:** Coordinates with Customer Portal for request tracking

### **Third-Party Integrations**
- **Payment Processing:** Integrates with Stripe/PayPal for service billing
- **Communication:** SMS/Email notifications via Twilio/SendGrid
- **Analytics:** Google Analytics for usage tracking and optimization

## 10. Development & Deployment

### **Module Federation Configuration**
```typescript
// Hotel Dashboard is exposed as a remote MFE
remotes: {
  hotelDashboard: 'http://localhost:5002/assets/remoteEntry.js'
}

// Shared dependencies for performance
      shared: {
        vue: { singleton: true },
        'vue-router': { singleton: true },
  pinia: { singleton: true },
  '@hotelexia/shared-supabase-client': {}
}
```

### **Environment Configuration**
- **Development:** `http://localhost:5002` with hot module replacement
- **Staging:** `https://hotel-staging.hotelexia.com` with source maps
- **Production:** `https://hotel.hotelexia.com` with optimized bundles

### **Build & Testing**
- **Type Safety:** Full TypeScript support with shared type definitions
- **Unit Tests:** Vitest for component and store testing
- **Integration Tests:** Cypress for end-to-end workflow testing
- **Performance Tests:** Lighthouse CI for bundle size and performance monitoring

This MFE provides hotel staff with a comprehensive, secure, and performant dashboard for managing all aspects of hotel operations while maintaining strict data isolation and role-based access control.
