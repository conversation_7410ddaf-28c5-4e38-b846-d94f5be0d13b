<template>
  <div class="p-6 bg-white dark:bg-navy-800 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-navy-700 dark:text-white">Bakım Görevleri Yönetimi</h2>
      <button
        @click="showCreateModal = true"
        class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
      >
        <PlusIcon class="w-5 h-5" />
        Ye<PERSON>öre<PERSON>
      </button>
    </div>

    <!-- Filters -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <select v-model="filterStatus" class="border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white">
        <option value="">Tüm Durumlar</option>
        <option value="PENDING">Beklemede</option>
        <option value="IN_PROGRESS">Devam Ediyor</option>
        <option value="COMPLETED">Tamamlandı</option>
        <option value="CANCELLED">İptal Edildi</option>
      </select>
      
      <select v-model="filterPriority" class="border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white">
        <option value="">Tüm Öncelikler</option>
        <option value="LOW">Düşük</option>
        <option value="MEDIUM">Orta</option>
        <option value="HIGH">Yüksek</option>
        <option value="URGENT">Acil</option>
      </select>

      <select v-model="filterRoom" class="border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white">
        <option value="">Tüm Odalar</option>
        <option v-for="room in uniqueRooms" :key="room" :value="room">{{ room }}</option>
      </select>

      <input
        v-model="searchTerm"
        type="text"
        placeholder="Görev ara..."
        class="border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white"
      />
    </div>

    <!-- Tasks Table -->
    <div class="overflow-x-auto">
      <table class="w-full table-auto">
        <thead>
          <tr class="bg-gray-50 dark:bg-navy-700">
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Görev</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Oda</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Öncelik</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Durum</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Atanan</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">Oluşturulma</th>
            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">İşlemler</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="task in filteredTasks" :key="task.id" class="border-b dark:border-navy-600">
            <td class="px-4 py-3">
              <div>
                <div class="font-medium text-navy-700 dark:text-white">{{ task.title }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ task.description }}</div>
              </div>
            </td>
            <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-300">
              {{ task.rooms?.room_number || 'N/A' }}
            </td>
            <td class="px-4 py-3">
              <span :class="getPriorityClass(task.priority || 'MEDIUM')" class="px-2 py-1 rounded-full text-xs font-medium">
                {{ getPriorityText(task.priority || 'MEDIUM') }}
              </span>
            </td>
            <td class="px-4 py-3">
              <span :class="getStatusClass(task.status || 'PENDING')" class="px-2 py-1 rounded-full text-xs font-medium">
                {{ getStatusText(task.status || 'PENDING') }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-300">
              {{ task.profiles?.full_name || 'Atanmamış' }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-900 dark:text-gray-300">
              {{ new Date(task.created_at).toLocaleDateString('tr-TR') }}
            </td>
            <td class="px-4 py-3">
              <div class="flex gap-2">
                <button
                  @click="editTask(task)"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                >
                  <PencilIcon class="w-4 h-4" />
                </button>
                <button
                  @click="deleteTask(task.id)"
                  class="text-red-600 hover:text-red-800 dark:text-red-400"
                >
                  <TrashIcon class="w-4 h-4" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Create/Edit Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-800 rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-bold mb-4 text-navy-700 dark:text-white">
          {{ showEditModal ? 'Görevi Düzenle' : 'Yeni Görev Oluştur' }}
        </h3>
        
        <form @submit.prevent="showEditModal ? updateTask() : createTask()">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Başlık</label>
              <input
                v-model="taskForm.title"
                type="text"
                required
                class="w-full border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Açıklama</label>
              <textarea
                v-model="taskForm.description"
                rows="3"
                class="w-full border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white"
              ></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Oda</label>
              <select
                v-model="taskForm.room_id"
                required
                class="w-full border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white"
              >
                <option value="">Oda Seçin</option>
                <option v-for="room in availableRooms" :key="room.id" :value="room.id">
                  {{ room.room_number }} - {{ room.room_type }}
                </option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Öncelik</label>
              <select
                v-model="taskForm.priority"
                required
                class="w-full border rounded-lg px-3 py-2 dark:bg-navy-700 dark:border-navy-600 dark:text-white"
              >
                <option value="LOW">Düşük</option>
                <option value="MEDIUM">Orta</option>
                <option value="HIGH">Yüksek</option>
                <option value="URGENT">Acil</option>
              </select>
            </div>
          </div>
          
          <div class="flex justify-end gap-3 mt-6">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              İptal
            </button>
            <button
              type="submit"
              class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg"
            >
              {{ showEditModal ? 'Güncelle' : 'Oluştur' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { supabase } from '@hotelexia/shared-supabase-client'
import type { Database } from '@hotelexia/shared-supabase-client'
import { 
  PlusIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'

type MaintenanceTask = Database['public']['Tables']['maintenance_tasks']['Row'] & {
  rooms: { room_number: string; room_type: string } | null;
  profiles: { full_name: string } | null;
}

type Room = Database['public']['Tables']['rooms']['Row']

const tasks = ref<MaintenanceTask[]>([])
const availableRooms = ref<Room[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

// Filters
const filterStatus = ref('')
const filterPriority = ref('')
const filterRoom = ref('')
const searchTerm = ref('')

// Modal states
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingTask = ref<MaintenanceTask | null>(null)

// Form data
const taskForm = ref({
  title: '',
  description: '',
  room_id: '',
  priority: 'MEDIUM',
  status: 'PENDING'
})

const fetchTasks = async () => {
  try {
    loading.value = true
    const { data, error: fetchError } = await (supabase as any)
      .from('maintenance_tasks')
      .select(`
        *,
        rooms ( room_number, room_type ),
        profiles:assigned_to ( full_name )
      `)
      .order('created_at', { ascending: false })

    if (fetchError) throw fetchError
    tasks.value = data || []
  } catch (e: any) {
    error.value = `Görevler yüklenemedi: ${e.message}`
  } finally {
    loading.value = false
  }
}

const fetchRooms = async () => {
  try {
    const { data, error: fetchError } = await (supabase as any)
      .from('rooms')
      .select('id, room_number, room_type')
      .eq('is_active', true)
      .order('room_number')

    if (fetchError) throw fetchError
    availableRooms.value = data || []
  } catch (e: any) {
    console.error('Error fetching rooms:', e.message)
  }
}

onMounted(() => {
  fetchTasks()
  fetchRooms()
})

const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    const statusMatch = !filterStatus.value || task.status === filterStatus.value
    const priorityMatch = !filterPriority.value || task.priority === filterPriority.value
    const roomMatch = !filterRoom.value || task.rooms?.room_number === filterRoom.value
    const searchMatch = !searchTerm.value || 
      task.title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      task.description?.toLowerCase().includes(searchTerm.value.toLowerCase())
    
    return statusMatch && priorityMatch && roomMatch && searchMatch
  })
})

const uniqueRooms = computed(() => {
  const rooms = tasks.value.map(task => task.rooms?.room_number).filter(Boolean)
  return [...new Set(rooms)]
})

const createTask = async () => {
  try {
    const { error } = await (supabase as any)
      .from('maintenance_tasks')
      .insert({
        ...taskForm.value,
        hotel_id: 'hotel_001', // Should be from user context
        created_at: new Date().toISOString()
      })

    if (error) throw error
    
    await fetchTasks()
    closeModal()
  } catch (error: any) {
    console.error('Error creating task:', error.message)
  }
}

const updateTask = async () => {
  if (!editingTask.value) return
  
  try {
    const { error } = await (supabase as any)
      .from('maintenance_tasks')
      .update({
        ...taskForm.value,
        updated_at: new Date().toISOString()
      })
      .eq('id', editingTask.value.id)

    if (error) throw error
    
    await fetchTasks()
    closeModal()
  } catch (error: any) {
    console.error('Error updating task:', error.message)
  }
}

const deleteTask = async (taskId: string) => {
  if (!confirm('Bu görevi silmek istediğinizden emin misiniz?')) return
  
  try {
    const { error } = await (supabase as any)
      .from('maintenance_tasks')
      .delete()
      .eq('id', taskId)

    if (error) throw error
    
    await fetchTasks()
  } catch (error: any) {
    console.error('Error deleting task:', error.message)
  }
}

const editTask = (task: MaintenanceTask) => {
  editingTask.value = task
  taskForm.value = {
    title: task.title,
    description: task.description || '',
    room_id: task.room_id || '',
    priority: task.priority || 'MEDIUM',
    status: task.status || 'PENDING'
  }
  showEditModal.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTask.value = null
  taskForm.value = {
    title: '',
    description: '',
    room_id: '',
    priority: 'MEDIUM',
    status: 'PENDING'
  }
}

const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200'
    case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-800/30 dark:text-orange-200'
    case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-200'
    case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-200'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-200'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'Acil'
    case 'HIGH': return 'Yüksek'
    case 'MEDIUM': return 'Orta'
    case 'LOW': return 'Düşük'
    default: return priority
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-200'
    case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-200'
    case 'PENDING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-200'
    case 'CANCELLED': return 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-200'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-200'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'Tamamlandı'
    case 'IN_PROGRESS': return 'Devam Ediyor'
    case 'PENDING': return 'Beklemede'
    case 'CANCELLED': return 'İptal Edildi'
    default: return status
  }
}
</script>
