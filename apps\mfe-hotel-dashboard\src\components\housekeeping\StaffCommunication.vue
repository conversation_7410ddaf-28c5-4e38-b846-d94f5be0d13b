<template>
  <div class="h-full flex bg-gray-50 dark:bg-gray-900">
    <!-- Sidebar -->
    <div class="w-80 bg-white dark:bg-navy-900 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      <!-- Header -->
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4"><PERSON><PERSON><PERSON><PERSON><PERSON></h2>
        
        <!-- Search -->
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Ara..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm"
          />
        </div>
      </div>

      <!-- Channels & DMs -->
      <div class="flex-1 overflow-y-auto">
        <!-- General Channels -->
        <div class="p-4">
          <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">Kanallar</h3>
          <div class="space-y-1">
            <div
              v-for="channel in filteredChannels"
              :key="channel.id"
              @click="selectChannel(channel)"
              :class="[
                'flex items-center justify-between px-3 py-2 rounded-lg cursor-pointer transition-colors',
                selectedChannel?.id === channel.id
                  ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
              ]"
            >
              <div class="flex items-center space-x-3 min-w-0 flex-1">
                <div class="flex-shrink-0">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
                <div class="min-w-0 flex-1">
                  <p class="text-sm font-medium truncate"># {{ channel.name }}</p>
                  <p v-if="channel.lastMessage" class="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {{ channel.lastMessage.senderName }}: {{ channel.lastMessage.message }}
                  </p>
                </div>
              </div>
              <div v-if="channel.unreadCount > 0" class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {{ channel.unreadCount }}
              </div>
            </div>
          </div>
        </div>

        <!-- Direct Messages -->
        <div class="p-4">
          <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">Direkt Mesajlar</h3>
          <div class="space-y-1">
            <div
              v-for="staff in filteredStaff"
              :key="staff.id"
              @click="selectDirectMessage(staff)"
              :class="[
                'flex items-center space-x-3 px-3 py-2 rounded-lg cursor-pointer transition-colors',
                selectedDM?.id === staff.id
                  ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
              ]"
            >
              <div class="relative">
                <img :src="staff.avatar" :alt="staff.name" class="w-8 h-8 rounded-full">
                <div :class="getStatusIndicatorClass(staff.status)" class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800"></div>
              </div>
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium truncate">{{ staff.name }} {{ staff.surname }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ getStatusText(staff.status) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="flex-1 flex flex-col">
      <!-- Chat Header -->
      <div v-if="selectedChannel || selectedDM" class="bg-white dark:bg-navy-900 border-b border-gray-200 dark:border-gray-700 p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div v-if="selectedChannel" class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <h2 class="text-lg font-semibold text-gray-800 dark:text-white"># {{ selectedChannel.name }}</h2>
              <span class="text-sm text-gray-500 dark:text-gray-400">({{ selectedChannel.participants.length }} üye)</span>
            </div>
            <div v-else-if="selectedDM" class="flex items-center space-x-3">
              <div class="relative">
                <img :src="selectedDM.avatar" :alt="selectedDM.name" class="w-10 h-10 rounded-full">
                <div :class="getStatusIndicatorClass(selectedDM.status)" class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800"></div>
              </div>
              <div>
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white">{{ selectedDM.name }} {{ selectedDM.surname }}</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ getStatusText(selectedDM.status) }}</p>
              </div>
            </div>
          </div>
          
          <!-- Actions -->
          <div class="flex items-center space-x-2">
            <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
            </button>
            <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </button>
            <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Messages Area -->
      <div v-if="selectedChannel || selectedDM" class="flex-1 overflow-y-auto p-4 space-y-4" ref="messagesContainer">
        <div
          v-for="message in currentMessages"
          :key="message.id"
          :class="[
            'flex items-start space-x-3',
            message.senderId === 'current-user' ? 'flex-row-reverse space-x-reverse' : ''
          ]"
        >
          <img :src="getSenderAvatar(message.senderId)" :alt="message.senderName" class="w-8 h-8 rounded-full flex-shrink-0">
          <div :class="['flex-1 max-w-lg', message.senderId === 'current-user' ? 'text-right' : '']">
            <div class="flex items-center space-x-2 mb-1">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ message.senderName }}</span>
              <span class="text-xs text-gray-500 dark:text-gray-400">{{ formatMessageTime(message.timestamp) }}</span>
            </div>
            <div
              :class="[
                'inline-block px-4 py-2 rounded-lg text-sm',
                message.senderId === 'current-user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-white'
              ]"
            >
              {{ message.message }}
            </div>
          </div>
        </div>
      </div>

      <!-- Message Input -->
      <div v-if="selectedChannel || selectedDM" class="bg-white dark:bg-navy-900 border-t border-gray-200 dark:border-gray-700 p-4">
        <div class="flex items-center space-x-4">
          <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
            </svg>
          </button>
          
          <div class="flex-1">
            <input
              v-model="newMessage"
              @keydown.enter="sendMessage"
              type="text"
              placeholder="Mesaj yazın..."
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>
          
          <button
            @click="sendMessage"
            :disabled="!newMessage.trim()"
            class="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <svg class="w-16 h-16 mx-auto text-gray-300 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Mesajlaşmaya başlayın</h3>
          <p class="text-gray-500 dark:text-gray-400">Bir kanal veya kişi seçerek mesajlaşmaya başlayabilirsiniz.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { ChatChannel, StaffMember, ChatMessage, StaffStatus } from '@/types/housekeeping'

// Store
const housekeepingStore = useHousekeepingStore()

// State
const searchQuery = ref('')
const selectedChannel = ref<ChatChannel | null>(null)
const selectedDM = ref<StaffMember | null>(null)
const newMessage = ref('')
const messagesContainer = ref<HTMLElement>()

// Computed
const filteredChannels = computed(() => {
  if (!searchQuery.value) return housekeepingStore.chatChannels
  return housekeepingStore.chatChannels.filter(channel =>
    channel.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const filteredStaff = computed(() => {
  if (!searchQuery.value) return housekeepingStore.staff
  return housekeepingStore.staff.filter(staff =>
    `${staff.name} ${staff.surname}`.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const currentMessages = computed(() => {
  if (selectedChannel.value) {
    return housekeepingStore.chatMessages.filter(m => m.channelId === selectedChannel.value!.id)
  } else if (selectedDM.value) {
    // For direct messages, we would filter by a DM channel ID
    // For now, return empty array as DM functionality needs backend support
    return []
  }
  return []
})

// Methods
const selectChannel = (channel: ChatChannel) => {
  selectedChannel.value = channel
  selectedDM.value = null
  housekeepingStore.markChannelAsRead(channel.id)
  scrollToBottom()
}

const selectDirectMessage = (staff: StaffMember) => {
  selectedDM.value = staff
  selectedChannel.value = null
  // For direct messages, we would create or select a DM channel
  scrollToBottom()
}

const sendMessage = () => {
  if (!newMessage.value.trim()) return
  
  if (selectedChannel.value) {
    // For demo purposes, use first staff member as sender
    housekeepingStore.sendChatMessage(selectedChannel.value.id, housekeepingStore.staff[0].id, newMessage.value)
    newMessage.value = ''
    scrollToBottom()
  } else if (selectedDM.value) {
    // Direct message implementation would go here
    console.log('Direct message to:', selectedDM.value.name, selectedDM.value.surname)
    newMessage.value = ''
  }
}

const getSenderAvatar = (senderId: string) => {
  const staff = housekeepingStore.staff.find(s => s.id === senderId)
  return staff?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
}

const getStatusIndicatorClass = (status: StaffStatus) => {
  const classes = {
    'AVAILABLE': 'bg-green-500',
    'BUSY': 'bg-red-500',
    'BREAK': 'bg-yellow-500',
    'OFF_DUTY': 'bg-gray-500'
  }
  return classes[status] || classes.AVAILABLE
}

const getStatusText = (status: StaffStatus) => {
  const texts = {
    'AVAILABLE': 'Müsait',
    'BUSY': 'Meşgul',
    'BREAK': 'Molada',
    'OFF_DUTY': 'Mesai Dışı'
  }
  return texts[status] || status
}

const formatMessageTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  if (messageDate.getTime() === today.getTime()) {
    return date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
  } else if (messageDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    return `Dün ${date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`
  } else {
    return date.toLocaleDateString('tr-TR', { 
      day: '2-digit', 
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// Initialize with first channel
onMounted(() => {
  if (housekeepingStore.chatChannels.length > 0) {
    selectChannel(housekeepingStore.chatChannels[0])
  }
})
</script> 