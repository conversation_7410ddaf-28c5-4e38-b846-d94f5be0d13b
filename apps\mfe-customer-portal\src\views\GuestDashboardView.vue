<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header with User Profile -->
    <div class="bg-white px-6 pt-12 pb-4 shadow-sm">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-r from-primary to-teal-600 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold text-sm">
              {{ currentGuest?.guest_name?.charAt(0) || 'M' }}
            </span>
          </div>
          <div>
            <p class="text-textDark font-semibold">{{ currentGuest?.guest_name || 'Mi<PERSON>fi<PERSON>' }}</p>
            <p class="text-textLight text-sm">Oda {{ currentGuest?.room_number || '101' }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <div class="bg-gray-100 rounded-full px-3 py-1 flex items-center space-x-1">
            <span class="text-textDark font-semibold">₺{{ cartTotal }}</span>
            <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
              <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m-.4-2L3 3h2l.4 2M7 13L5.4 5M7 13l-2.293 2.293c-.195.195-.195.512 0 .707L7 18M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6.001"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Hero Section with Special Offer -->
    <div class="bg-gradient-to-br from-teal-400 to-teal-600 mx-6 mt-6 rounded-3xl p-6 relative overflow-hidden">
      <!-- Background decorative elements -->
      <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full"></div>
      <div class="absolute bottom-2 left-8 w-12 h-12 bg-white/10 rounded-full"></div>
      <div class="absolute top-1/2 right-8 w-6 h-6 bg-orange-400 rounded-full"></div>
      
      <div class="relative z-10">
        <h2 class="text-white text-2xl font-bold mb-2 leading-tight">
          Bugün Somon<br>Steak Sipariş Et
        </h2>
        <p class="text-teal-100 text-sm mb-4">
          Ve Tasarruf Et
        </p>
        <div class="flex items-baseline space-x-1 mb-4">
          <span class="text-yellow-300 text-4xl font-bold">%35</span>
        </div>
        
        <!-- Food Image Placeholder -->
        <div class="absolute top-4 right-4 w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
          <span class="text-3xl">🍽️</span>
        </div>
      </div>
      
      <!-- Slide indicators -->
      <div class="flex space-x-2 mt-6">
        <div class="w-6 h-1 bg-white rounded-full"></div>
        <div class="w-1 h-1 bg-white/50 rounded-full"></div>
        <div class="w-1 h-1 bg-white/50 rounded-full"></div>
      </div>
    </div>

    <!-- We Offer Section -->
    <div class="px-6 mt-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-textDark text-xl font-semibold">Kategorilerimiz</h3>
        <button 
          @click="router.push('/room-service-menu')"
          class="text-primary text-sm font-medium hover:underline"
        >
          Tümünü Gör →
        </button>
      </div>
      
      <div class="flex space-x-4 overflow-x-auto pb-2">
        <div 
          v-for="category in foodCategories" 
          :key="category.id"
          @click="navigateToCategory(category.id)"
          class="flex-shrink-0 bg-white rounded-2xl p-4 w-20 h-20 flex flex-col items-center justify-center shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-shadow"
        >
          <span class="text-2xl mb-1">{{ category.icon }}</span>
          <span class="text-textDark text-xs font-medium text-center">{{ category.name }}</span>
        </div>
      </div>
    </div>

    <!-- Recommended For You -->
    <div class="px-6 mt-8">
      <h3 class="text-textDark text-xl font-semibold mb-4">Size Önerilerimiz</h3>
      
      <div class="space-y-4">
        <div 
          v-for="dish in recommendedDishes" 
          :key="dish.id"
          class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex items-center space-x-4"
        >
          <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center">
            <span class="text-2xl">{{ dish.icon }}</span>
          </div>
          
          <div class="flex-1">
            <h4 class="text-textDark font-semibold">{{ dish.name }}</h4>
            <div class="flex items-center space-x-2 mt-1">
              <span class="text-textDark font-bold">₺{{ dish.price }}</span>
              <span class="text-textLight text-sm">{{ dish.weight }}g</span>
              <div v-if="dish.isNew" class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                YENİ
              </div>
            </div>
          </div>
          
          <div class="flex flex-col space-y-2">
            <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200">
              <svg class="w-4 h-4 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </button>
            <button 
              @click="addToCart(dish)"
              class="w-8 h-8 bg-primary rounded-full flex items-center justify-center hover:bg-teal-600"
            >
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Happy Clients Say -->
    <div class="px-6 mt-8 mb-24">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-textDark text-xl font-semibold">Mutlu Müşterilerimiz</h3>
        <button class="text-primary text-sm font-medium">
          Tümünü Gör →
        </button>
      </div>
      
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-12 h-12 bg-gradient-to-r from-primary to-teal-600 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold">A</span>
          </div>
          <div class="flex-1">
            <p class="text-textDark font-semibold">Ahmet Yılmaz</p>
            <div class="flex items-center space-x-1 mt-1">
              <div class="flex space-x-1">
                <span class="text-yellow-400 text-sm">★</span>
                <span class="text-yellow-400 text-sm">★</span>
                <span class="text-yellow-400 text-sm">★</span>
                <span class="text-yellow-400 text-sm">★</span>
                <span class="text-yellow-400 text-sm">★</span>
              </div>
            </div>
          </div>
          <div class="text-right">
            <p class="text-textLight text-sm">23 Ağu, 2023</p>
            <button class="text-primary text-sm font-medium">Yanıtla</button>
          </div>
        </div>
        
        <p class="text-textMedium leading-relaxed">
          Yemekleri kesinlikle sevdim! Lezzetler çok canlıydı ve sunum birinci sınıftı. Tekrar sipariş vermek için sabırsızlanıyorum!
        </p>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-3">
      <div class="flex justify-around items-center">
        <router-link
          to="/dashboard"
          class="flex flex-col items-center space-y-1 text-primary"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          <span class="text-xs font-medium">Ana Sayfa</span>
        </router-link>
        
        <router-link
          to="/room-service-menu"
          class="flex flex-col items-center space-y-1 text-textMedium hover:text-primary"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 1.5M7 13l-1.5-1.5M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
          </svg>
          <span class="text-xs font-medium">Oda Servisi</span>
        </router-link>
        
        <router-link
          to="/my-orders"
          class="flex flex-col items-center space-y-1 text-textMedium hover:text-primary"
        >
          <div class="relative">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.195.195-.195.512 0 .707L7 18M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6.001"></path>
            </svg>
            <div v-if="cartItemCount > 0" class="absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
              <span class="text-white text-xs font-semibold">{{ cartItemCount }}</span>
            </div>
          </div>
          <span class="text-xs font-medium">Sipariş</span>
        </router-link>
        
        <button 
          @click="toggleFavorites"
          class="flex flex-col items-center space-y-1 text-textMedium hover:text-primary"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
          <span class="text-xs font-medium">Favoriler</span>
        </button>
        
        <router-link
          to="/my-requests"
          class="flex flex-col items-center space-y-1 text-textMedium hover:text-primary"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-xs font-medium">Bildirimler</span>
        </router-link>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-6 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-textDark">Bilgiler yükleniyor...</p>
      </div>
    </div>

    <!-- Authentication Guard -->
    <div v-if="!guestAuthStore.isAuthenticated && !isLoading" class="fixed inset-0 bg-backgroundLight flex items-center justify-center p-4">
      <div class="bg-white rounded-2xl p-6 text-center max-w-sm w-full">
        <h3 class="text-lg font-semibold text-textDark mb-4">Oturum Bulunamadı</h3>
        <p class="text-textMedium mb-4">Dashboard'a erişmek için giriş yapmalısınız.</p>
        <button 
          @click="redirectToLogin"
          class="w-full bg-primary hover:bg-teal-600 text-white px-4 py-3 rounded-xl text-sm font-medium transition-colors"
        >
          Giriş Sayfasına Git
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGuestAuthStore } from '../stores/guestAuthStore'
import { useCartStore } from '../stores/cartStore'
import { useNotificationStore } from '../stores/notificationStore'
import { useCustomerDataStore } from '../stores/customerDataStore'
import { storeToRefs } from 'pinia'

const router = useRouter()
const guestAuthStore = useGuestAuthStore()
const cartStore = useCartStore()
const notificationStore = useNotificationStore()
const customerDataStore = useCustomerDataStore()

// Use storeToRefs for reactive access to store state
const { currentGuest, isLoading } = storeToRefs(guestAuthStore)

// Local reactive data
const foodCategories = ref([
  { id: 'meat', name: 'Et', icon: '🥩' },
  { id: 'salads', name: 'Salatalar', icon: '🥗' },
  { id: 'pasta', name: 'Makarna', icon: '🍝' },
  { id: 'fish', name: 'Balık', icon: '🐟' },
  { id: 'desserts', name: 'Tatlılar', icon: '🍰' },
  { id: 'drinks', name: 'İçecekler', icon: '🥤' }
])

const recommendedDishes = ref([
  {
    id: 1,
    name: 'Tavuklu Caesar Salata',
    price: 45,
    weight: 200,
    icon: '🥗',
    isNew: false
  },
  {
    id: 2,
    name: 'Kremalı Mantarlı Tavuk',
    price: 68,
    weight: 250,
    icon: '🍗',
    isNew: true
  },
  {
    id: 3,
    name: 'Somon Steak',
    price: 85,
    weight: 300,
    icon: '🍽️',
    isNew: false
  }
])

// Computed properties with defensive programming
const cartTotal = computed(() => {
  if (Array.isArray(cartStore.cart) && cartStore.cart.length > 0) {
    return cartStore.cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  }
  return 0 // Return 0 if the cart is not an array or is empty
})

const cartItemCount = computed(() => {
  if (Array.isArray(cartStore.cart) && cartStore.cart.length > 0) {
    return cartStore.cart.reduce((count, item) => count + item.quantity, 0)
  }
  return 0 // Return 0 if the cart is not an array or is empty
})

// Methods
const navigateToCategory = (categoryId: string) => {
  router.push(`/room-service-menu?category=${categoryId}`)
}

const addToCart = (dish: any) => {
  // Create a CartItem compatible object for adding to cart
  const menuItem = {
    id: dish.id.toString(),
    name: dish.name,
    price: dish.price,
    calories: 110, // Default value
    weight: dish.weight,
    image: '', // No image for demo dishes
    isNew: dish.isNew
  }
  
  cartStore.addToCart(menuItem)
  
  // Show success feedback
  alert(`${dish.name} sepete eklendi!`)
}

const toggleFavorites = () => {
  alert('Favoriler özelliği yakında!')
}

const redirectToLogin = () => {
  router.push('/')
}

// Lifecycle
onMounted(async () => {
  console.log('Guest Dashboard mounted')
  
  try {
    // Check if guest is authenticated
    const hasSession = await guestAuthStore.checkExistingSession()
    
    if (!hasSession) {
      console.log('No valid session found, redirecting to login...')
      return
    }

    // Fetch hotel details if we have a guest session
    if (currentGuest.value?.hotel_id) {
      console.log('Fetching hotel details for:', currentGuest.value.hotel_id)
      await guestAuthStore.fetchHotelDetails(currentGuest.value.hotel_id)
    }

    // Load cart from storage
    cartStore.loadFromStorage()

    // Initialize customer data
    await customerDataStore.initializeData()

    // Simulate push notifications after 5 seconds
    setTimeout(() => {
      notificationStore.addNotification({
        type: 'order',
        title: 'Siparişiniz Yolda!',
        message: 'Oda servisi siparişiniz hazırlanmış olup size doğru yola çıkmıştır. Tahmini teslim süresi 10-15 dakikadır.',
        linkTo: '/guest/cart'
      })
    }, 5000) // 5 seconds after mount

    // Add activity reminder after 10 seconds
    setTimeout(() => {
      notificationStore.addNotification({
        type: 'activity',
        title: 'Yoga Seansı Başlıyor',
        message: 'Sabah Yoga seansı 30 dakika içinde Wellness Merkezinde başlayacak. Katılım için rezervasyon gerekli değildir.',
        linkTo: '/guest/activities'
      })
    }, 10000) // 10 seconds after mount

    // Add service notification after 15 seconds
    setTimeout(() => {
      notificationStore.addNotification({
        type: 'service',
        title: 'Spa Randevunuz Onaylandı',
        message: 'Yarın saat 14:00 için İsveç Masajı randevunuz onaylanmıştır. Lütfen 15 dakika önce Spa merkezinde olunuz.',
        linkTo: '/guest/services'
      })
    }, 15000) // 15 seconds after mount

    console.log('Dashboard initialization complete with notification simulation')
  } catch (error) {
    console.error('Dashboard initialization error:', error)
  }
})
</script>

<style scoped>
/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar for horizontal scroll */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 