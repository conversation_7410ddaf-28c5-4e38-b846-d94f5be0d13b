<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Bakım - <PERSON>el Yönetimi</h1>
      <p class="text-gray-600 dark:text-gray-400">Bakım departmanı personelini yönetin ve görevleri takip edin.</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div
        v-for="stat in staffStatistics"
        :key="stat.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ stat.value }}</p>
          </div>
          <div :class="[
            'w-12 h-12 rounded-xl flex items-center justify-center',
            stat.iconBg
          ]">
            <component :is="stat.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-navy-700 rounded-xl p-4 shadow-sm mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <!-- Search -->
        <div class="flex-1 min-w-64">
          <input
            v-model="searchTerm"
            type="text"
            placeholder="Personel adı ara..."
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          />
        </div>
        
        <!-- Specialization Filter -->
        <div>
          <select
            v-model="selectedSpecialization"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500"
          >
            <option value="">Tüm Uzmanlıklar</option>
            <option v-for="specialization in uniqueSpecializations" :key="specialization" :value="specialization">
              {{ specialization }}
            </option>
          </select>
        </div>

        <!-- Status Filter -->
        <div class="flex gap-2">
          <button
            v-for="status in statusOptions"
            :key="status.value"
            @click="selectedStatus = selectedStatus === status.value ? null : status.value"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-all',
              selectedStatus === status.value 
                ? `${status.activeClass} text-white` 
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            ]"
          >
            {{ status.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Staff Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="staff in filteredStaff"
        :key="staff.id"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
      >
        <!-- Staff Header -->
        <div class="flex items-center mb-4">
          <div class="relative">
            <!-- Avatar with online status -->
            <div v-if="staff.avatar" class="w-16 h-16 rounded-full overflow-hidden">
              <img :src="staff.avatar" :alt="staff.name" class="w-full h-full object-cover" />
            </div>
            <div v-else class="w-16 h-16 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              {{ getInitials(staff.name) }}
            </div>
            <!-- Online status indicator -->
            <div v-if="staff.status === 'AVAILABLE'" class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            <div v-else-if="staff.status === 'BUSY'" class="absolute -bottom-1 -right-1 w-4 h-4 bg-orange-500 rounded-full border-2 border-white"></div>
            <div v-else class="absolute -bottom-1 -right-1 w-4 h-4 bg-gray-400 rounded-full border-2 border-white"></div>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white">{{ staff.name }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ staff.specialization }}</p>
            <div :class="[
              'inline-flex px-2 py-1 text-xs font-medium rounded-full mt-1',
              getStatusClass(staff.status)
            ]">
              {{ getStatusText(staff.status) }}
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="mb-4 space-y-2">
          <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <PhoneIcon class="w-4 h-4 mr-2" />
            {{ staff.phone }}
          </div>
          <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <EnvelopeIcon class="w-4 h-4 mr-2" />
            {{ staff.email }}
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center p-3 bg-gray-50 dark:bg-navy-800 rounded-lg">
            <p class="text-2xl font-bold text-blue-500">{{ staff.experience }}</p>
            <p class="text-xs text-gray-600 dark:text-gray-400">Yıl Deneyim</p>
          </div>
          <div class="text-center p-3 bg-gray-50 dark:bg-navy-800 rounded-lg">
            <p class="text-2xl font-bold text-green-500">{{ staff.completedTasks }}</p>
            <p class="text-xs text-gray-600 dark:text-gray-400">Tamamlanan</p>
          </div>
        </div>

        <!-- Rating -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-1">
            <span class="text-sm text-gray-600 dark:text-gray-400">Performans</span>
            <span class="text-sm font-medium text-navy-700 dark:text-white">{{ staff.rating }}/5</span>
          </div>
          <div class="flex items-center space-x-1">
            <StarIcon
              v-for="i in 5"
              :key="i"
              :class="[
                'w-4 h-4',
                i <= Math.floor(staff.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300 dark:text-gray-600'
              ]"
            />
          </div>
        </div>

        <!-- Active Tasks -->
        <div class="mb-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">Aktif Görevler</span>
            <span class="text-lg font-bold text-orange-500">{{ staff.activeTasks }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex gap-2">
          <button
            @click="assignTask(staff)"
            :disabled="staff.status === 'OFF_DUTY'"
            :class="[
              'flex-1 px-4 py-2 rounded-lg font-medium transition-colors',
              staff.status === 'OFF_DUTY' 
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                : 'bg-brand-500 hover:bg-brand-600 text-white'
            ]"
          >
            Görev Ver
          </button>
          <button
            @click="viewStaffDetails(staff)"
            class="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
          >
            Detaylar
          </button>
        </div>

        <!-- Last Activity -->
        <div v-if="staff.lastActivity" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <p class="text-xs text-gray-500 dark:text-gray-400">
            Son Aktivite: {{ formatLastActivity(staff.lastActivity) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredStaff.length === 0" class="text-center py-12">
      <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
        <UsersIcon class="w-12 h-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Personel bulunamadı</h3>
      <p class="text-gray-500 dark:text-gray-400">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import { 
  UsersIcon,
  CheckIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PhoneIcon,
  EnvelopeIcon,
  StarIcon
} from '@heroicons/vue/24/outline'

const managementStore = useManagementStore()

// Reactive data
const searchTerm = ref('')
const selectedSpecialization = ref('')
const selectedStatus = ref<string | null>(null)

// Status options for filtering
const statusOptions = [
  { value: 'AVAILABLE', label: 'Müsait', activeClass: 'bg-green-500' },
  { value: 'BUSY', label: 'Meşgul', activeClass: 'bg-orange-500' },
  { value: 'OFF_DUTY', label: 'Mesai Dışı', activeClass: 'bg-gray-500' }
]

// Computed properties
const filteredStaff = computed(() => {
  let staff = managementStore.maintenanceStaff

  if (searchTerm.value) {
    staff = staff.filter(person => 
      person.name.toLowerCase().includes(searchTerm.value.toLowerCase())
    )
  }

  if (selectedSpecialization.value) {
    staff = staff.filter(person => person.specialization === selectedSpecialization.value)
  }

  if (selectedStatus.value) {
    staff = staff.filter(person => person.status === selectedStatus.value)
  }

  return staff
})

const uniqueSpecializations = computed(() => {
  return [...new Set(managementStore.maintenanceStaff.map(staff => staff.specialization))].sort()
})

const staffStatistics = computed(() => {
  const staff = managementStore.maintenanceStaff
  const totalStaff = staff.length
  const availableStaff = staff.filter(s => s.status === 'AVAILABLE').length
  const busyStaff = staff.filter(s => s.status === 'BUSY').length
  const offDutyStaff = staff.filter(s => s.status === 'OFF_DUTY').length

  return [
    {
      title: 'Toplam Personel',
      value: totalStaff,
      icon: UsersIcon,
      iconBg: 'bg-blue-500'
    },
    {
      title: 'Müsait',
      value: availableStaff,
      icon: CheckIcon,
      iconBg: 'bg-green-500'
    },
    {
      title: 'Meşgul',
      value: busyStaff,
      icon: ExclamationTriangleIcon,
      iconBg: 'bg-orange-500'
    },
    {
      title: 'Mesai Dışı',
      value: offDutyStaff,
      icon: ClockIcon,
      iconBg: 'bg-gray-500'
    }
  ]
})

// Helper methods
const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'AVAILABLE':
      return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
    case 'BUSY':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100'
    case 'OFF_DUTY':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'AVAILABLE':
      return 'Müsait'
    case 'BUSY':
      return 'Meşgul'
    case 'OFF_DUTY':
      return 'Mesai Dışı'
    default:
      return 'Bilinmiyor'
  }
}

const formatLastActivity = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes} dakika önce`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)} saat önce`
  } else {
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}

// Action methods
const assignTask = (staff: any) => {
  console.log('Assigning task to staff:', staff)
  // TODO: Implement task assignment
}

const viewStaffDetails = (staff: any) => {
  console.log('Viewing staff details:', staff)
  // TODO: Implement staff details view
}
</script> 