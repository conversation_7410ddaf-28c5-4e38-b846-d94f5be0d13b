# Data Mapping: Dummy Data → Supabase Tables

This document maps the current dummy data structures used across all MFEs to their corresponding Supabase database tables.

## Overview

The hotel management system uses a multi-tenant architecture where data is scoped by `hotel_id` and protected by Row Level Security (RLS) policies. Each MFE accesses different subsets of data based on user roles and context.

## App Shell Data Mapping

### User Profile & Authentication
- **Source**: `authStore.user`
- **Target**: `user_profiles` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface User {
    id: string
    name: string
    email: string
    role: string
    avatar?: string
  }
  
  // Supabase Mapping
  user_profiles {
    id: uuid (PK)
    first_name: varchar
    last_name: varchar  
    email: varchar
    role: user_role
    avatar_url: text
    hotel_id: uuid (FK)
  }
  ```

### Staff Notifications
- **Source**: Dummy notification arrays
- **Target**: `staff_notifications` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface Notification {
    id: string
    title: string
    message: string
    type: string
    isRead: boolean
    timestamp: string
  }
  
  // Supabase Mapping
  staff_notifications {
    id: uuid (PK)
    title: varchar
    message: text
    notification_type: varchar
    is_read: boolean
    created_at: timestamptz
    recipient_id: uuid (FK)
    hotel_id: uuid (FK)
  }
  ```

## Hotel Dashboard Data Mapping

### Rooms Management
- **Source**: `housekeepingStore.rooms`
- **Target**: `rooms` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface Room {
    id: string
    number: string
    type: string
    floor: number
    status: RoomStatus
    lastCleaned: string
    currentGuest?: string
    checkOutTime?: string
  }
  
  // Supabase Mapping
  rooms {
    id: uuid (PK)
    room_number: varchar
    room_type: text
    floor: integer
    status: room_status
    last_cleaned: timestamptz
    current_guest_id: uuid
    check_out_time: timestamptz
    hotel_id: uuid (FK)
  }
  ```

### Tasks & Housekeeping
- **Source**: `housekeepingStore.tasks`
- **Target**: `tasks` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface HousekeepingTask {
    id: string
    title: string
    description: string
    roomNumber: string
    status: TaskStatus
    priority: TaskPriority
    assignedTo?: string
    estimatedTime: number
    deadline: string
  }
  
  // Supabase Mapping
  tasks {
    id: uuid (PK)
    title: varchar
    description: text
    room_id: uuid (FK)
    status: task_status
    priority: task_priority
    assigned_to: uuid (FK)
    estimated_duration: integer
    deadline: timestamptz
    hotel_id: uuid (FK)
  }
  ```

### Staff Management
- **Source**: `housekeepingStore.staff`
- **Target**: `user_profiles` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface StaffMember {
    id: string
    name: string
    surname: string
    role: string
    status: StaffStatus
    performanceScore: number
    tasksCompleted: number
    phone: string
    email: string
  }
  
  // Supabase Mapping
  user_profiles {
    id: uuid (PK)
    first_name: varchar
    last_name: varchar
    role: user_role
    status: staff_status
    performance_score: integer
    total_tasks_completed: integer
    phone: varchar
    email: varchar
    hotel_id: uuid (FK)
  }
  ```

### Menu Items
- **Source**: `managementStore.menuItems`
- **Target**: `menu_items` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface MenuItem {
    id: string
    name: string
    description: string
    price: number
    category: string
    isAvailable: boolean
    image?: string
  }
  
  // Supabase Mapping
  menu_items {
    id: uuid (PK)
    name: varchar
    description: text
    price: numeric
    course_type: varchar
    is_available: boolean
    image_url: text
    hotel_id: uuid (FK)
  }
  ```

## Management Portal Data Mapping

### Hotels Management
- **Source**: `managementStore.hotels`
- **Target**: `hotels` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface Hotel {
    id: string
    name: string
    status: 'Aktif' | 'Pasif'
    plan: string
    registrationDate: string
    contactPerson: string
    email: string
    phone: string
    address: string
  }
  
  // Supabase Mapping
  hotels {
    id: uuid (PK)
    name: varchar
    status: hotel_status
    subscription_plan: varchar
    created_at: timestamptz
    contact_person: varchar
    contact_email: varchar
    contact_phone: varchar
    address: text
  }
  ```

### Platform Statistics
- **Source**: `managementStore.platformStats`
- **Target**: Reporting functions (`get_management_platform_overview`)
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface PlatformStats {
    totalHotels: number
    activeSubscriptions: number
    pendingRequests: number
    systemHealth: string
  }
  
  // Supabase Mapping (via RPC function)
  get_management_platform_overview() {
    total_hotels: bigint
    active_hotels: bigint
    total_users: bigint
    monthly_room_service_revenue: numeric
    // ... other metrics
  }
  ```

## Customer Portal Data Mapping

### Menu & Food Ordering
- **Source**: `menuStore.categories` and `menuStore.items`
- **Target**: `get_full_menu()` function result
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface MenuCategory {
    id: string
    name: string
    items: MenuItem[]
  }
  
  // Supabase Mapping (via RPC function)
  get_full_menu(hotel_id) {
    categories: [
      {
        id: string
        name: string
        items: MenuItem[]
      }
    ]
  }
  ```

### Service Requests
- **Source**: `serviceRequestStore.requests`
- **Target**: `service_requests` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface ServiceRequest {
    id: string
    type: ServiceRequestType
    description: string
    status: ServiceRequestStatus
    createdAt: string
  }
  
  // Supabase Mapping
  service_requests {
    id: uuid (PK)
    request_type: text
    details: text
    status: text
    created_at: timestamptz
    hotel_id: uuid (FK)
  }
  ```

### Guest Services
- **Source**: `servicesData.ts`
- **Target**: `guest_services` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface Service {
    id: number
    name: string
    description: string
    price: number
    duration_minutes: number
    image: string
  }
  
  // Supabase Mapping
  guest_services {
    id: uuid (PK)
    name: varchar
    description: text
    price: numeric
    duration_minutes: integer
    image_url: text
    hotel_id: uuid (FK)
  }
  ```

### Activities
- **Source**: `activitiesData.ts`
- **Target**: `hotel_activities` table
- **Key Fields**:
  ```typescript
  // Dummy Structure
  interface TimelineEvent {
    id: string
    name: string
    location: string
    startTime: string
    endTime: string
    description: string
  }
  
  // Supabase Mapping
  hotel_activities {
    id: uuid (PK)
    title: varchar
    location: varchar
    start_time: time
    end_time: time
    description: text
    hotel_id: uuid (FK)
  }
  ```

## Data Transformation Guidelines

### 1. ID Mapping
- Convert string IDs to UUIDs where needed
- Maintain referential integrity with foreign keys

### 2. Date/Time Handling
- Convert string dates to proper timestamp types
- Handle timezone considerations

### 3. Enum Mapping
- Map string constants to database enums
- Provide fallback values for unknown states

### 4. JSON Fields
- Transform arrays to JSONB where appropriate
- Maintain backward compatibility with existing structures

### 5. Multi-tenancy
- Always include `hotel_id` for data scoping
- Respect RLS policies in all queries

## Next Steps

1. **Configure Supabase clients** in each MFE
2. **Create data transformation utilities** for each mapping
3. **Implement loading states** and error handling
4. **Add caching strategies** for frequently accessed data
5. **Set up real-time subscriptions** for critical updates
