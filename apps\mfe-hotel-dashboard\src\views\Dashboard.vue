<template>
  <ErrorBoundary
    fallback-message="Hotel dashboard yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin."
    @retry="handleRetry"
  >
    <!-- Loading State -->
    <LoadingState
      v-if="isLoading"
      variant="dashboard"
      :show="true"
      message="Dashboard verileri yükleniyor..."
    />

    <!-- Error State -->
    <div v-else-if="hasError" class="text-center py-12">
      <ExclamationTriangleIcon class="w-16 h-16 text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Veri Yükleme Hatası
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        Dashboard verileri yüklenirken bir hata oluştu.
      </p>
      <button
        @click="handleRetry"
        class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
      >
        Tekrar Dene
      </button>
    </div>

    <!-- Main Content -->
    <div v-else class="pt-20 pb-10">
    <!-- Mini Statistics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-6 gap-5 mb-5">
      <!-- Earnings -->
      <MiniStatistics
        :start-content="() => h('div', { class: 'w-14 h-14 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center' }, h(ChartBarIcon, { class: 'w-8 h-8 text-brand-500' }))"
        name="Kazanç"
        :value="dashboardStore.statistics.earnings"
      />
      
      <!-- Spend This Month -->
      <MiniStatistics
        :start-content="() => h('div', { class: 'w-14 h-14 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center' }, h(CreditCardIcon, { class: 'w-8 h-8 text-brand-500' }))"
        name="Bu Ay Harcama"
        :value="dashboardStore.statistics.spendThisMonth"
      />
      
      <!-- Sales -->
      <MiniStatistics
        name="Satışlar"
        :value="dashboardStore.statistics.sales"
        :growth="dashboardStore.statistics.salesGrowth"
      />
      
      <!-- Balance -->
      <MiniStatistics
        :end-content="() => h('div', { class: 'flex items-center space-x-2' }, [
          h('img', { src: 'https://flagcdn.com/w20/tr.png', alt: 'TR', class: 'w-5 h-5 rounded-full' }),
          h('select', { class: 'text-sm bg-transparent font-medium text-navy-700 dark:text-white' }, [
            h('option', { value: 'try' }, 'TRY'),
            h('option', { value: 'usd' }, 'USD'),
            h('option', { value: 'eur' }, 'EUR')
          ])
        ])"
        name="Bakiye"
        :value="dashboardStore.statistics.balance"
      />
      
      <!-- New Tasks -->
      <MiniStatistics
        :start-content="() => h('div', { class: 'w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center' }, h(ClipboardDocumentListIcon, { class: 'w-7 h-7 text-white' }))"
        name="Yeni Görevler"
        :value="dashboardStore.statistics.newTasks"
      />
      
      <!-- Total Projects -->
      <MiniStatistics
        :start-content="() => h('div', { class: 'w-14 h-14 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center' }, h(DocumentDuplicateIcon, { class: 'w-8 h-8 text-brand-500' }))"
        name="Toplam Proje"
        :value="dashboardStore.statistics.totalProjects"
      />
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 md:grid-cols-1 xl:grid-cols-2 gap-5 mb-5">
      <TotalSpent />
      <WeeklyRevenue />
    </div>

    <!-- Tables and Charts Row -->
    <div class="grid grid-cols-1 md:grid-cols-1 xl:grid-cols-2 gap-5 mb-5">
      <CheckTable />
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-5">
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <!-- Daily Traffic Placeholder -->
          <h4 class="text-lg font-bold text-navy-700 dark:text-white mb-6">Günlük Trafik</h4>
          <div class="h-48 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
            <div class="text-white text-center">
              <UsersIcon class="w-12 h-12 mx-auto mb-2 opacity-80" />
              <p class="text-sm opacity-80">Daily Traffic</p>
              <p class="text-xs opacity-60">Chart.js entegrasyonu</p>
            </div>
          </div>
        </div>
        <PieCard />
      </div>
    </div>

    <!-- Bottom Row -->
    <div class="grid grid-cols-1 md:grid-cols-1 xl:grid-cols-2 gap-5">
      <ComplexTable />
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-5">
        <Tasks />
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <!-- Mini Calendar Placeholder -->
          <h4 class="text-lg font-bold text-navy-700 dark:text-white mb-6">Takvim</h4>
          <div class="h-64 bg-gradient-to-br from-indigo-400 to-blue-500 rounded-lg flex items-center justify-center">
            <div class="text-white text-center">
              <CalendarDaysIcon class="w-12 h-12 mx-auto mb-2 opacity-80" />
              <p class="text-sm opacity-80">Mini Calendar</p>
              <p class="text-xs opacity-60">Vue Calendar entegrasyonu</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { h, computed, onMounted } from 'vue'
import {
  ChartBarIcon,
  CreditCardIcon,
  ClipboardDocumentListIcon,
  DocumentDuplicateIcon,
  UsersIcon,
  CalendarDaysIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

// Components
import MiniStatistics from '@/components/widgets/MiniStatistics.vue'
import TotalSpent from '@/components/widgets/TotalSpent.vue'
import WeeklyRevenue from '@/components/widgets/WeeklyRevenue.vue'
import CheckTable from '@/components/widgets/CheckTable.vue'
import ComplexTable from '@/components/widgets/ComplexTable.vue'
import PieCard from '@/components/widgets/PieCard.vue'
import Tasks from '@/components/widgets/Tasks.vue'
import { ErrorBoundary, LoadingState, useHotelDashboardRefresh } from '@hotelexia/shared-components'

// Store
import { useDashboardStore } from '@/stores/dashboardStore'

const dashboardStore = useDashboardStore()

// Computed states for loading and error handling
const isLoading = computed(() => {
  return dashboardStore.isLoading
})

const hasError = computed(() => {
  return !!dashboardStore.error
})

// Retry function
const handleRetry = async () => {
  try {
    await dashboardStore.fetchDashboardStats()
  } catch (error) {
    console.error('Failed to retry dashboard data loading:', error)
  }
}

// Set up cross-MFE refresh listening
const refreshListener = useHotelDashboardRefresh(
  ['tasks', 'rooms', 'services', 'stats'],
  (event) => {
    console.log('Dashboard refresh triggered:', event.component)
    // Refresh dashboard data when cross-MFE events occur
    dashboardStore.fetchDashboardStats()
  }
)

// Initialize data on mount
onMounted(() => {
  if (!dashboardStore.statistics.earnings || dashboardStore.statistics.earnings === '₺0') {
    dashboardStore.fetchDashboardStats()
  }
})
</script>

<style scoped>
/* Dashboard specific styles */
</style> 