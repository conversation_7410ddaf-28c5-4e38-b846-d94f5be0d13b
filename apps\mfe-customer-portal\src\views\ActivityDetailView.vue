<template>
  <div class="min-h-screen bg-backgroundLight pb-24">
    <!-- Loading State -->
    <div v-if="!activity" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-textMedium">Aktivite yükleniyor...</p>
      </div>
    </div>

    <!-- Activity Detail Content -->
    <div v-else>
      <!-- Header with Back Button -->
      <div class="bg-backgroundWhite border-b border-borderColor">
        <div class="px-4 py-6">
          <div class="flex items-center justify-between">
            <button @click="goBack" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <svg class="w-6 h-6 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
              </svg>
            </button>
            <h1 class="text-xl font-bold text-textDark">Aktivite Detayı</h1>
            <div class="w-10"></div> <!-- Spacer for centering -->
          </div>
        </div>
      </div>

      <!-- Activity Image -->
      <div v-if="activity.image" class="relative h-64 bg-gradient-to-br from-primary/20 to-teal-600/20">
        <img 
          :src="activity.image" 
          :alt="activity.name"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-black/20"></div>
        
        <!-- Time Badge Overlay -->
        <div class="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm font-medium text-textDark">
              {{ activity.startTime }} - {{ activity.endTime }}
            </span>
          </div>
        </div>
      </div>

      <!-- Activity Information -->
      <div class="px-4 py-6">
        <!-- Activity Title and Type -->
        <div class="bg-white rounded-2xl p-6 mb-6">
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h2 class="text-2xl font-bold text-textDark mb-2">{{ activity.name }}</h2>
              <div class="flex items-center space-x-2 mb-3">
                <svg class="w-5 h-5 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                <span class="text-textMedium">{{ activity.location }}</span>
              </div>
            </div>
            <!-- Activity Type Badge -->
            <span 
              class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
              :class="{
                'bg-teal-100 text-teal-800': activity.type === 'activity',
                'bg-amber-100 text-amber-800': activity.type === 'service'
              }"
            >
              {{ activity.type === 'activity' ? 'Genel Aktivite' : 'Rezervasyonunuz' }}
            </span>
          </div>

          <!-- Time Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-gray-50 rounded-xl p-4">
              <div class="flex items-center space-x-3">
                <div class="bg-primary/10 p-2 rounded-lg">
                  <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-textMedium">Başlangıç Zamanı</p>
                  <p class="font-semibold text-textDark">{{ activity.startTime }}</p>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 rounded-xl p-4">
              <div class="flex items-center space-x-3">
                <div class="bg-red-500/10 p-2 rounded-lg">
                  <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-textMedium">Bitiş Zamanı</p>
                  <p class="font-semibold text-textDark">{{ activity.endTime }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Activity Status -->
          <div class="mb-6">
            <div 
              class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium"
              :class="{
                'bg-gray-100 text-gray-600': activityStatus === 'past',
                'bg-red-100 text-red-700': activityStatus === 'ongoing',
                'bg-green-100 text-green-700': activityStatus === 'future'
              }"
            >
              <span 
                v-if="activityStatus === 'ongoing'" 
                class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"
              ></span>
              <svg v-else class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path v-if="activityStatus === 'past'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z" clip-rule="evenodd"/>
                <path v-else fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
              </svg>
              {{
                activityStatus === 'past' ? 'Tamamlandı' :
                activityStatus === 'ongoing' ? 'Şu An Aktif' :
                'Yaklaşan Aktivite'
              }}
            </div>
          </div>

          <!-- Description -->
          <div>
            <h3 class="text-lg font-semibold text-textDark mb-3">Açıklama</h3>
            <p class="text-textMedium leading-relaxed">{{ activity.description }}</p>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="bg-gradient-to-r from-primary/5 to-teal-600/5 rounded-2xl p-6 mb-6">
          <div class="flex items-start space-x-4">
            <div class="bg-primary/10 p-3 rounded-full">
              <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-textDark mb-2">Faydalı Bilgiler</h3>
              <ul class="space-y-2 text-sm text-textMedium">
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span>Tüm otel misafirleri katılabilir</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span>Ek ücret bulunmamaktadır</span>
                </li>
                <li class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span>{{ activity.location }} lokasyonunda</span>
                </li>
                <li v-if="activity.type === 'service'" class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  <span>Bu sizin kişisel rezervasyonunuzdur</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
          <button 
            @click="goBack"
            class="w-full bg-primary hover:bg-teal-600 text-white px-6 py-4 rounded-xl text-base font-medium transition-colors"
          >
            Programa Geri Dön
          </button>
          
          <button 
            @click="goToActivities"
            class="w-full bg-gray-100 hover:bg-gray-200 text-textDark px-6 py-4 rounded-xl text-base font-medium transition-colors"
          >
            Tüm Aktiviteleri Görüntüle
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { dailyActivities, userBookedServices, type TimelineEvent } from '@/data/activitiesData'

const router = useRouter()
const route = useRoute()
const activity = ref<TimelineEvent | null>(null)

// Calculate activity status
const activityStatus = computed(() => {
  if (!activity.value) return 'future'
  
  const now = new Date()
  const currentTimeStr = now.toLocaleTimeString('tr-TR', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })

  if (currentTimeStr > activity.value.endTime) {
    return 'past'
  } else if (currentTimeStr >= activity.value.startTime && currentTimeStr <= activity.value.endTime) {
    return 'ongoing'
  }
  return 'future'
})

const findActivity = (id: string) => {
  const allActivities = [...dailyActivities, ...userBookedServices]
  return allActivities.find(act => act.id === id) || null
}

const goBack = () => {
  router.back()
}

const goToActivities = () => {
  router.push('/activities')
}

onMounted(() => {
  const activityId = route.params.id as string
  activity.value = findActivity(activityId)
  
  if (!activity.value) {
    // If activity not found, redirect to activities page
    setTimeout(() => {
      router.push('/activities')
    }, 2000)
  }
})
</script>

<style scoped>
/* Component-specific styles */
</style> 