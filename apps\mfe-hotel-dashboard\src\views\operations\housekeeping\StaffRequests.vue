<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h1 class="text-2xl font-bold text-navy-700 dark:text-white">Personel Talepleri</h1>
          <p class="text-gray-600 dark:text-gray-400">Personel taleplerini yönetin ve onaylayın</p>
        </div>
        <button
          @click="showRequestModal = true"
          class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          + <PERSON><PERSON>
        </button>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">12</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Toplam İstek</div>
          </div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">8</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Bekleyen</div>
          </div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">3</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Onaylandı</div>
          </div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="text-center">
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">1</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Reddedilen</div>
          </div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">2</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">İnceleme</div>
          </div>
        </div>
      </div>

      <!-- Filter Section -->
      <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 mb-6">
        <div class="flex flex-wrap gap-4 items-center">
          <div class="flex-1 min-w-64">
            <input
              v-model="searchTerm"
              type="text"
              placeholder="İstek ara..."
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
            />
          </div>
          <div class="flex gap-2">
            <select
              v-model="filterStatus"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
            >
              <option value="">Tüm Durumlar</option>
              <option value="PENDING">Bekleyen</option>
              <option value="APPROVED">Onaylandı</option>
              <option value="REJECTED">Reddedilen</option>
              <option value="REVIEWING">İnceleme</option>
            </select>
            <select
              v-model="filterType"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
            >
              <option value="">Tüm Tipler</option>
              <option value="OVERTIME">Mesai</option>
              <option value="LEAVE">İzin</option>
              <option value="SICK">Hastalık</option>
              <option value="MATERIAL">Malzeme</option>
            </select>
            <select
              v-model="filterDate"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
            >
              <option value="">Tüm Tarihler</option>
              <option value="today">Bugün</option>
              <option value="week">Bu Hafta</option>
              <option value="month">Bu Ay</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Requests Table -->
    <div class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Personel İstekleri</h3>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Personel
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İstek Tipi
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Açıklama
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tarih
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr
              v-for="request in filteredRequests"
              :key="request.id"
              class="hover:bg-gray-50 dark:hover:bg-navy-600"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-8 w-8">
                    <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                      <UserIcon class="h-4 w-4 text-gray-600" />
                    </div>
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ request.requesterName }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ request.type }}
                </span>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-white">{{ request.title }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">{{ request.description }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(request.requestDate) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(request.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(request.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  v-if="request.status === 'PENDING'"
                  @click="approveRequest(request.id)"
                  class="text-green-600 hover:text-green-900 transition-colors"
                >
                  Onayla
                </button>
                <button
                  v-if="request.status === 'PENDING'"
                  @click="rejectRequest(request.id)"
                  class="text-red-600 hover:text-red-900 transition-colors"
                >
                  Reddet
                </button>
                <button
                  v-if="request.status === 'APPROVED'"
                  @click="startRequest(request.id)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                >
                  Başlat
                </button>
                <button
                  v-if="request.status === 'IN_PROGRESS'"
                  @click="completeRequest(request.id)"
                  class="text-teal-600 hover:text-teal-900 transition-colors"
                >
                  Tamamla
                </button>
                <button
                  @click="viewRequestDetails(request)"
                  class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
                >
                  Detay
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="filteredRequests.length === 0" class="text-center py-12">
        <DocumentIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Talep bulunamadı</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Filtreleri değiştirmeyi deneyin veya yeni bir talep oluşturun.</p>
      </div>
    </div>

    <!-- Request Creation Modal -->
    <div v-if="showRequestModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Yeni Talep Oluştur</h3>
        
        <form @submit.prevent="createRequest" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Talep Eden</label>
            <select
              v-model="newRequest.requesterId"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            >
              <option value="">Personel seçin...</option>
              <option v-for="member in staff" :key="member.id" :value="member.id">
                {{ member.name }} {{ member.surname }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tür</label>
            <select
              v-model="newRequest.type"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            >
              <option value="">Tür seçin...</option>
              <option value="İzin Talebi">İzin Talebi</option>
              <option value="Vardiya Değişimi">Vardiya Değişimi</option>
              <option value="Ekipman Talebi">Ekipman Talebi</option>
              <option value="Eğitim Talebi">Eğitim Talebi</option>
              <option value="Diğer">Diğer</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Başlık</label>
            <input
              v-model="newRequest.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              placeholder="Talep başlığı..."
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Açıklama</label>
            <textarea
              v-model="newRequest.description"
              rows="3"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              placeholder="Talep açıklaması..."
            ></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Öncelik</label>
            <select
              v-model="newRequest.priority"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            >
              <option value="LOW">Düşük</option>
              <option value="MEDIUM">Orta</option>
              <option value="HIGH">Yüksek</option>
              <option value="URGENT">Acil</option>
            </select>
          </div>

          <div class="flex gap-3 pt-4">
            <button
              type="button"
              @click="showRequestModal = false"
              class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              class="flex-1 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Oluştur
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Request Details Modal -->
    <div v-if="selectedRequest" class="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-lg mx-4">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Talep Detayları</h3>
          <button
            @click="selectedRequest = null"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Talep Eden</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRequest.requesterName }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tür</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRequest.type }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Başlık</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRequest.title }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Açıklama</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRequest.description }}</p>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Öncelik</label>
              <span :class="getPriorityBadgeClass(selectedRequest.priority)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getPriorityText(selectedRequest.priority) }}
              </span>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Durum</label>
              <span :class="getStatusBadgeClass(selectedRequest.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getStatusText(selectedRequest.status) }}
              </span>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Talep Tarihi</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRequest.requestDate) }}</p>
          </div>

          <div v-if="selectedRequest.responseDate">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Yanıt Tarihi</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRequest.responseDate) }}</p>
          </div>

          <div v-if="selectedRequest.approvedBy">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Onaylayan</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRequest.approvedBy }}</p>
          </div>

          <div v-if="selectedRequest.reason">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sebep/Not</label>
            <p class="text-sm text-gray-900 dark:text-white">{{ selectedRequest.reason }}</p>
          </div>
        </div>

        <div class="flex gap-3 pt-6">
          <button
            @click="selectedRequest = null"
            class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Kapat
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { RequestStatus, TaskPriority, StaffRequest } from '@/types/housekeeping'
import {
  ClockIcon,
  CheckCircleIcon,
  PlayIcon,
  CheckIcon,
  XCircleIcon,
  UserIcon,
  DocumentIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const housekeepingStore = useHousekeepingStore()

// Reactive data
const selectedStatus = ref('')
const selectedType = ref('')
const selectedPriority = ref('')
const searchQuery = ref('')
const showRequestModal = ref(false)
const selectedRequest = ref<StaffRequest | null>(null)

// Missing reactive variables for template
const searchTerm = ref('')
const filterStatus = ref('')
const filterType = ref('')
const filterDate = ref('')

// New request form data
const newRequest = ref({
  requesterId: '',
  type: '',
  title: '',
  description: '',
  priority: 'MEDIUM' as TaskPriority
})

// Computed properties
const { staffRequests, staff } = housekeepingStore

const requestsByStatus = computed(() => {
  const pending = staffRequests.filter(r => r.status === 'PENDING')
  const approved = staffRequests.filter(r => r.status === 'APPROVED')
  const inProgress = staffRequests.filter(r => r.status === 'IN_PROGRESS')
  const completed = staffRequests.filter(r => r.status === 'COMPLETED')
  const rejected = staffRequests.filter(r => r.status === 'REJECTED')

  return { pending, approved, inProgress, completed, rejected }
})

const filteredRequests = computed(() => {
  return staffRequests.filter(request => {
    const matchesStatus = !selectedStatus.value || request.status === selectedStatus.value
    const matchesType = !selectedType.value || request.type === selectedType.value
    const matchesPriority = !selectedPriority.value || request.priority === selectedPriority.value
    const matchesSearch = !searchQuery.value || 
      request.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      request.description.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      request.requesterName.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    return matchesStatus && matchesType && matchesPriority && matchesSearch
  })
})

// Methods
const clearFilters = () => {
  selectedStatus.value = ''
  selectedType.value = ''
  selectedPriority.value = ''
  searchQuery.value = ''
}

const approveRequest = (requestId: string) => {
  housekeepingStore.approveStaffRequest(requestId, 'Yönetici')
}

const rejectRequest = (requestId: string) => {
  const reason = prompt('Ret sebebini giriniz:')
  if (reason) {
    housekeepingStore.rejectStaffRequest(requestId, reason)
  }
}

const startRequest = (requestId: string) => {
  const request = staffRequests.find(r => r.id === requestId)
  if (request) {
    request.status = 'IN_PROGRESS'
  }
}

const completeRequest = (requestId: string) => {
  const request = staffRequests.find(r => r.id === requestId)
  if (request) {
    request.status = 'COMPLETED'
  }
}

const viewRequestDetails = (request: any) => {
  selectedRequest.value = request
}

const createRequest = () => {
  const selectedStaff = staff.find(s => s.id === newRequest.value.requesterId)
  if (selectedStaff) {
    const request = {
      id: Date.now().toString(),
      requesterId: newRequest.value.requesterId,
      requesterName: `${selectedStaff.name} ${selectedStaff.surname}`,
      type: newRequest.value.type,
      title: newRequest.value.title,
      description: newRequest.value.description,
      status: 'PENDING' as RequestStatus,
      priority: newRequest.value.priority,
      requestDate: new Date().toISOString()
    }
    
    staffRequests.push(request)
    
    // Reset form
    newRequest.value = {
      requesterId: '',
      type: '',
      title: '',
      description: '',
      priority: 'MEDIUM' as TaskPriority
    }
    
    showRequestModal.value = false
  }
}

// Helper functions
const getPriorityBadgeClass = (priority: TaskPriority) => {
  switch (priority) {
    case 'URGENT':
      return 'bg-red-100 text-red-800'
    case 'HIGH':
      return 'bg-orange-100 text-orange-800'
    case 'MEDIUM':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-blue-100 text-blue-800'
  }
}

const getPriorityText = (priority: TaskPriority) => {
  switch (priority) {
    case 'URGENT':
      return 'Acil'
    case 'HIGH':
      return 'Yüksek'
    case 'MEDIUM':
      return 'Orta'
    default:
      return 'Düşük'
  }
}

const getStatusBadgeClass = (status: RequestStatus) => {
  switch (status) {
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800'
    case 'APPROVED':
      return 'bg-green-100 text-green-800'
    case 'IN_PROGRESS':
      return 'bg-blue-100 text-blue-800'
    case 'COMPLETED':
      return 'bg-teal-100 text-teal-800'
    case 'REJECTED':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: RequestStatus) => {
  switch (status) {
    case 'PENDING':
      return 'Bekleyen'
    case 'APPROVED':
      return 'Onaylandı'
    case 'IN_PROGRESS':
      return 'Devam Ediyor'
    case 'COMPLETED':
      return 'Tamamlandı'
    case 'REJECTED':
      return 'Reddedildi'
    default:
      return 'Bilinmiyor'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script> 