import { test, expect } from '@playwright/test'

test.describe('App Shell - Landing Page', () => {
  test('should load the landing page successfully', async ({ page }) => {
    await page.goto('/')
    
    // Check if the page loads
    await expect(page).toHaveTitle(/Hotelexia/)
    
    // Check for main navigation elements
    await expect(page.locator('nav')).toBeVisible()
    
    // Check for hero section
    await expect(page.locator('h1')).toBeVisible()
  })

  test('should navigate to different sections', async ({ page }) => {
    await page.goto('/')
    
    // Test navigation links
    const aboutLink = page.locator('a[href*="about"]').first()
    if (await aboutLink.isVisible()) {
      await aboutLink.click()
      await expect(page.url()).toContain('about')
    }
    
    // Navigate back to home
    await page.goto('/')
    
    const featuresLink = page.locator('a[href*="features"]').first()
    if (await featuresLink.isVisible()) {
      await featuresLink.click()
      await expect(page.url()).toContain('features')
    }
  })

  test('should have responsive design', async ({ page }) => {
    await page.goto('/')
    
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('nav')).toBeVisible()
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('body')).toBeVisible()
  })

  test('should handle login navigation', async ({ page }) => {
    await page.goto('/')
    
    // Look for login button/link
    const loginButton = page.locator('a[href*="login"], button:has-text("Giriş"), button:has-text("Login")').first()
    
    if (await loginButton.isVisible()) {
      await loginButton.click()
      
      // Should navigate to login page or show login modal
      await expect(page.locator('input[type="email"], input[type="text"]')).toBeVisible({ timeout: 5000 })
    }
  })

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = []
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    await page.goto('/')
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle')
    
    // Check for critical errors (ignore minor warnings)
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED')
    )
    
    expect(criticalErrors).toHaveLength(0)
  })
})
