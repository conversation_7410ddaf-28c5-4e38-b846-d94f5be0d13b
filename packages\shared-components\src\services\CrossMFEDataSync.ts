import {
  crossMFERealtimeService,
  type CrossMFEEventType,
  type HotelDataChangeEvent,
  type UserProfileChangeEvent,
  type TaskStatusChangeEvent,
  type ServiceRequestChangeEvent,
  type NotificationChangeEvent,
  type RoomStatusChangeEvent
} from '@hotelexia/shared-supabase-client'

/**
 * Service for handling specific cross-MFE data synchronization scenarios
 * This service provides high-level synchronization logic for common use cases
 */
export class CrossMFEDataSync {
  private static instance: CrossMFEDataSync | null = null
  private isInitialized = false
  private currentUserId: string | null = null
  private currentHotelId: string | null = null

  // Event handlers for different MFE contexts
  private eventHandlers = new Map<string, (event: any) => void>()

  static getInstance(): CrossMFEDataSync {
    if (!CrossMFEDataSync.instance) {
      CrossMFEDataSync.instance = new CrossMFEDataSync()
    }
    return CrossMFEDataSync.instance
  }

  /**
   * Initialize the sync service
   */
  initialize(userId: string, hotelId?: string) {
    this.currentUserId = userId
    this.currentHotelId = hotelId || null
    this.isInitialized = true

    // Initialize the underlying realtime service
    crossMFERealtimeService.initialize(userId, hotelId)

    console.log('CrossMFEDataSync initialized', { userId, hotelId })
  }

  /**
   * Start synchronization for a specific MFE context
   */
  startSync(mfeContext: 'hotel-dashboard' | 'management-portal' | 'customer-portal' | 'app-shell') {
    if (!this.isInitialized) {
      console.warn('CrossMFEDataSync not initialized')
      return
    }

    // Start the underlying realtime service
    crossMFERealtimeService.startRealtimeSubscriptions()

    // Set up context-specific event handlers
    this.setupEventHandlers(mfeContext)

    console.log(`CrossMFEDataSync started for ${mfeContext}`)
  }

  /**
   * Stop synchronization
   */
  stopSync() {
    // Remove all event handlers
    this.eventHandlers.forEach((handler, eventType) => {
      crossMFERealtimeService.unsubscribe(eventType, handler)
    })
    this.eventHandlers.clear()

    // Stop the underlying realtime service
    crossMFERealtimeService.stopRealtimeSubscriptions()

    console.log('CrossMFEDataSync stopped')
  }

  /**
   * Set up event handlers for specific MFE context
   */
  private setupEventHandlers(mfeContext: string) {
    switch (mfeContext) {
      case 'hotel-dashboard':
        this.setupHotelDashboardHandlers()
        break
      case 'management-portal':
        this.setupManagementPortalHandlers()
        break
      case 'customer-portal':
        this.setupCustomerPortalHandlers()
        break
      case 'app-shell':
        this.setupAppShellHandlers()
        break
    }
  }

  /**
   * Hotel Dashboard specific event handlers
   */
  private setupHotelDashboardHandlers() {
    // Task status changes - update dashboard statistics
    const taskHandler = (event: any) => {
      if (event.type === 'task_status_change') {
        const taskEvent = event
        console.log('Hotel Dashboard: Task status changed', taskEvent.payload)
        
        // Trigger dashboard refresh
        this.triggerDashboardRefresh('tasks')
      }
    }

    // Room status changes - update room management views
    const roomHandler = (event: CrossMFEEventType) => {
      if (event.type === 'room_status_change') {
        const roomEvent = event as RoomStatusChangeEvent
        console.log('Hotel Dashboard: Room status changed', roomEvent.payload)
        
        // Trigger room status refresh
        this.triggerDashboardRefresh('rooms')
      }
    }

    // Service request changes - update service management
    const serviceHandler = (event: CrossMFEEventType) => {
      if (event.type === 'service_request_change') {
        const serviceEvent = event as ServiceRequestChangeEvent
        console.log('Hotel Dashboard: Service request changed', serviceEvent.payload)
        
        // Trigger service requests refresh
        this.triggerDashboardRefresh('services')
      }
    }

    this.subscribeToEvent('task_status_change', taskHandler)
    this.subscribeToEvent('room_status_change', roomHandler)
    this.subscribeToEvent('service_request_change', serviceHandler)
  }

  /**
   * Management Portal specific event handlers
   */
  private setupManagementPortalHandlers() {
    // Hotel data changes - update hotel list and details
    const hotelHandler = (event: CrossMFEEventType) => {
      if (event.type === 'hotel_data_change') {
        const hotelEvent = event as HotelDataChangeEvent
        console.log('Management Portal: Hotel data changed', hotelEvent.payload)
        
        // Trigger hotel data refresh
        this.triggerManagementRefresh('hotels')
      }
    }

    // User profile changes - update user management
    const userHandler = (event: CrossMFEEventType) => {
      if (event.type === 'user_profile_change') {
        const userEvent = event as UserProfileChangeEvent
        console.log('Management Portal: User profile changed', userEvent.payload)
        
        // Trigger user data refresh
        this.triggerManagementRefresh('users')
      }
    }

    // Task status changes - update platform statistics
    const taskHandler = (event: CrossMFEEventType) => {
      if (event.type === 'task_status_change') {
        const taskEvent = event as TaskStatusChangeEvent
        console.log('Management Portal: Task status changed', taskEvent.payload)
        
        // Trigger platform stats refresh
        this.triggerManagementRefresh('stats')
      }
    }

    this.subscribeToEvent('hotel_data_change', hotelHandler)
    this.subscribeToEvent('user_profile_change', userHandler)
    this.subscribeToEvent('task_status_change', taskHandler)
  }

  /**
   * Customer Portal specific event handlers
   */
  private setupCustomerPortalHandlers() {
    // Service request changes - update order status
    const serviceHandler = (event: CrossMFEEventType) => {
      if (event.type === 'service_request_change') {
        const serviceEvent = event as ServiceRequestChangeEvent
        console.log('Customer Portal: Service request changed', serviceEvent.payload)
        
        // Trigger orders refresh
        this.triggerCustomerRefresh('orders')
      }
    }

    // Notification changes - update notification list
    const notificationHandler = (event: CrossMFEEventType) => {
      if (event.type === 'notification_change') {
        const notificationEvent = event as NotificationChangeEvent
        console.log('Customer Portal: Notification changed', notificationEvent.payload)
        
        // Trigger notifications refresh
        this.triggerCustomerRefresh('notifications')
      }
    }

    // Room status changes - update reservation status
    const roomHandler = (event: CrossMFEEventType) => {
      if (event.type === 'room_status_change') {
        const roomEvent = event as RoomStatusChangeEvent
        console.log('Customer Portal: Room status changed', roomEvent.payload)
        
        // Trigger reservations refresh
        this.triggerCustomerRefresh('reservations')
      }
    }

    this.subscribeToEvent('service_request_change', serviceHandler)
    this.subscribeToEvent('notification_change', notificationHandler)
    this.subscribeToEvent('room_status_change', roomHandler)
  }

  /**
   * App Shell specific event handlers
   */
  private setupAppShellHandlers() {
    // User profile changes - update auth state if current user
    const userHandler = (event: CrossMFEEventType) => {
      if (event.type === 'user_profile_change') {
        const userEvent = event as UserProfileChangeEvent
        
        if (userEvent.payload.userId === this.currentUserId) {
          console.log('App Shell: Current user profile changed', userEvent.payload)
          
          // Trigger auth state refresh
          this.triggerAppShellRefresh('auth')
        }
      }
    }

    // Notification changes - update global notification state
    const notificationHandler = (event: CrossMFEEventType) => {
      if (event.type === 'notification_change') {
        const notificationEvent = event as NotificationChangeEvent
        console.log('App Shell: Notification changed', notificationEvent.payload)
        
        // Trigger global notifications refresh
        this.triggerAppShellRefresh('notifications')
      }
    }

    this.subscribeToEvent('user_profile_change', userHandler)
    this.subscribeToEvent('notification_change', notificationHandler)
  }

  /**
   * Subscribe to an event with handler
   */
  private subscribeToEvent(eventType: string, handler: (event: CrossMFEEventType) => void) {
    crossMFERealtimeService.subscribe(eventType, handler)
    this.eventHandlers.set(eventType, handler)
  }

  /**
   * Trigger refresh for hotel dashboard components
   */
  private triggerDashboardRefresh(component: string) {
    // Emit custom events that dashboard components can listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('hotel-dashboard-refresh', {
        detail: { component, timestamp: Date.now() }
      }))
    }
  }

  /**
   * Trigger refresh for management portal components
   */
  private triggerManagementRefresh(component: string) {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('management-portal-refresh', {
        detail: { component, timestamp: Date.now() }
      }))
    }
  }

  /**
   * Trigger refresh for customer portal components
   */
  private triggerCustomerRefresh(component: string) {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('customer-portal-refresh', {
        detail: { component, timestamp: Date.now() }
      }))
    }
  }

  /**
   * Trigger refresh for app shell components
   */
  private triggerAppShellRefresh(component: string) {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('app-shell-refresh', {
        detail: { component, timestamp: Date.now() }
      }))
    }
  }

  /**
   * Get sync status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      currentUserId: this.currentUserId,
      currentHotelId: this.currentHotelId,
      activeHandlers: Array.from(this.eventHandlers.keys()),
      realtimeStatus: crossMFERealtimeService.getSubscriptionStatus()
    }
  }
}

// Export singleton instance
export const crossMFEDataSync = CrossMFEDataSync.getInstance()
export default crossMFEDataSync
