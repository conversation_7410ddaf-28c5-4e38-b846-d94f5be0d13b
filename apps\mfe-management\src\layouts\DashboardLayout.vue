<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900 flex"
       :class="{ 'sidebar-collapsed': store.isSidebarCollapsed }">
    <!-- Sidebar -->
    <Sidebar data-testid="sidebar" />
    
    <!-- Main Content -->
    <div class="flex-1 main-content transition-all duration-300 min-h-screen">
      <!-- Navbar -->
      <Navbar data-testid="navbar" />
      
      <!-- Page Content -->
      <main class="px-4 md:px-10 mx-auto w-full">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue'
import Sidebar from '@/components/sidebar/Sidebar.vue'
import Navbar from '@/components/navbar/Navbar.vue'
import { useManagementStore } from '@/stores/managementStore'
import { useAuthStore } from '@/stores/authStore'
import { useManagementPortalSync } from '@hotelexia/shared-components'

const store = useManagementStore()
const authStore = useAuthStore()

// Initialize Cross-MFE sync for management portal
const crossMFESync = useManagementPortalSync(authStore.user?.id || '')

// Initialize dark mode and Cross-MFE sync
onMounted(() => {
  if (store.darkMode) {
    document.documentElement.classList.add('dark')
  }

  // Initialize Cross-MFE sync if user is authenticated
  if (authStore.isAuthenticated && authStore.user?.id) {
    console.log('Initializing Cross-MFE sync for management portal')
    crossMFESync.initialize(authStore.user.id)
    crossMFESync.start()
  }
})

// Cleanup Cross-MFE sync when component unmounts
onUnmounted(() => {
  console.log('Cleaning up Cross-MFE sync for management portal')
  crossMFESync.stop()
})

// Watch for dark mode changes
watch(() => store.darkMode, (newValue) => {
  if (newValue) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
})
</script>

<style scoped>
.main-content {
  margin-left: 290px;
}

.sidebar-collapsed .main-content {
  margin-left: 80px;
}
</style>
