<template>
  <div class="order-card">
    <!-- Order Header with Hotel Info -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <!-- Hotel Logo/Icon -->
        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
        </div>
        <div>
          <h3 class="mobile-card-title">Hotel Room Service</h3>
          <p class="mobile-card-subtitle">Order #{{ order.id.slice(-8) }}</p>
        </div>
      </div>
      <!-- Status Badge -->
      <span 
        class="status-badge"
        :class="statusBadgeClass"
      >
        {{ statusText }}
      </span>
    </div>

    <!-- Order Items List -->
    <div v-if="order.order_items && order.order_items.length > 0" class="mb-4">
      <div class="space-y-2">
        <div 
          v-for="item in order.order_items" 
          :key="item.id"
          class="flex justify-between items-center"
        >
          <div class="flex-1">
            <p class="text-sm font-medium text-textDark">
              {{ item.menu_item?.name || 'Unknown Item' }}
            </p>
            <p v-if="item.menu_item?.description" class="text-xs text-textMedium">
              {{ item.menu_item.description }}
            </p>
          </div>
          <div class="text-right ml-3">
            <p class="text-sm font-medium text-textDark">{{ item.quantity }}x</p>
            <p class="text-xs text-textMedium">₺{{ formatPrice(item.price_at_time_of_order) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Footer -->
    <div class="flex items-center justify-between pt-4 border-t border-borderColor">
      <div class="text-left">
        <p class="text-xs text-textMedium">{{ formatDate(order.created_at || '') }}</p>
        <p v-if="order.special_instructions" class="text-xs text-textMedium mt-1">
          Note: {{ order.special_instructions }}
        </p>
      </div>
      <div class="text-right">
        <p class="text-lg font-bold text-textDark">₺{{ formatPrice(order.total_amount) }}</p>
      </div>
    </div>

    <!-- Cancel Button (only for cancelable orders) -->
    <div v-if="showCancel && canCancel" class="mt-4 pt-4 border-t border-borderColor">
      <button
        @click="handleCancelOrder"
        :disabled="isCancelling"
        class="w-full py-3 px-4 text-sm font-medium rounded-lg border transition-all duration-200"
        :class="isCancelling 
          ? 'bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed' 
          : 'bg-backgroundWhite text-primary border-primary hover:bg-primary/5'"
      >
        <div v-if="isCancelling" class="flex items-center justify-center">
          <div class="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent mr-2"></div>
          Cancelling...
        </div>
        <div v-else>
          Cancel Order
        </div>
      </button>
    </div>

    <!-- Status Message -->
    <div v-if="statusMessage" class="mt-4 p-3 rounded-lg bg-gray-50">
      <p class="text-sm text-textMedium">{{ statusMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoomServiceStore } from '@/stores/roomServiceStore'

// Types
import type { Order } from '@hotelexia/shared-supabase-client'

// Props
const props = defineProps<{
  order: Order
  isCancelling: boolean
  showCancel?: boolean
}>()

// Emits
defineEmits<{
  cancel: [orderId: string]
}>()

// Store
const roomServiceStore = useRoomServiceStore()
const { cancelOrder } = roomServiceStore

// Computed
const statusBadgeClass = computed(() => {
  switch (props.order.status) {
    case 'pending':
      return 'status-badge-pending'
    case 'confirmed':
    case 'in_progress':
    case 'preparing':
      return 'status-badge-preparing'
    case 'on_the_way':
      return 'status-badge-preparing'
    case 'delivered':
      return 'status-badge-delivered'
    case 'canceled':
      return 'status-badge-canceled'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

const statusText = computed(() => {
  switch (props.order.status) {
    case 'pending':
      return 'Pending'
    case 'confirmed':
      return 'Confirmed'
    case 'in_progress':
    case 'preparing':
      return 'Preparing'
    case 'on_the_way':
      return 'On the way'
    case 'delivered':
      return 'Delivered'
    case 'canceled':
      return 'Canceled'
    default:
      return 'Unknown'
  }
})

const canCancel = computed(() => {
  return ['pending', 'confirmed', 'in_progress', 'preparing'].includes(props.order.status || '')
})

const statusMessage = computed(() => {
  switch (props.order.status) {
    case 'pending':
      return 'Your order has been received and is awaiting confirmation.'
    case 'confirmed':
      return 'Your order has been confirmed and will be prepared soon.'
    case 'in_progress':
    case 'preparing':
      return 'Your order is being prepared and will be delivered to your room shortly.'
    case 'on_the_way':
      return 'Your order is on the way to your room.'
    case 'delivered':
      return 'Your order has been successfully delivered. Enjoy!'
    case 'canceled':
      return 'This order has been canceled.'
    default:
      return null
  }
})

// Methods
const handleCancelOrder = async () => {
  // Show confirmation dialog
  const confirmed = window.confirm(
    `Cancel order #${props.order.id.slice(-8)}?\n\nThis action cannot be undone.`
  )
  
  if (!confirmed) {
    return
  }

  try {
    const success = await cancelOrder(props.order.id)
    if (success) {
      // Success is automatically handled by the store's reactive state
      console.log('Order canceled successfully')
    }
  } catch (err) {
    console.error('Cancel order error:', err)
    // Error is already handled in the store and shown in the parent component
  }
}

const formatPrice = (price: number) => {
  return price.toFixed(2)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
/* Component-specific styles */
.order-card {
  /* Any additional styles specific to this component */
}
</style> 