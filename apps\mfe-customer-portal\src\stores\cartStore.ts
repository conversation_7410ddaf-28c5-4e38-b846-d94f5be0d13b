import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { MenuItem } from '@hotelexia/shared-supabase-client'

export interface CartItem {
  id: string
  name: string
  price: number
  calories?: number
  weight?: number
  quantity: number
  image?: string
  isNew?: boolean
  customizations?: string[]
  // MenuItem fields for compatibility
  category?: string
  description?: string
  hotel_id?: string
  image_url?: string
  is_available?: boolean
}

export const useCartStore = defineStore('cart', () => {
  // State
  const cartItems = ref<CartItem[]>([])
  const showPromoCode = ref(false)
  const promoCode = ref('')
  const appliedPromoDiscount = ref(0)

  // Computed
  const cartItemCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })

  const subtotal = computed(() => {
    return cartItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
  })

  const deliveryFee = computed(() => {
    return cartItems.value.length > 0 ? 2.00 : 0
  })

  const promoDiscount = computed(() => {
    return appliedPromoDiscount.value
  })

  const cartTotal = computed(() => {
    return Math.max(0, subtotal.value + deliveryFee.value - promoDiscount.value)
  })

  const isEmpty = computed(() => {
    return cartItems.value.length === 0
  })

  // Legacy compatibility for roomServiceStore migration
  const cart = computed(() => cartItems.value)

  // Actions
  const addToCart = (item: MenuItem | Omit<CartItem, 'quantity'>, quantity: number = 1, customizations: string[] = []) => {
    // Handle both MenuItem (from database) and CartItem (demo data) types
    const cartItem: CartItem = {
      id: item.id,
      name: item.name,
      price: item.price,
      quantity: 0,
      calories: 'calories' in item ? (item.calories || undefined) : undefined,
      weight: 'weight' in item ? item.weight : undefined,
      image: 'image' in item ? item.image : item.image_url || '',
      isNew: 'isNew' in item ? item.isNew : false,
      customizations,
      // MenuItem fields
      category: 'category' in item ? item.category : undefined,
      description: 'description' in item ? (item.description || undefined) : undefined,
      hotel_id: 'hotel_id' in item ? item.hotel_id : undefined,
      image_url: 'image_url' in item ? (item.image_url || undefined) : undefined,
      is_available: 'is_available' in item ? item.is_available : true
    }

    const existingItem = cartItems.value.find(cartItemValue => 
      cartItemValue.id === item.id && 
      JSON.stringify(cartItemValue.customizations || []) === JSON.stringify(customizations)
    )

    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      cartItem.quantity = quantity
      cartItems.value.push(cartItem)
    }

    saveToStorage()
  }

  const removeFromCart = (itemId: string, customizations: string[] = []) => {
    const index = cartItems.value.findIndex(item => 
      item.id === itemId && 
      JSON.stringify(item.customizations || []) === JSON.stringify(customizations)
    )
    
    if (index > -1) {
      cartItems.value.splice(index, 1)
      saveToStorage()
    }
  }

  const updateQuantity = (itemId: string, newQuantity: number, customizations: string[] = []) => {
    const item = cartItems.value.find(cartItem => 
      cartItem.id === itemId && 
      JSON.stringify(cartItem.customizations || []) === JSON.stringify(customizations)
    )

    if (item) {
      if (newQuantity <= 0) {
        removeFromCart(itemId, customizations)
      } else {
        item.quantity = newQuantity
        saveToStorage()
      }
    }
  }

  const incrementQuantity = (itemId: string, customizations: string[] = []) => {
    const item = cartItems.value.find(cartItem => 
      cartItem.id === itemId && 
      JSON.stringify(cartItem.customizations || []) === JSON.stringify(customizations)
    )

    if (item) {
      item.quantity++
      saveToStorage()
    }
  }

  const decrementQuantity = (itemId: string, customizations: string[] = []) => {
    const item = cartItems.value.find(cartItem => 
      cartItem.id === itemId && 
      JSON.stringify(cartItem.customizations || []) === JSON.stringify(customizations)
    )

    if (item) {
      if (item.quantity > 1) {
        item.quantity--
        saveToStorage()
      } else {
        removeFromCart(itemId, customizations)
      }
    }
  }

  const clearCart = () => {
    cartItems.value = []
    appliedPromoDiscount.value = 0
    promoCode.value = ''
    saveToStorage()
  }

  // Legacy compatibility methods for roomServiceStore migration
  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    updateQuantity(itemId, newQuantity)
  }

  const getCartItemQuantity = (itemId: string): number => {
    const item = cartItems.value.find(cartItem => cartItem.id === itemId)
    return item ? item.quantity : 0
  }

  const applyPromoCode = (code: string) => {
    // Simple promo code logic
    const discountCodes = {
      'HOTELEXIA10': 0.10,
      'WELCOME20': 0.20,
      'FIRST15': 0.15
    }

    const discount = discountCodes[code as keyof typeof discountCodes]
    if (discount) {
      appliedPromoDiscount.value = subtotal.value * discount
      promoCode.value = code
      showPromoCode.value = false
      saveToStorage()
      return true
    }
    return false
  }

  const saveToStorage = () => {
    const cartData = {
      items: cartItems.value,
      promoCode: promoCode.value,
      appliedPromoDiscount: appliedPromoDiscount.value
    }
    localStorage.setItem('hotelexia_cart', JSON.stringify(cartData))
  }

  // Legacy compatibility method for roomServiceStore migration
  const saveCartToStorage = () => {
    saveToStorage()
  }

  const loadFromStorage = () => {
    const savedCart = localStorage.getItem('hotelexia_cart')
    if (savedCart) {
      try {
        const cartData = JSON.parse(savedCart)
        cartItems.value = cartData.items || []
        promoCode.value = cartData.promoCode || ''
        appliedPromoDiscount.value = cartData.appliedPromoDiscount || 0
      } catch (error) {
        console.error('Error loading cart from storage:', error)
        // Initialize with empty cart if parsing fails
        cartItems.value = []
      }
    }
  }

  // Legacy compatibility method for roomServiceStore migration
  const loadCartFromStorage = () => {
    loadFromStorage()
  }

  const initializeDemoData = () => {
    cartItems.value = [
      {
        id: 'beef-stroganoff',
        name: 'Beef Stroganoff',
        price: 14.99,
        calories: 110,
        weight: 200,
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop&crop=center',
        isNew: false
      },
      {
        id: 'vegetable-salad',
        name: 'Vegetable salad',
        price: 11.49,
        calories: 110,
        weight: 200,
        quantity: 1,
        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=400&fit=crop&crop=center',
        isNew: true
      }
    ]
    saveToStorage()
  }

  return {
    // State
    cartItems,
    showPromoCode,
    promoCode,
    appliedPromoDiscount,
    
    // Computed
    cartItemCount,
    subtotal,
    deliveryFee,
    promoDiscount,
    cartTotal,
    isEmpty,
    cart, // Legacy compatibility
    
    // Actions
    addToCart,
    removeFromCart,
    updateQuantity,
    incrementQuantity,
    decrementQuantity,
    clearCart,
    applyPromoCode,
    saveToStorage,
    loadFromStorage,
    initializeDemoData,
    
    // Legacy compatibility methods
    updateCartItemQuantity,
    getCartItemQuantity,
    saveCartToStorage,
    loadCartFromStorage
  }
}) 