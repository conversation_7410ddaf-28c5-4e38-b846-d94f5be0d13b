import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { User } from '@supabase/supabase-js'
import { supabase, type Database } from '@hotelexia/shared-supabase-client'

type UserRole = Database['public']['Enums']['user_role']

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: UserRole | null
  hotel_id: string | null
  created_at: string | null
  updated_at: string | null
}

interface HotelContext {
  id: string
  name: string
  address: string | null
  phone: string | null
  email: string | null
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const profile = ref<UserProfile | null>(null)
  const hotelContext = ref<HotelContext | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Session management
  const INACTIVITY_TIMEOUT = 30 * 60 * 1000 // 30 minutes
  let inactivityTimer: NodeJS.Timeout | null = null
  let lastActivity = Date.now()

  // Computed
  const isAuthenticated = computed(() => !!user.value && !!profile.value)
  const userRole = computed(() => profile.value?.role || null)
  const userHotelId = computed(() => profile.value?.hotel_id || null)
  const userDisplayName = computed(() => profile.value?.full_name || profile.value?.email || 'User')
  const isHotelAdmin = computed(() => userRole.value === ('HOTEL_ADMIN' as UserRole))
  const isStaff = computed(() => userRole.value === ('STAFF' as UserRole))
  const hasHotelAccess = computed(() => {
    const role = userRole.value
    return role === ('HOTEL_ADMIN' as UserRole) || role === ('STAFF' as UserRole)
  })

  // Actions
  const login = async (email: string, password: string): Promise<boolean> => {
    isLoading.value = true
    error.value = null

    try {
      // Sign in with Supabase
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        throw new Error(authError.message)
      }

      if (!authData.user) {
        throw new Error('Kullanıcı bilgileri alınamadı.')
      }

      // Set user
      user.value = authData.user

      // Fetch user profile
      const userProfile = await fetchUserProfile(authData.user.id)
      if (!userProfile) {
        throw new Error('Kullanıcı profili alınamadı.')
      }

      profile.value = userProfile

      // Check if user has appropriate role for hotel dashboard
      if (!hasHotelAccess.value) {
        await signOut()
        throw new Error('Bu panele erişim yetkiniz bulunmuyor. Sadece otel personeli giriş yapabilir.')
      }

      // Fetch hotel context if user has hotel_id
      if (userProfile.hotel_id) {
        await fetchHotelContext(userProfile.hotel_id)
      }

      // Store session data
      persistSession()

      // Start inactivity timer
      startInactivityTimer()

      return true
    } catch (err: any) {
      console.error('Login error:', err)
      error.value = err.message || 'Giriş sırasında bir hata oluştu.'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const signOut = async (): Promise<void> => {
    try {
      await supabase.auth.signOut()
      clearSession()
    } catch (err) {
      console.error('Sign out error:', err)
    }
  }

  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return null
      }

      return data as UserProfile
    } catch (err) {
      console.error('Unexpected error fetching user profile:', err)
      return null
    }
  }

  const fetchHotelContext = async (hotelId: string): Promise<void> => {
    try {
      const { data, error } = await supabase
        .from('hotels')
        .select('id, name, address, phone, email')
        .eq('id', hotelId)
        .single()

      if (error) {
        console.error('Error fetching hotel context:', error)
        return
      }

      hotelContext.value = data as HotelContext
    } catch (err) {
      console.error('Unexpected error fetching hotel context:', err)
    }
  }

  const persistSession = () => {
    if (user.value && profile.value) {
      const sessionData = {
        user: user.value,
        profile: profile.value,
        hotelContext: hotelContext.value,
        timestamp: Date.now()
      }
      sessionStorage.setItem('hotelexia_hotel_user', JSON.stringify(sessionData))
    }
  }

  const clearSession = () => {
    user.value = null
    profile.value = null
    hotelContext.value = null
    error.value = null
    sessionStorage.removeItem('hotelexia_hotel_user')
    sessionStorage.removeItem('hotelexia_hotel_redirect')
    clearInactivityTimer()
  }

  const restoreSession = async (): Promise<boolean> => {
    try {
      const sessionData = sessionStorage.getItem('hotelexia_hotel_user')
      if (!sessionData) return false

      const parsed = JSON.parse(sessionData)

      // Check if session is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      if (Date.now() - parsed.timestamp > maxAge) {
        clearSession()
        return false
      }

      user.value = parsed.user
      profile.value = parsed.profile
      hotelContext.value = parsed.hotelContext

      // Start inactivity timer after restoring session
      startInactivityTimer()

      return true
    } catch (err) {
      console.error('Error restoring session:', err)
      clearSession()
      return false
    }
  }

  // Inactivity timer functions
  const startInactivityTimer = () => {
    clearInactivityTimer()
    lastActivity = Date.now()

    inactivityTimer = setTimeout(() => {
      console.log('User inactive for too long, signing out...')
      signOut()
    }, INACTIVITY_TIMEOUT)
  }

  const clearInactivityTimer = () => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
      inactivityTimer = null
    }
  }

  const resetInactivityTimer = () => {
    if (isAuthenticated.value) {
      startInactivityTimer()
    }
  }

  const trackUserActivity = () => {
    lastActivity = Date.now()
    resetInactivityTimer()
  }

  // Initialize auth listener
  const setupAuthListener = () => {
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        clearSession()
      } else if (event === 'SIGNED_IN' && session.user) {
        user.value = session.user

        // Fetch profile if not already loaded
        if (!profile.value) {
          const userProfile = await fetchUserProfile(session.user.id)
          if (userProfile) {
            profile.value = userProfile

            // Fetch hotel context
            if (userProfile.hotel_id) {
              await fetchHotelContext(userProfile.hotel_id)
            }

            persistSession()
            startInactivityTimer()
          }
        }
      } else if (event === 'TOKEN_REFRESHED') {
        // Reset inactivity timer on token refresh
        resetInactivityTimer()
      }
    })
  }

  // Setup activity listeners
  const setupActivityListeners = () => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    const handleActivity = () => {
      trackUserActivity()
    }

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true)
    })

    // Cleanup function
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true)
      })
    }
  }

  // Initialize
  setupAuthListener()
  setupActivityListeners()

  return {
    // State
    user: readonly(user),
    profile: readonly(profile),
    hotelContext: readonly(hotelContext),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    isAuthenticated,
    userRole,
    userHotelId,
    userDisplayName,
    isHotelAdmin,
    isStaff,
    hasHotelAccess,

    // Actions
    login,
    signOut,
    fetchUserProfile,
    fetchHotelContext,
    persistSession,
    clearSession,
    restoreSession,
    trackUserActivity,
    resetInactivityTimer
  }
})
