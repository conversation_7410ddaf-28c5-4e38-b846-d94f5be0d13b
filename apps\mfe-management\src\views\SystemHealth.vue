<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-navy-700 dark:text-white"><PERSON>ste<PERSON> Sağlığı</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Platform servislerini izleyin ve sistem durumunu kontrol edin</p>
      </div>
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-green-600 dark:text-green-400">Tüm Servisler Stabil</span>
        <span class="text-xs text-gray-500 dark:text-gray-400">Son Kontrol: Az Önce</span>
      </div>
    </div>

    <!-- System Status Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">API Gateway</p>
            <p class="text-lg font-bold text-green-600">Çevrimiçi</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <CheckCircleIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Ping: {{ systemMetrics.apiGateway.ping }}ms
        </div>
        <div class="mt-2 w-full bg-gray-200 rounded-full h-1 dark:bg-gray-700">
          <div class="bg-green-500 h-1 rounded-full" style="width: 98%"></div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Database</p>
            <p class="text-lg font-bold text-green-600">Stabil</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <CircleStackIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Bağlantılar: {{ systemMetrics.database.connections }}/100
        </div>
        <div class="mt-2 w-full bg-gray-200 rounded-full h-1 dark:bg-gray-700">
          <div class="bg-green-500 h-1 rounded-full" style="width: 95%"></div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Realtime Services</p>
            <p class="text-lg font-bold text-green-600">Aktif</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <BoltIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          WebSocket: {{ systemMetrics.realtime.connections }} aktif
        </div>
        <div class="mt-2 w-full bg-gray-200 rounded-full h-1 dark:bg-gray-700">
          <div class="bg-green-500 h-1 rounded-full" style="width: 92%"></div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">CDN</p>
            <p class="text-lg font-bold text-green-600">Optimal</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <GlobeAltIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Hit rate: {{ systemMetrics.cdn.hitRate }}%
        </div>
        <div class="mt-2 w-full bg-gray-200 rounded-full h-1 dark:bg-gray-700">
          <div class="bg-green-500 h-1 rounded-full" style="width: 94%"></div>
        </div>
      </div>
    </div>

    <!-- Latency Monitoring Chart -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white">API Yanıt Süreleri</h2>
        <div class="text-sm text-gray-500 dark:text-gray-400">Son 6 saat</div>
      </div>
      
      <!-- Chart Container -->
      <div class="relative h-64 bg-gray-50 dark:bg-navy-700 rounded-lg p-4">
        <div class="absolute inset-4">
          <!-- Y-axis labels -->
          <div class="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>500ms</span>
            <span>400ms</span>
            <span>300ms</span>
            <span>200ms</span>
            <span>100ms</span>
            <span>0ms</span>
          </div>
          
          <!-- Chart area -->
          <div class="ml-12 h-full relative">
            <!-- Grid lines -->
            <div class="absolute inset-0">
              <div class="h-full flex flex-col justify-between">
                <div class="border-t border-gray-200 dark:border-gray-600"></div>
                <div class="border-t border-gray-200 dark:border-gray-600"></div>
                <div class="border-t border-gray-200 dark:border-gray-600"></div>
                <div class="border-t border-gray-200 dark:border-gray-600"></div>
                <div class="border-t border-gray-200 dark:border-gray-600"></div>
              </div>
            </div>
            
            <!-- Chart lines -->
            <svg class="absolute inset-0 w-full h-full">
              <!-- /auth endpoint -->
              <polyline
                :points="generateChartPoints(latencyData.auth)"
                fill="none"
                stroke="#10B981"
                stroke-width="2"
                class="opacity-80"
              />
              <!-- /orders endpoint -->
              <polyline
                :points="generateChartPoints(latencyData.orders)"
                fill="none"
                stroke="#3B82F6"
                stroke-width="2"
                class="opacity-80"
              />
              <!-- /hotels endpoint -->
              <polyline
                :points="generateChartPoints(latencyData.hotels)"
                fill="none"
                stroke="#8B5CF6"
                stroke-width="2"
                class="opacity-80"
              />
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Chart Legend -->
      <div class="flex items-center justify-center space-x-6 mt-4">
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">/auth ({{ getAverageLatency(latencyData.auth) }}ms)</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">/orders ({{ getAverageLatency(latencyData.orders) }}ms)</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">/hotels ({{ getAverageLatency(latencyData.hotels) }}ms)</span>
        </div>
      </div>
    </div>

    <!-- Uptime History -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white">Son 30 Gün Uptime Geçmişi</h2>
        <div class="text-sm text-gray-500 dark:text-gray-400">Günlük uptime oranları</div>
      </div>
      
      <!-- Uptime Grid -->
      <div class="grid grid-cols-15 gap-1 mb-4">
        <div 
          v-for="(day, index) in uptimeHistory" 
          :key="index"
          class="aspect-square rounded cursor-pointer transition-all hover:scale-110"
          :class="getUptimeDayClass(day.uptime)"
          :title="`${day.date}: ${day.uptime}% uptime`"
        ></div>
      </div>
      
      <!-- Uptime Legend -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-400 rounded"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">&lt;99%</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-400 rounded"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">99-99.9%</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-400 rounded"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">&gt;99.9%</span>
          </div>
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-400">
          Ortalama: {{ averageUptime }}%
        </div>
      </div>
    </div>

    <!-- Detailed Service Status -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Detaylı Servis Durumu</h2>
      <div class="space-y-4">
        <div 
          v-for="service in detailedServices" 
          :key="service.name"
          class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors"
        >
          <div class="flex items-center space-x-3">
            <div 
              class="w-3 h-3 rounded-full"
              :class="service.status === 'online' ? 'bg-green-500' : service.status === 'warning' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'"
            ></div>
            <div>
              <h3 class="font-medium text-navy-700 dark:text-white">{{ service.name }}</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ service.description }}</p>
            </div>
          </div>
          <div class="text-right">
            <p 
              class="text-sm font-medium"
              :class="service.status === 'online' ? 'text-green-600' : service.status === 'warning' ? 'text-yellow-600' : 'text-red-600'"
            >
              {{ service.statusText }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.details }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Metrics and Recent Events -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Server Performance -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Server Performansı</h2>
        <div class="space-y-4">
          <div>
            <div class="flex justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">CPU Kullanımı</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ performanceMetrics.cpu }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="performanceMetrics.cpu > 80 ? 'bg-red-500' : performanceMetrics.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                :style="`width: ${performanceMetrics.cpu}%`"
              ></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">RAM Kullanımı</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ performanceMetrics.ram }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="performanceMetrics.ram > 80 ? 'bg-red-500' : performanceMetrics.ram > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                :style="`width: ${performanceMetrics.ram}%`"
              ></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Disk Kullanımı</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ performanceMetrics.disk }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="performanceMetrics.disk > 80 ? 'bg-red-500' : performanceMetrics.disk > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                :style="`width: ${performanceMetrics.disk}%`"
              ></div>
            </div>
          </div>

          <div>
            <div class="flex justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Network I/O</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ performanceMetrics.network }} MB/s</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
              <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 67%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Events -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Son Sistem Olayları</h2>
        <div class="space-y-3 max-h-64 overflow-y-auto">
          <div 
            v-for="event in recentEvents" 
            :key="event.id"
            class="flex items-start space-x-3 p-3 rounded-lg"
            :class="event.type === 'success' ? 'bg-green-50 dark:bg-green-900/20' : 
                    event.type === 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/20' : 
                    event.type === 'error' ? 'bg-red-50 dark:bg-red-900/20' : 
                    'bg-blue-50 dark:bg-blue-900/20'"
          >
            <component 
              :is="getEventIcon(event.type)" 
              class="h-5 w-5 mt-0.5"
              :class="event.type === 'success' ? 'text-green-500' : 
                      event.type === 'warning' ? 'text-yellow-500' : 
                      event.type === 'error' ? 'text-red-500' : 
                      'text-blue-500'"
            />
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ event.message }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ event.time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  CheckCircleIcon,
  CircleStackIcon,
  BoltIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline'

// Types
interface SystemMetrics {
  apiGateway: { ping: number }
  database: { connections: number }
  realtime: { connections: number }
  cdn: { hitRate: number }
}

interface LatencyData {
  auth: number[]
  orders: number[]
  hotels: number[]
}

interface UptimeDay {
  date: string
  uptime: number
}

interface Service {
  name: string
  description: string
  status: 'online' | 'warning' | 'offline'
  statusText: string
  details: string
}

interface PerformanceMetrics {
  cpu: number
  ram: number
  disk: number
  network: number
}

interface SystemEvent {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  message: string
  time: string
}

// Reactive data
const systemMetrics = ref<SystemMetrics>({
  apiGateway: { ping: 45 },
  database: { connections: 24 },
  realtime: { connections: 1247 },
  cdn: { hitRate: 94.2 }
})

const latencyData = ref<LatencyData>({
  auth: [120, 115, 130, 125, 118, 122, 119, 124, 117, 121, 123, 118],
  orders: [180, 175, 185, 182, 178, 181, 179, 183, 177, 180, 182, 179],
  hotels: [95, 92, 98, 96, 93, 97, 94, 99, 91, 95, 97, 94]
})

const performanceMetrics = ref<PerformanceMetrics>({
  cpu: 23,
  ram: 67,
  disk: 45,
  network: 12.4
})

const detailedServices = ref<Service[]>([
  {
    name: 'Supabase Database',
    description: 'PostgreSQL 15.4',
    status: 'online',
    statusText: 'Çevrimiçi',
    details: 'Uptime: 99.98%'
  },
  {
    name: 'Vercel Edge Functions',
    description: 'Deno Runtime',
    status: 'online',
    statusText: 'Aktif',
    details: 'Ortalama yanıt: 125ms'
  },
  {
    name: 'Redis Cache',
    description: 'v7.0.11',
    status: 'warning',
    statusText: 'Uyarı',
    details: 'Bellek kullanımı yüksek'
  },
  {
    name: 'SMS Service',
    description: 'Twilio API',
    status: 'online',
    statusText: 'Çalışıyor',
    details: 'Bugün: 1,247 SMS'
  },
  {
    name: 'WhatsApp Business API',
    description: 'Meta Business Platform',
    status: 'online',
    statusText: 'Aktif',
    details: 'Bugün: 892 mesaj'
  },
  {
    name: 'Email Service',
    description: 'SendGrid API',
    status: 'online',
    statusText: 'Çalışıyor',
    details: 'Bugün: 3,456 email'
  }
])

const recentEvents = ref<SystemEvent[]>([
  {
    id: '1',
    type: 'success',
    message: 'Database backup tamamlandı',
    time: '2 dakika önce'
  },
  {
    id: '2',
    type: 'warning',
    message: 'Redis bellek kullanımı %75\'i aştı',
    time: '15 dakika önce'
  },
  {
    id: '3',
    type: 'info',
    message: 'Yeni deployment başarılı (v2.2.1)',
    time: '1 saat önce'
  },
  {
    id: '4',
    type: 'success',
    message: 'SSL sertifikası yenilendi',
    time: '3 saat önce'
  },
  {
    id: '5',
    type: 'info',
    message: 'Cache temizliği tamamlandı',
    time: '6 saat önce'
  },
  {
    id: '6',
    type: 'warning',
    message: 'API rate limit yaklaşıldı',
    time: '8 saat önce'
  }
])

// Generate uptime history for last 30 days
const generateUptimeHistory = (): UptimeDay[] => {
  const history: UptimeDay[] = []
  const today = new Date()
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    
    // Generate realistic uptime data (mostly 99.9%+)
    let uptime = 99.95
    if (Math.random() < 0.1) { // 10% chance of lower uptime
      uptime = 99.0 + Math.random() * 0.9 // 99.0-99.9%
    }
    if (Math.random() < 0.02) { // 2% chance of much lower uptime
      uptime = 98.5 + Math.random() * 0.5 // 98.5-99.0%
    }
    
    history.push({
      date: date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'short' }),
      uptime: Math.round(uptime * 100) / 100
    })
  }
  
  return history
}

const uptimeHistory = ref<UptimeDay[]>(generateUptimeHistory())

// Computed properties
const averageUptime = computed(() => {
  const total = uptimeHistory.value.reduce((sum, day) => sum + day.uptime, 0)
  return (total / uptimeHistory.value.length).toFixed(2)
})

// Methods
const getUptimeDayClass = (uptime: number) => {
  if (uptime >= 99.9) return 'bg-green-400'
  if (uptime >= 99.0) return 'bg-yellow-400'
  return 'bg-red-400'
}

const generateChartPoints = (data: number[]) => {
  const maxValue = 500
  const chartWidth = 100
  const chartHeight = 100
  
  return data.map((value, index) => {
    const x = (index / (data.length - 1)) * chartWidth
    const y = chartHeight - (value / maxValue) * chartHeight
    return `${x},${y}`
  }).join(' ')
}

const getAverageLatency = (data: number[]) => {
  return Math.round(data.reduce((sum, val) => sum + val, 0) / data.length)
}

const getEventIcon = (type: string) => {
  switch (type) {
    case 'success': return CheckCircleIcon
    case 'warning': return ExclamationTriangleIcon
    case 'error': return XCircleIcon
    default: return InformationCircleIcon
  }
}

// Simulate real-time updates
onMounted(() => {
  setInterval(() => {
    // Update metrics slightly
    systemMetrics.value.apiGateway.ping = 40 + Math.floor(Math.random() * 20)
    systemMetrics.value.database.connections = 20 + Math.floor(Math.random() * 15)
    systemMetrics.value.realtime.connections = 1200 + Math.floor(Math.random() * 100)
    systemMetrics.value.cdn.hitRate = 92 + Math.random() * 6
    
    // Update performance metrics
    performanceMetrics.value.cpu = Math.max(15, Math.min(85, performanceMetrics.value.cpu + (Math.random() - 0.5) * 5))
    performanceMetrics.value.ram = Math.max(50, Math.min(90, performanceMetrics.value.ram + (Math.random() - 0.5) * 3))
    performanceMetrics.value.disk = Math.max(30, Math.min(70, performanceMetrics.value.disk + (Math.random() - 0.5) * 2))
    performanceMetrics.value.network = Math.max(5, Math.min(25, performanceMetrics.value.network + (Math.random() - 0.5) * 2))
  }, 5000) // Update every 5 seconds
})
</script>

<style scoped>
.grid-cols-15 {
  grid-template-columns: repeat(15, minmax(0, 1fr));
}
</style> 