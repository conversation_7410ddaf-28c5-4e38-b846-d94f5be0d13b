<template>
  <div class="p-8">
    <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Otellerimiz</h1>
    <p class="text-gray-600 dark:text-gray-400 mt-1">Konaklamak için bir otel seçin.</p>

    <LoadingState
      v-if="loading"
      variant="page"
      message="Oteller yükleniyor..."
      sub-message="Mevcut otelleri getiriyoruz..."
    />

    <ErrorDisplay
      v-if="error"
      :show="!!error"
      variant="banner"
      severity="error"
      title="Oteller Yüklenemedi"
      :message="error"
      :retryable="true"
      @retry="fetchHotels"
    />

    <div v-if="!loading && !error" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
      <div v-for="hotel in hotels" :key="hotel.id" class="bg-white dark:bg-navy-800 rounded-20 shadow-card overflow-hidden">
        <img :src="getSafeImageUrl((hotel as any).main_image_url, 'hotel')" alt="Otel Resmi" class="w-full h-48 object-cover">
        <div class="p-6">
          <h3 class="text-xl font-bold text-navy-700 dark:text-white">{{ hotel.name }}</h3>
          <p class="text-gray-600 dark:text-gray-400 mt-2">{{ hotel.city }}, {{ hotel.country }}</p>
          <p class="text-gray-700 dark:text-gray-300 mt-4 h-24 overflow-hidden">{{ hotel.description }}</p>
          <button @click="selectHotel(hotel.id)" class="mt-4 w-full px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors">
            Odayı Görüntüle ve Rezerve Et
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { supabase } from '@hotelexia/shared-supabase-client';
import { useRouter } from 'vue-router';
import type { Database } from '@hotelexia/shared-supabase-client';
import { LoadingState, ErrorDisplay } from '@hotelexia/shared-components';
import { getSafeImageUrl } from '@/utils/imageUtils';

type Hotel = Database['public']['Tables']['hotels']['Row'];

const router = useRouter();
const hotels = ref<Hotel[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);

const fetchHotels = async () => {
  try {
    loading.value = true;
    const { data, error: fetchError } = await supabase
      .from('hotels')
      .select('*')
      .eq('is_active', true);

    if (fetchError) {
      throw fetchError;
    }
    hotels.value = data || [];
  } catch (e: any) {
    error.value = `Oteller yüklenirken bir hata oluştu: ${e.message}`;
    console.error(e);
  } finally {
    loading.value = false;
  }
};

const selectHotel = (hotelId: string) => {
  // Bu, kullanıcıyı otel detay veya oda seçimi sayfasına yönlendirecek.
  // Şimdilik bir sonraki adım için yer tutucu.
  router.push(`/booking/${hotelId}`);
};

onMounted(() => {
  fetchHotels();
});
</script> 