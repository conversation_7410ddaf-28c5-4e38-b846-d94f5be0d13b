<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        Customer Portal Management
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Manage content and features for the customer-facing portal
      </p>
    </div>

    <!-- Sub Navigation Tabs -->
    <div class="mb-8">
      <nav class="flex space-x-8 border-b border-gray-200 dark:border-gray-700">
        <router-link
          :to="{ name: 'ManageDigitalOrdering' }"
          class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
          :class="$route.name === 'ManageDigitalOrdering' 
            ? 'border-brand-500 text-brand-600 dark:text-brand-400' 
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
        >
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Digital Ordering
          </span>
        </router-link>

        <router-link
          :to="{ name: 'ManageEvents' }"
          class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
          :class="$route.name === 'ManageEvents' 
            ? 'border-brand-500 text-brand-600 dark:text-brand-400' 
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
        >
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Events
          </span>
        </router-link>

        <router-link
          :to="{ name: 'ManageRoomServiceCategories' }"
          class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
          :class="$route.name === 'ManageRoomServiceCategories' 
            ? 'border-brand-500 text-brand-600 dark:text-brand-400' 
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
        >
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 5c7.18 0 13 5.82 13 13M6 11a7 7 0 017 7m-6 0a1 1 0 11-2 0 1 1 0 012 0z" />
            </svg>
            Oda Servisi Kategorileri
          </span>
        </router-link>

        <router-link
          :to="{ name: 'ManageGuestServiceCategories' }"
          class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
          :class="$route.name === 'ManageGuestServiceCategories' 
            ? 'border-brand-500 text-brand-600 dark:text-brand-400' 
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
        >
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            Hizmet Kategorileri
          </span>
        </router-link>
      </nav>
    </div>

    <!-- Content Area -->
    <div class="bg-white dark:bg-navy-800 rounded-20 p-6 shadow-card">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
// This component serves as a layout wrapper for the management section
</script>

<style scoped>
/* Additional styles if needed */
</style> 