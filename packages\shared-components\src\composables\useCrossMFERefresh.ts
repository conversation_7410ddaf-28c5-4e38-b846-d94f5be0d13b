import { ref, onMounted, onUnmounted } from 'vue'

export interface RefreshEvent {
  component: string
  timestamp: number
}

export interface CrossMFERefreshOptions {
  components?: string[]
  onRefresh?: (event: RefreshEvent) => void
  debounceMs?: number
}

export function useCrossMFERefresh(
  mfeContext: 'hotel-dashboard' | 'management-portal' | 'customer-portal' | 'app-shell',
  options: CrossMFERefreshOptions = {}
) {
  const {
    components = [],
    onRefresh,
    debounceMs = 1000
  } = options

  // State
  const lastRefresh = ref<RefreshEvent | null>(null)
  const refreshCount = ref(0)
  const isRefreshing = ref(false)

  // Debounce timer
  let debounceTimer: NodeJS.Timeout | null = null

  // Event listener
  let eventListener: ((event: CustomEvent) => void) | null = null

  // Get the event name for this MFE context
  const getEventName = () => {
    switch (mfeContext) {
      case 'hotel-dashboard':
        return 'hotel-dashboard-refresh'
      case 'management-portal':
        return 'management-portal-refresh'
      case 'customer-portal':
        return 'customer-portal-refresh'
      case 'app-shell':
        return 'app-shell-refresh'
      default:
        return 'cross-mfe-refresh'
    }
  }

  // Handle refresh event
  const handleRefreshEvent = (event: CustomEvent) => {
    const refreshEvent: RefreshEvent = event.detail

    // Filter by component if specified
    if (components.length > 0 && !components.includes(refreshEvent.component)) {
      return
    }

    // Clear existing debounce timer
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }

    // Set refreshing state
    isRefreshing.value = true

    // Debounce the refresh
    debounceTimer = setTimeout(() => {
      lastRefresh.value = refreshEvent
      refreshCount.value++
      isRefreshing.value = false

      console.log(`Cross-MFE refresh triggered for ${mfeContext}:`, refreshEvent)

      // Call custom refresh handler
      if (onRefresh) {
        onRefresh(refreshEvent)
      }
    }, debounceMs)
  }

  // Start listening for refresh events
  const startListening = () => {
    if (typeof window === 'undefined') return

    const eventName = getEventName()
    eventListener = handleRefreshEvent as any

    window.addEventListener(eventName, eventListener as EventListener)
    console.log(`Started listening for ${eventName} events`)
  }

  // Stop listening for refresh events
  const stopListening = () => {
    if (typeof window === 'undefined' || !eventListener) return

    const eventName = getEventName()
    window.removeEventListener(eventName, eventListener as EventListener)
    
    if (debounceTimer) {
      clearTimeout(debounceTimer)
      debounceTimer = null
    }

    console.log(`Stopped listening for ${eventName} events`)
  }

  // Manually trigger a refresh
  const triggerRefresh = (component: string) => {
    const refreshEvent: RefreshEvent = {
      component,
      timestamp: Date.now()
    }

    if (typeof window !== 'undefined') {
      const eventName = getEventName()
      window.dispatchEvent(new CustomEvent(eventName, {
        detail: refreshEvent
      }))
    }
  }

  // Clear refresh history
  const clearHistory = () => {
    lastRefresh.value = null
    refreshCount.value = 0
  }

  // Lifecycle hooks
  onMounted(() => {
    startListening()
  })

  onUnmounted(() => {
    stopListening()
  })

  return {
    // State
    lastRefresh,
    refreshCount,
    isRefreshing,

    // Actions
    startListening,
    stopListening,
    triggerRefresh,
    clearHistory
  }
}

// Specialized hooks for each MFE context

/**
 * Hook for hotel dashboard components to listen for refresh events
 */
export function useHotelDashboardRefresh(
  components: string[] = [],
  onRefresh?: (event: RefreshEvent) => void
) {
  return useCrossMFERefresh('hotel-dashboard', {
    components,
    onRefresh: (event) => {
      console.log('Hotel Dashboard refresh:', event.component)
      if (onRefresh) {
        onRefresh(event)
      }
    }
  })
}

/**
 * Hook for management portal components to listen for refresh events
 */
export function useManagementPortalRefresh(
  components: string[] = [],
  onRefresh?: (event: RefreshEvent) => void
) {
  return useCrossMFERefresh('management-portal', {
    components,
    onRefresh: (event) => {
      console.log('Management Portal refresh:', event.component)
      if (onRefresh) {
        onRefresh(event)
      }
    }
  })
}

/**
 * Hook for customer portal components to listen for refresh events
 */
export function useCustomerPortalRefresh(
  components: string[] = [],
  onRefresh?: (event: RefreshEvent) => void
) {
  return useCrossMFERefresh('customer-portal', {
    components,
    onRefresh: (event) => {
      console.log('Customer Portal refresh:', event.component)
      if (onRefresh) {
        onRefresh(event)
      }
    }
  })
}

/**
 * Hook for app shell components to listen for refresh events
 */
export function useAppShellRefresh(
  components: string[] = [],
  onRefresh?: (event: RefreshEvent) => void
) {
  return useCrossMFERefresh('app-shell', {
    components,
    onRefresh: (event) => {
      console.log('App Shell refresh:', event.component)
      if (onRefresh) {
        onRefresh(event)
      }
    }
  })
}

// Utility function to trigger refresh across all MFEs
export function triggerGlobalRefresh(component: string) {
  const refreshEvent: RefreshEvent = {
    component,
    timestamp: Date.now()
  }

  if (typeof window !== 'undefined') {
    const events = [
      'hotel-dashboard-refresh',
      'management-portal-refresh',
      'customer-portal-refresh',
      'app-shell-refresh'
    ]

    events.forEach(eventName => {
      window.dispatchEvent(new CustomEvent(eventName, {
        detail: refreshEvent
      }))
    })

    console.log('Triggered global refresh for component:', component)
  }
}
