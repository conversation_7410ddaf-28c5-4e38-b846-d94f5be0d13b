<template>
  <div class="p-4 md:p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        <PERSON><PERSON><PERSON><PERSON> Hizmet Promo Yönetimi
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        <PERSON><PERSON><PERSON>r hizmetleri için özel promosyon ve kampanya yönetimi
      </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Aktif Hizmet Promolar</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">6</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
            <SparklesIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Bu Ay Kullanım</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">89</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
            <ChartBarIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Toplam Tasarruf</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">₺3,240</p>
          </div>
          <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
            <CurrencyDollarIcon class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Memnuniyet Oranı</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">96%</p>
          </div>
          <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
            <StarIcon class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </div>
    </div>

    <!-- Active Promos Table -->
    <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm mb-8">
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Aktif Misafir Hizmet Promoları
          </h3>
          <button class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
            <PlusIcon class="w-4 h-4 mr-2" />
            Yeni Promo Oluştur
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Promo Adı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Hizmet Kategorisi
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İndirim
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Geçerlilik
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Kullanım
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="promo in guestServicePromos" :key="promo.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
                    <TicketIcon class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-navy-700 dark:text-white">{{ promo.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ promo.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                  {{ promo.category }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ promo.discount }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ promo.validUntil }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ promo.usageCount }}/{{ promo.maxUsage || '∞' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="promo.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'">
                  {{ promo.status === 'active' ? 'Aktif' : 'Pasif' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button class="text-brand-500 hover:text-brand-600">
                    <PencilIcon class="w-4 h-4" />
                  </button>
                  <button class="text-red-500 hover:text-red-600">
                    <TrashIcon class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Service Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Spa & Wellness -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Spa & Wellness
          </h3>
          <span class="bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 px-3 py-1 rounded-full text-sm font-medium">
            3 Aktif
          </span>
        </div>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Masaj Paketi</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%30 indirim</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Sauna + Hamam</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">2. kişi ücretsiz</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
        </div>
      </div>

      <!-- Laundry & Cleaning -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Çamaşır & Temizlik
          </h3>
          <span class="bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
            2 Aktif
          </span>
        </div>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Ekspres Temizlik</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%20 indirim</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Haftalık Paket</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">7. gün ücretsiz</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
        </div>
      </div>

      <!-- Transportation -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Ulaşım Hizmetleri
          </h3>
          <span class="bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium">
            1 Aktif
          </span>
        </div>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Havalimanı Transfer</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%25 indirim</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Şehir Turu</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">Yakında</p>
            </div>
            <span class="text-yellow-600 dark:text-yellow-400 text-sm">Beklemede</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Popular Service Requests -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm mb-8">
      <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">
        Popüler Hizmet Talepleri
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div v-for="service in popularServices" :key="service.name" 
             class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-brand-100 dark:bg-brand-900/20 rounded-lg flex items-center justify-center mr-3">
              <component :is="service.icon" class="w-5 h-5 text-brand-600 dark:text-brand-400" />
            </div>
            <div>
              <p class="font-medium text-navy-700 dark:text-white text-sm">{{ service.name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ service.count }} talep</p>
            </div>
          </div>
          <span class="text-brand-500 text-sm font-medium">{{ service.growth }}%</span>
        </div>
      </div>
    </div>

    <!-- Development Notice -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
      <div class="flex items-start">
        <InformationCircleIcon class="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" />
        <div>
          <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">
            Misafir Hizmet Promo Yönetimi - Geliştirme Aşamasında
          </h3>
          <p class="text-blue-700 dark:text-blue-300 mb-3">
            Bu sayfa misafir hizmetleri için özel promo yönetimi sağlar. Gelecekte eklenecek özellikler:
          </p>
          <ul class="list-disc list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm">
            <li>Hizmet kategorisi bazlı promo oluşturma</li>
            <li>Sezonsal kampanya yönetimi</li>
            <li>VIP misafir özel indirimleri</li>
            <li>Combo hizmet paketleri</li>
            <li>Erken rezervasyon teşvikleri</li>
            <li>Sadakat puanı kazandırma sistemı</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  SparklesIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  StarIcon,
  TicketIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  InformationCircleIcon,
  ClockIcon,
  ShoppingBagIcon,
  TruckIcon,
  GiftIcon
} from '@heroicons/vue/24/outline'

// Dummy data for guest service promos
const guestServicePromos = ref([
  {
    id: 1,
    name: 'Spa Paketi İndirimi',
    description: 'Masaj ve sauna kombini',
    category: 'Spa & Wellness',
    discount: '%30 indirim',
    validUntil: '31.12.2024',
    usageCount: 23,
    maxUsage: 50,
    status: 'active'
  },
  {
    id: 2,
    name: 'Çamaşır Hizmeti',
    description: '2. parça ücretsiz',
    category: 'Temizlik',
    discount: '2. parça ücretsiz',
    validUntil: '20.12.2024',
    usageCount: 31,
    maxUsage: null,
    status: 'active'
  },
  {
    id: 3,
    name: 'Havalimanı Transfer',
    description: 'Transfer hizmeti indirimi',
    category: 'Ulaşım',
    discount: '%25 indirim',
    validUntil: '15.01.2025',
    usageCount: 18,
    maxUsage: 40,
    status: 'active'
  },
  {
    id: 4,
    name: 'Sauna + Hamam',
    description: 'İkili paket özel fiyat',
    category: 'Spa & Wellness',
    discount: '2. kişi ücretsiz',
    validUntil: '10.12.2024',
    usageCount: 8,
    maxUsage: 20,
    status: 'inactive'
  }
])

// Popular services data
const popularServices = ref([
  { name: 'Ekspres Temizlik', count: 45, growth: 15, icon: ClockIcon },
  { name: 'Oda Servisi', count: 38, growth: 8, icon: ShoppingBagIcon },
  { name: 'Transfer Hizmeti', count: 29, growth: 22, icon: TruckIcon },
  { name: 'Özel İstekler', count: 17, growth: 12, icon: GiftIcon }
])
</script> 