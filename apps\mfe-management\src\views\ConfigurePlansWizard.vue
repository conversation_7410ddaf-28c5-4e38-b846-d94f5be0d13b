<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <!-- Header with <PERSON> Button -->
    <div class="bg-white dark:bg-navy-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <router-link
              to="/billing"
              class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
            >
              <ArrowLeftIcon class="h-5 w-5" />
            </router-link>
            <div>
              <h1 class="text-2xl font-bold text-navy-700 dark:text-white">Abonelik Planlarını Yapılandır</h1>
              <p class="text-gray-600 dark:text-gray-400 mt-1">Platform abonelik planlarını ve özelliklerini yönetin</p>
            </div>
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Adım {{ currentStep }} / 4
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Stepper -->
    <div class="bg-white dark:bg-navy-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-6 py-6">
        <div class="flex items-center justify-between">
          <div v-for="(step, index) in steps" :key="index" class="flex-1 flex items-center">
            <div class="flex items-center">
              <div 
                class="w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300"
                :class="getStepClasses(index + 1)"
              >
                <CheckIcon v-if="index + 1 < currentStep" class="h-5 w-5 text-white" />
                <span v-else class="text-sm font-semibold">{{ index + 1 }}</span>
              </div>
              <div class="ml-3 text-left">
                <p class="text-sm font-medium text-navy-700 dark:text-white">{{ step.title }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ step.description }}</p>
              </div>
            </div>
            <div 
              v-if="index < steps.length - 1" 
              class="flex-1 h-0.5 mx-4 transition-all duration-300"
              :class="index + 1 < currentStep ? 'bg-gradient-to-r from-brand-500 to-brand-600' : 'bg-gray-200 dark:bg-gray-700'"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8">
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm">
        
        <!-- Step 1: Plan Detayları -->
        <div v-if="currentStep === 1" class="p-8">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-brand-500 to-brand-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <CurrencyDollarIcon class="h-8 w-8 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Plan Detayları</h2>
            <p class="text-gray-600 dark:text-gray-400">Abonelik planlarının fiyat ve temel bilgilerini düzenleyin</p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div v-for="plan in configData.plans" :key="plan.id" class="border-2 rounded-xl p-6 transition-all duration-300 hover:shadow-lg"
                 :class="plan.recommended ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20' : 'border-gray-200 dark:border-gray-700'">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-navy-700 dark:text-white">{{ plan.name }}</h3>
                <span v-if="plan.recommended" class="bg-brand-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  ÖNERİLEN
                </span>
              </div>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Plan Adı</label>
                  <input
                    v-model="plan.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-navy-700 dark:text-white"
                  />
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Aylık Fiyat (TL)</label>
                    <input
                      v-model="plan.pricing.monthly"
                      type="number"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-navy-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Yıllık Fiyat (TL)</label>
                    <input
                      v-model="plan.pricing.yearly"
                      type="number"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-navy-700 dark:text-white"
                    />
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Açıklama</label>
                  <textarea
                    v-model="plan.description"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-navy-700 dark:text-white"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-8 text-center">
            <button 
              @click="addNewPlan"
              class="px-6 py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-600 dark:text-gray-400 hover:border-brand-500 hover:text-brand-500 transition-colors"
            >
              <PlusIcon class="h-5 w-5 inline-block mr-2" />
              Yeni Plan Ekle
            </button>
          </div>
        </div>

        <!-- Step 2: Modül Atamaları -->
        <div v-if="currentStep === 2" class="p-8">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <PuzzlePieceIcon class="h-8 w-8 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Modül Atamaları</h2>
            <p class="text-gray-600 dark:text-gray-400">Her plan için dahil edilecek modülleri seçin</p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div v-for="plan in configData.plans" :key="plan.id">
              <div class="bg-gray-50 dark:bg-navy-700 rounded-xl p-6">
                <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4 text-center">{{ plan.name }}</h3>
                <div class="space-y-3">
                  <div v-for="module in availableModules" :key="module.id" class="flex items-center">
                    <input
                      :id="`${plan.id}-${module.id}`"
                      v-model="plan.included_modules"
                      :value="module.id"
                      type="checkbox"
                      class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
                    />
                    <label 
                      :for="`${plan.id}-${module.id}`"
                      class="ml-3 flex-1 cursor-pointer"
                    >
                      <div class="flex items-center">
                        <component :is="module.icon" class="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
                        <div>
                          <p class="text-sm font-medium text-navy-700 dark:text-white">{{ module.name }}</p>
                          <p class="text-xs text-gray-500 dark:text-gray-400">{{ module.description }}</p>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Özellik Limitleri -->
        <div v-if="currentStep === 3" class="p-8">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <AdjustmentsHorizontalIcon class="h-8 w-8 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Özellik Limitleri</h2>
            <p class="text-gray-600 dark:text-gray-400">Her plan için kaynak ve özellik limitlerini belirleyin</p>
          </div>

          <div class="space-y-8">
            <div v-for="plan in configData.plans" :key="plan.id" class="bg-gray-50 dark:bg-navy-700 rounded-xl p-6">
              <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-6">{{ plan.name }} - Limitler</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maksimum Personel Hesabı
                  </label>
                  <input
                    v-model="plan.feature_limits.max_staff"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maksimum Oda Sayısı
                  </label>
                  <input
                    v-model="plan.feature_limits.max_rooms"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maksimum Eş Zamanlı Misafir
                  </label>
                  <input
                    v-model="plan.feature_limits.max_concurrent_guests"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Veri Saklama Süresi (Ay)
                  </label>
                  <input
                    v-model="plan.feature_limits.data_retention_months"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    API İstek Limiti (Günlük)
                  </label>
                  <input
                    v-model="plan.feature_limits.api_requests_per_day"
                    type="number"
                    min="100"
                    step="100"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Depolama Alanı (GB)
                  </label>
                  <input
                    v-model="plan.feature_limits.storage_gb"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Bildirim Limiti (Aylık)
                  </label>
                  <input
                    v-model="plan.feature_limits.notifications_per_month"
                    type="number"
                    min="1"
                    step="100"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Özel Entegrasyon Sayısı
                  </label>
                  <input
                    v-model="plan.feature_limits.custom_integrations"
                    type="number"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-600 text-navy-700 dark:text-white"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Özet ve Kaydet -->
        <div v-if="currentStep === 4" class="p-8">
          <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <DocumentCheckIcon class="h-8 w-8 text-white" />
            </div>
            <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Özet ve Onay</h2>
            <p class="text-gray-600 dark:text-gray-400">Yapılandırılan plan detaylarını gözden geçirin ve kaydedin</p>
          </div>

          <div class="space-y-8">
            <div v-for="plan in configData.plans" :key="plan.id" class="bg-gray-50 dark:bg-navy-700 rounded-xl p-6">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-navy-700 dark:text-white">{{ plan.name }}</h3>
                <div class="text-right">
                  <p class="text-2xl font-bold text-brand-600">₺{{ plan.pricing.monthly }}/ay</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">₺{{ plan.pricing.yearly }}/yıl</p>
                </div>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Dahil Edilen Modüller</h4>
                  <div class="space-y-2">
                    <div v-for="moduleId in plan.included_modules" :key="moduleId" class="flex items-center">
                      <CheckIcon class="h-4 w-4 text-green-500 mr-2" />
                      <span class="text-sm text-gray-600 dark:text-gray-400">
                        {{ getModuleName(moduleId) }}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Özellik Limitleri</h4>
                  <div class="grid grid-cols-2 gap-2 text-sm">
                    <div class="text-gray-600 dark:text-gray-400">Personel: {{ plan.feature_limits.max_staff }}</div>
                    <div class="text-gray-600 dark:text-gray-400">Oda: {{ plan.feature_limits.max_rooms }}</div>
                    <div class="text-gray-600 dark:text-gray-400">Misafir: {{ plan.feature_limits.max_concurrent_guests }}</div>
                    <div class="text-gray-600 dark:text-gray-400">Saklama: {{ plan.feature_limits.data_retention_months }} ay</div>
                    <div class="text-gray-600 dark:text-gray-400">API: {{ plan.feature_limits.api_requests_per_day }}/gün</div>
                    <div class="text-gray-600 dark:text-gray-400">Depolama: {{ plan.feature_limits.storage_gb }} GB</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-8 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex items-start">
              <ExclamationTriangleIcon class="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3" />
              <div>
                <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Dikkat</p>
                <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  Bu değişiklikler mevcut abonelikleri etkileyebilir. Onaylamadan önce lütfen mevcut müşterilerin durumunu kontrol edin.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="border-t border-gray-200 dark:border-gray-700 px-8 py-6">
          <div class="flex items-center justify-between">
            <button
              v-if="currentStep > 1"
              @click="previousStep"
              class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors"
            >
              <ArrowLeftIcon class="h-4 w-4 inline-block mr-2" />
              Önceki Adım
            </button>
            <div v-else></div>

            <div class="flex items-center space-x-3">
              <router-link
                to="/billing"
                class="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                İptal
              </router-link>
              <button
                v-if="currentStep < 4"
                @click="nextStep"
                class="px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
              >
                Sonraki Adım
                <ArrowRightIcon class="h-4 w-4 inline-block ml-2" />
              </button>
              <button
                v-else
                @click="savePlans"
                class="px-8 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                <CheckIcon class="h-4 w-4 inline-block mr-2" />
                Değişiklikleri Kaydet
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  CheckIcon,
  CurrencyDollarIcon,
  PuzzlePieceIcon,
  AdjustmentsHorizontalIcon,
  DocumentCheckIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  HomeIcon,
  BuildingOffice2Icon,
  WrenchScrewdriverIcon,
  ShoppingCartIcon,
  UserIcon,
  CalendarDaysIcon,
  SpeakerWaveIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()

// Current step state
const currentStep = ref(1)

// Steps configuration
const steps = [
  {
    title: 'Plan Detayları',
    description: 'Fiyat ve temel bilgiler'
  },
  {
    title: 'Modül Atamaları',
    description: 'Dahil edilecek özellikler'
  },
  {
    title: 'Özellik Limitleri',
    description: 'Kaynak ve limitler'
  },
  {
    title: 'Özet',
    description: 'Gözden geçir ve kaydet'
  }
]

// Available modules
const availableModules = [
  {
    id: 'mfe_housekeeping',
    name: 'Akıllı Kat Hizmetleri',
    description: 'Oda temizlik ve kat yönetimi',
    icon: HomeIcon
  },
  {
    id: 'mfe_maintenance',
    name: 'Bakım Yönetimi',
    description: 'Preventif ve reaktif bakım',
    icon: WrenchScrewdriverIcon
  },
  {
    id: 'mfe_digital_ordering',
    name: 'Dijital Sipariş',
    description: 'Oda servisi ve menü yönetimi',
    icon: ShoppingCartIcon
  },
  {
    id: 'mfe_guest_services',
    name: 'Misafir Hizmetleri',
    description: 'Konsiyerj ve hizmet talepleri',
    icon: UserIcon
  },
  {
    id: 'mfe_activities',
    name: 'Aktivite Yönetimi',
    description: 'Etkinlik ve program yönetimi',
    icon: CalendarDaysIcon
  },
  {
    id: 'mfe_notifications',
    name: 'Bildirim Sistemi',
    description: 'Push, SMS ve email bildirimleri',
    icon: SpeakerWaveIcon
  },
  {
    id: 'mfe_spa_wellness',
    name: 'SPA & Wellness',
    description: 'SPA rezervasyon ve yönetimi',
    icon: BuildingOffice2Icon
  },
  {
    id: 'mfe_analytics',
    name: 'Gelişmiş Raporlar',
    description: 'İş zekası ve analitik',
    icon: ChartBarIcon
  }
]

// Configuration data
const configData = reactive({
  plans: [
    {
      id: 'basic',
      name: 'Basic',
      description: 'Küçük oteller için temel özellikler',
      recommended: false,
      pricing: {
        monthly: 899,
        yearly: 8990
      },
      included_modules: ['mfe_housekeeping', 'mfe_digital_ordering'],
      feature_limits: {
        max_staff: 10,
        max_rooms: 50,
        max_concurrent_guests: 150,
        data_retention_months: 12,
        api_requests_per_day: 1000,
        storage_gb: 10,
        notifications_per_month: 1000,
        custom_integrations: 0
      }
    },
    {
      id: 'premium',
      name: 'Premium',
      description: 'Orta ölçekli oteller için gelişmiş özellikler',
      recommended: true,
      pricing: {
        monthly: 1499,
        yearly: 14990
      },
      included_modules: ['mfe_housekeeping', 'mfe_maintenance', 'mfe_digital_ordering', 'mfe_guest_services', 'mfe_activities', 'mfe_notifications'],
      feature_limits: {
        max_staff: 50,
        max_rooms: 200,
        max_concurrent_guests: 600,
        data_retention_months: 24,
        api_requests_per_day: 5000,
        storage_gb: 50,
        notifications_per_month: 5000,
        custom_integrations: 2
      }
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Büyük otel zincirleri için kurumsal çözümler',
      recommended: false,
      pricing: {
        monthly: 2499,
        yearly: 24990
      },
      included_modules: ['mfe_housekeeping', 'mfe_maintenance', 'mfe_digital_ordering', 'mfe_guest_services', 'mfe_activities', 'mfe_notifications', 'mfe_spa_wellness', 'mfe_analytics'],
      feature_limits: {
        max_staff: 200,
        max_rooms: 1000,
        max_concurrent_guests: 3000,
        data_retention_months: 60,
        api_requests_per_day: 25000,
        storage_gb: 500,
        notifications_per_month: 25000,
        custom_integrations: 10
      }
    }
  ]
})

// Step classes
const getStepClasses = (stepNumber: number) => {
  if (stepNumber < currentStep.value) {
    return 'bg-gradient-to-r from-brand-500 to-brand-600 border-brand-500 text-white'
  } else if (stepNumber === currentStep.value) {
    return 'bg-white border-brand-500 text-brand-600'
  } else {
    return 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400'
  }
}

// Navigation methods
const nextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// Add new plan
const addNewPlan = () => {
  const newPlan = {
    id: `custom_${Date.now()}`,
    name: 'Yeni Plan',
    description: 'Yeni plan açıklaması',
    recommended: false,
    pricing: {
      monthly: 0,
      yearly: 0
    },
    included_modules: [],
    feature_limits: {
      max_staff: 1,
      max_rooms: 1,
      max_concurrent_guests: 1,
      data_retention_months: 1,
      api_requests_per_day: 100,
      storage_gb: 1,
      notifications_per_month: 100,
      custom_integrations: 0
    }
  }
  configData.plans.push(newPlan)
}

// Get module name by ID
const getModuleName = (moduleId: string) => {
  const module = availableModules.find(m => m.id === moduleId)
  return module ? module.name : moduleId
}

// Save plans
const savePlans = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show success message (you can implement a toast notification here)
    alert('Abonelik planları başarıyla güncellendi!')
    
    // Navigate back to billing page
    router.push('/billing')
  } catch (error) {
    alert('Bir hata oluştu. Lütfen tekrar deneyin.')
  }
}
</script> 