<template>
  <div class="space-y-6">
    <!-- Targeting Section -->
    <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">He<PERSON><PERSON></h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Targeting Type -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            He<PERSON><PERSON><PERSON>
          </label>
          <select 
            v-model="notification.targetType"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
          >
            <option value="all">Tüm Misafirler</option>
            <option value="specific"><PERSON><PERSON><PERSON><PERSON></option>
            <option value="service">Hizmet<PERSON> Göre</option>
            <option value="activity">Aktiviteye Göre</option>
            <option value="room">Oda Tipine Göre</option>
          </select>
        </div>

        <!-- Specific Selection -->
        <div v-if="notification.targetType !== 'all'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ getTargetLabel() }}
          </label>
          <select 
            v-model="notification.targetValue"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
          >
            <option v-for="option in getTargetOptions()" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Channel Selection -->
    <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Gönderim Kanalları</h3>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <label v-for="channel in channels" :key="channel.id" class="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            v-model="notification.channels"
            :value="channel.id"
            class="h-5 w-5 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
          />
          <div class="flex items-center space-x-2">
            <component :is="channel.icon" class="w-5 h-5" :class="channel.color" />
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ channel.name }}</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Content Form -->
    <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Bildirim İçeriği</h3>
      
      <div class="space-y-4">
        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Başlık *
          </label>
          <input
            type="text"
            v-model="notification.title"
            placeholder="Bildirim başlığını giriniz"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            required
          />
        </div>

        <!-- Message -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Mesaj İçeriği *
          </label>
          <textarea
            v-model="notification.message"
            placeholder="Bildirim mesajınızı yazınız..."
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white resize-none"
            required
          ></textarea>
        </div>

        <!-- Image Upload -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Görsel (Opsiyonel)
          </label>
          <div class="flex items-center justify-center w-full">
            <label for="image-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-navy-600 dark:bg-navy-800 hover:bg-gray-100 dark:border-gray-600">
              <div class="flex flex-col items-center justify-center pt-5 pb-6">
                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                  <span class="font-semibold">Görsel yüklemek için tıklayın</span>
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG veya GIF (MAX. 10MB)</p>
              </div>
              <input id="image-upload" type="file" class="hidden" accept="image/*" />
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between bg-gray-50 dark:bg-navy-700 rounded-lg p-6">
      <div class="flex items-center space-x-4">
        <button
          @click="showPreview = true"
          :disabled="!isFormValid"
          class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <EyeIcon class="w-5 h-5 inline-block mr-2" />
          Önizleme Yap
        </button>
      </div>

      <div class="flex items-center space-x-4">
        <button
          @click="resetForm"
          class="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
        >
          Temizle
        </button>
        <button
          @click="sendNotification"
          :disabled="!isSendEnabled"
          class="px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 focus:ring-2 focus:ring-brand-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PaperAirplaneIcon class="w-5 h-5 inline-block mr-2" />
          {{ isSendEnabled ? 'Gönder' : 'Önce Önizleme Yapın' }}
        </button>
      </div>
    </div>

    <!-- Preview Modal -->
    <NotificationPreviewModal 
      v-if="showPreview"
      :notification="notification"
      @close="showPreview = false"
      @confirm="enableSend"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  EyeIcon, 
  PaperAirplaneIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  BellIcon
} from '@heroicons/vue/24/outline'
import { useNotificationStore } from '@/stores/notificationStore'
import NotificationPreviewModal from '@/components/modals/NotificationPreviewModal.vue'

const notificationStore = useNotificationStore()

const showPreview = ref(false)
const isSendEnabled = ref(false)

const notification = ref({
  targetType: 'all',
  targetValue: '',
  channels: ['in-app'],
  title: '',
  message: '',
  image: null
})

const channels = [
  {
    id: 'in-app',
    name: 'Uygulama İçi',
    icon: BellIcon,
    color: 'text-blue-500'
  },
  {
    id: 'sms',
    name: 'SMS',
    icon: DevicePhoneMobileIcon,
    color: 'text-green-500'
  },
  {
    id: 'email',
    name: 'E-posta',
    icon: EnvelopeIcon,
    color: 'text-red-500'
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: ChatBubbleLeftRightIcon,
    color: 'text-emerald-500'
  }
]

const isFormValid = computed(() => {
  return notification.value.title.trim() && 
         notification.value.message.trim() && 
         notification.value.channels.length > 0
})

const getTargetLabel = () => {
  const labels = {
    specific: 'Misafir Seçin',
    service: 'Hizmet Seçin',
    activity: 'Aktivite Seçin',
    room: 'Oda Tipi Seçin'
  }
  return labels[notification.value.targetType] || ''
}

const getTargetOptions = () => {
  const options = {
    specific: notificationStore.guests.map(guest => ({
      value: guest.id,
      label: `${guest.name} - Oda ${guest.roomNumber}`
    })),
    service: [
      { value: 'room-service', label: 'Oda Servisi' },
      { value: 'housekeeping', label: 'Kat Hizmetleri' },
      { value: 'maintenance', label: 'Teknik Servis' }
    ],
    activity: [
      { value: 'pool-music', label: 'Havuz Başında Canlı Müzik' },
      { value: 'yoga', label: 'Sabah Yoga Seansı' },
      { value: 'cooking', label: 'Yemek Pişirme Atölyesi' }
    ],
    room: [
      { value: 'standard', label: 'Standart Oda' },
      { value: 'deluxe', label: 'Deluxe Oda' },
      { value: 'suite', label: 'Suit Oda' }
    ]
  }
  return options[notification.value.targetType] || []
}

const resetForm = () => {
  notification.value = {
    targetType: 'all',
    targetValue: '',
    channels: ['in-app'],
    title: '',
    message: '',
    image: null
  }
  isSendEnabled.value = false
}

const enableSend = () => {
  isSendEnabled.value = true
  showPreview.value = false
}

const sendNotification = () => {
  if (!isSendEnabled.value) return
  
  // Add to notification store
  notificationStore.addNotification({
    title: notification.value.title,
    message: notification.value.message,
    targetType: notification.value.targetType,
    targetValue: notification.value.targetValue,
    channels: notification.value.channels,
    status: 'sent',
    sentAt: new Date(),
    recipients: getRecipientCount()
  })
  
  // Reset form
  resetForm()
  
  // Show success message
  alert('Bildirim başarıyla gönderildi!')
}

const getRecipientCount = () => {
  if (notification.value.targetType === 'all') {
    return notificationStore.guests.length
  } else if (notification.value.targetType === 'specific') {
    return 1
  } else {
    // Estimate based on service/activity/room type
    return Math.floor(Math.random() * 10) + 1
  }
}
</script> 