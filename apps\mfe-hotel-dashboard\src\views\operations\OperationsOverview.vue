<template>
  <ErrorBoundary
    fallback-message="Operasyonlar özet görünümü yüklenirken bir hata oluştu."
    @retry="handleRetry"
  >
    <!-- Loading State -->
    <LoadingState
      v-if="isLoading"
      variant="dashboard"
      :show="true"
      message="Operasyonel veriler yükleniyor..."
    />

    <!-- Error State -->
    <div v-else-if="hasError" class="text-center py-12">
      <ExclamationTriangleIcon class="w-16 h-16 text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Veri Yükleme Hatası
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        Operasyonel veriler yüklenirken bir hata oluştu.
      </p>
      <button
        @click="handleRetry"
        class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
      >
        <PERSON><PERSON><PERSON>
      </button>
    </div>

    <!-- Main Content -->
    <div v-else class="p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Header Section -->
    <div class="mb-8">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Operasyonlar Özet Görünüm</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-2">Hotelexia Demo Hotel - Güncel operasyonel durum</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-right">
            <div class="text-sm text-gray-500 dark:text-gray-400">Son Güncelleme</div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ lastUpdated }}</div>
          </div>
          <button
            @click="refreshData"
            :disabled="isRefreshing"
            class="inline-flex items-center px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 disabled:opacity-50 transition-colors"
          >
            <ArrowPathIcon :class="['w-4 h-4 mr-2', isRefreshing ? 'animate-spin' : '']" />
            Yenile
          </button>
        </div>
      </div>
    </div>

    <!-- Critical Alerts -->
    <div v-if="criticalAlerts.length > 0" class="mb-8">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-center mb-2">
          <ExclamationTriangleIcon class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
          <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">Kritik Uyarılar</h3>
        </div>
        <div class="space-y-2">
          <div v-for="alert in criticalAlerts" :key="alert.id" class="text-sm text-red-700 dark:text-red-300">
            • {{ alert.message }}
          </div>
        </div>
      </div>
    </div>

    <!-- KPI Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Housekeeping Stats -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Kat Hizmetleri</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ housekeepingStats.completed }}/{{ housekeepingStats.total }}</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">%{{ housekeepingStats.completionRate }} tamamlandı</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
            <HomeIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <!-- Maintenance Stats -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Bakım Onarım</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ maintenanceStats.active }}</p>
            <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">{{ maintenanceStats.urgent }} acil</p>
          </div>
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
            <WrenchScrewdriverIcon class="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
        </div>
      </div>

      <!-- Guest Services -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Misafir Hizmetleri</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ guestServiceStats.pending }}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">{{ guestServiceStats.avgResponseTime }}dk ort. yanıt</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
            <UserGroupIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>

      <!-- Staff Performance -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Personel Performansı</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">%{{ staffPerformance.efficiency }}</p>
            <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">{{ staffPerformance.activeStaff }} aktif personel</p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
            <ChartBarIcon class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold text-navy-700 dark:text-white mb-4">Hızlı İşlemler</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <button
          v-for="action in quickActions"
          :key="action.id"
          @click="executeQuickAction(action.id)"
          class="flex flex-col items-center p-4 bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all"
        >
          <component :is="action.icon" class="w-8 h-8 text-brand-500 mb-2" />
          <span class="text-sm font-medium text-gray-900 dark:text-white text-center">{{ action.label }}</span>
        </button>
      </div>
    </div>

    <!-- Operations Status Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Current Tasks -->
      <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
        <div class="p-6 border-b border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Güncel Görevler</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">Son 1 saat</span>
          </div>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="task in recentTasks" :key="task.id" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div :class="getTaskPriorityClass(task.priority)" class="w-3 h-3 rounded-full"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ task.title }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ task.location }} - {{ task.assignedTo }}</p>
                </div>
              </div>
              <div class="text-right">
                <span :class="getTaskStatusClass(task.status)" class="px-2 py-1 text-xs rounded-full">
                  {{ task.statusText }}
                </span>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ task.timeAgo }}</p>
              </div>
            </div>
          </div>
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <router-link
              to="/hotel/housekeeping/tasks"
              class="text-sm text-brand-500 hover:text-brand-600 font-medium"
            >
              Kanban Board'u Göster →
            </router-link>
          </div>
        </div>
      </div>

      <!-- System Health -->
      <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
        <div class="p-6 border-b border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Sistem Durumu</h3>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm text-green-600 dark:text-green-400">Normal</span>
            </div>
          </div>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="service in systemServices" :key="service.name" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div :class="getServiceStatusClass(service.status)" class="w-3 h-3 rounded-full"></div>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</span>
              </div>
              <div class="text-right">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ service.responseTime }}ms</span>
              </div>
            </div>
          </div>
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <router-link
              to="/hotel/system-health"
              class="text-sm text-brand-500 hover:text-brand-600 font-medium"
            >
              Detaylı sistem durumu →
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Floor Status Overview -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
      <div class="p-6 border-b border-gray-200 dark:border-gray-600">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Kat Durumu Özeti</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div v-for="floor in floorStatus" :key="floor.number" class="text-center">
            <div class="mb-3">
              <h4 class="text-lg font-semibold text-navy-700 dark:text-white">{{ floor.name }}</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ floor.rooms.length }} oda</p>
            </div>
            <div class="relative w-16 h-16 mx-auto mb-3">
              <svg class="w-16 h-16 transform -rotate-90">
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  stroke-width="4"
                  fill="transparent"
                  class="text-gray-200 dark:text-gray-600"
                />
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  stroke-width="4"
                  fill="transparent"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="circumference - (floor.completionRate / 100) * circumference"
                  class="text-green-500 transition-all duration-300"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-semibold text-navy-700 dark:text-white">{{ floor.completionRate }}%</span>
              </div>
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ floor.cleanRooms }}/{{ floor.rooms.length }} temiz
            </div>
          </div>
        </div>
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
          <router-link
            to="/hotel/operations/housekeeping/floor-management"
            class="text-sm text-brand-500 hover:text-brand-600 font-medium"
          >
            Kat yönetimini görüntüle →
          </router-link>
        </div>
      </div>
    </div>
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  ExclamationTriangleIcon,
  HomeIcon,
  WrenchScrewdriverIcon,
  UserGroupIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon,
  PlusIcon,
  Cog6ToothIcon,
  BellIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/vue/24/outline'
import { ErrorBoundary, LoadingState } from '@hotelexia/shared-components'

// Router
const router = useRouter()

// Reactive state
const isRefreshing = ref(false)
const lastUpdated = ref('')
const circumference = 2 * Math.PI * 28
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')

// Critical alerts
const criticalAlerts = ref([
  { id: 1, message: 'Oda 305 - Elektrik arızası, acil bakım gerekli', severity: 'critical' },
  { id: 2, message: 'Asansör 2 - Servis dışı, teknisyen çağrıldı', severity: 'high' }
])

// Statistics
const housekeepingStats = ref({
  total: 48,
  completed: 42,
  completionRate: 88
})

const maintenanceStats = ref({
  active: 7,
  urgent: 2
})

const guestServiceStats = ref({
  pending: 12,
  avgResponseTime: 8
})

const staffPerformance = ref({
  efficiency: 94,
  activeStaff: 23
})

// Quick actions
const quickActions = ref([
  { id: 'new-task', label: 'Yeni Görev', icon: PlusIcon },
  { id: 'room-status', label: 'Oda Durumu', icon: HomeIcon },
  { id: 'maintenance', label: 'Bakım İsteği', icon: WrenchScrewdriverIcon },
  { id: 'reports', label: 'Raporlar', icon: DocumentTextIcon },
  { id: 'settings', label: 'Ayarlar', icon: Cog6ToothIcon },
  { id: 'notifications', label: 'Bildirimler', icon: BellIcon }
])

// Recent tasks
const recentTasks = ref([
  {
    id: 1,
    title: 'Oda 101 Temizlik',
    location: '1. Kat',
    assignedTo: 'Ayşe Demir',
    priority: 'high',
    status: 'in-progress',
    statusText: 'Devam Ediyor',
    timeAgo: '15 dk önce'
  },
  {
    id: 2,
    title: 'Lobi Vakumlama',
    location: 'Zemin Kat',
    assignedTo: 'Mehmet Yılmaz',
    priority: 'medium',
    status: 'completed',
    statusText: 'Tamamlandı',
    timeAgo: '32 dk önce'
  },
  {
    id: 3,
    title: 'Oda 205 Bakım',
    location: '2. Kat',
    assignedTo: 'Fatma Özkan',
    priority: 'urgent',
    status: 'pending',
    statusText: 'Bekliyor',
    timeAgo: '5 dk önce'
  },
  {
    id: 4,
    title: 'Restoran Temizlik',
    location: 'Zemin Kat',
    assignedTo: 'Ali Kaya',
    priority: 'medium',
    status: 'in-progress',
    statusText: 'Devam Ediyor',
    timeAgo: '45 dk önce'
  }
])

// System services
const systemServices = ref([
  { name: 'Veritabanı', status: 'online', responseTime: 23 },
  { name: 'Oda Sistemi', status: 'online', responseTime: 45 },
  { name: 'Ödeme Sistemi', status: 'online', responseTime: 67 },
  { name: 'Bildirim Servisi', status: 'warning', responseTime: 156 },
  { name: 'Yedekleme', status: 'online', responseTime: 89 }
])

// Floor status
const floorStatus = ref([
  {
    number: 1,
    name: '1. Kat',
    rooms: Array.from({length: 12}, (_, i) => ({ number: 100 + i + 1, status: 'clean' })),
    cleanRooms: 10,
    completionRate: 83
  },
  {
    number: 2,
    name: '2. Kat',
    rooms: Array.from({length: 12}, (_, i) => ({ number: 200 + i + 1, status: 'clean' })),
    cleanRooms: 11,
    completionRate: 92
  },
  {
    number: 3,
    name: '3. Kat',
    rooms: Array.from({length: 12}, (_, i) => ({ number: 300 + i + 1, status: 'clean' })),
    cleanRooms: 9,
    completionRate: 75
  },
  {
    number: 4,
    name: '4. Kat',
    rooms: Array.from({length: 12}, (_, i) => ({ number: 400 + i + 1, status: 'clean' })),
    cleanRooms: 12,
    completionRate: 100
  }
])

// Auto-refresh timer
let refreshTimer: NodeJS.Timeout | null = null

// Methods
const refreshData = async () => {
  isRefreshing.value = true
  hasError.value = false
  errorMessage.value = ''

  try {
    // Simulate data refresh - in real implementation, this would call actual data services
    await new Promise(resolve => setTimeout(resolve, 1000))
    updateLastUpdated()
  } catch (error) {
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : 'Veri yenileme sırasında hata oluştu'
  } finally {
    isRefreshing.value = false
  }
}

const handleRetry = async () => {
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''

  try {
    // Simulate initial data loading
    await new Promise(resolve => setTimeout(resolve, 1500))
    updateLastUpdated()
  } catch (error) {
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : 'Veri yükleme sırasında hata oluştu'
  } finally {
    isLoading.value = false
  }
}

const updateLastUpdated = () => {
  const now = new Date()
  lastUpdated.value = now.toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const executeQuickAction = (actionId: string) => {
  switch (actionId) {
    case 'new-task':
      // Navigate to task assignment for new task creation
      router.push('/hotel/housekeeping/tasks')
      break
    case 'room-status':
      // Navigate to room management page
      router.push('/hotel/housekeeping/rooms')
      break
    case 'maintenance':
      // Navigate to maintenance requests
      router.push('/hotel/maintenance/overview')
      break
    case 'reports':
      // Navigate to reports - will be created later
      router.push('/hotel/reports/operational')
      break
    case 'settings':
      // Navigate to settings - will be created later
      router.push('/hotel/management/settings')
      break
    case 'notifications':
      // Navigate to notifications
      router.push('/notifications')
      break
    default:
      console.log('Bilinmeyen aksiyon:', actionId)
  }
}

const getTaskPriorityClass = (priority: string) => {
  const classes = {
    'low': 'bg-gray-400',
    'medium': 'bg-blue-500',
    'high': 'bg-orange-500',
    'urgent': 'bg-red-500'
  }
  return classes[priority] || classes['medium']
}

const getTaskStatusClass = (status: string) => {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
    'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
    'completed': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
  }
  return classes[status] || classes['pending']
}

const getServiceStatusClass = (status: string) => {
  const classes = {
    'online': 'bg-green-500',
    'warning': 'bg-yellow-500',
    'offline': 'bg-red-500'
  }
  return classes[status] || classes['online']
}

// Lifecycle
onMounted(async () => {
  // Initial data loading
  try {
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate initial load
    updateLastUpdated()
  } catch (error) {
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : 'İlk veri yükleme sırasında hata oluştu'
  } finally {
    isLoading.value = false
  }

  // Auto-refresh every 30 seconds
  refreshTimer = setInterval(() => {
    updateLastUpdated()
  }, 30000)
})

onBeforeUnmount(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script> 