<testsuites id="" name="" tests="9" failures="6" skipped="0" errors="0" time="24.904824">
<testsuite name="supabase-data-integration.spec.ts" timestamp="2025-07-02T20:56:24.820Z" hostname="chromium" tests="9" failures="6" skipped="0" time="78.825" errors="0">
<testcase name="Supabase Data Integration - Cross-MFE Tests › should display consistent hotel data across Management Portal and Hotel Dashboard" classname="supabase-data-integration.spec.ts" time="12.224">
<failure message="supabase-data-integration.spec.ts:5:3 should display consistent hotel data across Management Portal and Hotel Dashboard" type="FAILURE">
<![CDATA[  [chromium] › supabase-data-integration.spec.ts:5:3 › Supabase Data Integration - Cross-MFE Tests › should display consistent hotel data across Management Portal and Hotel Dashboard 

    TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('table tbody tr') to be visible


       8 |
       9 |     // Wait for hotels to load
    > 10 |     await page.waitForSelector('table tbody tr', { timeout: 10000 })
         |                ^
      11 |
      12 |     // Get first hotel data from Management Portal
      13 |     const firstHotelRow = page.locator('table tbody tr').first()
        at C:\hotel-yedek\hotelexia\tests\e2e\supabase-data-integration.spec.ts:10:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\test-results\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\video.webm]]

[[ATTACHMENT|..\test-results\supabase-data-integration--85e95--Portal-and-Hotel-Dashboard-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should fetch and display live hotel data from Supabase" classname="supabase-data-integration.spec.ts" time="17.12">
<failure message="supabase-data-integration.spec.ts:33:3 should fetch and display live hotel data from Supabase" type="FAILURE">
<![CDATA[  [chromium] › supabase-data-integration.spec.ts:33:3 › Supabase Data Integration - Cross-MFE Tests › should fetch and display live hotel data from Supabase 

    TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.
    Call log:
      - waiting for locator('table tbody tr') to be visible


      35 |
      36 |     // Wait for data to load
    > 37 |     await page.waitForSelector('table tbody tr', { timeout: 15000 })
         |                ^
      38 |
      39 |     // Check that hotels are loaded from Supabase (not dummy data)
      40 |     const hotelRows = page.locator('table tbody tr')
        at C:\hotel-yedek\hotelexia\tests\e2e\supabase-data-integration.spec.ts:37:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\test-results\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\video.webm]]

[[ATTACHMENT|..\test-results\supabase-data-integration--5a4cf-ve-hotel-data-from-Supabase-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should display live room data in Hotel Dashboard" classname="supabase-data-integration.spec.ts" time="5.024">
<failure message="supabase-data-integration.spec.ts:54:3 should display live room data in Hotel Dashboard" type="FAILURE">
<![CDATA[  [chromium] › supabase-data-integration.spec.ts:54:3 › Supabase Data Integration - Cross-MFE Tests › should display live room data in Hotel Dashboard 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      73 |       // If no rooms, check for empty state or loading
      74 |       const emptyState = page.locator('.empty-state, .no-data, .loading')
    > 75 |       expect(await emptyState.count()).toBeGreaterThan(0)
         |                                        ^
      76 |     }
      77 |   })
      78 |
        at C:\hotel-yedek\hotelexia\tests\e2e\supabase-data-integration.spec.ts:75:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\supabase-data-integration--447bc-oom-data-in-Hotel-Dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\supabase-data-integration--447bc-oom-data-in-Hotel-Dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\test-results\supabase-data-integration--447bc-oom-data-in-Hotel-Dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\supabase-data-integration--447bc-oom-data-in-Hotel-Dashboard-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should display live user data in Management Portal" classname="supabase-data-integration.spec.ts" time="5.128">
<failure message="supabase-data-integration.spec.ts:79:3 should display live user data in Management Portal" type="FAILURE">
<![CDATA[  [chromium] › supabase-data-integration.spec.ts:79:3 › Supabase Data Integration - Cross-MFE Tests › should display live user data in Management Portal 

    Error: expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

       97 |       // Check for empty state
       98 |       const emptyState = page.locator('.empty-state, .no-data, .loading')
    >  99 |       expect(await emptyState.count()).toBeGreaterThan(0)
          |                                        ^
      100 |     }
      101 |   })
      102 |
        at C:\hotel-yedek\hotelexia\tests\e2e\supabase-data-integration.spec.ts:99:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\test-results\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\video.webm]]

[[ATTACHMENT|..\test-results\supabase-data-integration--bbac0-r-data-in-Management-Portal-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should display live reservation data in Customer Portal" classname="supabase-data-integration.spec.ts" time="2">
<failure message="supabase-data-integration.spec.ts:103:3 should display live reservation data in Customer Portal" type="FAILURE">
<![CDATA[  [chromium] › supabase-data-integration.spec.ts:103:3 › Supabase Data Integration - Cross-MFE Tests › should display live reservation data in Customer Portal 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      119 |     const hasHotels = await hotelElements.count() > 0
      120 |     
    > 121 |     expect(hasReservations || hasHotels).toBe(true)
          |                                          ^
      122 |     
      123 |     if (hasReservations) {
      124 |       console.log('Reservation data found')
        at C:\hotel-yedek\hotelexia\tests\e2e\supabase-data-integration.spec.ts:121:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\test-results\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\video.webm]]

[[ATTACHMENT|..\test-results\supabase-data-integration--d3f1a-ion-data-in-Customer-Portal-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should handle data loading states properly" classname="supabase-data-integration.spec.ts" time="7.004">
<failure message="supabase-data-integration.spec.ts:130:3 should handle data loading states properly" type="FAILURE">
<![CDATA[  [chromium] › supabase-data-integration.spec.ts:130:3 › Supabase Data Integration - Cross-MFE Tests › should handle data loading states properly 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      145 |     const hasEmptyState = await page.locator('.empty-state, .no-data').count() > 0
      146 |
    > 147 |     expect(hasData || hasEmptyState).toBe(true)
          |                                      ^
      148 |   })
      149 |
      150 |   test('should handle error states gracefully', async ({ page }) => {
        at C:\hotel-yedek\hotelexia\tests\e2e\supabase-data-integration.spec.ts:147:38

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\supabase-data-integration--95156-ata-loading-states-properly-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\supabase-data-integration--95156-ata-loading-states-properly-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\supabase-data-integration--95156-ata-loading-states-properly-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\test-results\supabase-data-integration--95156-ata-loading-states-properly-chromium\test-failed-1.png]]

[[ATTACHMENT|..\test-results\supabase-data-integration--95156-ata-loading-states-properly-chromium\video.webm]]

[[ATTACHMENT|..\test-results\supabase-data-integration--95156-ata-loading-states-properly-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should handle error states gracefully" classname="supabase-data-integration.spec.ts" time="11.413">
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should maintain data consistency during navigation" classname="supabase-data-integration.spec.ts" time="8.348">
</testcase>
<testcase name="Supabase Data Integration - Cross-MFE Tests › should display real-time data updates" classname="supabase-data-integration.spec.ts" time="10.564">
</testcase>
</testsuite>
</testsuites>