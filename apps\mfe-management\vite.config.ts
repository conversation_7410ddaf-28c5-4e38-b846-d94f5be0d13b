import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import federation from '@originjs/vite-plugin-federation'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    federation({
      name: 'mfeManagement',
      filename: 'remoteEntry.js',
      exposes: {
        './Management': './src/App.vue',
        './Dashboard': './src/views/Dashboard.vue',
        './HotelList': './src/views/HotelList.vue',
        './HotelEdit': './src/views/HotelEdit.vue',
        './HotelMfeConfig': './src/views/HotelMfeConfig.vue',
        './UserManagement': './src/views/UserManagement.vue',
        './Settings': './src/views/Settings.vue',
        './Billing': './src/views/Billing.vue',
        './Analytics': './src/views/Analytics.vue',
        './SystemHealth': './src/views/SystemHealth.vue',
        './Announcements': './src/views/Announcements.vue',
        './AuditLog': './src/views/AuditLog.vue',
        './HotelSetupWizard': './src/components/wizards/HotelSetupWizard.vue',
        './ConfigurePlansWizard': './src/views/ConfigurePlansWizard.vue',
        './bootstrap': './src/bootstrap.ts',
        './exports': './src/exports/index.ts'
      },
      shared: {
        vue: { singleton: true, requiredVersion: '^3.4.15' },
        'vue-router': { singleton: true, requiredVersion: '^4.2.5' },
        pinia: { singleton: true, requiredVersion: '^2.1.7' },
        '@hotelexia/shared-supabase-client': { singleton: true },
        '@hotelexia/shared-components': { singleton: true }
      }
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@hotelexia/shared-components': resolve(__dirname, '../../packages/shared-components/src/index.ts'),
      '@hotelexia/shared-supabase-client': resolve(__dirname, '../../packages/shared-supabase-client/src/index.ts')
    }
  },
  server: {
    port: 5001,
    host: true,
    origin: 'http://localhost:5001',
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    },
    fs: {
      allow: ['..']
    }
  },
  build: {
    modulePreload: false,
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      external: []
    }
  },
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
}) 