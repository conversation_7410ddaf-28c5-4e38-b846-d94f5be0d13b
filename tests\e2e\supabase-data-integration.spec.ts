import { test, expect } from '@playwright/test'

test.describe('Supabase Data Integration - Cross-MFE Tests', () => {
  // Test data consistency across MFEs
  test('should handle MFE loading and authentication states', async ({ page, context }) => {
    // Test Management Portal
    await page.goto('http://localhost:5001/hotels')
    await page.waitForTimeout(3000)

    // Should either show login form or hotel data
    const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const hasHotelData = await page.locator('table tbody tr').count() > 0
    const hasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0

    expect(hasLoginForm || hasHotelData || hasContent).toBe(true)
    console.log(`Management Portal: login=${hasLoginForm}, data=${hasHotelData}, content=${hasContent}`)

    // Test Hotel Dashboard
    await page.goto('http://localhost:5002')
    await page.waitForTimeout(3000)

    const hotelHasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const hotelHasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0

    expect(hotelHasLogin || hotelHasContent).toBe(true)
    console.log(`Hotel Dashboard: login=${hotelHasLogin}, content=${hotelHasContent}`)
  })

  test('should handle authentication and data loading in Management Portal', async ({ page }) => {
    await page.goto('http://localhost:5001/hotels')
    await page.waitForTimeout(3000)

    // Check if we're on login page or data page
    const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const hasHotelData = await page.locator('table tbody tr').count() > 0

    if (hasLoginForm) {
      console.log('Management Portal requires authentication')
      // Verify login form is properly structured
      const emailInput = page.locator('input[type="email"], input[name="email"]')
      const passwordInput = page.locator('input[type="password"], input[name="password"]')

      expect(await emailInput.count()).toBeGreaterThan(0)
      expect(await passwordInput.count()).toBeGreaterThan(0)
    } else if (hasHotelData) {
      console.log('Management Portal showing hotel data')
      // Verify hotel data structure
      const firstRow = page.locator('table tbody tr').first()
      const hotelName = await firstRow.locator('td').nth(0).textContent()

      expect(hotelName).toBeTruthy()
    } else {
      // Check for other valid states (loading, empty, etc.)
      const hasValidState = await page.locator('main, .dashboard, .loading, .empty-state').count() > 0
      expect(hasValidState).toBe(true)
    }
  })

  test('should handle Hotel Dashboard room management interface', async ({ page }) => {
    await page.goto('http://localhost:5002/housekeeping/rooms')
    await page.waitForTimeout(3000)

    // Check if we need authentication first
    const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0

    if (hasLoginForm) {
      console.log('Hotel Dashboard requires authentication')
      expect(hasLoginForm).toBe(true)
    } else {
      // Check for room management interface
      const roomElements = page.locator('[data-testid="room-card"], .room-card, table tbody tr')
      const hasRoomInterface = await page.locator('main, .dashboard, .room-management').count() > 0
      const hasEmptyState = await page.locator('.empty-state, .no-data, .loading').count() > 0

      // Should have some form of room interface or valid state
      expect(await roomElements.count() > 0 || hasRoomInterface || hasEmptyState).toBe(true)

      if (await roomElements.count() > 0) {
        console.log(`Found ${await roomElements.count()} room elements`)
      } else {
        console.log('Room interface in loading or empty state')
      }
    }
  })

  test('should handle user management interface in Management Portal', async ({ page }) => {
    await page.goto('http://localhost:5001/users')
    await page.waitForTimeout(3000)

    // Check authentication state
    const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0

    if (hasLoginForm) {
      console.log('User management requires authentication')
      expect(hasLoginForm).toBe(true)
    } else {
      // Check for user management interface
      const userElements = page.locator('table tbody tr, .user-card, [data-testid="user-item"]')
      const hasUserInterface = await page.locator('main, .dashboard, .user-management').count() > 0
      const hasValidState = await page.locator('.empty-state, .no-data, .loading').count() > 0

      // Should have some form of user interface or valid state
      expect(await userElements.count() > 0 || hasUserInterface || hasValidState).toBe(true)

      if (await userElements.count() > 0) {
        console.log(`Found ${await userElements.count()} user elements`)
      } else {
        console.log('User interface in loading or empty state')
      }
    }
  })

  test('should handle Customer Portal interface', async ({ page }) => {
    await page.goto('http://localhost:3001')
    await page.waitForTimeout(3000)

    // Check for customer portal interface
    const hasContent = await page.locator('main, .portal, .customer-portal').count() > 0
    const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const hasBookingInterface = await page.locator('.hotel-card, .booking-card, [data-testid="hotel"]').count() > 0

    // Should have some form of customer interface
    expect(hasContent || hasLoginForm || hasBookingInterface).toBe(true)

    if (hasBookingInterface) {
      console.log('Customer Portal showing booking interface')
    } else if (hasLoginForm) {
      console.log('Customer Portal showing login form')
    } else if (hasContent) {
      console.log('Customer Portal showing content')
    }

    // Navigate to reservations if available
    const reservationsLink = page.locator('a[href*="reservation"], a[href*="booking"], button:has-text("Rezervasyon")')
    if (await reservationsLink.count() > 0) {
      await reservationsLink.first().click()
      await page.waitForTimeout(2000)

      // Should navigate successfully
      const hasReservationInterface = await page.locator('.reservation-card, .booking-card, table tbody tr').count() > 0 ||
                                     await page.locator('main, .portal').count() > 0

      expect(hasReservationInterface).toBe(true)
    }
  })

  test('should handle loading states across MFEs', async ({ page }) => {
    const testUrls = [
      'http://localhost:5001/hotels',
      'http://localhost:5002/housekeeping/rooms',
      'http://localhost:3001'
    ]

    for (const url of testUrls) {
      await page.goto(url)
      await page.waitForTimeout(3000)

      // Should have some valid state after loading
      const hasContent = await page.locator('main, .dashboard, .portal').count() > 0
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      const hasData = await page.locator('table tbody tr, .card, .item').count() > 0
      const hasValidState = await page.locator('.loading, .empty-state, .no-data').count() > 0

      expect(hasContent || hasLoginForm || hasData || hasValidState).toBe(true)
      console.log(`${url}: content=${hasContent}, login=${hasLoginForm}, data=${hasData}, state=${hasValidState}`)
    }
  })

  test('should handle error states and console errors gracefully', async ({ page }) => {
    // Monitor console errors
    const errors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })

    // Test error handling in different MFEs
    const testUrls = [
      'http://localhost:5001/hotels',
      'http://localhost:5002/housekeeping/rooms',
      'http://localhost:3001'
    ]

    for (const url of testUrls) {
      await page.goto(url)
      await page.waitForTimeout(3000)

      // Should handle pages gracefully (login, content, or error boundaries)
      const hasValidState = await page.locator('main, .dashboard, .portal, input[type="email"], .error-boundary').count() > 0
      expect(hasValidState).toBe(true)

      // Filter and log critical errors
      const criticalErrors = errors.filter(error =>
        !error.includes('favicon') &&
        !error.includes('404') &&
        !error.includes('WebSocket') &&
        !error.includes('net::ERR_FAILED') &&
        !error.includes('Failed to load resource')
      )

      if (criticalErrors.length > 0) {
        console.warn(`Critical errors found on ${url}:`, criticalErrors.slice(0, 3))
      }
    }
  })

  test('should maintain data consistency during navigation', async ({ page }) => {
    // Start from Management Portal
    await page.goto('http://localhost:5001/hotels')
    await page.waitForTimeout(2000)

    // Get hotel count
    const initialHotelCount = await page.locator('table tbody tr').count()

    // Navigate to dashboard and back
    await page.goto('http://localhost:5001/dashboard')
    await page.waitForTimeout(2000)

    await page.goto('http://localhost:5001/hotels')
    await page.waitForTimeout(2000)

    // Hotel count should be consistent
    const finalHotelCount = await page.locator('table tbody tr').count()
    expect(finalHotelCount).toBe(initialHotelCount)
  })

  test('should display real-time data updates', async ({ page, context }) => {
    // This test checks if real-time updates work
    await page.goto('http://localhost:5002/housekeeping/rooms')
    await page.waitForTimeout(3000)
    
    // Get initial room count
    const initialRoomCount = await page.locator('[data-testid="room-card"], .room-card, table tbody tr').count()
    
    // Wait for potential real-time updates
    await page.waitForTimeout(5000)
    
    // Check if data is still consistent
    const finalRoomCount = await page.locator('[data-testid="room-card"], .room-card, table tbody tr').count()
    
    // Count should be stable (no flickering or inconsistent updates)
    expect(Math.abs(finalRoomCount - initialRoomCount)).toBeLessThanOrEqual(1)
  })
})
