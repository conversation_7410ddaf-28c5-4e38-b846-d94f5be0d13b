// Export all exposed components for Module Federation
export { default as Dashboard } from '../views/Dashboard.vue'
export { default as HotelList } from '../views/HotelList.vue'
export { default as HotelEdit } from '../views/HotelEdit.vue'
export { default as HotelMfeConfig } from '../views/HotelMfeConfig.vue'
export { default as Settings } from '../views/Settings.vue'
export { default as Billing } from '../views/Billing.vue'
export { default as Analytics } from '../views/Analytics.vue'
export { default as SystemHealth } from '../views/SystemHealth.vue'
export { default as Announcements } from '../views/Announcements.vue'
export { default as AuditLog } from '../views/AuditLog.vue'
export { default as ConfigurePlansWizard } from '../views/ConfigurePlansWizard.vue'

// Export wizards
export { default as HotelSetupWizard } from '../components/wizards/HotelSetupWizard.vue'

// Export stores for shared state management
export { useAuthStore } from '../stores/authStore'
export { useManagementStore } from '../stores/managementStore'

// Export router for integration
export { default as router } from '../router'

// Export bootstrap function
export { bootstrap } from '../bootstrap'

// Export types if any
export type { User, Hotel, MfeConfig } from '../types'
