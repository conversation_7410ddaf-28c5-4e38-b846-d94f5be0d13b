<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card overflow-hidden">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="text-lg font-bold text-navy-700 dark:text-white">{{ title }}</h4>
          <p v-if="subtitle" class="text-sm text-gray-600 dark:text-gray-400">{{ subtitle }}</p>
        </div>
        <div v-if="$slots.actions" class="flex items-center space-x-2">
          <slot name="actions" />
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-navy-900">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :class="[
                'px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider',
                column.align === 'center' && 'text-center',
                column.align === 'right' && 'text-right'
              ]"
            >
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <tr
            v-for="(row, index) in data"
            :key="index"
            class="hover:bg-gray-50 dark:hover:bg-navy-900/50 transition-colors"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :class="[
                'px-6 py-4 whitespace-nowrap text-sm',
                column.align === 'center' && 'text-center',
                column.align === 'right' && 'text-right'
              ]"
            >
              <div v-if="column.type === 'status'" class="flex items-center">
                <span :class="[
                  'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  getStatusColor(row[column.key])
                ]">
                  {{ row[column.key] }}
                </span>
              </div>
              <div v-else-if="column.type === 'currency'" class="text-navy-700 dark:text-white font-medium">
                {{ formatCurrency(row[column.key]) }}
              </div>
              <div v-else-if="column.type === 'percentage'" class="text-navy-700 dark:text-white font-medium">
                {{ row[column.key] }}%
              </div>
              <div v-else-if="column.type === 'trend'" class="flex items-center">
                <span :class="[
                  'text-sm font-medium',
                  row[column.key] >= 0 ? 'text-green-500' : 'text-red-500'
                ]">
                  {{ row[column.key] >= 0 ? '+' : '' }}{{ row[column.key] }}%
                </span>
              </div>
              <div v-else class="text-navy-700 dark:text-white">
                {{ row[column.key] }}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div v-if="!data || data.length === 0" class="px-6 py-12 text-center">
      <div class="text-gray-500 dark:text-gray-400">
        <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-sm">{{ emptyMessage || 'Veri bulunamadı' }}</p>
      </div>
    </div>

    <!-- Footer -->
    <div v-if="$slots.footer" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string
  label: string
  type?: 'text' | 'currency' | 'percentage' | 'status' | 'trend'
  align?: 'left' | 'center' | 'right'
}

interface Props {
  title: string
  subtitle?: string
  columns: Column[]
  data: Record<string, any>[]
  emptyMessage?: string
}

defineProps<Props>()

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(value)
}

const getStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    'active': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
    'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'high': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'low': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
  }
  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
}
</script>

<style scoped>
/* Data table specific styles */
</style>
