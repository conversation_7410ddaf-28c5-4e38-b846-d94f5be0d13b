<template>
  <div class="space-y-6">
    <!-- Header with Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
            <PaperAirplaneIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Toplam Gönderim</p>
            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">15</p>
          </div>
        </div>
      </div>

      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
            <CheckCircleIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-green-600 dark:text-green-400">Başarılı</p>
            <p class="text-2xl font-bold text-green-900 dark:text-green-100">12</p>
          </div>
        </div>
      </div>

      <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-800 rounded-lg">
            <ClockIcon class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Beklemede</p>
            <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">2</p>
          </div>
        </div>
      </div>

      <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 dark:bg-red-800 rounded-lg">
            <ExclamationTriangleIcon class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-red-600 dark:text-red-400">Başarısız</p>
            <p class="text-2xl font-bold text-red-900 dark:text-red-100">1</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-navy-800 rounded-lg shadow-card p-4">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex-1 min-w-[200px]">
          <input
            type="text"
            placeholder="Başlık veya mesaj ara..."
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
          />
        </div>
        <select
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="">Tüm Durumlar</option>
          <option value="sent">Gönderildi</option>
          <option value="pending">Beklemede</option>
          <option value="failed">Başarısız</option>
        </select>
        <select
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="">Tüm Kanallar</option>
          <option value="in-app">Uygulama İçi</option>
          <option value="sms">SMS</option>
          <option value="email">E-posta</option>
          <option value="whatsapp">WhatsApp</option>
        </select>
      </div>
    </div>

    <!-- Notifications Table -->
    <div class="bg-white dark:bg-navy-800 rounded-lg shadow-card overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-navy-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Bildirim
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Hedef Kitle
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Kanallar
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Alıcı Sayısı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Gönderim Tarihi
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr class="hover:bg-gray-50 dark:hover:bg-navy-700">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">Yeni Menü Duyurusu</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">Yeni lezzetlerimizi keşfedin! Bugün itibariyle menümüze eklenen...</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">Tüm Misafirler</div>
              </td>
              <td class="px-6 py-4">
                <div class="flex flex-wrap gap-1">
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    <BellIcon class="w-3 h-3 mr-1" />
                    Uygulama
                  </span>
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <DevicePhoneMobileIcon class="w-3 h-3 mr-1" />
                    SMS
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">45</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  <div class="w-2 h-2 rounded-full mr-1 bg-green-400"></div>
                  Gönderildi
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                15 Oca 2024, 14:30
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300">
                    <EyeIcon class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
            <!-- More sample rows can be added here -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  PaperAirplaneIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  BellIcon,
  DevicePhoneMobileIcon
} from '@heroicons/vue/24/outline'
</script> 