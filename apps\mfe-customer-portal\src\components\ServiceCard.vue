<template>
  <div 
    class="bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
    @click="$emit('click')"
  >
    <!-- Service Image -->
    <div class="relative h-32 bg-gradient-to-br from-gray-200 to-gray-300">
      <img
        :src="safeImageUrl"
        :alt="title"
        class="w-full h-full object-cover"
        @error="handleImageError"
      />
      <div class="absolute inset-0 bg-black/20"></div>
      <div class="absolute top-2 right-2">
        <span class="text-2xl">{{ icon }}</span>
      </div>
    </div>

    <!-- Service Content -->
    <div class="p-4">
      <h3 class="text-lg font-semibold text-navy-700 mb-2">{{ title }}</h3>
      <p class="text-sm text-gray-600 mb-3">{{ description }}</p>
      
      <!-- Additional Info -->
      <div v-if="additionalInfo" class="flex items-center text-xs text-gray-500">
        <span class="mr-2">ℹ️</span>
        <span>{{ additionalInfo }}</span>
      </div>
    </div>

    <!-- Action Button -->
    <div class="px-4 pb-4">
      <button 
        class="w-full bg-brand-500 hover:bg-brand-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
      >
        {{ buttonText || 'Başlat' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getSafeImageUrl, handleImageError as handleImgError } from '@/utils/imageUtils'

interface Props {
  title: string
  description: string
  icon: string
  imageUrl?: string
  buttonText?: string
  additionalInfo?: string
}

const props = withDefaults(defineProps<Props>(), {
  imageUrl: '',
  buttonText: 'Başlat',
  additionalInfo: ''
})

defineEmits<{
  click: []
}>()

// Generate safe image URL
const safeImageUrl = computed(() => {
  return getSafeImageUrl(props.imageUrl, 'service')
})

function handleImageError(event: Event) {
  handleImgError(event, 'service')
}
</script> 