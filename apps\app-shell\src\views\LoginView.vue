<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full">
      <!-- Header Section -->
      <div class="text-center mb-8">
        <div class="mx-auto h-16 w-16 bg-white rounded-2xl flex items-center justify-center shadow-2xl mb-6">
          <svg class="h-8 w-8 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-bold text-white mb-2">
          {{ getPortalTitle() }}
        </h1>
        <p class="text-purple-200 text-lg">
          {{ getPortalDescription() }}
        </p>
      </div>

      <!-- Login Card -->
      <div class="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8">
        <form class="space-y-6" @submit.prevent="handleLogin">
          <!-- Email Field -->
          <div>
            <label for="email-address" class="block text-sm font-medium text-white mb-2">
              E-posta Adresi
            </label>
            <div class="relative">
              <input
                id="email-address"
                v-model="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                :disabled="authStore.isLoading"
                class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200 disabled:opacity-50"
                placeholder="<EMAIL>"
              />
              <svg class="absolute right-3 top-3 h-5 w-5 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            </div>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-white mb-2">
              Şifre
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="password"
                name="password"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="current-password"
                required
                :disabled="authStore.isLoading"
                class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200 disabled:opacity-50 pr-12"
                placeholder="••••••••"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                :disabled="authStore.isLoading"
                class="absolute right-3 top-3 text-white/40 hover:text-white/60 transition-colors"
              >
                <svg v-if="!showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                name="remember-me"
                type="checkbox"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-white/20 rounded bg-white/10"
              />
              <label for="remember-me" class="ml-2 block text-sm text-white/80">
                Beni hatırla
              </label>
            </div>

            <div class="text-sm">
              <a href="#" class="font-medium text-purple-300 hover:text-purple-200 transition-colors">
                Şifrenizi mi unuttunuz?
              </a>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="authStore.isLoading"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] shadow-lg"
          >
            <span v-if="authStore.isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            <span v-else class="absolute left-0 inset-y-0 flex items-center pl-3">
              <LockClosedIcon class="h-5 w-5 text-white/80 group-hover:text-white" aria-hidden="true" />
            </span>
            {{ authStore.isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap' }}
          </button>

          <!-- Error Message -->
          <div v-if="authStore.error" class="p-4 bg-red-500/20 border border-red-500/30 rounded-xl backdrop-blur-sm">
            <div class="flex">
              <div class="flex-shrink-0">
                <ExclamationTriangleIcon class="h-5 w-5 text-red-300" aria-hidden="true" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-200">
                  Giriş Hatası
                </h3>
                <div class="mt-2 text-sm text-red-100">
                  <p>{{ authStore.error }}</p>
                </div>
              </div>
            </div>
          </div>
        </form>

        <!-- Portal Info -->
        <div class="mt-8 text-center">
          <p class="text-white/60 text-sm">
            {{ getPortalInfo() }}
          </p>
        </div>
      </div>

      <!-- Loading Overlay -->
      <LoadingOverlay
        :show="authStore.isLoading"
        title="Giriş Yapılıyor"
        message="Bilgileriniz doğrulanıyor, lütfen bekleyiniz..."
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { LockClosedIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/solid'
import { useAuthStore } from '../stores/authStore'
import LoadingOverlay from '../components/LoadingOverlay.vue'

// Props and reactive data
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const rememberMe = ref(false)
const showPassword = ref(false)

// Portal detection based on current route
const currentPortal = ref('')

onMounted(() => {
  // Detect portal from route path
  const path = route.path
  if (path.includes('/guest/')) {
    currentPortal.value = 'guest'
  } else if (path.includes('/hotel/')) {
    currentPortal.value = 'hotel'
  } else if (path.includes('/management/')) {
    currentPortal.value = 'management'
  } else {
    currentPortal.value = 'guest' // default
  }
})

const getPortalTitle = () => {
  switch (currentPortal.value) {
    case 'guest':
      return 'Misafir Girişi'
    case 'hotel':
      return 'Otel Personeli Girişi'
    case 'management':
      return 'Süper Admin Girişi'
    default:
      return 'Hotelexia Girişi'
  }
}

const getPortalDescription = () => {
  switch (currentPortal.value) {
    case 'guest':
      return 'Rezervasyon numaranız ile giriş yapın'
    case 'hotel':
      return 'Otel hesabınız ile giriş yapın'
    case 'management':
      return 'Platform yönetici hesabınız ile giriş yapın'
    default:
      return 'Hesabınız ile giriş yapın'
  }
}

const getPortalInfo = () => {
  switch (currentPortal.value) {
    case 'guest':
      return 'Misafir portalına hoş geldiniz'
    case 'hotel':
      return 'Otel yönetim sistemine hoş geldiniz'
    case 'management':
      return 'Hotelexia yönetim paneline hoş geldiniz'
    default:
      return 'Hotelexia\'ya hoş geldiniz'
  }
}

const handleLogin = async () => {
  if (!email.value || !password.value) {
    return
  }

  const success = await authStore.login(email.value, password.value)
  
  if (success && authStore.profile && authStore.profile.role) {
    // Validate role for portal access
    if (!validatePortalAccess(authStore.profile.role)) {
      await authStore.signOut()
      return
    }

    // Check for stored redirect path first
    const redirectPath = sessionStorage.getItem('hotelexia_redirect_after_login')
    if (redirectPath) {
      sessionStorage.removeItem('hotelexia_redirect_after_login')
      router.push(redirectPath)
      return
    }

    // Redirect based on role
    redirectToPortal(authStore.profile.role)
  }
}

const validatePortalAccess = (role: string): boolean => {
  switch (currentPortal.value) {
    case 'guest':
      return role === 'GUEST'
    case 'hotel':
      return role === 'HOTEL_ADMIN' || role === 'STAFF'
    case 'management':
      return role === 'SUPER_ADMIN'
    default:
      return false
  }
}

const redirectToPortal = (role: string) => {
  switch (role) {
    case 'SUPER_ADMIN':
      router.push({ name: 'management-dashboard' })
      break
    case 'HOTEL_ADMIN':
    case 'STAFF':
      router.push({ name: 'hotel-dashboard' })
      break
    case 'GUEST':
      router.push({ name: 'guest-dashboard' })
      break
    default:
      router.push('/')
  }
}
</script> 