import { test, expect } from '@playwright/test'

test.describe('MFE-Specific Functionality Tests', () => {
  test.describe('Management Portal Functionality', () => {
    test('should display and interact with hotel management features', async ({ page }) => {
      await page.goto('http://localhost:3000/management/hotels')
      await page.waitForTimeout(3000)
      
      // Check for hotel list functionality
      const hotelTable = page.locator('table')
      const addButton = page.locator('button:has-text("Ekle"), button:has-text("Add"), a[href*="create"]')
      const searchInput = page.locator('input[placeholder*="ara"], input[type="search"]')
      
      if (await hotelTable.isVisible()) {
        expect(await hotelTable.isVisible()).toBe(true)
        
        // Check for action buttons
        const editButtons = page.locator('button:has-text("Düzenle"), button:has-text("Edit"), a[href*="edit"]')
        const deleteButtons = page.locator('button:has-text("Sil"), button:has-text("Delete")')
        
        if (await editButtons.count() > 0) {
          expect(await editButtons.first().isVisible()).toBe(true)
        }
      }
      
      // Test search functionality if available
      if (await searchInput.isVisible()) {
        await searchInput.fill('test')
        await page.waitForTimeout(1000)
        // Should filter results or show no results
        const hasResults = await page.locator('table tbody tr').count() >= 0
        expect(hasResults).toBe(true)
      }
    })

    test('should handle user management features', async ({ page }) => {
      await page.goto('http://localhost:3000/management/users')
      await page.waitForTimeout(3000)
      
      // Check for user management interface
      const userList = page.locator('table, .user-list, [data-testid="user-list"]')
      const addUserButton = page.locator('button:has-text("Kullanıcı"), button:has-text("User"), button:has-text("Ekle")')
      
      // Should have user management interface
      const hasUserInterface = await userList.count() > 0 || await addUserButton.count() > 0
      expect(hasUserInterface).toBe(true)
      
      // Check for role management
      const roleElements = page.locator('text=/role/i, text=/rol/i, .role, [data-testid="role"]')
      if (await roleElements.count() > 0) {
        console.log('Role management features found')
      }
    })

    test('should display platform statistics and analytics', async ({ page }) => {
      await page.goto('http://localhost:3000/management/dashboard')
      await page.waitForTimeout(3000)
      
      // Check for dashboard statistics
      const statsCards = page.locator('.stat-card, .metric-card, [data-testid="stat"]')
      const charts = page.locator('canvas, .chart, [data-testid="chart"]')
      const summaryNumbers = page.locator('.number, .count, .total')
      
      // Should have some form of statistics display
      const hasStats = await statsCards.count() > 0 || 
                     await charts.count() > 0 || 
                     await summaryNumbers.count() > 0
      
      expect(hasStats).toBe(true)
      console.log(`Dashboard stats: cards=${await statsCards.count()}, charts=${await charts.count()}`)
    })
  })

  test.describe('Hotel Dashboard Functionality', () => {
    test('should display housekeeping room management', async ({ page }) => {
      await page.goto('http://localhost:5002/housekeeping/rooms')
      await page.waitForTimeout(3000)
      
      // Check for room management interface
      const roomGrid = page.locator('.room-grid, [data-testid="room-grid"]')
      const roomCards = page.locator('.room-card, [data-testid="room-card"]')
      const roomTable = page.locator('table tbody tr')
      const viewToggle = page.locator('button:has-text("Grid"), button:has-text("List")')
      
      // Should have room display in some format
      const hasRoomDisplay = await roomGrid.count() > 0 || 
                            await roomCards.count() > 0 || 
                            await roomTable.count() > 0
      
      expect(hasRoomDisplay).toBe(true)
      
      // Test view toggle if available
      if (await viewToggle.count() > 0) {
        await viewToggle.first().click()
        await page.waitForTimeout(1000)
        // Should still show rooms after view change
        const stillHasRooms = await roomGrid.count() > 0 || 
                             await roomCards.count() > 0 || 
                             await roomTable.count() > 0
        expect(stillHasRooms).toBe(true)
      }
    })

    test('should handle maintenance task management', async ({ page }) => {
      await page.goto('http://localhost:5002/maintenance/tasks')
      await page.waitForTimeout(3000)
      
      // Check for task management interface
      const taskList = page.locator('table, .task-list, [data-testid="task-list"]')
      const addTaskButton = page.locator('button:has-text("Görev"), button:has-text("Task"), button:has-text("Ekle")')
      const taskCards = page.locator('.task-card, [data-testid="task-card"]')
      
      // Should have task management interface
      const hasTaskInterface = await taskList.count() > 0 || 
                              await addTaskButton.count() > 0 || 
                              await taskCards.count() > 0
      
      expect(hasTaskInterface).toBe(true)
      
      // Check for task status indicators
      const statusElements = page.locator('.status, .badge, [data-testid="status"]')
      if (await statusElements.count() > 0) {
        console.log('Task status indicators found')
      }
    })

    test('should display operational dashboard metrics', async ({ page }) => {
      await page.goto('http://localhost:5002/dashboard')
      await page.waitForTimeout(3000)
      
      // Check for operational metrics
      const metricCards = page.locator('.metric, .stat, .kpi, [data-testid="metric"]')
      const occupancyInfo = page.locator('text=/occupancy/i, text=/doluluk/i')
      const revenueInfo = page.locator('text=/revenue/i, text=/gelir/i')
      const taskSummary = page.locator('text=/task/i, text=/görev/i')
      
      // Should have operational information
      const hasOperationalInfo = await metricCards.count() > 0 || 
                                 await occupancyInfo.count() > 0 || 
                                 await revenueInfo.count() > 0 || 
                                 await taskSummary.count() > 0
      
      expect(hasOperationalInfo).toBe(true)
      console.log(`Operational metrics found: ${await metricCards.count()} metric cards`)
    })

    test('should handle real-time notifications', async ({ page }) => {
      await page.goto('http://localhost:5002')
      await page.waitForTimeout(3000)
      
      // Check for notification system
      const notificationBell = page.locator('.notification-bell, [data-testid="notifications"]')
      const notificationPanel = page.locator('.notification-panel, .notifications')
      const alertBadge = page.locator('.badge, .alert-count')
      
      // Should have notification system
      const hasNotifications = await notificationBell.count() > 0 || 
                              await notificationPanel.count() > 0 || 
                              await alertBadge.count() > 0
      
      if (hasNotifications) {
        console.log('Notification system found')
        
        // Test notification interaction if available
        if (await notificationBell.isVisible()) {
          await notificationBell.click()
          await page.waitForTimeout(1000)
          
          // Should show notification panel or dropdown
          const panelVisible = await page.locator('.notification-dropdown, .notification-list').isVisible()
          if (panelVisible) {
            console.log('Notification panel interaction working')
          }
        }
      }
    })
  })

  test.describe('Customer Portal Functionality', () => {
    test('should display hotel selection and booking interface', async ({ page }) => {
      await page.goto('http://localhost:3001')
      await page.waitForTimeout(3000)
      
      // Check for hotel selection
      const hotelCards = page.locator('.hotel-card, [data-testid="hotel-card"]')
      const hotelList = page.locator('.hotel-list, table')
      const bookingButton = page.locator('button:has-text("Rezervasyon"), button:has-text("Book")')
      
      // Should have hotel selection interface
      const hasHotelSelection = await hotelCards.count() > 0 || 
                               await hotelList.count() > 0 || 
                               await bookingButton.count() > 0
      
      expect(hasHotelSelection).toBe(true)
      
      // Test hotel selection if available
      if (await hotelCards.count() > 0) {
        await hotelCards.first().click()
        await page.waitForTimeout(2000)
        
        // Should navigate to booking or hotel details
        const hasBookingInterface = await page.locator('input[type="date"], .date-picker, .room-selection').count() > 0
        const hasHotelDetails = await page.locator('.hotel-details, .hotel-info').count() > 0
        
        expect(hasBookingInterface || hasHotelDetails).toBe(true)
      }
    })

    test('should handle room service ordering', async ({ page }) => {
      await page.goto('http://localhost:3001')
      await page.waitForTimeout(2000)
      
      // Look for room service link
      const roomServiceLink = page.locator('a[href*="room-service"], a[href*="service"], button:has-text("Room Service")')
      
      if (await roomServiceLink.count() > 0) {
        await roomServiceLink.first().click()
        await page.waitForTimeout(2000)
        
        // Check for menu interface
        const menuItems = page.locator('.menu-item, .food-item, [data-testid="menu-item"]')
        const categories = page.locator('.category, .menu-category')
        const addToCartButton = page.locator('button:has-text("Sepet"), button:has-text("Cart"), button:has-text("Add")')
        
        // Should have menu interface
        const hasMenuInterface = await menuItems.count() > 0 || 
                                await categories.count() > 0 || 
                                await addToCartButton.count() > 0
        
        expect(hasMenuInterface).toBe(true)
        console.log(`Menu interface: items=${await menuItems.count()}, categories=${await categories.count()}`)
      }
    })

    test('should display guest reservations and history', async ({ page }) => {
      await page.goto('http://localhost:3001')
      await page.waitForTimeout(2000)
      
      // Look for reservations link
      const reservationsLink = page.locator('a[href*="reservation"], a[href*="booking"], button:has-text("Rezervasyon")')
      
      if (await reservationsLink.count() > 0) {
        await reservationsLink.first().click()
        await page.waitForTimeout(2000)
        
        // Check for reservations interface
        const reservationCards = page.locator('.reservation-card, [data-testid="reservation"]')
        const reservationTable = page.locator('table tbody tr')
        const emptyState = page.locator('.empty-state, .no-reservations')
        
        // Should have reservations interface or empty state
        const hasReservationInterface = await reservationCards.count() > 0 || 
                                       await reservationTable.count() > 0 || 
                                       await emptyState.count() > 0
        
        expect(hasReservationInterface).toBe(true)
        console.log(`Reservations: cards=${await reservationCards.count()}, table rows=${await reservationTable.count()}`)
      }
    })

    test('should handle guest authentication flow', async ({ page }) => {
      await page.goto('http://localhost:3001')
      await page.waitForTimeout(2000)
      
      // Look for guest login
      const guestLoginButton = page.locator('button:has-text("Giriş"), button:has-text("Login"), a[href*="login"]')
      
      if (await guestLoginButton.count() > 0) {
        await guestLoginButton.first().click()
        await page.waitForTimeout(2000)
        
        // Check for guest authentication form
        const reservationInput = page.locator('input[placeholder*="rezervasyon"], input[name*="reservation"]')
        const surnameInput = page.locator('input[placeholder*="soyad"], input[name*="surname"]')
        const emailInput = page.locator('input[type="email"]')
        
        // Should have guest authentication form
        const hasGuestAuth = await reservationInput.count() > 0 || 
                            await surnameInput.count() > 0 || 
                            await emailInput.count() > 0
        
        expect(hasGuestAuth).toBe(true)
        console.log('Guest authentication form found')
      }
    })
  })

  test.describe('Cross-MFE Navigation', () => {
    test('should handle navigation between MFEs', async ({ page }) => {
      // Test navigation from App Shell
      await page.goto('http://localhost:3000')
      await page.waitForTimeout(2000)
      
      // Look for navigation links to other MFEs
      const managementLink = page.locator('a[href*="management"], button:has-text("Management")')
      const hotelLink = page.locator('a[href*="hotel"], button:has-text("Hotel")')
      const customerLink = page.locator('a[href*="customer"], button:has-text("Customer")')
      
      // Test Management Portal navigation
      if (await managementLink.count() > 0) {
        await managementLink.first().click()
        await page.waitForTimeout(3000)
        
        const hasManagementContent = await page.locator('.management, [data-testid="management"]').count() > 0 ||
                                     page.url().includes('management')
        
        if (hasManagementContent) {
          console.log('Management Portal navigation successful')
        }
      }
      
      // Test direct MFE access
      const directMFETests = [
        'http://localhost:5002',
        'http://localhost:3001'
      ]
      
      for (const url of directMFETests) {
        await page.goto(url)
        await page.waitForTimeout(3000)
        
        // Should load successfully
        const hasContent = await page.locator('main, .app, [data-testid="content"]').count() > 0
        const hasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
        
        expect(hasContent || hasLogin).toBe(true)
        console.log(`Direct access to ${url}: content=${hasContent}, login=${hasLogin}`)
      }
    })
  })
})
