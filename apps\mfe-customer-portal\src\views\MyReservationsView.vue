<template>
  <div class="p-8">
    <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Rezervasyonlarım</h1>
    <p class="text-gray-600 dark:text-gray-400 mt-1">Geçmiş ve gelecek tüm rezervasyonlarınız.</p>

    <div v-if="customerDataStore.reservationsLoading" class="text-center py-10">
      <p>Rezervasyonlar yükleniyor...</p>
    </div>

    <div v-if="customerDataStore.reservationsError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-6" role="alert">
      <strong class="font-bold">Hata!</strong>
      <span class="block sm:inline">{{ customerDataStore.reservationsError }}</span>
    </div>

    <div v-if="!customerDataStore.reservationsLoading && !customerDataStore.reservationsError && customerDataStore.reservations.length === 0" class="text-center py-10 mt-6">
        <p class="text-gray-500">Hen<PERSON>z bir rezervasyonunuz bulunmamaktadır.</p>
    </div>

    <div v-if="!customerDataStore.reservationsLoading && !customerDataStore.reservationsError && customerDataStore.reservations.length > 0" class="space-y-4 mt-6">
      <div v-for="reservation in customerDataStore.reservations" :key="reservation.id" class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
        <div class="flex justify-between items-start">
            <div>
                <h3 class="text-xl font-bold text-navy-700 dark:text-white">Otel: {{ reservation.hotelName }}</h3>
                <p class="text-gray-600 dark:text-gray-400">Oda: {{ reservation.roomNumber }} - {{ reservation.roomType }}</p>
                <p class="mt-2">
                    <span class="font-semibold">Giriş:</span> {{ formatDate(reservation.checkInDate) }}
                    <span class="font-semibold ml-4">Çıkış:</span> {{ formatDate(reservation.checkOutDate) }}
                </p>
            </div>
            <div class="text-right">
                <p class="text-lg font-bold text-brand-500">{{ reservation.totalPrice }} TL</p>
                <p :class="['font-semibold', reservation.status === 'confirmed' ? 'text-green-500' : 'text-yellow-500']">{{ reservation.status.toUpperCase() }}</p>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useCustomerDataStore } from '@/stores/customerDataStore';

const customerDataStore = useCustomerDataStore();

const formatDate = (dateString: string) => {
    if(!dateString) return ''
    return new Date(dateString).toLocaleDateString('tr-TR');
}

onMounted(async () => {
  await customerDataStore.fetchReservations();
});
</script> 