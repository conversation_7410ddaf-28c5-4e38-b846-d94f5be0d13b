import { ref, computed, watch } from 'vue'

export interface LoadingStateOptions {
  /**
   * Initial loading state
   */
  initialLoading?: boolean
  
  /**
   * Minimum loading time in milliseconds to prevent flashing
   */
  minLoadingTime?: number
  
  /**
   * Maximum loading time before showing timeout warning
   */
  maxLoadingTime?: number
  
  /**
   * Enable automatic timeout handling
   */
  enableTimeout?: boolean
  
  /**
   * Custom timeout message
   */
  timeoutMessage?: string
  
  /**
   * Enable loading state persistence across component remounts
   */
  persistent?: boolean
  
  /**
   * Unique key for persistent loading state
   */
  persistentKey?: string
}

export interface LoadingOperation {
  id: string
  name: string
  startTime: number
  timeout?: number
}

export function useLoadingState(options: LoadingStateOptions = {}) {
  const {
    initialLoading = false,
    minLoadingTime = 300,
    maxLoadingTime = 30000,
    enableTimeout = true,
    timeoutMessage = 'Operation is taking longer than expected...',
    persistent = false,
    persistentKey = 'default'
  } = options

  // State
  const isLoading = ref(initialLoading)
  const loadingMessage = ref('')
  const loadingProgress = ref(0)
  const hasTimedOut = ref(false)
  const activeOperations = ref<Map<string, LoadingOperation>>(new Map())
  
  // Timers
  let minLoadingTimer: NodeJS.Timeout | null = null
  let timeoutTimer: NodeJS.Timeout | null = null
  let progressTimer: NodeJS.Timeout | null = null

  // Computed
  const isLoadingWithMinTime = computed(() => {
    return isLoading.value || minLoadingTimer !== null
  })

  const operationCount = computed(() => activeOperations.value.size)

  const longestRunningOperation = computed(() => {
    let longest: LoadingOperation | null = null
    let maxDuration = 0

    for (const operation of activeOperations.value.values()) {
      const duration = Date.now() - operation.startTime
      if (duration > maxDuration) {
        maxDuration = duration
        longest = operation
      }
    }

    return longest
  })

  // Load persistent state
  if (persistent && persistentKey) {
    try {
      const saved = localStorage.getItem(`loading-state-${persistentKey}`)
      if (saved) {
        const state = JSON.parse(saved)
        isLoading.value = state.isLoading || false
        loadingMessage.value = state.loadingMessage || ''
      }
    } catch (error) {
      console.warn('Failed to load persistent loading state:', error)
    }
  }

  // Save persistent state
  const savePersistentState = () => {
    if (persistent && persistentKey) {
      try {
        localStorage.setItem(`loading-state-${persistentKey}`, JSON.stringify({
          isLoading: isLoading.value,
          loadingMessage: loadingMessage.value
        }))
      } catch (error) {
        console.warn('Failed to save persistent loading state:', error)
      }
    }
  }

  // Watch for state changes to save persistent state
  if (persistent) {
    watch([isLoading, loadingMessage], savePersistentState)
  }

  /**
   * Start loading with optional message and operation tracking
   */
  const startLoading = (message?: string, operationId?: string, operationName?: string) => {
    // Clear any existing timers
    clearTimers()

    // Set loading state
    isLoading.value = true
    if (message) {
      loadingMessage.value = message
    }
    hasTimedOut.value = false
    loadingProgress.value = 0

    // Track operation if provided
    if (operationId) {
      activeOperations.value.set(operationId, {
        id: operationId,
        name: operationName || operationId,
        startTime: Date.now()
      })
    }

    // Start timeout timer if enabled
    if (enableTimeout && maxLoadingTime > 0) {
      timeoutTimer = setTimeout(() => {
        hasTimedOut.value = true
        loadingMessage.value = timeoutMessage
      }, maxLoadingTime)
    }

    // Start progress simulation
    startProgressSimulation()
  }

  /**
   * Stop loading with minimum time enforcement
   */
  const stopLoading = (operationId?: string) => {
    // Remove specific operation if provided
    if (operationId) {
      activeOperations.value.delete(operationId)
      
      // If there are still active operations, don't stop loading
      if (activeOperations.value.size > 0) {
        return
      }
    } else {
      // Clear all operations
      activeOperations.value.clear()
    }

    const stopImmediately = () => {
      isLoading.value = false
      loadingMessage.value = ''
      loadingProgress.value = 0
      hasTimedOut.value = false
      clearTimers()
    }

    // Enforce minimum loading time to prevent flashing
    if (minLoadingTime > 0) {
      minLoadingTimer = setTimeout(stopImmediately, minLoadingTime)
    } else {
      stopImmediately()
    }
  }

  /**
   * Update loading message
   */
  const updateMessage = (message: string) => {
    loadingMessage.value = message
  }

  /**
   * Update loading progress (0-100)
   */
  const updateProgress = (progress: number) => {
    loadingProgress.value = Math.max(0, Math.min(100, progress))
  }

  /**
   * Start progress simulation for better UX
   */
  const startProgressSimulation = () => {
    let progress = 0
    const increment = () => {
      if (progress < 90 && isLoading.value) {
        progress += Math.random() * 10
        loadingProgress.value = Math.min(progress, 90)
        progressTimer = setTimeout(increment, 500 + Math.random() * 1000)
      }
    }
    increment()
  }

  /**
   * Clear all timers
   */
  const clearTimers = () => {
    if (minLoadingTimer) {
      clearTimeout(minLoadingTimer)
      minLoadingTimer = null
    }
    if (timeoutTimer) {
      clearTimeout(timeoutTimer)
      timeoutTimer = null
    }
    if (progressTimer) {
      clearTimeout(progressTimer)
      progressTimer = null
    }
  }

  /**
   * Reset loading state
   */
  const reset = () => {
    clearTimers()
    isLoading.value = false
    loadingMessage.value = ''
    loadingProgress.value = 0
    hasTimedOut.value = false
    activeOperations.value.clear()
  }

  /**
   * Get loading status
   */
  const getStatus = () => {
    return {
      isLoading: isLoading.value,
      isLoadingWithMinTime: isLoadingWithMinTime.value,
      message: loadingMessage.value,
      progress: loadingProgress.value,
      hasTimedOut: hasTimedOut.value,
      operationCount: operationCount.value,
      longestRunningOperation: longestRunningOperation.value,
      activeOperations: Array.from(activeOperations.value.values())
    }
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    clearTimers()
    if (persistent && persistentKey) {
      try {
        localStorage.removeItem(`loading-state-${persistentKey}`)
      } catch (error) {
        console.warn('Failed to cleanup persistent loading state:', error)
      }
    }
  }

  return {
    // State
    isLoading,
    isLoadingWithMinTime,
    loadingMessage,
    loadingProgress,
    hasTimedOut,
    operationCount,
    longestRunningOperation,

    // Actions
    startLoading,
    stopLoading,
    updateMessage,
    updateProgress,
    reset,
    getStatus,
    cleanup
  }
}

/**
 * Global loading state for app-wide operations
 */
export const useGlobalLoadingState = () => {
  return useLoadingState({
    persistent: true,
    persistentKey: 'global',
    minLoadingTime: 500,
    maxLoadingTime: 60000,
    enableTimeout: true,
    timeoutMessage: 'The application is taking longer than expected to load...'
  })
}

/**
 * Page-specific loading state
 */
export const usePageLoadingState = (pageKey: string) => {
  return useLoadingState({
    persistent: true,
    persistentKey: `page-${pageKey}`,
    minLoadingTime: 300,
    maxLoadingTime: 30000,
    enableTimeout: true
  })
}

/**
 * Operation-specific loading state for data fetching
 */
export const useOperationLoadingState = () => {
  return useLoadingState({
    minLoadingTime: 200,
    maxLoadingTime: 15000,
    enableTimeout: true
  })
}
