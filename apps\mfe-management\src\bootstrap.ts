import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './assets/css/index.css'

// Bootstrap function for standalone mode
export function bootstrap() {
  const app = createApp(App)
  
  app.use(createPinia())
  app.use(router)
  
  app.mount('#app')
  
  return app
}

// Auto-bootstrap if running standalone
if (process.env.NODE_ENV === 'development' || !(window as any).__POWERED_BY_QIANKUN__) {
  bootstrap()
}

// Export for module federation
export default bootstrap
