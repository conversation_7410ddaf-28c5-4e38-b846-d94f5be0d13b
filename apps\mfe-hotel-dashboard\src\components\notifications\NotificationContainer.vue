<template>
  <div
    aria-live="assertive"
    class="fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"
  >
    <div class="w-full flex flex-col items-center space-y-4 sm:items-end">
      <TransitionGroup
        name="notification"
        tag="div"
        class="space-y-4"
      >
        <NotificationToast
          v-for="notification in visibleNotifications"
          :key="notification.id"
          :notification="notification"
          :auto-dismiss="true"
          :duration="getDuration(notification.type)"
          @close="removeNotification(notification.id)"
          @click="handleNotificationClick(notification)"
        />
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRealtimeStore } from '@/stores/realtimeStore'
import NotificationToast from './NotificationToast.vue'

const realtimeStore = useRealtimeStore()

const visibleNotifications = computed(() => {
  // Show only the most recent 5 notifications
  return realtimeStore.recentNotifications.slice(0, 5)
})

const getDuration = (type: string): number => {
  switch (type) {
    case 'error':
      return 8000 // Errors stay longer
    case 'warning':
      return 6000 // Warnings stay a bit longer
    case 'success':
      return 4000 // Success messages can be shorter
    case 'info':
    default:
      return 5000 // Default duration
  }
}

const removeNotification = (notificationId: string) => {
  realtimeStore.removeNotification(notificationId)
}

const handleNotificationClick = (notification: any) => {
  // Mark as read when clicked
  realtimeStore.markAsRead(notification.id)
  
  // Handle navigation based on notification data
  if (notification.data?.type === 'maintenance_task' && notification.data?.taskId) {
    // Navigate to maintenance task details
    console.log('Navigate to maintenance task:', notification.data.taskId)
    // router.push(`/operations/maintenance/tasks/${notification.data.taskId}`)
  } else if (notification.data?.type === 'service_request' && notification.data?.requestId) {
    // Navigate to service request details
    console.log('Navigate to service request:', notification.data.requestId)
    // router.push(`/operations/services/requests/${notification.data.requestId}`)
  }
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
