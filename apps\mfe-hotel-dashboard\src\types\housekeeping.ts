// Housekeeping Types for Hotel Dashboard MFE

export type RoomStatus = 'CLEAN' | 'DIRTY' | 'OUT_OF_ORDER' | 'MAINTENANCE' | 'OCCUPIED'

export type TaskStatus = 'NEW' | 'ASSIGNED' | 'IN_PROGRESS' | 'QUALITY_CHECK' | 'COMPLETED' | 'CANCELLED'

export type TaskPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'

export type StaffStatus = 'AVAILABLE' | 'BUSY' | 'BREAK' | 'OFF_DUTY'

export type RequestStatus = 'PENDING' | 'APPROVED' | 'REJECTED'

export interface Room {
  id: string
  number: string
  type: string
  floor: number
  status: RoomStatus
  lastCleaned?: string
  nextInspection?: string
  currentGuest?: string
  guestName?: string
  checkOutTime?: string
  checkInTime?: string
  notes?: string
  assignedStaff?: string
}

export interface HousekeepingTask {
  id: string
  title: string
  description: string
  roomNumber: string
  status: TaskStatus
  priority: TaskPriority
  assignedTo?: string
  estimatedTime?: number
  deadline: string
  notes?: string
  createdAt: string
  completedAt?: string
  roomId?: string
}

export interface StaffMember {
  id: string
  name: string
  surname: string
  role: string
  status: StaffStatus
  avatar?: string
  shift?: string
  performanceScore: number
  tasksCompleted: number
  averageTime: number
  phone?: string
  email?: string
  startDate?: string
  currentTasks?: number
}

export interface Floor {
  id: string
  number: number
  totalRooms: number
  cleanRooms: number
  dirtyRooms: number
  outOfOrderRooms: number
  rooms: Room[]
}

export interface StaffRequest {
  id: string
  staffId: string
  type: string
  description: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  createdAt: string
  respondedAt?: string
}

export interface ChatChannel {
  id: string
  name: string
  description: string
  memberCount: number
  unreadCount: number
  lastMessage?: string
  lastMessageTime?: string
}

export interface ChatMessage {
  id: string
  senderId: string
  senderName: string
  message: string
  timestamp: string
  isRead: boolean
}

export interface PerformanceMetric {
  id: string
  staffId: string
  metric: string
  value: number
  period: string
  createdAt: string
}

export interface DirectMessage {
  id: string
  participantId: string
  participantName: string
  lastMessage?: string
  lastMessageTime?: string
  unreadCount: number
}
