<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-navy-800 rounded-20 p-6 w-full max-w-md mx-4 shadow-card">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-bold text-navy-700 dark:text-white">
          {{ category ? 'Kategori Düzenle' : 'Ye<PERSON> Kategor<PERSON>ştur' }}
        </h3>
        <button 
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="saveCategory">
        <!-- <PERSON><PERSON><PERSON> -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-navy-700 dark:text-white mb-2">
            Kategori Adı <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.name"
            type="text"
            required
            placeholder="Kategori adını girin"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 dark:bg-navy-700 dark:text-white"
          />
        </div>

        <!-- Kategori Görseli -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-navy-700 dark:text-white mb-2">
            Kategori Görseli URL <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.image_url"
            type="url"
            required
            placeholder="https://example.com/category-image.jpg"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 dark:bg-navy-700 dark:text-white"
          />
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Kategori için görsel URL'si girin. Önerilen boyut: 300x200px
          </p>
        </div>

        <!-- Görsel Önizleme -->
        <div v-if="formData.image_url" class="mb-4">
          <label class="block text-sm font-medium text-navy-700 dark:text-white mb-2">
            Önizleme
          </label>
          <div class="border border-gray-300 dark:border-gray-600 rounded-lg p-2">
            <img
              :src="formData.image_url"
              :alt="formData.name || 'Kategori görseli'"
              class="w-full h-32 object-cover rounded-lg"
              @error="imageError = true"
              @load="imageError = false"
            />
            <p v-if="imageError" class="text-red-500 text-xs mt-1">
              Görsel yüklenemedi. URL'yi kontrol edin.
            </p>
          </div>
        </div>

        <!-- Sıralama -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-navy-700 dark:text-white mb-2">
            Sıralama
          </label>
          <input
            v-model.number="formData.display_order"
            type="number"
            min="0"
            placeholder="0"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 dark:bg-navy-700 dark:text-white"
          />
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Kategorilerin görüntülenme sırası (0 = en üstte)
          </p>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium transition-colors"
          >
            İptal
          </button>
          <button
            type="submit"
            :disabled="!formData.name.trim() || !formData.image_url.trim() || imageError"
            class="bg-brand-500 hover:bg-brand-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            {{ category ? 'Güncelle' : 'Oluştur' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { ServiceCategory } from '../../types/management'

// Props
interface Props {
  category?: ServiceCategory | null
  categoryType: 'ROOM_SERVICE' | 'GUEST_SERVICE' | 'ACTIVITY'
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [categoryData: Omit<ServiceCategory, 'id'> | ServiceCategory]
}>()

// Form data
const formData = ref({
  name: '',
  image_url: '',
  display_order: 0,
  type: props.categoryType as 'ROOM_SERVICE' | 'GUEST_SERVICE' | 'ACTIVITY'
})

const imageError = ref(false)

// Watch for prop changes to populate form
watch(() => props.category, (category) => {
  if (category) {
    formData.value = {
      name: category.name,
      image_url: category.image_url || '',
      display_order: category.display_order || 0,
      type: category.type
    }
  } else {
    formData.value = {
      name: '',
      image_url: '',
      display_order: 0,
      type: props.categoryType
    }
  }
  imageError.value = false
}, { immediate: true })

// Methods
const saveCategory = () => {
  if (!formData.value.name.trim()) {
    alert('Kategori adı zorunludur!')
    return
  }

  if (!formData.value.image_url.trim()) {
    alert('Kategori görseli URL\'si zorunludur!')
    return
  }

  if (imageError.value) {
    alert('Görsel yüklenemedi. Lütfen geçerli bir URL girin.')
    return
  }

  const categoryData = {
    ...formData.value,
    name: formData.value.name.trim(),
    image_url: formData.value.image_url.trim()
  }

  if (props.category) {
    // Update existing category
    emit('save', { ...categoryData, id: props.category.id })
  } else {
    // Create new category
    emit('save', categoryData)
  }
}
</script> 