import { describe, it, expect } from 'vitest'

// Simple utility functions to test
export function formatCurrency(amount: number, currency = 'TRY'): string {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('tr-TR')
}

export function formatTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleTimeString('tr-TR', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

describe('Formatter Utilities', () => {
  describe('formatCurrency', () => {
    it('formats Turkish Lira correctly', () => {
      expect(formatCurrency(1234.56)).toBe('₺1.234,56')
    })

    it('formats USD correctly', () => {
      expect(formatCurrency(1234.56, 'USD')).toBe('$1.234,56')
    })

    it('handles zero amount', () => {
      expect(formatCurrency(0)).toBe('₺0,00')
    })

    it('handles negative amounts', () => {
      expect(formatCurrency(-100)).toBe('-₺100,00')
    })
  })

  describe('formatDate', () => {
    it('formats Date object correctly', () => {
      const date = new Date('2024-01-15')
      expect(formatDate(date)).toBe('15.01.2024')
    })

    it('formats date string correctly', () => {
      expect(formatDate('2024-01-15')).toBe('15.01.2024')
    })
  })

  describe('formatTime', () => {
    it('formats time correctly', () => {
      const date = new Date('2024-01-15T14:30:00')
      expect(formatTime(date)).toBe('14:30')
    })

    it('formats time from string correctly', () => {
      expect(formatTime('2024-01-15T09:05:00')).toBe('09:05')
    })
  })

  describe('truncateText', () => {
    it('truncates long text', () => {
      const longText = 'This is a very long text that should be truncated'
      expect(truncateText(longText, 20)).toBe('This is a very long ...')
    })

    it('returns original text if shorter than max length', () => {
      const shortText = 'Short text'
      expect(truncateText(shortText, 20)).toBe('Short text')
    })

    it('returns original text if exactly max length', () => {
      const text = 'Exactly twenty chars'
      expect(truncateText(text, 20)).toBe('Exactly twenty chars')
    })

    it('handles empty string', () => {
      expect(truncateText('', 10)).toBe('')
    })
  })
})
