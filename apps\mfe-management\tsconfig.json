{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "bundler", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "vue"], "allowJs": true, "strict": true, "noImplicitAny": false, "skipLibCheck": true}}