<template>
  <div class="flex gap-6 h-full min-h-[600px]">
    <!-- Left Column: Category Management (30%) -->
    <div class="w-[30%] bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
          Hizmet Kategorileri
        </h3>
        <button
          @click="showCategoryModal = true"
          class="bg-brand-500 hover:bg-brand-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center"
        >
          <PlusIcon class="w-4 h-4 mr-1" />
          Ekle
        </button>
      </div>

      <!-- Category List -->
      <div class="space-y-2">
        <!-- All Services Option -->
        <div
          @click="selectedCategoryId = null"
          :class="[
            'cursor-pointer px-4 py-3 rounded-lg transition-colors flex items-center justify-between group',
            selectedCategoryId === null
              ? 'bg-brand-50 border-l-4 border-brand-500 text-brand-700 dark:bg-brand-900/20 dark:text-brand-300'
              : 'hover:bg-gray-50 dark:hover:bg-navy-600 text-gray-700 dark:text-gray-300'
          ]"
        >
          <div class="flex items-center">
            <component :is="ListBulletIcon" class="w-5 h-5 mr-3" />
            <span class="font-medium">Tüm Hizmetler</span>
          </div>
          <span class="text-sm text-gray-500 dark:text-gray-400">
            ({{ store.guestServices.length }})
          </span>
        </div>

        <!-- Guest Service Categories -->
        <div
          v-for="category in guestServiceCategories"
          :key="category.id"
          @click="selectedCategoryId = category.id"
          :class="[
            'cursor-pointer px-4 py-3 rounded-lg transition-colors flex items-center justify-between group',
            selectedCategoryId === category.id
              ? 'bg-brand-50 border-l-4 border-brand-500 text-brand-700 dark:bg-brand-900/20 dark:text-brand-300'
              : 'hover:bg-gray-50 dark:hover:bg-navy-600 text-gray-700 dark:text-gray-300'
          ]"
        >
          <div class="flex items-center min-w-0 flex-1">
            <div class="w-8 h-8 mr-3 flex-shrink-0 rounded-lg overflow-hidden">
              <img 
                :src="category.image_url || 'https://via.placeholder.com/32x32.png?text=' + category.name.charAt(0)" 
                :alt="category.name"
                class="w-full h-full object-cover"
              />
            </div>
            <div class="min-w-0">
              <div class="font-medium truncate">{{ category.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                Sıra: {{ category.display_order }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2 ml-2">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              ({{ getServicesForCategory(category.id).length }})
            </span>
            <div class="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
              <button
                @click.stop="editCategory(category)"
                class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300 p-1"
                title="Düzenle"
              >
                <PencilIcon class="w-4 h-4" />
              </button>
              <button
                @click.stop="deleteCategory(category.id)"
                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1"
                title="Sil"
              >
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column: Service Management (70%) -->
    <div class="w-[70%] bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
      <!-- Header with Add Button -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-lg font-semibold text-navy-700 dark:text-white">
            {{ selectedCategoryName }} Hizmetleri
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ selectedCategoryId ? 'Seçili kategorideki hizmetleri yönetin' : 'Tüm misafir hizmetlerini yönetin' }}
          </p>
        </div>
        <button
          @click="showServiceModal = true"
          class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
        >
          <PlusIcon class="w-5 h-5 mr-2" />
          Yeni Hizmet Ekle
        </button>
      </div>

      <!-- Services Table -->
      <div class="bg-gray-50 dark:bg-navy-600 rounded-lg overflow-hidden">
        <div v-if="filteredServices.length > 0" class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-100 dark:bg-navy-500">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Hizmet Adı
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Açıklama
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Kategori
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Durum
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
              <tr
                v-for="service in filteredServices"
                :key="service.id"
                class="hover:bg-gray-50 dark:hover:bg-navy-600 transition-colors"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ service.name }}
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-500 dark:text-gray-400 max-w-md">
                    {{ service.description }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    {{ getCategoryName(service.categoryId) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <button
                    @click="store.toggleGuestServiceStatus(service.id)"
                    class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
                    :class="service.isActive ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
                  >
                    <span
                      class="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                      :class="service.isActive ? 'translate-x-5' : 'translate-x-0'"
                    ></span>
                  </button>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    @click="editService(service)"
                    class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300"
                  >
                    Düzenle
                  </button>
                  <button
                    @click="deleteService(service.id)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Sil
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div
          v-else
          class="text-center py-12 bg-white dark:bg-navy-700"
        >
          <component :is="DocumentIcon" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            {{ selectedCategoryId ? 'Bu kategoride hizmet bulunmuyor' : 'Henüz hizmet bulunmuyor' }}
          </h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {{ selectedCategoryId ? 'Bu kategoriye yeni hizmet ekleyerek başlayın.' : 'İlk hizmetinizi ekleyerek başlayın.' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Category Modal -->
    <CategoryModal
      v-if="showCategoryModal"
      :category="editingCategory"
      :category-type="'GUEST_SERVICE'"
      @close="closeCategoryModal"
      @save="saveCategory"
    />

    <!-- Guest Service Modal -->
    <GuestServiceModal
      v-if="showServiceModal"
      :service="editingService"
      @close="closeServiceModal"
      @save="saveService"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  ListBulletIcon, 
  DocumentIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import CategoryModal from '@/components/modals/CategoryModal.vue'
import GuestServiceModal from '@/components/modals/GuestServiceModal.vue'
import type { GuestService, CreateGuestServiceData, ServiceCategory } from '@/types/management'

const store = useManagementStore()

// State
const selectedCategoryId = ref<string | null>(null)
const showCategoryModal = ref(false)
const showServiceModal = ref(false)
const editingCategory = ref<ServiceCategory | null>(null)
const editingService = ref<GuestService | null>(null)

// Computed properties
const guestServiceCategories = computed(() => 
  store.categories.filter(cat => cat.type === 'GUEST_SERVICE')
)

const selectedCategoryName = computed(() => {
  if (!selectedCategoryId.value) return 'Tüm'
  const category = guestServiceCategories.value.find(cat => cat.id === selectedCategoryId.value)
  return category?.name || 'Tüm'
})

const filteredServices = computed(() => {
  if (!selectedCategoryId.value) return store.guestServices
  
  return store.guestServices.filter(service => service.categoryId === selectedCategoryId.value)
})

// Helper functions
const getServicesForCategory = (categoryId: string) => {
  return store.guestServices.filter(service => service.categoryId === categoryId)
}

const getCategoryName = (categoryId: string) => {
  const category = guestServiceCategories.value.find(cat => cat.id === categoryId)
  return category?.name || 'Bilinmeyen'
}

// Category management
const editCategory = (category: ServiceCategory) => {
  editingCategory.value = category
  showCategoryModal.value = true
}

const deleteCategory = async (categoryId: string) => {
  const currentHotelId = '4dfec6ed-6ad4-4591-b60c-4f68c74ec150' // TODO: Get from auth store
  
  const servicesInCategory = getServicesForCategory(categoryId).length
  if (servicesInCategory > 0) {
    alert(`Bu kategori silinemez. Bu kategoride ${servicesInCategory} hizmet bulunmaktadır. Önce hizmetleri silin veya başka kategoriye taşıyın.`)
    return
  }
  
  if (confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')) {
    try {
      await store.deleteCategory(categoryId, currentHotelId)
      if (selectedCategoryId.value === categoryId) {
        selectedCategoryId.value = null
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('Kategori silinirken bir hata oluştu. Lütfen tekrar deneyin.')
    }
  }
}

const closeCategoryModal = () => {
  showCategoryModal.value = false
  editingCategory.value = null
}

const saveCategory = async (categoryData: Omit<ServiceCategory, 'id'>) => {
  const currentHotelId = '4dfec6ed-6ad4-4591-b60c-4f68c74ec150' // TODO: Get from auth store
  
  try {
    if (editingCategory.value) {
      await store.updateCategory({ ...editingCategory.value, ...categoryData }, currentHotelId)
    } else {
      await store.addCategory(categoryData, currentHotelId)
    }
    closeCategoryModal()
  } catch (error) {
    console.error('Error saving category:', error)
    alert('Kategori kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}

// Service management
const editService = (service: GuestService) => {
  editingService.value = service
  showServiceModal.value = true
}

const deleteService = (serviceId: string) => {
  if (confirm('Bu hizmeti silmek istediğinizden emin misiniz?')) {
    store.deleteGuestService(serviceId)
  }
}

const closeServiceModal = () => {
  showServiceModal.value = false
  editingService.value = null
}

const saveService = (serviceData: CreateGuestServiceData) => {
  if (editingService.value) {
    store.updateGuestService(editingService.value.id, serviceData)
  } else {
    store.addGuestService(serviceData)
  }
  closeServiceModal()
}
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style> 