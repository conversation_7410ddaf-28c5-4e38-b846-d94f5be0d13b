<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900 flex"
       :class="{ 'sidebar-collapsed': dashboardStore.isSidebarCollapsed }">
    <!-- Sidebar -->
    <Sidebar data-testid="sidebar" />
    
    <!-- Main Content -->
    <div class="flex-1 main-content transition-all duration-300 min-h-screen">
      <!-- Navbar -->
      <Navbar data-testid="navbar" />
      
      <!-- Page Content -->
      <main class="px-4 md:px-10 mx-auto w-full">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue'
import Sidebar from '@/components/sidebar/Sidebar.vue'
import Navbar from '@/components/navbar/Navbar.vue'
import { useDashboardStore } from '@/stores/dashboardStore'
import { useAuthStore } from '@/stores/authStore'
import { useRealtimeStore } from '@/stores/realtimeStore'
import { useHotelDashboardSync } from '@hotelexia/shared-components'
import { useRoomDataConsistency, useHotelDataConsistency } from '@hotelexia/shared-components'
import { HotelDashboardService } from '@hotelexia/shared-supabase-client'

const dashboardStore = useDashboardStore()
const authStore = useAuthStore()
const realtimeStore = useRealtimeStore()

// Initialize Cross-MFE sync
const crossMFESync = useHotelDashboardSync(
  authStore.user?.id || '',
  authStore.userHotelId || ''
)

// Initialize data consistency hooks
const roomConsistency = useRoomDataConsistency(async () => {
  if (authStore.userHotelId) {
    const response = await HotelDashboardService.getRooms(authStore.userHotelId)
    return response.data
  }
  return []
})

const hotelConsistency = useHotelDataConsistency(async () => {
  if (authStore.userHotelId) {
    const response = await HotelDashboardService.getHotelInfo(authStore.userHotelId)
    return response.data
  }
  return null
})

// Initialize dark mode and realtime subscriptions
onMounted(() => {
  if (dashboardStore.darkMode) {
    document.documentElement.classList.add('dark')
  }

  // Initialize realtime subscriptions if user is authenticated and has hotel access
  if (authStore.isAuthenticated && authStore.userHotelId) {
    console.log('Initializing realtime subscriptions for hotel:', authStore.userHotelId)
    realtimeStore.initializeSubscriptions()

    // Initialize Cross-MFE sync
    if (authStore.user?.id) {
      crossMFESync.initialize(authStore.user.id, authStore.userHotelId)
      crossMFESync.start()
    }
  }
})

// Cleanup subscriptions when component unmounts
onUnmounted(() => {
  console.log('Cleaning up realtime subscriptions')
  realtimeStore.cleanup()
  crossMFESync.stop()
  roomConsistency.stopMonitoring()
  hotelConsistency.stopMonitoring()
})

// Watch for dark mode changes
watch(() => dashboardStore.darkMode, (newValue) => {
  if (newValue) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
})

// Watch for authentication changes to manage subscriptions
watch(() => authStore.isAuthenticated, (isAuthenticated) => {
  if (isAuthenticated && authStore.userHotelId) {
    console.log('User authenticated, initializing realtime subscriptions')
    realtimeStore.initializeSubscriptions()
  } else {
    console.log('User not authenticated, cleaning up realtime subscriptions')
    realtimeStore.cleanup()
  }
})

// Watch for hotel ID changes (in case user switches hotels)
watch(() => authStore.userHotelId, (newHotelId, oldHotelId) => {
  if (newHotelId && newHotelId !== oldHotelId) {
    console.log('Hotel ID changed, reinitializing realtime subscriptions:', newHotelId)
    realtimeStore.cleanup()
    realtimeStore.initializeSubscriptions()
  } else if (!newHotelId) {
    console.log('Hotel ID removed, cleaning up realtime subscriptions')
    realtimeStore.cleanup()
  }
})
</script>

<style scoped>
.main-content {
  margin-left: 290px;
}

.sidebar-collapsed .main-content {
  margin-left: 80px;
}
</style>
