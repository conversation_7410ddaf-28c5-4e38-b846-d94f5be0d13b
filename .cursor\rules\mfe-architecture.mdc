---
description: 
globs: 
alwaysApply: true
---
# Hotelexia: Micro-Frontend (MFE) Architecture

This document provides a detailed overview of the Module Federation setup, routing strategies, and integration patterns used across the Hotelexia platform.

## 1. MFE Overview
The Hotelexia platform consists of **4 main applications** connected via Module Federation:

### **App Shell (Host Application)**
- **Path:** `apps/app-shell`
- **Port:** 5000
- **Role:** Central orchestrator and routing coordinator
- **Framework:** Vue 3 + TypeScript + Vite
- **Responsibilities:**
    - Top-level routing and navigation
    - Global state management
    - Authentication coordination
    - MFE loading and error handling

### **Customer Portal MFE (Remote)**
- **Path:** `packages/mfe-customer-portal`
- **Port:** 3001
- **Module Name:** `mfeCustomerPortal`
- **Target Users:** Hotel guests and visitors
- **Key Features:**
    - Hotel search and booking interface
    - Guest dashboard and profile management
    - Room service ordering system
    - Activity browsing and registration
    - Service request submission

### **Hotel Dashboard MFE (Remote)**
- **Path:** `apps/mfe-hotel-dashboard`
- **Port:** 5002
- **Module Name:** `hotelDashboard`
- **Target Users:** Hotel staff and managers
- **Key Features:**
    - Comprehensive operational dashboard
    - Housekeeping and maintenance management
    - Activity and content management
    - Staff communication tools
    - Performance analytics and reporting

### **Management Portal MFE (Remote)**
- **Path:** `apps/mfe-management`
- **Port:** 5001
- **Module Name:** `managementPortal`
- **Target Users:** Platform administrators
- **Key Features:**
    - Multi-hotel management
    - System configuration and settings
    - User role and permission management
    - Platform analytics and monitoring
    - Billing and subscription management

## 2. Module Federation Configuration

### **App Shell Federation Setup**
```typescript
// apps/app-shell/vite.config.ts
export default defineConfig({
  plugins: [
    vue(),
    federation({
      name: 'app-shell',
      remotes: {
        mfeCustomerPortal: 'http://localhost:3001/assets/remoteEntry.js',
        hotelDashboard: 'http://localhost:5002/assets/remoteEntry.js',
        managementPortal: 'http://localhost:5001/assets/remoteEntry.js'
      },
      shared: {
        vue: { singleton: true },
        'vue-router': { singleton: true },
        pinia: { singleton: true },
        '@shared/supabase-client': {}
      }
    })
  ]
})
```

### **Customer Portal Exposed Components**
```typescript
// packages/mfe-customer-portal/vite.config.ts
exposes: {
  './OnboardingView': './src/views/OnboardingView.vue',
  './GuestLoginView': './src/views/GuestLoginView.vue',
  './DevLoginView': './src/views/DevLoginView.vue',
  './DashboardView': './src/views/GuestDashboardView.vue',
  './RoomServiceMenuView': './src/views/RoomServiceMenuView.vue',
  './RoomServiceCategoryView': './src/views/RoomServiceCategoryView.vue',
  './RoomServiceView': './src/views/RoomServiceView.vue',
  './RoomServiceItemDetailView': './src/views/RoomServiceItemDetailView.vue',
  './ActivitiesView': './src/views/ActivitiesView.vue',
  './ActivityDetailView': './src/views/ActivityDetailView.vue',
  './ServicesView': './src/views/ServicesView.vue',
  './ServiceDetailView': './src/views/ServiceDetailView.vue',
  './ServiceHistoryView': './src/views/ServiceHistoryView.vue',
  './CartView': './src/views/CartView.vue',
  './OrderSuccessView': './src/views/OrderSuccessView.vue',
  './OrderFailedView': './src/views/OrderFailedView.vue',
  './NotificationsView': './src/views/NotificationsView.vue'
}
```

### **Hotel Dashboard Exposed Components**
```typescript
// apps/mfe-hotel-dashboard/vite.config.ts
exposes: {
  './HotelDashboard': './src/App.vue',
  './Dashboard': './src/views/Dashboard.vue',
  './ContentOverview': './src/views/content/ContentOverview.vue',
  './RoomServiceManagement': './src/views/content/RoomServiceManagement.vue',
  './GuestServiceManagement': './src/views/content/GuestServiceManagement.vue',
  './ActivityManagement': './src/views/content/ActivityManagement.vue',
  './ActivityCategoryManagement': './src/views/content/ActivityCategoryManagement.vue',
  './HousekeepingOverview': './src/views/operations/HousekeepingOverview.vue',
  './TaskManagement': './src/views/operations/housekeeping/TaskManagement.vue',
  './MaintenanceOverview': './src/views/operations/maintenance/MaintenanceOverview.vue',
  './FloorManagement': './src/views/operations/maintenance/FloorManagement.vue',
  './RoomManagement': './src/views/operations/maintenance/RoomManagement.vue',
  './StaffManagement': './src/views/operations/maintenance/StaffManagement.vue',
  './StaffCommunication': './src/views/operations/maintenance/StaffCommunication.vue',
  './PerformanceAnalysis': './src/views/operations/maintenance/PerformanceAnalysis.vue',
  './CustomerReports': './src/views/reports/CustomerReports.vue',
  './StaffReports': './src/views/reports/StaffReports.vue',
  './OperationalReports': './src/views/reports/OperationalReports.vue',
  './NotificationCenter': './src/views/NotificationCenter.vue',
  './Settings': './src/views/Settings.vue',
  './Profile': './src/views/Profile.vue'
}
```

### **Management Portal Exposed Components**
```typescript
// apps/mfe-management/vite.config.ts
exposes: {
  './Management': './src/App.vue',
  './Dashboard': './src/views/Dashboard.vue',
  './HotelList': './src/views/HotelList.vue',
  './HotelEdit': './src/views/HotelEdit.vue',
  './HotelMfeConfig': './src/views/HotelMfeConfig.vue',
  './Settings': './src/views/Settings.vue',
  './Billing': './src/views/Billing.vue',
  './Analytics': './src/views/Analytics.vue',
  './SystemHealth': './src/views/SystemHealth.vue',
  './Announcements': './src/views/Announcements.vue',
  './AuditLog': './src/views/AuditLog.vue',
  './HotelSetupWizard': './src/components/wizards/HotelSetupWizard.vue'
}
```

## 3. Routing Architecture

### **App Shell Primary Routes**
```typescript
// apps/app-shell/src/router/index.ts
const routes = [
  // Customer Portal Routes
  { path: '/guest/onboarding', component: () => import('mfeCustomerPortal/OnboardingView') },
  { path: '/guest/login', component: () => import('mfeCustomerPortal/GuestLoginView') },
  { path: '/guest/dev-login', component: () => import('mfeCustomerPortal/DevLoginView') },
  { path: '/guest/dashboard', component: () => import('mfeCustomerPortal/DashboardView') },
  { path: '/guest/room-service', component: () => import('mfeCustomerPortal/RoomServiceMenuView') },
  { path: '/guest/activities', component: () => import('mfeCustomerPortal/ActivitiesView') },
  { path: '/guest/services', component: () => import('mfeCustomerPortal/ServicesView') },
  { path: '/guest/notifications', component: () => import('mfeCustomerPortal/NotificationsView') },

  // Hotel Management Routes
  { path: '/hotel', component: () => import('hotelDashboard/HotelDashboard') },
  { path: '/hotel/dashboard', component: () => import('hotelDashboard/Dashboard') },
  { path: '/hotel/content/overview', component: () => import('hotelDashboard/ContentOverview') },
  { path: '/hotel/operations/housekeeping', component: () => import('hotelDashboard/HousekeepingOverview') },
  { path: '/hotel/operations/maintenance', component: () => import('hotelDashboard/MaintenanceOverview') },
  { path: '/hotel/reports/customer', component: () => import('hotelDashboard/CustomerReports') },

  // Platform Management Routes
  { path: '/management', component: () => import('managementPortal/Management') },
  { path: '/management/dashboard', component: () => import('managementPortal/Dashboard') },
  { path: '/management/hotels', component: () => import('managementPortal/HotelList') },
  { path: '/management/settings', component: () => import('managementPortal/Settings') }
]
```

## 4. Shared Dependencies Strategy

### **Shared Libraries**
- **Vue 3**: Singleton instance shared across all MFEs
- **Vue Router**: Single router instance for consistent navigation
- **Pinia**: Unified state management across the platform
- **Supabase Client**: Shared database connection and authentication

### **Shared Packages**
```typescript
// packages/shared-components
export { Button, Card, Input, Modal, Table } from './components'

// packages/shared-supabase-client
export { supabase, useAuth, useDatabase } from './client'
```

## 5. Type Safety & Module Federation

### **TypeScript Module Definitions**
```typescript
// apps/app-shell/src/types/module-federation.d.ts
declare module 'mfeCustomerPortal/OnboardingView' {
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/Dashboard' {
  const component: DefineComponent
  export default component
}

declare module 'managementPortal/HotelList' {
  const component: DefineComponent
  export default component
}
```

## 6. Development Workflow

### **Development Commands**
```bash
# Start all MFEs concurrently
npm run dev

# Build all MFEs for production
npm run build

# Type checking across the monorepo
npm run type-check

# Start individual MFEs
cd apps/app-shell && npm run dev       # Port 5000
cd packages/mfe-customer-portal && npm run dev  # Port 3001
cd apps/mfe-hotel-dashboard && npm run dev      # Port 5002
cd apps/mfe-management && npm run dev           # Port 5001
```

### **Integration Testing**
- **Customer Experience**: http://localhost:5000/guest
- **Hotel Management**: http://localhost:5000/hotel
- **Platform Administration**: http://localhost:5000/management

## 7. Error Handling & Fallbacks

### **MFE Loading Error Handling**
```vue
<template>
  <Suspense>
    <component :is="dynamicComponent" />
    <template #fallback>
      <LoadingSpinner />
    </template>
  </Suspense>
</template>

<script setup>
import { defineAsyncComponent } from 'vue'

const dynamicComponent = defineAsyncComponent({
  loader: () => import('mfeCustomerPortal/DashboardView'),
  errorComponent: ErrorFallback,
  delay: 200,
  timeout: 3000
})
</script>
```

## 8. Performance Optimization

### **Code Splitting Strategy**
- **Route-based Splitting**: Each MFE route is lazy-loaded
- **Component-based Splitting**: Large components are dynamically imported
- **Vendor Splitting**: Shared dependencies are extracted into separate chunks

### **Bundle Optimization**
- **Tree Shaking**: Unused code elimination across MFEs
- **Asset Optimization**: Image compression and font subsetting
- **Cache Optimization**: Long-term caching for shared dependencies

## 9. Deployment Architecture

### **Production Deployment**
- **CDN Distribution**: Each MFE deployed to separate CDN endpoints
- **Independent Versioning**: MFEs can be deployed independently
- **Rollback Strategy**: Individual MFE rollbacks without affecting others
- **Health Monitoring**: Per-MFE health checks and monitoring

### **Environment Management**
```typescript
// Environment-specific remote URLs
const remotes = {
  development: {
    mfeCustomerPortal: 'http://localhost:3001/assets/remoteEntry.js',
    hotelDashboard: 'http://localhost:5002/assets/remoteEntry.js',
    managementPortal: 'http://localhost:5001/assets/remoteEntry.js'
  },
  production: {
    mfeCustomerPortal: 'https://customer.hotelexia.com/assets/remoteEntry.js',
    hotelDashboard: 'https://hotel.hotelexia.com/assets/remoteEntry.js',
    managementPortal: 'https://management.hotelexia.com/assets/remoteEntry.js'
  }
}
```

## 10. Security Considerations

### **Cross-Origin Resource Sharing (CORS)**
- Proper CORS configuration for MFE communication
- Secure token sharing across federated modules
- Content Security Policy (CSP) headers for XSS protection

### **Authentication Flow**
- Centralized authentication in App Shell
- Secure token propagation to remote MFEs
- Role-based access control enforcement

---

## 🎯 **MFE Architecture Benefits**

### **Development Benefits**
- **Independent Development**: Teams can work on different MFEs simultaneously
- **Technology Flexibility**: Each MFE can adopt new technologies independently
- **Scalable Teams**: Feature teams aligned with specific MFE boundaries
- **Faster Development**: Parallel development and deployment cycles

### **Operational Benefits**
- **Independent Deployment**: Deploy MFEs without affecting others
- **Fault Isolation**: Failures in one MFE don't crash the entire platform
- **Performance Optimization**: Optimize each MFE for its specific use case
- **A/B Testing**: Test different versions of individual MFEs

### **Business Benefits**
- **Faster Time to Market**: Independent feature releases
- **Reduced Risk**: Smaller, isolated deployments
- **Team Autonomy**: Self-contained teams with clear ownership
- **Scalable Architecture**: Support for platform growth and expansion

**🚀 STATUS: ENTERPRISE-GRADE MFE ARCHITECTURE COMPLETE**

