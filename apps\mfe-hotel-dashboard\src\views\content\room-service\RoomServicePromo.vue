<template>
  <div class="p-4 md:p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        Oda Servisi Promo Yönetimi
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Oda servisi için özel promosyon ve kampanya yönetimi
      </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Aktif Oda Servisi Promolar</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">8</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
            <ShoppingBagIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Bu Ay Kullanım</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">127</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
            <ChartBarIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Toplam İndirim</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">₺1,890</p>
          </div>
          <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
            <CurrencyDollarIcon class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Ortalama İndirim</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">₺15</p>
          </div>
          <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
            <TicketIcon class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </div>
    </div>

    <!-- Active Promos Table -->
    <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm mb-8">
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Aktif Oda Servisi Promoları
          </h3>
          <button class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
            <PlusIcon class="w-4 h-4 mr-2" />
            Yeni Promo Oluştur
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Promo Adı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İndirim Türü
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İndirim Miktarı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Geçerlilik
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Kullanım
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="promo in roomServicePromos" :key="promo.id" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mr-3">
                    <TicketIcon class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-navy-700 dark:text-white">{{ promo.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ promo.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="promo.type === 'percentage' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'">
                  {{ promo.type === 'percentage' ? 'Yüzde' : 'Sabit' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ promo.type === 'percentage' ? `%${promo.amount}` : `₺${promo.amount}` }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ promo.validUntil }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ promo.usageCount }}/{{ promo.maxUsage || '∞' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="promo.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'">
                  {{ promo.status === 'active' ? 'Aktif' : 'Pasif' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button class="text-brand-500 hover:text-brand-600">
                    <PencilIcon class="w-4 h-4" />
                  </button>
                  <button class="text-red-500 hover:text-red-600">
                    <TrashIcon class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Promo Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Category Based Promos -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">
          Kategori Bazlı Promolar
        </h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Kahvaltı Kategorisi</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%20 indirim, 06:00-11:00</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">İçecek Kategorisi</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">2. içecek %50 indirim</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
        </div>
      </div>

      <!-- Time Based Promos -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">
          Zaman Bazlı Promolar
        </h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Gece Yarısı Menüsü</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">₺50 indirim, 22:00-02:00</p>
            </div>
            <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Öğle Menü Paketi</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%15 indirim, 12:00-14:00</p>
            </div>
            <span class="text-yellow-600 dark:text-yellow-400 text-sm">Beklemede</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Development Notice -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
      <div class="flex items-start">
        <InformationCircleIcon class="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" />
        <div>
          <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">
            Oda Servisi Promo Yönetimi - Geliştirme Aşamasında
          </h3>
          <p class="text-blue-700 dark:text-blue-300 mb-3">
            Bu sayfa oda servisi için özel promo yönetimi sağlar. Gelecekte eklenecek özellikler:
          </p>
          <ul class="list-disc list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm">
            <li>Menü kategorisi bazlı promo oluşturma</li>
            <li>Zaman aralığı bazlı otomatik aktivasyon</li>
            <li>Minimum sipariş tutarı koşulları</li>
            <li>Ücretsiz teslimat promoları</li>
            <li>Combo menü indirimleri</li>
            <li>Müşteri sadakat programları entegrasyonu</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ShoppingBagIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  TicketIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline'

// Dummy data for room service promos
const roomServicePromos = ref([
  {
    id: 1,
    name: 'Kahvaltı İndirimi',
    description: 'Sabah saatleri için özel indirim',
    type: 'percentage',
    amount: 20,
    validUntil: '31.12.2024',
    usageCount: 45,
    maxUsage: 100,
    status: 'active'
  },
  {
    id: 2,
    name: 'Gece Yarısı Menü',
    description: 'Geç saatler için sabit indirim',
    type: 'fixed',
    amount: 50,
    validUntil: '15.12.2024',
    usageCount: 28,
    maxUsage: null,
    status: 'active'
  },
  {
    id: 3,
    name: 'İkinci İçecek',
    description: '2. içecek yarı fiyatına',
    type: 'percentage',
    amount: 50,
    validUntil: '20.12.2024',
    usageCount: 67,
    maxUsage: 200,
    status: 'active'
  },
  {
    id: 4,
    name: 'Öğle Menü Paketi',
    description: 'Öğle saatleri combo menü',
    type: 'percentage',
    amount: 15,
    validUntil: '25.12.2024',
    usageCount: 12,
    maxUsage: 50,
    status: 'inactive'
  }
])
</script> 