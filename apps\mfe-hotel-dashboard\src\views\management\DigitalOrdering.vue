<template>
  <div>
    <!-- Header with Add Button -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white">
          Menu Items Management
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Manage in-room dining menu items for the customer portal
        </p>
      </div>
      <button
        @click="showModal = true"
        class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
      >
        <PlusIcon class="w-5 h-5 mr-2" />
        Create New Menu Item
      </button>
    </div>

    <!-- Menu Items Table -->
    <div class="bg-white dark:bg-navy-700 rounded-lg overflow-hidden shadow-sm">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-600">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Image
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Name
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Category
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Price
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Available
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr
              v-for="item in store.menuItems"
              :key="item.id"
              class="hover:bg-gray-50 dark:hover:bg-navy-600 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <img
                  :src="item.image"
                  :alt="item.name"
                  class="w-12 h-12 rounded-lg object-cover"
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ item.name }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                  {{ item.description }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {{ item.category }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ₺{{ item.price.toFixed(2) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <button
                  @click="toggleAvailability(item.id)"
                  class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
                  :class="item.isAvailable ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
                >
                  <span
                    class="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                    :class="item.isAvailable ? 'translate-x-5' : 'translate-x-0'"
                  ></span>
                </button>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="editItem(item)"
                  class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300"
                >
                  Edit
                </button>
                <button
                  @click="deleteItem(item.id)"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Menu Item Modal -->
    <MenuItemModal
      v-if="showModal"
      :item="editingItem"
      @close="closeModal"
      @save="saveItem"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PlusIcon } from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import MenuItemModal from '@/components/modals/MenuItemModal.vue'
import type { MenuItem, CreateMenuItemData } from '@/types/management'

const store = useManagementStore()
const showModal = ref(false)
const editingItem = ref<MenuItem | null>(null)

// Get current hotel ID from auth store profile
const currentHotelId = ref<string>('4dfec6ed-6ad4-4591-b60c-4f68c74ec150')

// Initialize data on mount
onMounted(async () => {
  if (currentHotelId.value) {
    try {
      await Promise.all([
        store.fetchCategories(currentHotelId.value),
        store.fetchMenuItems(currentHotelId.value)
      ])
    } catch (error) {
      console.error('Error loading digital ordering data:', error)
    }
  }
})

const editItem = (item: MenuItem) => {
  editingItem.value = item
  showModal.value = true
}

const deleteItem = async (id: string) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  if (confirm('Bu menü öğesini silmek istediğinizden emin misiniz?')) {
    try {
      await store.deleteMenuItem(id, currentHotelId.value)
    } catch (error) {
      console.error('Error deleting menu item:', error)
      alert('Ürün silinirken bir hata oluştu. Lütfen tekrar deneyin.')
    }
  }
}

const toggleAvailability = async (id: string) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  try {
    await store.toggleMenuItemAvailability(id, currentHotelId.value)
  } catch (error) {
    console.error('Error toggling menu item availability:', error)
    alert('Ürün durumu güncellenirken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}

const closeModal = () => {
  showModal.value = false
  editingItem.value = null
}

const saveItem = async (itemData: CreateMenuItemData) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  try {
    if (editingItem.value) {
      // Update existing item
      await store.updateMenuItem(editingItem.value.id, itemData, currentHotelId.value)
    } else {
      // Create new item
      await store.addMenuItem(itemData, currentHotelId.value)
    }
    closeModal()
  } catch (error) {
    console.error('Error saving menu item:', error)
    alert('Ürün kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style> 