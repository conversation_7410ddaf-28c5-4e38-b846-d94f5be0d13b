import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@hotelexia/shared-supabase-client'
import { getSafeImageUrl } from '@/utils/imageUtils'

// TypeScript interfaces for menu data
interface MenuItem {
  id: string
  name: string
  description: string
  price: number
  image_url: string | null
  is_available: boolean
  category_id: string
  display_order: number
  preparation_time_minutes: number | null
  item_code: string
  is_vegetarian: boolean
  is_vegan: boolean
  is_gluten_free: boolean
  tags: string[]
}

interface MenuCategory {
  id: string
  name: string
  image_url: string
  display_order: number
  items: MenuItem[]
}

export const useMenuStore = defineStore('menu', () => {
  // State
  const categories = ref<MenuCategory[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastFetchTime = ref<Date | null>(null)

  // Computed
  const menuItemsCount = computed(() => {
    return categories.value.reduce((total, category) => total + category.items.length, 0)
  })

  const availableItemsCount = computed(() => {
    return categories.value.reduce((total, category) => {
      const availableItems = category.items.filter(item => item.is_available)
      return total + availableItems.length
    }, 0)
  })

  // Get category by name
  const getCategoryByName = computed(() => (name: string) => {
    return categories.value.find(category => 
      category.name.toLowerCase() === name.toLowerCase()
    )
  })

  // Get menu item by ID
  const getItemById = computed(() => (id: string) => {
    for (const category of categories.value) {
      const item = category.items.find(item => item.id === id)
      if (item) return item
    }
    return null
  })

  // Get items by category ID
  const getItemsByCategoryId = computed(() => (categoryId: string) => {
    const category = categories.value.find(cat => cat.id === categoryId)
    return category?.items || []
  })

  // Get recommended items (items with high prices or from popular categories)
  const recommendedItems = computed(() => {
    const allItems = categories.value.flatMap(category => category.items)
    return allItems
      .filter(item => item.is_available)
      .sort((a, b) => {
        // First sort by display_order if available, then by price descending
        if (a.display_order !== b.display_order) {
          return a.display_order - b.display_order
        }
        return b.price - a.price
      })
      .slice(0, 6) // Take top 6 items
  })

  // Actions
  const fetchFullMenu = async (hotelId: string) => {
    if (isLoading.value) return

    isLoading.value = true
    error.value = null

    try {
      console.log('Fetching menu data for hotel:', hotelId)
      
      const { data, error: supabaseError } = await supabase
        .rpc('get_full_menu', { p_hotel_id: hotelId })

      if (supabaseError) {
        console.error('Supabase error:', supabaseError)
        throw new Error(`Menu yüklenirken hata oluştu: ${supabaseError.message}`)
      }

      if (!data) {
        console.warn('No menu data returned from Supabase')
        categories.value = []
        return
      }

      console.log('Menu data fetched successfully:', data)
      // Handle Json type from Supabase RPC
      if (Array.isArray(data)) {
        categories.value = data as unknown as MenuCategory[]
      } else {
        categories.value = []
      }
      lastFetchTime.value = new Date()

    } catch (err) {
      console.error('Error fetching menu:', err)
      error.value = err instanceof Error ? err.message : 'Menü yüklenirken bilinmeyen bir hata oluştu'
      
      // Set fallback data in case of error
      categories.value = []
    } finally {
      isLoading.value = false
    }
  }

  const clearMenu = () => {
    categories.value = []
    error.value = null
    lastFetchTime.value = null
  }

  const refreshMenu = async (hotelId: string) => {
    clearMenu()
    await fetchFullMenu(hotelId)
  }

  // Helper function to generate safe image URL for items
  const getItemImageUrl = (item: MenuItem, fallbackCategory?: string) => {
    const category = fallbackCategory || (item as any).category || 'food'
    return getSafeImageUrl(item.image_url, category)
  }

  return {
    // State
    categories,
    isLoading,
    error,
    lastFetchTime,
    
    // Computed
    menuItemsCount,
    availableItemsCount,
    getCategoryByName,
    getItemById,
    getItemsByCategoryId,
    recommendedItems,
    
    // Actions
    fetchFullMenu,
    clearMenu,
    refreshMenu,
    getItemImageUrl
  }
}) 