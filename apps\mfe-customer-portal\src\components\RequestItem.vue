<template>
  <div class="bg-white rounded-xl shadow-lg p-4 mb-4">
    <!-- Request Header -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center">
        <span class="text-2xl mr-3">{{ getRequestIcon(request.request_type) }}</span>
        <div>
          <h3 class="text-lg font-semibold text-navy-700">
            {{ getRequestTypeText(request.request_type) }}
          </h3>
          <p class="text-sm text-gray-600">
            {{ formatDate(request.created_at) }}
          </p>
        </div>
      </div>
      
      <!-- Request Status -->
      <div class="text-right">
        <span 
          :class="getStatusClass(request.status)"
          class="px-3 py-1 rounded-full text-xs font-medium"
        >
          {{ getStatusText(request.status) }}
        </span>
      </div>
    </div>

    <!-- Request Image -->
    <div v-if="getRequestImage(request.request_type)" class="mb-3">
      <img
        :src="getRequestImage(request.request_type)"
        :alt="getRequestTypeText(request.request_type)"
        class="w-full h-32 rounded-lg object-cover"
        @error="handleImageError"
      />
    </div>

    <!-- Request Description -->
    <div class="mb-3">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Talep Detayı:</h4>
      <div class="bg-gray-50 rounded-lg p-3">
        <p class="text-sm text-gray-700">{{ request.description }}</p>
      </div>
    </div>

    <!-- Room Information -->
    <div class="mb-3">
      <div class="flex items-center text-sm text-gray-600">
        <span class="text-lg mr-2">🏠</span>
        <span>Oda: {{ request.room_number }}</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex gap-2 mt-3">
      <!-- Cancel Button (only for pending and in_progress requests) -->
      <button
        v-if="request.status === 'pending' || request.status === 'in_progress'"
        @click="handleCancelRequest"
        :disabled="isLoading"
        class="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
      >
        <span v-if="isLoading">İptal Ediliyor...</span>
        <span v-else>Talebi İptal Et</span>
      </button>

      <!-- Create Similar Button -->
      <button
        @click="handleCreateSimilar"
        class="flex-1 bg-brand-500 hover:bg-brand-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
      >
        Benzer Talep Oluştur
      </button>

      <!-- Track Request Button -->
      <button
        v-if="request.status === 'pending' || request.status === 'in_progress'"
        @click="$emit('track', request.id)"
        class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
      >
        Talebi Takip Et
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">

import type { ServiceRequest } from '@hotelexia/shared-supabase-client'
import { getSafeImageUrl, handleImageError as handleImgError } from '@/utils/imageUtils'

interface Props {
  request: ServiceRequest
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const emit = defineEmits<{
  cancel: [requestId: string]
  createSimilar: [request: ServiceRequest]
  track: [requestId: string]
}>()

function handleCancelRequest() {
  if (confirm('Bu talebi iptal etmek istediğinizden emin misiniz?')) {
    emit('cancel', props.request.id)
  }
}

function handleCreateSimilar() {
  emit('createSimilar', props.request)
}

function getRequestIcon(type: string) {
  const icons = {
    'Housekeeping': '🧹',
    'Maintenance': '🔧',
    'Amenities': '🏊‍♂️',
    'Other': '📋'
  }
  return icons[type as keyof typeof icons] || '📋'
}

function getRequestTypeText(type: string) {
  const texts = {
    'Housekeeping': 'Temizlik Hizmeti',
    'Maintenance': 'Bakım Onarım',
    'Amenities': 'Otel Olanakları',
    'Other': 'Diğer Hizmetler'
  }
  return texts[type as keyof typeof texts] || type
}

function getRequestImage(type: string) {
  const category = type.toLowerCase()
  return getSafeImageUrl(undefined, category)
}

function getStatusClass(status: string) {
  const statusClasses = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'in_progress': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'canceled': 'bg-red-100 text-red-800'
  }
  return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string) {
  const statusTexts = {
    'pending': 'Bekliyor',
    'in_progress': 'İşlemde',
    'completed': 'Tamamlandı',
    'canceled': 'İptal Edildi'
  }
  return statusTexts[status as keyof typeof statusTexts] || 'Bilinmiyor'
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

function handleImageError(event: Event) {
  handleImgError(event, 'service')
}
</script> 