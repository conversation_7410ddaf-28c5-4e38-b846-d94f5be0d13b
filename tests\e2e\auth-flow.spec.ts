import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test('should handle login redirect flow', async ({ page }) => {
    // Start from app shell
    await page.goto('/')
    
    // Look for login button
    const loginButton = page.locator('a[href*="login"], button:has-text("Giriş"), button:has-text("Login")').first()
    
    if (await loginButton.isVisible()) {
      await loginButton.click()
      
      // Should show login form
      await expect(page.locator('input[type="email"], input[type="text"]')).toBeVisible({ timeout: 10000 })
      await expect(page.locator('input[type="password"]')).toBeVisible()
      
      // Check for role selection or login type
      const roleButtons = page.locator('button:has-text("Hotel"), button:has-text("Admin"), button:has-text("Super")')
      if (await roleButtons.count() > 0) {
        await expect(roleButtons.first()).toBeVisible()
      }
    }
  })

  test('should validate login form', async ({ page }) => {
    await page.goto('/')
    
    // Navigate to login
    const loginButton = page.locator('a[href*="login"], button:has-text("Giriş"), button:has-text("Login")').first()
    
    if (await loginButton.isVisible()) {
      await loginButton.click()
      
      // Wait for form to load
      await page.waitForSelector('input[type="email"], input[type="text"]', { timeout: 10000 })
      
      // Try to submit empty form
      const submitButton = page.locator('button[type="submit"], button:has-text("Giriş"), button:has-text("Login")').first()
      if (await submitButton.isVisible()) {
        await submitButton.click()
        
        // Should show validation errors or prevent submission
        const errorMessages = page.locator('.error, .invalid, [class*="error"]')
        const emailInput = page.locator('input[type="email"], input[type="text"]').first()
        
        // Either should show error messages or input should be marked as invalid
        const hasErrors = await errorMessages.count() > 0
        const inputInvalid = await emailInput.evaluate(el => !el.checkValidity())
        
        expect(hasErrors || inputInvalid).toBe(true)
      }
    }
  })

  test('should handle role-based routing', async ({ page }) => {
    await page.goto('/')
    
    // Test direct navigation to protected routes
    const protectedRoutes = [
      'http://localhost:5002', // Hotel dashboard
      'http://localhost:3001'  // Customer portal
    ]
    
    for (const route of protectedRoutes) {
      await page.goto(route)
      
      // Should either show login form or content (depending on auth state)
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      const hasContent = await page.locator('main, .dashboard, .portal').count() > 0
      
      expect(hasLoginForm || hasContent).toBe(true)
    }
  })

  test('should handle logout flow', async ({ page }) => {
    // Start from any authenticated area
    await page.goto('http://localhost:5002')
    
    // Look for logout button
    const logoutButton = page.locator('button:has-text("Çıkış"), button:has-text("Logout"), a:has-text("Çıkış"), a:has-text("Logout")').first()
    
    if (await logoutButton.isVisible()) {
      await logoutButton.click()
      
      // Should redirect to login or home page
      await page.waitForTimeout(2000) // Wait for redirect
      
      const currentUrl = page.url()
      const isLoginPage = currentUrl.includes('login') || 
                         await page.locator('input[type="email"], input[type="password"]').count() > 0
      const isHomePage = currentUrl === 'http://localhost:3000/' || currentUrl === '/'
      
      expect(isLoginPage || isHomePage).toBe(true)
    }
  })

  test('should persist authentication state', async ({ page, context }) => {
    // Test if authentication state persists across page reloads
    await page.goto('http://localhost:5002')
    
    // Check initial auth state
    const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const hasContent = await page.locator('main, .dashboard').count() > 0
    
    if (hasContent && !hasLoginForm) {
      // If already authenticated, test persistence
      await page.reload()
      
      // Should still be authenticated
      const stillHasContent = await page.locator('main, .dashboard').count() > 0
      const nowHasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      
      expect(stillHasContent && !nowHasLoginForm).toBe(true)
    }
  })

  test('should handle session timeout', async ({ page }) => {
    await page.goto('http://localhost:5002')
    
    // Check if there's any session timeout handling
    const hasContent = await page.locator('main, .dashboard').count() > 0
    
    if (hasContent) {
      // Wait for potential session timeout (short wait for testing)
      await page.waitForTimeout(5000)
      
      // Check if session is still valid
      await page.reload()
      
      const stillHasContent = await page.locator('main, .dashboard').count() > 0
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      
      // Should either maintain session or redirect to login
      expect(stillHasContent || hasLoginForm).toBe(true)
    }
  })

  test('should handle multiple tabs authentication', async ({ context }) => {
    // Create two pages (tabs)
    const page1 = await context.newPage()
    const page2 = await context.newPage()
    
    // Navigate both to protected areas
    await page1.goto('http://localhost:5002')
    await page2.goto('http://localhost:5002')
    
    // Check if auth state is consistent across tabs
    const page1HasLogin = await page1.locator('input[type="email"], input[type="password"]').count() > 0
    const page2HasLogin = await page2.locator('input[type="email"], input[type="password"]').count() > 0
    
    const page1HasContent = await page1.locator('main, .dashboard').count() > 0
    const page2HasContent = await page2.locator('main, .dashboard').count() > 0
    
    // Both tabs should have consistent auth state
    expect(page1HasLogin).toBe(page2HasLogin)
    expect(page1HasContent).toBe(page2HasContent)
    
    await page1.close()
    await page2.close()
  })
})
