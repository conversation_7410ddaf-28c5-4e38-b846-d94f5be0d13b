<template>
  <nav class="bg-white dark:bg-navy-800 border-b border-gray-200 dark:border-gray-700 px-4 md:px-10 py-4">
    <div class="flex items-center justify-between">
      <!-- Left Side - Hamburger Menu + Breadcrumbs -->
      <div class="flex items-center space-x-3">
        <!-- Hamburger Menu -->
        <button 
          @click="store.toggleSidebar()"
          class="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
        >
          <Bars3Icon class="h-5 w-5" />
        </button>
        
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <span>Sayfalar</span>
          <span class="mx-2">/</span>
          <span class="text-navy-700 dark:text-white font-medium">Dashboard</span>
        </div>
      </div>

      <!-- Right Side - Actions -->
      <div class="flex items-center space-x-4">
        <!-- Search -->
        <div class="relative hidden md:block">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="search"
            placeholder="Ara..."
            class="w-64 pl-10 pr-4 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
        </div>

        <!-- Notifications -->
        <NotificationBell />

        <!-- Dark Mode Toggle -->
        <button 
          @click="toggleDarkMode"
          class="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
        >
          <SunIcon v-if="store.darkMode" class="h-5 w-5" />
          <MoonIcon v-else class="h-5 w-5" />
        </button>

        <!-- User Profile -->
        <div class="relative">
          <button 
            @click="showUserMenu = !showUserMenu"
            class="flex items-center space-x-3 p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
          >
            <div class="w-8 h-8 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-medium">{{ userInitials }}</span>
            </div>
            <div class="hidden md:block text-left">
              <div class="text-sm font-medium text-navy-700 dark:text-white">{{ store.userName }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ store.hotelName }}</div>
            </div>
          </button>

          <!-- User Dropdown Menu -->
          <div 
            v-if="showUserMenu"
            class="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-navy-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
          >
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700">Profil</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700">Ayarlar</a>
            <hr class="my-1 border-gray-200 dark:border-gray-600">
            <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-navy-700">Çıkış Yap</a>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  MagnifyingGlassIcon,
  BellIcon,
  SunIcon,
  MoonIcon,
  Bars3Icon
} from '@heroicons/vue/24/outline'
import { useDashboardStore } from '@/stores/dashboardStore'
import NotificationBell from '@/components/notifications/NotificationBell.vue'

const store = useDashboardStore()
const showUserMenu = ref(false)

// Computed
const userInitials = computed(() => {
  const name = store.userName
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
})

// Methods
const toggleDarkMode = () => {
  store.toggleDarkMode()
}

const closeUserMenu = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserMenu.value = false
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', closeUserMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', closeUserMenu)
})
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style> 