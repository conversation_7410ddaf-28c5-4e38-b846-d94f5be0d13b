export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      rooms: {
        Row: {
          id: string;
          hotel_id: string | null;
          floor_id: string | null;
          room_number: string;
          room_type: string | null;
          status: 'CLEAN' | 'DIRTY' | 'OCCUPIED' | 'OUT_OF_ORDER' | 'MAINTENANCE' | 'INSPECTION' | null;
          max_occupancy: number | null;
          size_sqm: number | null;
          bed_type: string | null;
          amenities: Json | null;
          base_price: number | null;
          seasonal_rates: Json | null;
          last_cleaned: string | null;
          last_inspection: string | null;
          next_maintenance: string | null;
          current_guest_id: string | null;
          check_in_time: string | null;
          check_out_time: string | null;
          estimated_checkout: string | null;
          maintenance_notes: string | null;
          housekeeping_notes: string | null;
          special_requirements: Json | null;
          assigned_housekeeper_id: string | null;
          assigned_maintenance_id: string | null;
          is_active: boolean | null;
          out_of_order_reason: string | null;
          created_at: string | null;
          updated_at: string | null;
          floor: number | null;
          is_available: boolean | null;
        };
        Insert: {
          id?: string;
          hotel_id?: string | null;
          floor_id?: string | null;
          room_number: string;
          room_type?: string | null;
          status?: 'CLEAN' | 'DIRTY' | 'OCCUPIED' | 'OUT_OF_ORDER' | 'MAINTENANCE' | 'INSPECTION' | null;
          max_occupancy?: number | null;
          size_sqm?: number | null;
          bed_type?: string | null;
          amenities?: Json | null;
          base_price?: number | null;
          seasonal_rates?: Json | null;
          last_cleaned?: string | null;
          last_inspection?: string | null;
          next_maintenance?: string | null;
          current_guest_id?: string | null;
          check_in_time?: string | null;
          check_out_time?: string | null;
          estimated_checkout?: string | null;
          maintenance_notes?: string | null;
          housekeeping_notes?: string | null;
          special_requirements?: Json | null;
          assigned_housekeeper_id?: string | null;
          assigned_maintenance_id?: string | null;
          is_active?: boolean | null;
          out_of_order_reason?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          floor?: number | null;
          is_available?: boolean | null;
        };
        Update: {
          id?: string;
          hotel_id?: string | null;
          floor_id?: string | null;
          room_number?: string;
          room_type?: string | null;
          status?: 'CLEAN' | 'DIRTY' | 'OCCUPIED' | 'OUT_OF_ORDER' | 'MAINTENANCE' | 'INSPECTION' | null;
          max_occupancy?: number | null;
          size_sqm?: number | null;
          bed_type?: string | null;
          amenities?: Json | null;
          base_price?: number | null;
          seasonal_rates?: Json | null;
          last_cleaned?: string | null;
          last_inspection?: string | null;
          next_maintenance?: string | null;
          current_guest_id?: string | null;
          check_in_time?: string | null;
          check_out_time?: string | null;
          estimated_checkout?: string | null;
          maintenance_notes?: string | null;
          housekeeping_notes?: string | null;
          special_requirements?: Json | null;
          assigned_housekeeper_id?: string | null;
          assigned_maintenance_id?: string | null;
          is_active?: boolean | null;
          out_of_order_reason?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          floor?: number | null;
          is_available?: boolean | null;
        };
        Relationships: [];
      };
      maintenance_tasks: {
        Row: {
          id: string;
          created_at: string;
          hotel_id: string;
          room_id: string | null;
          assigned_to: string | null;
          title: string;
          description: string | null;
          priority: string | null;
          status: string;
        };
        Insert: {
          id?: string;
          created_at?: string;
          hotel_id: string;
          room_id?: string | null;
          assigned_to?: string | null;
          title: string;
          description?: string | null;
          priority?: string | null;
          status?: string;
        };
        Update: {
          id?: string;
          created_at?: string;
          hotel_id?: string;
          room_id?: string | null;
          assigned_to?: string | null;
          title?: string;
          description?: string | null;
          priority?: string | null;
          status?: string;
        };
        Relationships: [];
      };
      hotels: {
        Row: {
          address: string;
          amenities: string[] | null;
          check_in_time: string | null;
          check_out_time: string | null;
          city: string;
          country: string;
          created_at: string | null;
          description: string | null;
          email: string | null;
          id: string;
          image_urls: string[] | null;
          name: string;
          phone: string | null;
          rating: number | null;
          status: Database["public"]["Enums"]["hotel_status"] | null;
          updated_at: string | null;
          website: string | null;
        };
        Insert: {
          address: string;
          amenities?: string[] | null;
          check_in_time?: string | null;
          check_out_time?: string | null;
          city: string;
          country: string;
          created_at?: string | null;
          description?: string | null;
          email?: string | null;
          id?: string;
          image_urls?: string[] | null;
          name: string;
          phone?: string | null;
          rating?: number | null;
          status?: Database["public"]["Enums"]["hotel_status"] | null;
          updated_at?: string | null;
          website?: string | null;
        };
        Update: {
          address?: string;
          amenities?: string[] | null;
          check_in_time?: string | null;
          check_out_time?: string | null;
          city?: string;
          country?: string;
          created_at?: string | null;
          description?: string | null;
          email?: string | null;
          id?: string;
          image_urls?: string[] | null;
          name?: string;
          phone?: string | null;
          rating?: number | null;
          status?: Database["public"]["Enums"]["hotel_status"] | null;
          updated_at?: string | null;
          website?: string | null;
        };
        Relationships: [];
      };
      housekeeping_tasks: {
        Row: {
          assigned_to_user_id: string | null;
          created_at: string | null;
          description: string | null;
          due_date: string | null;
          hotel_id: string;
          id: string;
          priority: string | null;
          room_id: string | null;
          status: string | null;
          updated_at: string | null;
        };
        Insert: {
          assigned_to_user_id?: string | null;
          created_at?: string | null;
          description?: string | null;
          due_date?: string | null;
          hotel_id: string;
          id?: string;
          priority?: string | null;
          room_id?: string | null;
          status?: string | null;
          updated_at?: string | null;
        };
        Update: {
          assigned_to_user_id?: string | null;
          created_at?: string | null;
          description?: string | null;
          due_date?: string | null;
          hotel_id?: string;
          id?: string;
          priority?: string | null;
          room_id?: string | null;
          status?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "fk_room";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "hotel_rooms";
            referencedColumns: ["id"];
          },
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string | null;
          email: string;
          full_name: string | null;
          hotel_id: string | null;
          id: string;
          phone: string | null;
          role: Database["public"]["Enums"]["user_role"] | null;
          updated_at: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string | null;
          email: string;
          full_name?: string | null;
          hotel_id?: string | null;
          id: string;
          phone?: string | null;
          role?: Database["public"]["Enums"]["user_role"] | null;
          updated_at?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string | null;
          email?: string;
          full_name?: string | null;
          hotel_id?: string | null;
          id?: string;
          phone?: string | null;
          role?: Database["public"]["Enums"]["user_role"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "fk_profiles_hotel";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
        ];
      };
      reservations: {
        Row: {
          id: string;
          hotel_id: string;
          room_id: string;
          guest_name: string;
          guest_last_name: string;
          reservation_number: string;
          check_in_date: string;
          check_out_date: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          hotel_id: string;
          room_id: string;
          guest_name: string;
          guest_last_name: string;
          reservation_number: string;
          check_in_date: string;
          check_out_date: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          hotel_id?: string;
          room_id?: string;
          guest_name?: string;
          guest_last_name?: string;
          reservation_number?: string;
          check_in_date?: string;
          check_out_date?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "reservations_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "reservations_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "hotel_rooms";
            referencedColumns: ["id"];
          },
        ];
      };

      user_profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string | null;
          full_name: string | null;
          hotel_id: string | null;
          id: string;
          role: string;
          updated_at: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string | null;
          full_name?: string | null;
          hotel_id?: string | null;
          id: string;
          role: string;
          updated_at?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string | null;
          full_name?: string | null;
          hotel_id?: string | null;
          id?: string;
          role?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "user_profiles_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
        ];
      };
      menu_items: {
        Row: {
          id: string;
          hotel_id: string;
          name: string;
          description: string | null;
          price: number;
          category: 'Food' | 'Beverages' | 'Extras';
          image_url: string | null;
          is_available: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          hotel_id: string;
          name: string;
          description?: string | null;
          price: number;
          category: 'Food' | 'Beverages' | 'Extras';
          image_url?: string | null;
          is_available?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          hotel_id?: string;
          name?: string;
          description?: string | null;
          price?: number;
          category?: 'Food' | 'Beverages' | 'Extras';
          image_url?: string | null;
          is_available?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "menu_items_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
        ];
      };
      orders: {
        Row: {
          id: string;
          hotel_id: string;
          reservation_id: string;
          room_number: string;
          total_price: number;
          status: 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled';
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          hotel_id: string;
          reservation_id: string;
          room_number: string;
          total_price: number;
          status?: 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled';
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          hotel_id?: string;
          reservation_id?: string;
          room_number?: string;
          total_price?: number;
          status?: 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled';
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "orders_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "orders_reservation_id_fkey";
            columns: ["reservation_id"];
            isOneToOne: false;
            referencedRelation: "reservations";
            referencedColumns: ["id"];
          },
        ];
      };
      room_service_orders: {
        Row: {
          id: string;
          hotel_id: string | null;
          reservation_id: string | null;
          room_id: string | null;
          order_number: string;
          guest_name: string;
          room_number: string;
          guest_phone: string | null;
          status: 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled' | null;
          special_instructions: string | null;
          dietary_notes: string | null;
          order_time: string | null;
          requested_delivery_time: string | null;
          estimated_delivery_time: string | null;
          actual_delivery_time: string | null;
          preparation_started_at: string | null;
          preparation_completed_at: string | null;
          subtotal: number;
          tax_amount: number | null;
          service_charge: number | null;
          delivery_fee: number | null;
          discount_amount: number | null;
          total_amount: number;
          payment_method: string | null;
          payment_status: string | null;
          tip_amount: number | null;
          assigned_kitchen_staff_id: string | null;
          assigned_delivery_staff_id: string | null;
          kitchen_notes: string | null;
          guest_rating: number | null;
          guest_feedback: string | null;
          delivery_rating: number | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          hotel_id?: string | null;
          reservation_id?: string | null;
          room_id?: string | null;
          order_number: string;
          guest_name: string;
          room_number: string;
          guest_phone?: string | null;
          status?: 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled' | null;
          special_instructions?: string | null;
          dietary_notes?: string | null;
          order_time?: string | null;
          requested_delivery_time?: string | null;
          estimated_delivery_time?: string | null;
          actual_delivery_time?: string | null;
          preparation_started_at?: string | null;
          preparation_completed_at?: string | null;
          subtotal: number;
          tax_amount?: number | null;
          service_charge?: number | null;
          delivery_fee?: number | null;
          discount_amount?: number | null;
          total_amount: number;
          payment_method?: string | null;
          payment_status?: string | null;
          tip_amount?: number | null;
          assigned_kitchen_staff_id?: string | null;
          assigned_delivery_staff_id?: string | null;
          kitchen_notes?: string | null;
          guest_rating?: number | null;
          guest_feedback?: string | null;
          delivery_rating?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          hotel_id?: string | null;
          reservation_id?: string | null;
          room_id?: string | null;
          order_number?: string;
          guest_name?: string;
          room_number?: string;
          guest_phone?: string | null;
          status?: 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled' | null;
          special_instructions?: string | null;
          dietary_notes?: string | null;
          order_time?: string | null;
          requested_delivery_time?: string | null;
          estimated_delivery_time?: string | null;
          actual_delivery_time?: string | null;
          preparation_started_at?: string | null;
          preparation_completed_at?: string | null;
          subtotal?: number;
          tax_amount?: number | null;
          service_charge?: number | null;
          delivery_fee?: number | null;
          discount_amount?: number | null;
          total_amount?: number;
          payment_method?: string | null;
          payment_status?: string | null;
          tip_amount?: number | null;
          assigned_kitchen_staff_id?: string | null;
          assigned_delivery_staff_id?: string | null;
          kitchen_notes?: string | null;
          guest_rating?: number | null;
          guest_feedback?: string | null;
          delivery_rating?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      order_items: {
        Row: {
          id: string;
          order_id: string;
          menu_item_id: string;
          quantity: number;
          price_at_time_of_order: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          menu_item_id: string;
          quantity: number;
          price_at_time_of_order: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          menu_item_id?: string;
          quantity?: number;
          price_at_time_of_order?: number;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey";
            columns: ["order_id"];
            isOneToOne: false;
            referencedRelation: "orders";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "order_items_menu_item_id_fkey";
            columns: ["menu_item_id"];
            isOneToOne: false;
            referencedRelation: "menu_items";
            referencedColumns: ["id"];
          },
        ];
      };
      service_requests: {
        Row: {
          id: string;
          hotel_id: string;
          reservation_id: string;
          room_number: string | null;
          request_type: 'Housekeeping' | 'Maintenance' | 'Amenities' | 'Other';
          description: string;
          status: 'pending' | 'in_progress' | 'completed' | 'canceled';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          hotel_id: string;
          reservation_id: string;
          room_number?: string | null;
          request_type: 'Housekeeping' | 'Maintenance' | 'Amenities' | 'Other';
          description: string;
          status?: 'pending' | 'in_progress' | 'completed' | 'canceled';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          hotel_id?: string;
          reservation_id?: string;
          room_number?: string | null;
          request_type?: 'Housekeeping' | 'Maintenance' | 'Amenities' | 'Other';
          description?: string;
          status?: 'pending' | 'in_progress' | 'completed' | 'canceled';
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "service_requests_hotel_id_fkey";
            columns: ["hotel_id"];
            isOneToOne: false;
            referencedRelation: "hotels";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "service_requests_reservation_id_fkey";
            columns: ["reservation_id"];
            isOneToOne: false;
            referencedRelation: "reservations";
            referencedColumns: ["id"];
          },
        ];
      };
      hotel_activities: {
        Row: {
          id: string;
          hotel_id: string | null;
          category_id: string | null;
          title: string;
          title_en: string | null;
          description: string | null;
          description_en: string | null;
          activity_code: string | null;
          start_time: string | null;
          end_time: string | null;
          is_recurring: boolean | null;
          recurrence_pattern: Json | null;
          location: string | null;
          indoor_location: boolean | null;
          weather_dependent: boolean | null;
          max_participants: number | null;
          min_participants: number | null;
          current_registrations: number | null;
          price: number | null;
          member_price: number | null;
          child_price: number | null;
          age_min: number | null;
          age_max: number | null;
          skill_level: string | null;
          physical_requirements: string | null;
          equipment_provided: Json | null;
          what_to_bring: Json | null;
          instructor_id: string | null;
          instructor_name: string | null;
          staff_required: number | null;
          equipment_needed: Json | null;
          registration_required: boolean | null;
          advance_booking_required: boolean | null;
          cancellation_policy: string | null;
          waitlist_enabled: boolean | null;
          image_url: string | null;
          gallery_images: Json | null;
          is_featured: boolean | null;
          is_promoted: boolean | null;
          tags: Json | null;
          is_active: boolean | null;
          status: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          hotel_id?: string | null;
          category_id?: string | null;
          title: string;
          title_en?: string | null;
          description?: string | null;
          description_en?: string | null;
          activity_code?: string | null;
          start_time?: string | null;
          end_time?: string | null;
          is_recurring?: boolean | null;
          recurrence_pattern?: Json | null;
          location?: string | null;
          indoor_location?: boolean | null;
          weather_dependent?: boolean | null;
          max_participants?: number | null;
          min_participants?: number | null;
          current_registrations?: number | null;
          price?: number | null;
          member_price?: number | null;
          child_price?: number | null;
          age_min?: number | null;
          age_max?: number | null;
          skill_level?: string | null;
          physical_requirements?: string | null;
          equipment_provided?: Json | null;
          what_to_bring?: Json | null;
          instructor_id?: string | null;
          instructor_name?: string | null;
          staff_required?: number | null;
          equipment_needed?: Json | null;
          registration_required?: boolean | null;
          advance_booking_required?: boolean | null;
          cancellation_policy?: string | null;
          waitlist_enabled?: boolean | null;
          image_url?: string | null;
          gallery_images?: Json | null;
          is_featured?: boolean | null;
          is_promoted?: boolean | null;
          tags?: Json | null;
          is_active?: boolean | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          hotel_id?: string | null;
          category_id?: string | null;
          title?: string;
          title_en?: string | null;
          description?: string | null;
          description_en?: string | null;
          activity_code?: string | null;
          start_time?: string | null;
          end_time?: string | null;
          is_recurring?: boolean | null;
          recurrence_pattern?: Json | null;
          location?: string | null;
          indoor_location?: boolean | null;
          weather_dependent?: boolean | null;
          max_participants?: number | null;
          min_participants?: number | null;
          current_registrations?: number | null;
          price?: number | null;
          member_price?: number | null;
          child_price?: number | null;
          age_min?: number | null;
          age_max?: number | null;
          skill_level?: string | null;
          physical_requirements?: string | null;
          equipment_provided?: Json | null;
          what_to_bring?: Json | null;
          instructor_id?: string | null;
          instructor_name?: string | null;
          staff_required?: number | null;
          equipment_needed?: Json | null;
          registration_required?: boolean | null;
          advance_booking_required?: boolean | null;
          cancellation_policy?: string | null;
          waitlist_enabled?: boolean | null;
          image_url?: string | null;
          gallery_images?: Json | null;
          is_featured?: boolean | null;
          is_promoted?: boolean | null;
          tags?: Json | null;
          is_active?: boolean | null;
          status?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      service_categories: {
        Row: {
          id: string;
          hotel_id: string | null;
          name: string;
          name_en: string | null;
          description: string | null;
          description_en: string | null;
          category_type: string;
          icon: string | null;
          color: string | null;
          image_url: string | null;
          display_order: number | null;
          is_active: boolean | null;
          is_featured: boolean | null;
          min_advance_booking: number | null;
          max_advance_booking: number | null;
          operating_hours: Json | null;
          availability_rules: Json | null;
          base_markup_percentage: number | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          hotel_id?: string | null;
          name: string;
          name_en?: string | null;
          description?: string | null;
          description_en?: string | null;
          category_type: string;
          icon?: string | null;
          color?: string | null;
          image_url?: string | null;
          display_order?: number | null;
          is_active?: boolean | null;
          is_featured?: boolean | null;
          min_advance_booking?: number | null;
          max_advance_booking?: number | null;
          operating_hours?: Json | null;
          availability_rules?: Json | null;
          base_markup_percentage?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          hotel_id?: string | null;
          name?: string;
          name_en?: string | null;
          description?: string | null;
          description_en?: string | null;
          category_type?: string;
          icon?: string | null;
          color?: string | null;
          image_url?: string | null;
          display_order?: number | null;
          is_active?: boolean | null;
          is_featured?: boolean | null;
          min_advance_booking?: number | null;
          max_advance_booking?: number | null;
          operating_hours?: Json | null;
          availability_rules?: Json | null;
          base_markup_percentage?: number | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      guest_notifications: {
        Row: {
          id: string;
          hotel_id: string | null;
          reservation_id: string | null;
          template_id: string | null;
          title: string;
          message: string;
          notification_type: string | null;
          channels: string[];
          guest_name: string | null;
          room_number: string | null;
          guest_email: string | null;
          guest_phone: string | null;
          scheduled_at: string | null;
          expires_at: string | null;
          status: string | null;
          sent_at: string | null;
          delivered_at: string | null;
          read_at: string | null;
          in_app_status: string | null;
          email_status: string | null;
          sms_status: string | null;
          whatsapp_status: string | null;
          push_status: string | null;
          metadata: Json | null;
          delivery_attempts: number | null;
          failure_reason: string | null;
          clicked_at: string | null;
          action_taken: string | null;
          guest_feedback: string | null;
          sent_by: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          hotel_id?: string | null;
          reservation_id?: string | null;
          template_id?: string | null;
          title: string;
          message: string;
          notification_type?: string | null;
          channels: string[];
          guest_name?: string | null;
          room_number?: string | null;
          guest_email?: string | null;
          guest_phone?: string | null;
          scheduled_at?: string | null;
          expires_at?: string | null;
          status?: string | null;
          sent_at?: string | null;
          delivered_at?: string | null;
          read_at?: string | null;
          in_app_status?: string | null;
          email_status?: string | null;
          sms_status?: string | null;
          whatsapp_status?: string | null;
          push_status?: string | null;
          metadata?: Json | null;
          delivery_attempts?: number | null;
          failure_reason?: string | null;
          clicked_at?: string | null;
          action_taken?: string | null;
          guest_feedback?: string | null;
          sent_by?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          hotel_id?: string | null;
          reservation_id?: string | null;
          template_id?: string | null;
          title?: string;
          message?: string;
          notification_type?: string | null;
          channels?: string[];
          guest_name?: string | null;
          room_number?: string | null;
          guest_email?: string | null;
          guest_phone?: string | null;
          scheduled_at?: string | null;
          expires_at?: string | null;
          status?: string | null;
          sent_at?: string | null;
          delivered_at?: string | null;
          read_at?: string | null;
          in_app_status?: string | null;
          email_status?: string | null;
          sms_status?: string | null;
          whatsapp_status?: string | null;
          push_status?: string | null;
          metadata?: Json | null;
          delivery_attempts?: number | null;
          failure_reason?: string | null;
          clicked_at?: string | null;
          action_taken?: string | null;
          guest_feedback?: string | null;
          sent_by?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      staff_notifications: {
        Row: {
          id: string;
          hotel_id: string | null;
          recipient_id: string | null;
          title: string;
          message: string;
          notification_type: string | null;
          priority: string | null;
          category: string | null;
          related_task_id: string | null;
          related_order_id: string | null;
          related_request_id: string | null;
          is_read: boolean | null;
          read_at: string | null;
          is_urgent: boolean | null;
          requires_action: boolean | null;
          action_taken: boolean | null;
          action_taken_at: string | null;
          metadata: Json | null;
          sent_by: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          hotel_id?: string | null;
          recipient_id?: string | null;
          title: string;
          message: string;
          notification_type?: string | null;
          priority?: string | null;
          category?: string | null;
          related_task_id?: string | null;
          related_order_id?: string | null;
          related_request_id?: string | null;
          is_read?: boolean | null;
          read_at?: string | null;
          is_urgent?: boolean | null;
          requires_action?: boolean | null;
          action_taken?: boolean | null;
          action_taken_at?: string | null;
          metadata?: Json | null;
          sent_by?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          hotel_id?: string | null;
          recipient_id?: string | null;
          title?: string;
          message?: string;
          notification_type?: string | null;
          priority?: string | null;
          category?: string | null;
          related_task_id?: string | null;
          related_order_id?: string | null;
          related_request_id?: string | null;
          is_read?: boolean | null;
          read_at?: string | null;
          is_urgent?: boolean | null;
          requires_action?: boolean | null;
          action_taken?: boolean | null;
          action_taken_at?: string | null;
          metadata?: Json | null;
          sent_by?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_orders_by_reservation: {
        Args: {
          p_reservation_id: string;
        };
        Returns: Array<{
          id: string;
          reservation_id: string;
          hotel_id: string;
          room_number: string;
          status: string;
          total_price: number;
          notes: string | null;
          created_at: string;
          updated_at: string;
        }>;
      };
      get_orders_with_items_by_reservation: {
        Args: {
          p_reservation_id: string;
        };
        Returns: Json;
      };
      place_order_by_reservation: {
        Args: {
          p_reservation_id: string;
          p_hotel_id: string;
          p_room_number: string;
          p_total_price: number;
          p_notes?: string;
          p_order_items?: Json;
        };
        Returns: string;
      };
      cancel_order_by_reservation: {
        Args: {
          order_id_to_cancel: string;
          user_reservation_id: string;
        };
        Returns: Json;
      };
      cancel_service_request_by_reservation: {
        Args: {
          service_request_id_to_cancel: string;
          user_reservation_id: string;
        };
        Returns: Json;
      };
      get_full_menu: {
        Args: {
          p_hotel_id: string;
        };
        Returns: Json;
      };
    };
    Enums: {
      hotel_status: "active" | "inactive" | "pending";
      payment_status: "pending" | "paid" | "partially_paid" | "refunded";
      reservation_status:
        | "pending"
        | "confirmed"
        | "checked_in"
        | "checked_out"
        | "cancelled";
      room_status: "available" | "occupied" | "maintenance" | "out_of_order";
      user_role: "customer" | "hotel_staff" | "hotel_manager" | "system_admin";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

// Helper types for easier access
export type DbTable<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Row"];
export type DbEnum<T extends keyof Database["public"]["Enums"]> =
  Database["public"]["Enums"][T];

// Specific types for easier usage
export type Profile = DbTable<"profiles">;
export type UserProfile = DbTable<"user_profiles">;
export type Hotel = DbTable<"hotels">;
export type Room = DbTable<"rooms">;

export type HousekeepingTask = DbTable<"housekeeping_tasks">;
export type Reservation = DbTable<"reservations">;

// Enums
export type UserRole = DbEnum<"user_role">;
export type RoomStatus = DbEnum<"room_status">;
export type HotelStatus = DbEnum<"hotel_status">;
export type ReservationStatus = DbEnum<"reservation_status">;
export type PaymentStatus = DbEnum<"payment_status">;

export const Constants = {
  public: {
    Enums: {
      hotel_status: ["active", "inactive", "pending"],
      payment_status: ["pending", "paid", "partially_paid", "refunded"],
      reservation_status: [
        "pending",
        "confirmed",
        "checked_in",
        "checked_out",
        "cancelled",
      ],
      room_status: ["available", "occupied", "maintenance", "out_of_order"],
      user_role: ["customer", "hotel_staff", "hotel_manager", "system_admin"],
    },
  },
} as const;

// Customer Portal specific types
export interface GuestSession {
  id: string;
  hotel_id: string;
  room_id: string;
  guest_name: string;
  guest_last_name: string;
  guest_phone?: string;
  reservation_number: string;
  check_in_date: string;
  check_out_date: string;
  room_number: string;
}

export interface MenuItem {
  id: string;
  hotel_id: string;
  name: string;
  name_en?: string | null;
  description: string | null;
  description_en?: string | null;
  item_code?: string | null;
  price: number;
  cost?: number | null;
  seasonal_prices?: any | null;
  calories?: number | null;
  ingredients?: any | null;
  allergens?: any | null;
  nutritional_info?: any | null;
  is_vegetarian?: boolean | null;
  is_vegan?: boolean | null;
  is_gluten_free?: boolean | null;
  is_halal?: boolean | null;
  is_kosher?: boolean | null;
  preparation_time_minutes?: number | null;
  kitchen_station?: string | null;
  complexity_level?: number | null;
  is_available: boolean;
  availability_hours?: any | null;
  seasonal_availability?: any | null;
  stock_quantity?: number | null;
  low_stock_threshold?: number | null;
  image_url: string | null;
  gallery_images?: any | null;
  display_order?: number | null;
  is_featured?: boolean | null;
  is_chef_special?: boolean | null;
  is_new?: boolean | null;
  tags?: string[] | null;
  course_type?: string | null;
  portion_size?: string | null;
  serving_temperature?: string | null;
  created_by?: string | null;
  updated_by?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface CartItem extends MenuItem {
  quantity: number;
}

export interface Order {
  id: string;
  hotel_id?: string | null;
  reservation_id?: string | null;
  room_id?: string | null;
  order_number: string;
  guest_name: string;
  room_number: string;
  guest_phone?: string | null;
  status?: OrderStatus | null;
  special_instructions?: string | null;
  dietary_notes?: string | null;
  order_time?: string | null;
  requested_delivery_time?: string | null;
  estimated_delivery_time?: string | null;
  actual_delivery_time?: string | null;
  preparation_started_at?: string | null;
  preparation_completed_at?: string | null;
  subtotal: number;
  tax_amount?: number | null;
  service_charge?: number | null;
  delivery_fee?: number | null;
  discount_amount?: number | null;
  total_amount: number;
  payment_method?: string | null;
  payment_status?: string | null;
  tip_amount?: number | null;
  assigned_kitchen_staff_id?: string | null;
  assigned_delivery_staff_id?: string | null;
  kitchen_notes?: string | null;
  guest_rating?: number | null;
  guest_feedback?: string | null;
  delivery_rating?: number | null;
  created_at?: string | null;
  updated_at?: string | null;
  order_items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  order_id: string;
  menu_item_id: string;
  quantity: number;
  price_at_time_of_order: number;
  created_at: string;
  menu_item?: MenuItem;
}

// Type helpers for Customer Portal
export type MenuCategory = 'Food' | 'Beverages' | 'Desserts' | 'Extras';
export type OrderStatus = 'pending' | 'confirmed' | 'in_progress' | 'preparing' | 'on_the_way' | 'delivered' | 'canceled';

// Service Request types
export type ServiceRequest = DbTable<"service_requests">;
export type ServiceRequestType = 'Housekeeping' | 'Maintenance' | 'Amenities' | 'Other';
export type ServiceRequestStatus = 'pending' | 'in_progress' | 'completed' | 'canceled';

export interface CreateRequestData {
  request_type: ServiceRequestType;
  description: string;
}
