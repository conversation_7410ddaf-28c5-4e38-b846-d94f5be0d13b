---
type: "agent_requested"
---

# MFE: Customer Portal Integration Summary

## 1. Purpose
A guest-facing portal for searching hotels, creating and managing reservations, and accessing guest-specific services.

## 2. Exposed Components (`vite.config.ts`)
- `GuestDashboardView.vue` (as `./DashboardView`)
- `RoomServiceView.vue`
- `ServicesHomeView.vue`
- `ActivitiesView.vue`
- `ActivityDetailView.vue`
- `ProfileView.vue`
- `ReservationsView.vue`
- `DevLoginView.vue`

## 3. State Management
- **Consumed:** Relies on the global `userStore` from `app-shell` to get authenticated user information.
- **Internal:** Manages its own local state for UI and forms. Uses `guestAuthStore` for development-specific login flows.

## 4. API & Backend Interaction
- Interacts with the shared Supabase client.
- **Primary Calls:**
    - Fetching hotel listings and details.
    - Checking room availability.
    - Creating new reservations in the `reservations` table.
    - Fetching personal reservation history for the logged-in user.
    - Submitting service requests to the `service_requests` table.

