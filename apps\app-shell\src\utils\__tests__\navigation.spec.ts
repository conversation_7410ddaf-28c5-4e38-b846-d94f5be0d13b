import { describe, it, expect } from 'vitest'

// Simple navigation utilities for app shell
export function isActiveRoute(currentPath: string, routePath: string): boolean {
  if (routePath === '/') {
    return currentPath === '/'
  }
  return currentPath.startsWith(routePath)
}

export function generateBreadcrumbs(path: string): Array<{ name: string; path: string }> {
  const segments = path.split('/').filter(Boolean)
  const breadcrumbs = [{ name: 'Ana Sayfa', path: '/' }]
  
  let currentPath = ''
  segments.forEach(segment => {
    currentPath += `/${segment}`
    const name = segment.charAt(0).toUpperCase() + segment.slice(1)
    breadcrumbs.push({ name, path: currentPath })
  })
  
  return breadcrumbs
}

export function getPageTitle(routeName: string): string {
  const titleMap: Record<string, string> = {
    'home': 'Ana Sayfa - Hotelexia',
    'about': 'Hakkımızda - Hotelexia',
    'features': 'Özellikler - Hotelexia',
    'contact': 'İletişim - Hotelexia',
    'login': 'Giriş Yap - Hotelexia'
  }
  return titleMap[routeName] || 'Hotelexia'
}

export function isExternalLink(url: string): boolean {
  return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//')
}

describe('Navigation Utilities', () => {
  describe('isActiveRoute', () => {
    it('correctly identifies active home route', () => {
      expect(isActiveRoute('/', '/')).toBe(true)
      expect(isActiveRoute('/about', '/')).toBe(false)
    })

    it('correctly identifies active nested routes', () => {
      expect(isActiveRoute('/about', '/about')).toBe(true)
      expect(isActiveRoute('/about/team', '/about')).toBe(true)
      expect(isActiveRoute('/contact', '/about')).toBe(false)
    })

    it('handles route matching correctly', () => {
      expect(isActiveRoute('/features', '/features')).toBe(true)
      expect(isActiveRoute('/features/pricing', '/features')).toBe(true)
      expect(isActiveRoute('/feat', '/features')).toBe(false)
    })
  })

  describe('generateBreadcrumbs', () => {
    it('generates breadcrumbs for home page', () => {
      const breadcrumbs = generateBreadcrumbs('/')
      expect(breadcrumbs).toEqual([
        { name: 'Ana Sayfa', path: '/' }
      ])
    })

    it('generates breadcrumbs for single level path', () => {
      const breadcrumbs = generateBreadcrumbs('/about')
      expect(breadcrumbs).toEqual([
        { name: 'Ana Sayfa', path: '/' },
        { name: 'About', path: '/about' }
      ])
    })

    it('generates breadcrumbs for nested paths', () => {
      const breadcrumbs = generateBreadcrumbs('/features/pricing')
      expect(breadcrumbs).toEqual([
        { name: 'Ana Sayfa', path: '/' },
        { name: 'Features', path: '/features' },
        { name: 'Pricing', path: '/features/pricing' }
      ])
    })

    it('handles empty segments correctly', () => {
      const breadcrumbs = generateBreadcrumbs('/about/')
      expect(breadcrumbs).toEqual([
        { name: 'Ana Sayfa', path: '/' },
        { name: 'About', path: '/about' }
      ])
    })
  })

  describe('getPageTitle', () => {
    it('returns correct titles for known routes', () => {
      expect(getPageTitle('home')).toBe('Ana Sayfa - Hotelexia')
      expect(getPageTitle('about')).toBe('Hakkımızda - Hotelexia')
      expect(getPageTitle('features')).toBe('Özellikler - Hotelexia')
      expect(getPageTitle('contact')).toBe('İletişim - Hotelexia')
      expect(getPageTitle('login')).toBe('Giriş Yap - Hotelexia')
    })

    it('returns default title for unknown routes', () => {
      expect(getPageTitle('unknown')).toBe('Hotelexia')
      expect(getPageTitle('')).toBe('Hotelexia')
    })
  })

  describe('isExternalLink', () => {
    it('identifies external HTTP links', () => {
      expect(isExternalLink('http://example.com')).toBe(true)
      expect(isExternalLink('https://example.com')).toBe(true)
      expect(isExternalLink('//example.com')).toBe(true)
    })

    it('identifies internal links', () => {
      expect(isExternalLink('/about')).toBe(false)
      expect(isExternalLink('about')).toBe(false)
      expect(isExternalLink('#section')).toBe(false)
      expect(isExternalLink('mailto:<EMAIL>')).toBe(false)
    })
  })
})
