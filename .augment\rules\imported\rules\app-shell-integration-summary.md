---
type: "agent_requested"
---

# App Shell Integration Summary

The `app-shell` is the orchestrator of the Hotelexia platform.

## 1. Role and Responsibilities
- **Primary Container:** Renders the main layout, including `AppHeader.vue` and `AppFooter.vue`.
- **Global State Manager:** Owns and provides the global `userStore` (via Pinia) to all MFEs, managing the authenticated user's state, profile, and role.
- **Top-Level Router:** Manages the main application routing using `vue-router`, lazy-loading MFEs based on the URL path.

## 2. Module Federation Configuration (`vite.config.ts`)
The shell consumes the following remotes:
- `mfe_auth`
- `mfe_customer_portal`
- `mfe_hotel_dashboard`
- `mfe_management`

## 3. Routing Logic (`src/router/index.ts`)
- The router uses dynamic, lazy-loaded components to render views from the MFEs.
- **Auth Routes (`/login`, `/register`):** Loads components from `mfe_auth`.
- **Portal Routes (`/customer`, `/hotel`, `/manager`, `/dashboard`):** Loads the main application component from the corresponding MFE.
- **Nested Routes:** Each MFE is responsible for its own nested routing (e.g., `/hotel/maintenance/tasks`). The app-shell provides the entry point.
- **Auth Guards:** Implements `beforeEach` guards to protect routes based on authentication status and user roles defined in the `userStore`.

## 4. Shared Dependencies
The shell shares singleton instances of `vue`, `pinia`, `vue-router`, and `@supabase/supabase-js` with all MFEs to ensure consistency and performance.
