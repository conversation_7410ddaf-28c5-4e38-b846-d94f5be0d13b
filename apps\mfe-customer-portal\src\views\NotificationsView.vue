<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-6">
        <div class="flex items-center justify-between">
          <button @click="goBack" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <svg class="w-6 h-6 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          </button>
          <h1 class="text-2xl font-bold text-textDark">Bildirimler</h1>
          <button 
            v-if="filteredNotifications.length > 0" 
            @click="markAllAsRead"
            class="text-sm text-primary hover:text-teal-600 font-medium transition-colors"
          >
            Tü<PERSON><PERSON><PERSON><PERSON> Okundu İşaretle
          </button>
          <div v-else class="w-10"></div>
        </div>
        
        <!-- Stats -->
        <div v-if="allNotifications.length > 0" class="mt-4 flex gap-4 text-sm text-textMedium">
          <span>{{ allNotifications.length }} toplam bildirim</span>
          <span v-if="customerDataStore.unreadNotifications.length > 0" class="text-primary font-medium">
            {{ customerDataStore.unreadNotifications.length }} okunmamış
          </span>
        </div>
      </div>
    </div>

    <!-- Filter Tabs -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-3">
        <div class="flex space-x-1 overflow-x-auto">
          <button 
            v-for="filter in filters" 
            :key="filter.value"
            @click="activeFilter = filter.value"
            :class="[
              'flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-colors',
              activeFilter === filter.value 
                ? 'bg-primary text-white' 
                : 'bg-gray-100 text-textMedium hover:bg-gray-200'
            ]"
          >
            <span class="mr-2">{{ filter.icon }}</span>
            {{ filter.label }}
            <span v-if="filter.count > 0" class="ml-2 px-2 py-0.5 bg-black/10 rounded-full text-xs">
              {{ filter.count }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="px-4 py-6">
      <!-- Notifications List -->
      <div v-if="filteredNotifications.length > 0" class="space-y-3">
        <component 
          v-for="notification in filteredNotifications" 
          :key="notification.id"
          :is="notification.linkTo ? 'router-link' : 'div'"
          :to="notification.linkTo"
          @click="() => handleNotificationClick(notification)"
          :class="[
            'block bg-backgroundWhite rounded-2xl p-4 transition-all duration-200',
            notification.linkTo ? 'hover:shadow-lg cursor-pointer' : 'cursor-default',
            !notification.isRead ? 'ring-2 ring-primary/20 bg-primary/5' : ''
          ]"
        >
          <div class="flex items-start space-x-3">
            <!-- Notification Icon -->
            <div :class="[
              'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-lg',
              getNotificationColorClass(notification.type)
            ]">
              {{ notificationStore.getNotificationIcon(notification.type) }}
            </div>
            
            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-1">
                    <h3 :class="[
                      'font-semibold text-textDark',
                      !notification.isRead ? 'font-bold' : 'font-medium'
                    ]">
                      {{ notification.title }}
                    </h3>
                    <div v-if="!notification.isRead" class="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
                  </div>
                  <p class="text-textMedium text-sm mb-2 leading-relaxed">{{ notification.message }}</p>
                  <div class="flex items-center gap-3 text-xs text-textLight">
                    <span class="bg-gray-100 px-2 py-1 rounded-lg">
                      {{ notificationStore.getNotificationTypeText(notification.type) }}
                    </span>
                    <span>{{ formatRelativeTime(notification.timestamp) }}</span>
                  </div>
                </div>
                
                <!-- Action Button -->
                <button 
                  v-if="!notification.isRead"
                  @click.stop="() => markNotificationAsRead(notification.id)"
                  class="flex-shrink-0 p-2 text-textLight hover:text-primary transition-colors"
                  title="Okundu işaretle"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </component>
      </div>

      <!-- Empty State -->
      <div v-else class="bg-backgroundWhite rounded-2xl p-8 text-center">
        <div class="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-10 h-10 text-textLight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 1h7v5H4V1zm7 7h5v5h-5V8zM4 8h5v5H4V8z"/>
          </svg>
        </div>
        
        <h2 class="text-xl font-semibold text-textDark mb-4">
          {{ activeFilter === 'all' ? 'Henüz bildirim yok' : `${getActiveFilterLabel()} bildirimi yok` }}
        </h2>
        <p class="text-textMedium mb-6">
          {{ activeFilter === 'all' 
            ? 'Sipariş durumları ve önemli güncellemeler burada görünecek.' 
            : `${getActiveFilterLabel()} kategorisinde henüz bildirim bulunmuyor.` 
          }}
        </p>
        
        <button 
          @click="goBack"
          class="bg-primary hover:bg-teal-600 text-white px-6 py-3 rounded-xl text-sm font-medium transition-colors"
        >
          Ana Sayfaya Dön
        </button>
      </div>

      <!-- Quick Actions -->
      <div v-if="notificationStore.notifications.length > 10" class="mt-6 bg-backgroundWhite rounded-2xl p-4">
        <h3 class="font-semibold text-textDark mb-3">Hızlı İşlemler</h3>
        <div class="flex gap-3">
          <button 
            @click="notificationStore.clearOldNotifications"
            class="flex-1 bg-gray-100 hover:bg-gray-200 text-textDark px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            Eski Bildirimleri Temizle
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/notificationStore'
import { useCustomerDataStore } from '@/stores/customerDataStore'
import type { Notification } from '@/stores/notificationStore'

const router = useRouter()
const notificationStore = useNotificationStore()
const customerDataStore = useCustomerDataStore()

const activeFilter = ref<'all' | 'order' | 'service' | 'activity' | 'alert'>('all')

// Combine live notifications with dummy notifications
const allNotifications = computed(() => {
  const liveNotifications = customerDataStore.notifications
  const dummyNotifications = notificationStore.notifications
  return [...liveNotifications, ...dummyNotifications]
})

const filters = computed(() => [
  {
    value: 'all' as const,
    label: 'Tümü',
    icon: '📱',
    count: allNotifications.value.length
  },
  {
    value: 'order' as const,
    label: 'Siparişler',
    icon: '🍽️',
    count: allNotifications.value.filter(n => n.type === 'order').length
  },
  {
    value: 'service' as const,
    label: 'Hizmetler',
    icon: '🏨',
    count: allNotifications.value.filter(n => n.type === 'service').length
  },
  {
    value: 'activity' as const,
    label: 'Aktiviteler',
    icon: '🎯',
    count: allNotifications.value.filter(n => n.type === 'activity').length
  },
  {
    value: 'alert' as const,
    label: 'Uyarılar',
    icon: '⚠️',
    count: allNotifications.value.filter(n => n.type === 'alert').length
  }
])

const filteredNotifications = computed(() => {
  const notifications = allNotifications.value
  if (activeFilter.value === 'all') {
    return notifications
  }
  return notifications.filter(n => n.type === activeFilter.value)
})

const getNotificationColorClass = (type: Notification['type']): string => {
  switch (type) {
    case 'order':
      return 'bg-orange-100 text-orange-600'
    case 'service':
      return 'bg-blue-100 text-blue-600'
    case 'activity':
      return 'bg-green-100 text-green-600'
    case 'alert':
      return 'bg-red-100 text-red-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

const getActiveFilterLabel = (): string => {
  const filter = filters.value.find(f => f.value === activeFilter.value)
  return filter?.label || 'Bildirim'
}

const formatRelativeTime = (timestamp: Date): string => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return 'Şimdi'
  if (minutes < 60) return `${minutes} dakika önce`
  if (hours < 24) return `${hours} saat önce`
  if (days === 1) return 'Dün'
  if (days < 7) return `${days} gün önce`
  
  return timestamp.toLocaleDateString('tr-TR', {
    day: 'numeric',
    month: 'short'
  })
}

const handleNotificationClick = (notification: Notification) => {
  if (!notification.isRead) {
    notificationStore.markAsRead(notification.id)
  }
  
  if (notification.linkTo && router.currentRoute.value.path !== notification.linkTo) {
    router.push(notification.linkTo)
  }
}

const markNotificationAsRead = (notificationId: number) => {
  notificationStore.markAsRead(notificationId)
}

const markAllAsRead = () => {
  notificationStore.markAllAsRead()
}

const goBack = () => {
  router.back()
}

// Fetch live notifications on mount
onMounted(async () => {
  await customerDataStore.fetchNotifications()
})
</script>

<style scoped>
/* Component-specific styles */
</style> 