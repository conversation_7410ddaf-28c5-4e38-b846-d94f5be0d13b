{"extends": "../../tsconfig.json", "compilerOptions": {"declaration": true, "declarationMap": true, "outDir": "./dist", "module": "ES2020", "moduleResolution": "bundler", "allowImportingTsExtensions": false, "composite": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "resolveJsonModule": true, "types": ["node", "vite/client"], "skipLibCheck": true, "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@hotelexia/shared-supabase-client": ["../shared-supabase-client/src/index.ts"]}}, "include": ["src/**/*", "src/**/*.json", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}