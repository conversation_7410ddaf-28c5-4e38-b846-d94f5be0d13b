import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export interface Notification {
  id: number;
  type: 'order' | 'service' | 'activity' | 'alert';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  linkTo?: string; // Optional: for navigation on click
}

export const useNotificationStore = defineStore('notifications', () => {
  const notifications = ref<Notification[]>([
    // Pre-populate with some dummy data
    { 
      id: 1, 
      type: 'order', 
      title: 'Siparişiniz Alındı', 
      message: 'Somon Steak siparişiniz mutfağımıza iletildi. Tahmini hazırlanma süresi 25-30 dakikadır.', 
      timestamp: new Date(Date.now() - 5 * 60000), 
      isRead: true, 
      linkTo: '/guest/cart' 
    },
    { 
      id: 2, 
      type: 'service', 
      title: 'Rezervasyon Onaylandı', 
      message: 'Yarın 11:00 için Tenis <PERSON> rezervasyonunuz onaylandı. Lütfen 10 dakika önce hazır olunuz.', 
      timestamp: new Date(Date.now() - 30 * 60000), 
      isRead: true, 
      linkTo: '/guest/activities' 
    },
    { 
      id: 3, 
      type: 'activity', 
      title: 'E<PERSON><PERSON><PERSON> Başlıyor!', 
      message: 'Aqua Aerobik etkinliği 15 dakika içinde Gösteri Havuzunda başlayacak. Katılım ücretsizdir.', 
      timestamp: new Date(Date.now() - 2 * 60000), 
      isRead: false, 
      linkTo: '/guest/activities' 
    },
    { 
      id: 4, 
      type: 'alert', 
      title: 'Bakım Bildirimi', 
      message: 'Yarın 14:00-16:00 arası asansör bakımı yapılacaktır. Bu sürede merdivenleri kullanabilirsiniz.', 
      timestamp: new Date(Date.now() - 45 * 60000), 
      isRead: false, 
      linkTo: undefined 
    },
    { 
      id: 5, 
      type: 'order', 
      title: 'Sipariş Teslim Edildi', 
      message: 'Dün akşam verdiğiniz Caesar Salad siparişi başarıyla teslim edilmiştir. Keyifli bir yemek olmuştur umarız!', 
      timestamp: new Date(Date.now() - 24 * 60 * 60000), 
      isRead: true, 
      linkTo: '/guest/cart' 
    }
  ]);

  const unreadCount = computed(() => notifications.value.filter(n => !n.isRead).length);
  
  const notificationsByType = computed(() => {
    const grouped = notifications.value.reduce((acc, notification) => {
      if (!acc[notification.type]) {
        acc[notification.type] = [];
      }
      acc[notification.type].push(notification);
      return acc;
    }, {} as Record<string, Notification[]>);
    
    return grouped;
  });

  const recentNotifications = computed(() => 
    notifications.value
      .filter(n => Date.now() - n.timestamp.getTime() < 24 * 60 * 60 * 1000) // Last 24 hours
      .slice(0, 5)
  );

  function addNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) {
    const newNotification: Notification = {
      ...notification,
      id: Date.now(),
      timestamp: new Date(),
      isRead: false,
    };
    notifications.value.unshift(newNotification);
  }

  function markAsRead(notificationId: number) {
    const notification = notifications.value.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
    }
  }

  function markAllAsRead() {
    notifications.value.forEach(n => n.isRead = true);
  }

  function removeNotification(notificationId: number) {
    const index = notifications.value.findIndex(n => n.id === notificationId);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
  }

  function clearOldNotifications() {
    // Remove notifications older than 7 days
    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    notifications.value = notifications.value.filter(n => 
      n.timestamp.getTime() > oneWeekAgo || !n.isRead
    );
  }

  function getNotificationIcon(type: Notification['type']): string {
    switch (type) {
      case 'order':
        return '🍽️';
      case 'service':
        return '🏨';
      case 'activity':
        return '🎯';
      case 'alert':
        return '⚠️';
      default:
        return '📱';
    }
  }

  function getNotificationTypeText(type: Notification['type']): string {
    switch (type) {
      case 'order':
        return 'Sipariş';
      case 'service':
        return 'Hizmet';
      case 'activity':
        return 'Aktivite';
      case 'alert':
        return 'Uyarı';
      default:
        return 'Bildirim';
    }
  }

  return { 
    notifications, 
    unreadCount, 
    notificationsByType,
    recentNotifications,
    addNotification, 
    markAsRead, 
    markAllAsRead,
    removeNotification,
    clearOldNotifications,
    getNotificationIcon,
    getNotificationTypeText
  };
}); 