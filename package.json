{"name": "hotelexia-monorepo", "version": "1.0.0", "description": "Comprehensive SaaS platform for hotel management with micro-frontend architecture", "type": "module", "private": true, "scripts": {"dev": "concurrently \"pnpm --filter @hotelexia/app-shell dev\" \"pnpm --filter @hotelexia/mfe-hotel-dashboard dev\" \"pnpm --filter @hotelexia/mfe-management dev\" \"pnpm --filter @hotelexia/mfe-customer-portal dev\"", "dev:all": "concurrently \"pnpm --filter @hotelexia/mfe-customer-portal dev\" \"pnpm --filter @hotelexia/mfe-hotel-dashboard dev\" \"pnpm --filter @hotelexia/app-shell dev\"", "dev:mfe": "concurrently \"pnpm --filter @hotelexia/mfe-customer-portal dev\" \"pnpm --filter @hotelexia/mfe-hotel-dashboard dev\"", "dev:shell": "pnpm --filter @hotelexia/app-shell dev", "start:dev": "concurrently -n \"CUSTOMER,HOTEL\" -c \"blue,green\" \"pnpm --filter @hotelexia/mfe-customer-portal dev\" \"pnpm --filter @hotelexia/mfe-hotel-dashboard dev\"", "build": "pnpm --recursive build", "build:app-shell": "pnpm --filter app-shell build", "build:customer-portal": "pnpm --filter @hotelexia/mfe-customer-portal build", "build:hotel-dashboard": "pnpm --filter @hotelexia/mfe-hotel-dashboard build", "preview": "pnpm --parallel preview", "lint": "pnpm --recursive run lint", "type-check": "pnpm --recursive run type-check", "format": "prettier --write .", "test": "pnpm --recursive test", "test:unit": "vitest", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "clean": "pnpm --recursive clean", "install-deps": "pnpm install"}, "devDependencies": {"@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/vue": "^8.0.2", "@types/node": "^20.11.17", "@vitest/coverage-v8": "^1.3.1", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.5.1", "concurrently": "^9.1.2", "jsdom": "^24.0.0", "prettier": "^3.2.5", "typescript": "~5.3.3", "vitest": "^1.3.1"}, "keywords": ["hotel", "management", "saas", "vue3", "typescript", "supabase", "pinia", "vue-router", "micro-frontend", "module-federation", "hospitality"], "author": "Hotelexia Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/hotelexia.git"}, "bugs": {"url": "https://github.com/your-username/hotelexia/issues"}, "homepage": "https://hotelexia.com"}