import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { AppShellService, AppShell } from '@hotelexia/shared-supabase-client'

export const useNotificationStore = defineStore('notifications', () => {
  // State
  const notifications = ref<AppShell.Notification[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const unreadCount = computed(() => 
    notifications.value.filter(n => !n.isRead).length
  )

  const recentNotifications = computed(() => 
    notifications.value
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10)
  )

  const highPriorityNotifications = computed(() =>
    notifications.value.filter(n => n.priority === 'HIGH' && !n.isRead)
  )

  // Actions
  const fetchNotifications = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await AppShellService.getNotifications()
      
      if (response.error) {
        throw new Error(response.error)
      }

      notifications.value = response.data || []
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error fetching notifications:', err)
    } finally {
      isLoading.value = false
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await AppShellService.markNotificationAsRead(notificationId)

      if (response.error) {
        throw new Error(response.error)
      }

      // Update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = true
      }
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error marking notification as read:', err)
    }
  }

  const markAllAsRead = async () => {
    try {
      // Mark all unread notifications as read
      const unreadNotifications = notifications.value.filter(n => !n.isRead)

      for (const notification of unreadNotifications) {
        await AppShellService.markNotificationAsRead(notification.id)
      }

      // Update local state
      notifications.value.forEach(n => n.isRead = true)
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error marking all notifications as read:', err)
    }
  }

  const addNotification = (notification: Omit<AppShell.Notification, 'id' | 'timestamp'>) => {
    const newNotification: AppShell.Notification = {
      ...notification,
      id: `notif_${Date.now()}`,
      timestamp: new Date().toISOString(),
      isRead: false
    }
    notifications.value.unshift(newNotification)
  }

  const removeNotification = (notificationId: string) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAll = () => {
    notifications.value = []
  }

  // Initialize notifications on store creation
  fetchNotifications()

  return {
    // State
    notifications: readonly(notifications),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Computed
    unreadCount,
    recentNotifications,
    highPriorityNotifications,

    // Actions
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    addNotification,
    removeNotification,
    clearAll
  }
})
