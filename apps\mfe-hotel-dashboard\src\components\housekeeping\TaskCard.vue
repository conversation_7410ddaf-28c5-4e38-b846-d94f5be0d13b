<template>
  <div class="bg-white dark:bg-navy-900 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-shadow duration-200">
    <!-- Task Header -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex-1">
        <h4 class="font-semibold text-gray-800 dark:text-white text-sm mb-1">
          {{ task.title }}
        </h4>
        <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">
          {{ task.description }}
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <!-- Priority Badge -->
        <span :class="getPriorityBadgeClass(task.priority)" class="px-2 py-1 rounded-full text-xs font-medium">
          {{ getPriorityText(task.priority) }}
        </span>
      </div>
    </div>

    <!-- Room & Timing Info -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-2">
        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
          Oda {{ task.roomNumber }}
        </span>
      </div>
      <div class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>{{ task.estimatedTime }}dk</span>
      </div>
    </div>

    <!-- Deadline -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-1">
        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <span class="text-xs text-gray-600 dark:text-gray-400">
          {{ formatDeadline(task.deadline) }}
        </span>
      </div>
      <div v-if="isOverdue" class="flex items-center space-x-1 text-red-500">
        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-xs font-medium">Gecikti</span>
      </div>
    </div>

    <!-- Assigned Staff -->
    <div v-if="assignedStaff" class="flex items-center space-x-2 mb-3">
      <img :src="assignedStaff.avatar" :alt="assignedStaff.name" class="w-6 h-6 rounded-full">
      <span class="text-sm text-gray-700 dark:text-gray-300">
        {{ assignedStaff.name }} {{ assignedStaff.surname }}
      </span>
      <span :class="getStatusBadgeClass(assignedStaff.status)" class="px-2 py-1 rounded-full text-xs font-medium">
        {{ getStatusText(assignedStaff.status) }}
      </span>
    </div>

    <!-- Notes -->
    <div v-if="task.notes" class="mb-3">
      <p class="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded p-2">
        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        {{ task.notes }}
      </p>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center space-x-2">
      <button 
        @click="$emit('edit', task)"
        class="flex-1 px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
      >
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        Düzenle
      </button>
      
      <div class="relative">
        <button 
          @click="showStatusMenu = !showStatusMenu"
          class="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
        >
          <span>{{ getStatusText(task.status) }}</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <!-- Status Dropdown -->
        <div v-if="showStatusMenu" class="absolute right-0 mt-1 w-48 bg-white dark:bg-navy-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
          <div class="py-1">
            <button 
              v-for="status in getAvailableStatuses()" 
              :key="status.value"
              @click="changeStatus(status.value)"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              {{ status.label }}
            </button>
          </div>
        </div>
      </div>
      
      <button 
        v-if="task.status === 'NEW'"
        @click="$emit('assign', task)"
        class="px-3 py-2 text-sm font-medium text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
      >
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        Ata
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { HousekeepingTask, TaskStatus, StaffStatus } from '@/types/housekeeping'
import { useHousekeepingStore } from '@/stores/housekeepingStore'

// Props & Emits
interface Props {
  task: HousekeepingTask
}

const props = defineProps<Props>()

const emit = defineEmits<{
  edit: [task: HousekeepingTask]
  assign: [task: HousekeepingTask]
  'status-change': [taskId: string, status: TaskStatus]
}>()

// Store
const housekeepingStore = useHousekeepingStore()

// State
const showStatusMenu = ref(false)

// Computed
const assignedStaff = computed(() => {
  if (!props.task.assignedTo) return null
  return housekeepingStore.staff.find(s => s.id === props.task.assignedTo)
})

const isOverdue = computed(() => {
  const deadline = new Date(props.task.deadline)
  const now = new Date()
  return deadline < now && props.task.status !== 'COMPLETED'
})

// Methods
const getPriorityBadgeClass = (priority: string) => {
  const classes = {
    'LOW': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    'MEDIUM': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
    'HIGH': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'URGENT': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
  }
  return classes[priority as keyof typeof classes] || classes.MEDIUM
}

const getPriorityText = (priority: string) => {
  const texts = {
    'LOW': 'Düşük',
    'MEDIUM': 'Orta',
    'HIGH': 'Yüksek',
    'URGENT': 'Acil'
  }
  return texts[priority as keyof typeof texts] || priority
}

const getStatusBadgeClass = (status: StaffStatus) => {
  const classes = {
    'AVAILABLE': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    'BUSY': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'BREAK': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'OFF_DUTY': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
  return classes[status] || classes.AVAILABLE
}

const getStatusText = (status: string) => {
  const texts = {
    'NEW': 'Atanmamış',
    'IN_PROGRESS': 'Devam Ediyor',
    'QUALITY_CHECK': 'Kalite Kontrolü',
    'COMPLETED': 'Tamamlandı',
    'CANCELLED': 'İptal',
    'AVAILABLE': 'Müsait',
    'BUSY': 'Meşgul',
    'BREAK': 'Molada',
    'OFF_DUTY': 'Mesai Dışı'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDeadline = (deadline: string) => {
  const date = new Date(deadline)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const deadlineDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  if (deadlineDate.getTime() === today.getTime()) {
    return `Bugün ${date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`
  } else if (deadlineDate.getTime() === today.getTime() + 24 * 60 * 60 * 1000) {
    return `Yarın ${date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}`
  } else {
    return date.toLocaleDateString('tr-TR', { 
      day: '2-digit', 
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const getAvailableStatuses = () => {
  const statuses = [
    { value: 'NEW', label: 'Atanmamış' },
    { value: 'IN_PROGRESS', label: 'Devam Ediyor' },
    { value: 'QUALITY_CHECK', label: 'Kalite Kontrolü' },
    { value: 'COMPLETED', label: 'Tamamlandı' },
    { value: 'CANCELLED', label: 'İptal' }
  ]
  return statuses.filter(s => s.value !== props.task.status)
}

const changeStatus = (newStatus: string) => {
  emit('status-change', props.task.id, newStatus as TaskStatus)
  showStatusMenu.value = false
}

// Click outside to close menu
const closeMenu = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showStatusMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', closeMenu)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 