<template>
  <div class="pt-20 pb-10">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-navy-700 dark:text-white mb-2">Rapor<PERSON> ve Analitik</h1>
      <p class="text-gray-600 dark:text-gray-400">Otel performansınızın kapsamlı analizi ve raporları</p>
    </div>

    <!-- Error State -->
    <div v-if="reportingStore.error" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
      <div class="flex items-center justify-between">
        <p class="text-red-600 dark:text-red-400">{{ reportingStore.error }}</p>
        <button
          @click="refreshReports"
          class="text-sm bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg font-medium transition-colors"
        >
          <PERSON><PERSON><PERSON> Dene
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="reportingStore.isLoading && !hasAnyData" class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div v-for="i in 4" :key="i" class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6 animate-pulse">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
            <div class="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div class="space-y-2">
            <div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <MetricCard
        label="Doluluk Oranı"
        :value="reportingStore.occupancyRate"
        format="percentage"
        :icon="HomeIcon"
        icon-color="text-blue-600 dark:text-blue-400"
        icon-bg-color="bg-blue-100 dark:bg-blue-900/30"
        :trend="dailySummary?.reservation_growth_rate"
        description="Güncel oda doluluk oranı"
      />
      <MetricCard
        label="Günlük Gelir"
        :value="reportingStore.totalRevenue"
        format="currency"
        :icon="CurrencyDollarIcon"
        icon-color="text-green-600 dark:text-green-400"
        icon-bg-color="bg-green-100 dark:bg-green-900/30"
        :trend="dailySummary?.room_service_revenue_growth_rate"
        description="Bugünkü toplam gelir"
      />
      <MetricCard
        label="Görev Tamamlama"
        :value="reportingStore.taskCompletionRate"
        format="percentage"
        :icon="CheckCircleIcon"
        icon-color="text-purple-600 dark:text-purple-400"
        icon-bg-color="bg-purple-100 dark:bg-purple-900/30"
        description="Genel görev tamamlama oranı"
      />
      <MetricCard
        label="Misafir Memnuniyeti"
        :value="reportingStore.guestSatisfaction"
        :icon="StarIcon"
        icon-color="text-yellow-600 dark:text-yellow-400"
        icon-bg-color="bg-yellow-100 dark:bg-yellow-900/30"
        description="Ortalama misafir puanı (5 üzerinden)"
        :additional-info="{ label: 'Değerlendirme sayısı', value: guestAnalytics?.total_room_service_orders || 0 }"
      />
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
      <!-- Revenue Trend Chart -->
      <ChartCard
        title="Gelir Trendi"
        subtitle="Son 30 günlük gelir analizi"
        type="line"
        :has-data="false"
        placeholder="Gelir trend verileri yükleniyor..."
        :legend="[
          { label: 'Rezervasyon Geliri', color: 'bg-blue-500' },
          { label: 'Oda Servisi Geliri', color: 'bg-green-500' }
        ]"
      >
        <template #actions>
          <select class="text-sm border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-1 bg-white dark:bg-navy-700 text-gray-700 dark:text-gray-300">
            <option>Son 7 Gün</option>
            <option>Son 30 Gün</option>
            <option>Son 3 Ay</option>
          </select>
        </template>
      </ChartCard>

      <!-- Task Performance Chart -->
      <ChartCard
        title="Görev Performansı"
        subtitle="Günlük görev tamamlama oranları"
        type="bar"
        :has-data="false"
        placeholder="Görev performans verileri yükleniyor..."
        :legend="[
          { label: 'Oluşturulan', color: 'bg-blue-500' },
          { label: 'Tamamlanan', color: 'bg-green-500' }
        ]"
      />
    </div>

    <!-- Detailed Reports Section -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
      <!-- Operational Metrics -->
      <ReportCard title="Operasyonel Metrikler" subtitle="Güncel operasyonel durum">
        <div v-if="operationalMetrics" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-4 bg-gray-50 dark:bg-navy-900 rounded-lg">
              <div class="text-2xl font-bold text-navy-700 dark:text-white">{{ operationalMetrics.total_rooms }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">Toplam Oda</div>
            </div>
            <div class="text-center p-4 bg-gray-50 dark:bg-navy-900 rounded-lg">
              <div class="text-2xl font-bold text-navy-700 dark:text-white">{{ operationalMetrics.occupied_rooms }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">Dolu Oda</div>
            </div>
          </div>
          <div class="grid grid-cols-3 gap-2 text-sm">
            <div class="text-center">
              <div class="font-semibold text-green-600">{{ operationalMetrics.clean_rooms }}</div>
              <div class="text-gray-500">Temiz</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-yellow-600">{{ operationalMetrics.dirty_rooms }}</div>
              <div class="text-gray-500">Kirli</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-red-600">{{ operationalMetrics.maintenance_rooms }}</div>
              <div class="text-gray-500">Bakımda</div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
          Operasyonel veriler yükleniyor...
        </div>
      </ReportCard>

      <!-- Financial Summary -->
      <ReportCard title="Finansal Özet" subtitle="Günlük ve aylık gelir analizi">
        <div v-if="financialMetrics" class="space-y-4">
          <div class="grid grid-cols-1 gap-4">
            <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-navy-900 rounded-lg">
              <span class="text-sm text-gray-600 dark:text-gray-400">Günlük Rezervasyon</span>
              <span class="font-semibold text-navy-700 dark:text-white">
                {{ formatCurrency(financialMetrics.daily_reservation_revenue) }}
              </span>
            </div>
            <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-navy-900 rounded-lg">
              <span class="text-sm text-gray-600 dark:text-gray-400">Günlük Oda Servisi</span>
              <span class="font-semibold text-navy-700 dark:text-white">
                {{ formatCurrency(financialMetrics.daily_room_service_revenue) }}
              </span>
            </div>
            <div class="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
              <span class="text-sm font-medium text-blue-700 dark:text-blue-300">Ortalama Oda Fiyatı</span>
              <span class="font-bold text-blue-700 dark:text-blue-300">
                {{ formatCurrency(financialMetrics.average_daily_rate) }}
              </span>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500 dark:text-gray-400">
          Finansal veriler yükleniyor...
        </div>
      </ReportCard>
    </div>

    <!-- Task Performance Table -->
    <DataTable
      title="Görev Performans Detayları"
      subtitle="Departman bazında görev analizi"
      :columns="taskColumns as any"
      :data="taskTableData"
      empty-message="Görev performans verileri bulunamadı"
    >
      <template #actions>
        <button class="text-sm text-brand-500 hover:text-brand-600 font-medium">
          Detaylı Rapor
        </button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useReportingStore } from '@/stores/reportingStore'
import {
  HomeIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  StarIcon
} from '@heroicons/vue/24/outline'

// Components
import MetricCard from '@/components/reports/MetricCard.vue'
import ChartCard from '@/components/reports/ChartCard.vue'
import ReportCard from '@/components/reports/ReportCard.vue'
import DataTable from '@/components/reports/DataTable.vue'

// Store
const reportingStore = useReportingStore()

// Computed values
const operationalMetrics = computed(() => reportingStore.operationalMetrics)
const financialMetrics = computed(() => reportingStore.financialMetrics)
const taskPerformance = computed(() => reportingStore.taskPerformance)
const guestAnalytics = computed(() => reportingStore.guestAnalytics)
const dailySummary = computed(() => reportingStore.dailySummary)

// Check if we have any data loaded
const hasAnyData = computed(() => {
  return !!(operationalMetrics.value || financialMetrics.value || taskPerformance.value || guestAnalytics.value || dailySummary.value)
})

// Table configuration
const taskColumns = [
  { key: 'department', label: 'Departman', type: 'text' },
  { key: 'total_tasks', label: 'Toplam Görev', type: 'text', align: 'center' },
  { key: 'completed_tasks', label: 'Tamamlanan', type: 'text', align: 'center' },
  { key: 'completion_rate', label: 'Tamamlama Oranı', type: 'percentage', align: 'center' },
  { key: 'avg_duration', label: 'Ort. Süre', type: 'text', align: 'center' }
]

const taskTableData = computed(() => {
  if (!taskPerformance.value) return []
  
  return [
    {
      department: 'Bakım',
      total_tasks: taskPerformance.value.total_maintenance_tasks,
      completed_tasks: taskPerformance.value.completed_maintenance_tasks,
      completion_rate: taskPerformance.value.maintenance_completion_rate,
      avg_duration: `${taskPerformance.value.avg_task_duration_minutes || 0}dk`
    },
    {
      department: 'Genel Görevler',
      total_tasks: taskPerformance.value.total_general_tasks,
      completed_tasks: taskPerformance.value.completed_general_tasks,
      completion_rate: taskPerformance.value.general_task_completion_rate,
      avg_duration: `${taskPerformance.value.avg_task_duration_minutes || 0}dk`
    },
    {
      department: 'Servis Talepleri',
      total_tasks: taskPerformance.value.total_service_requests,
      completed_tasks: taskPerformance.value.completed_service_requests,
      completion_rate: taskPerformance.value.service_request_resolution_rate,
      avg_duration: '-'
    }
  ]
})

// Utility functions
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(value)
}

// Methods
const refreshReports = async () => {
  try {
    await reportingStore.fetchAllMetrics()
  } catch (error) {
    console.error('Failed to refresh reports:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await refreshReports()
})
</script>

<style scoped>
/* Reports overview specific styles */
</style>
