/**
 * Global error handling utilities for the app shell
 * Handles Module Federation loading errors, authentication failures, and other critical errors
 */

export interface ErrorContext {
  route?: string
  component?: string
  mfe?: string
  timestamp: number
  userAgent: string
  url: string
}

export interface ErrorReport {
  error: Error
  context: ErrorContext
  severity: 'low' | 'medium' | 'high' | 'critical'
}

class ErrorHandler {
  private errorQueue: ErrorReport[] = []
  private maxQueueSize = 50
  private retryAttempts = new Map<string, number>()
  private maxRetries = 3

  /**
   * Handle Module Federation loading errors
   */
  handleModuleFederationError(error: Error, mfeName: string, componentName: string): void {
    const errorKey = `${mfeName}:${componentName}`
    const attempts = this.retryAttempts.get(errorKey) || 0
    
    console.error(`Module Federation Error [${mfeName}/${componentName}]:`, error)
    
    if (attempts < this.maxRetries) {
      this.retryAttempts.set(errorKey, attempts + 1)
      console.warn(`Retrying ${errorKey} (attempt ${attempts + 1}/${this.maxRetries})`)
      
      // Retry after a delay
      setTimeout(() => {
        window.location.reload()
      }, 1000 * Math.pow(2, attempts)) // Exponential backoff
    } else {
      // Max retries reached, show fallback
      this.showModuleFederationFallback(mfeName, componentName)
      this.reportError(error, {
        route: window.location.pathname,
        component: componentName,
        mfe: mfeName,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      }, 'critical')
    }
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error: Error, context: Partial<ErrorContext> = {}): void {
    console.error('Authentication Error:', error)
    
    this.reportError(error, {
      route: window.location.pathname,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    }, 'high')

    // Clear any stale auth data
    sessionStorage.removeItem('hotelexia_redirect_after_login')
    localStorage.removeItem('supabase.auth.token')
    
    // Redirect to login
    window.location.href = '/login'
  }

  /**
   * Handle data fetching errors
   */
  handleDataFetchError(error: Error, context: Partial<ErrorContext> = {}): void {
    console.error('Data Fetch Error:', error)
    
    this.reportError(error, {
      route: window.location.pathname,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    }, 'medium')
  }

  /**
   * Handle general application errors
   */
  handleGeneralError(error: Error, context: Partial<ErrorContext> = {}): void {
    console.error('Application Error:', error)
    
    this.reportError(error, {
      route: window.location.pathname,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    }, 'low')
  }

  /**
   * Show fallback UI for Module Federation failures
   */
  private showModuleFederationFallback(mfeName: string, componentName: string): void {
    const fallbackHTML = `
      <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-navy-900">
        <div class="max-w-md w-full text-center p-8">
          <div class="mb-6">
            <svg class="w-16 h-16 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Modül Yükleme Hatası
          </h2>
          <p class="text-gray-600 dark:text-gray-300 mb-6">
            ${mfeName} modülü yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin veya daha sonra tekrar deneyin.
          </p>
          <div class="space-y-3">
            <button 
              onclick="window.location.reload()" 
              class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Sayfayı Yenile
            </button>
            <button 
              onclick="window.location.href='/'" 
              class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Ana Sayfaya Dön
            </button>
          </div>
          <details class="mt-6 text-left">
            <summary class="cursor-pointer text-sm text-gray-500">Teknik Detaylar</summary>
            <pre class="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto">
MFE: ${mfeName}
Component: ${componentName}
Time: ${new Date().toISOString()}
            </pre>
          </details>
        </div>
      </div>
    `
    
    document.body.innerHTML = fallbackHTML
  }

  /**
   * Report error to monitoring system
   */
  private reportError(error: Error, context: ErrorContext, severity: ErrorReport['severity']): void {
    const report: ErrorReport = {
      error,
      context,
      severity
    }
    
    // Add to queue
    this.errorQueue.push(report)
    
    // Trim queue if too large
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
    
    // In production, send to monitoring service
    if (import.meta.env.PROD) {
      this.sendToMonitoring(report)
    }
  }

  /**
   * Send error report to monitoring service
   */
  private async sendToMonitoring(report: ErrorReport): Promise<void> {
    try {
      // This would integrate with your monitoring service (e.g., Sentry, LogRocket, etc.)
      console.log('Would send to monitoring:', report)
    } catch (err) {
      console.error('Failed to send error report:', err)
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): { total: number; bySeverity: Record<string, number> } {
    const bySeverity = this.errorQueue.reduce((acc, report) => {
      acc[report.severity] = (acc[report.severity] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return {
      total: this.errorQueue.length,
      bySeverity
    }
  }

  /**
   * Clear error queue
   */
  clearErrors(): void {
    this.errorQueue = []
    this.retryAttempts.clear()
  }
}

// Global error handler instance
export const globalErrorHandler = new ErrorHandler()

// Set up global error listeners
if (typeof window !== 'undefined') {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    globalErrorHandler.handleGeneralError(
      new Error(event.reason?.message || 'Unhandled promise rejection'),
      { component: 'global' }
    )
  })

  // Handle general JavaScript errors
  window.addEventListener('error', (event) => {
    globalErrorHandler.handleGeneralError(
      event.error || new Error(event.message),
      { component: 'global' }
    )
  })
}

export default globalErrorHandler
