<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-navy-700 dark:text-white">{{ pageTitle }}</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">{{ pageSubtitle }}</p>
      </div>
      <div class="flex items-center space-x-3">
        <button 
          @click="goBack"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors"
        >
          İptal
        </button>
        <button 
          @click="saveHotel"
          class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
          :disabled="isLoading"
        >
          {{ isLoading ? 'Kaydediliyor...' : saveButtonText }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && !isNewHotel" class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6 text-center">
      <p class="text-gray-500 dark:text-gray-400">Otel bilgileri yükleniyor...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg relative" role="alert">
      <strong class="font-bold">Hata!</strong>
      <span class="block sm:inline">{{ error }}</span>
    </div>

    <!-- Success State -->
    <div v-else-if="successMessage" class="bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg relative" role="alert">
      <strong class="font-bold">Başarılı!</strong>
      <span class="block sm:inline">{{ successMessage }}</span>
    </div>

    <!-- Form -->
    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Form -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-6">Temel Bilgiler</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Otel Adı *</label>
              <input v-model="formData.name" type="text" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500" placeholder="Otel adını girin">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Adres</label>
              <input v-model="formData.address" type="text" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500" placeholder="Otel adresi">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Şehir</label>
              <input v-model="formData.city" type="text" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500" placeholder="Şehir">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ülke</label>
              <input v-model="formData.country" type="text" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500" placeholder="Ülke">
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Açıklama</label>
              <textarea v-model="formData.description" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500" placeholder="Otel açıklaması" rows="4"></textarea>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ana Resim</label>
              <div class="space-y-3">
                <input
                  type="file"
                  @change="handleImageUpload"
                  accept="image/*"
                  class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500"
                >
                <input
                  v-model="formData.main_image_url"
                  type="url"
                  class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  placeholder="Veya resim URL'si girin"
                >
                <div v-if="formData.main_image_url" class="mt-2">
                  <img :src="formData.main_image_url" alt="Otel resmi" class="w-32 h-24 object-cover rounded-lg border border-gray-200 dark:border-gray-600">
                </div>
              </div>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ana Resim URL'si</label>
              <input v-model="formData.main_image_url" type="text" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500" placeholder="https://example.com/image.png">
            </div>
          </div>
        </div>

        <!-- Status Control -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-6">Durum Kontrolü</h3>
          <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-navy-700 rounded-lg">
            <div>
              <h4 class="font-medium text-navy-700 dark:text-white">Otel Aktifliği</h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">Otelin platformda görünür ve aktif olup olmadığını belirler.</p>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-sm font-medium uppercase" :class="formData.is_active ? 'text-green-500' : 'text-red-500'">
                {{ formData.is_active ? 'Aktif' : 'Pasif' }}
              </span>
              <button
                @click="toggleStatus"
                class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
                :class="formData.is_active ? 'bg-brand-500' : 'bg-gray-300'"
              >
                <span
                  class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                  :class="formData.is_active ? 'translate-x-6' : 'translate-x-1'"
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar Info -->
      <div class="space-y-6">
        <!-- Hotel Stats -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Otel Bilgisi</h3>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">Kayıt Tarihi</span>
              <span class="text-sm font-medium text-navy-700 dark:text-white">
                {{ isNewHotel ? 'Yeni Otel' : formatDate((hotel as any)?.created_at) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Hızlı İşlemler</h3>
          <div class="space-y-3">
            <button class="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2">
              <DocumentTextIcon class="h-4 w-4" />
              <span>Raporları Görüntüle</span>
            </button>
            <button
              v-if="!isNewHotel"
              @click="deleteHotel"
              :disabled="isLoading"
              class="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center space-x-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <TrashIcon class="h-4 w-4" />
              <span>{{ isLoading ? 'Siliniyor...' : 'Oteli Sil' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ExclamationTriangleIcon,
  CogIcon,
  DocumentTextIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import { supabase } from '@hotelexia/shared-supabase-client'
import type { Database } from '@hotelexia/shared-supabase-client'
import { useManagementStore } from '@/stores/managementStore'

type Hotel = Database['public']['Tables']['hotels']['Row']

const router = useRouter()
const route = useRoute()
const store = useManagementStore()

const hotelId = route.params.id === 'new' ? null : (route.params.id as string)
const hotel = ref<Hotel | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)
const successMessage = ref<string | null>(null)

// Form data - matching the database schema
const formData = ref({
  name: '',
  address: '',
  city: '',
  country: '',
  description: '',
  main_image_url: '',
  is_active: true,
})

// Computed
const isNewHotel = computed(() => !hotelId)
const pageTitle = computed(() => isNewHotel.value ? 'Yeni Otel Oluştur' : 'Otel Düzenle')
const pageSubtitle = computed(() => isNewHotel.value ? 'Yeni bir otel için bilgileri doldurun' : `${hotel.value?.name || 'Otel'} bilgilerini düzenleyin`)

const saveButtonText = computed(() => isNewHotel.value ? 'Oteli Oluştur' : 'Değişiklikleri Kaydet')

// Methods
const goBack = () => {
  router.push('/management/hotels')
}

const saveHotel = async () => {
  if (isLoading.value) return
  isLoading.value = true
  error.value = null
  successMessage.value = null

  // Enhanced validation
  if (!formData.value.name?.trim()) {
    error.value = "Otel Adı zorunlu bir alandır."
    isLoading.value = false
    return
  }

  if (!formData.value.city?.trim()) {
    error.value = "Şehir bilgisi zorunlu bir alandır."
    isLoading.value = false
    return
  }

  if (!formData.value.country?.trim()) {
    error.value = "Ülke bilgisi zorunlu bir alandır."
    isLoading.value = false
    return
  }

  // Prepare data for save
  const dataToSave: any = {
    name: formData.value.name.trim(),
    address: formData.value.address?.trim() || '',
    city: formData.value.city.trim(),
    country: formData.value.country.trim(),
    description: formData.value.description?.trim() || '',
    main_image_url: formData.value.main_image_url?.trim() || '',
    is_active: formData.value.is_active,
    updated_at: new Date().toISOString()
  }

  // Add created_at for new hotels
  if (isNewHotel.value) {
    dataToSave.created_at = new Date().toISOString()
  }

  try {
    let result

    if (isNewHotel.value) {
      // Use the enhanced store handler for creating new hotel
      result = await store.handleAddHotelSubmit(formData.value)
    } else {
      // Use the enhanced store handler for updating hotel (will be implemented in subtask 3)
      result = await store.handleEditHotelSubmit(hotelId!, formData.value)
    }

    if (result.success) {
      // Success feedback - show success message
      console.log(`Hotel ${isNewHotel.value ? 'created' : 'updated'} successfully:`, result.data)

      // Show success message (you can implement a toast service here)
      showSuccessMessage(result.message || `Hotel ${isNewHotel.value ? 'created' : 'updated'} successfully!`)

      // Navigate back to hotel list after a short delay
      setTimeout(() => {
        goBack()
      }, 1500)
    } else {
      // Handle error from store
      error.value = result.error || 'An unexpected error occurred'
    }

  } catch (e: any) {
    console.error('Unexpected error saving hotel:', e)
    error.value = `Otel kaydedilirken beklenmeyen bir hata oluştu: ${e.message}`
  } finally {
    isLoading.value = false
  }
}

const toggleStatus = () => {
  formData.value.is_active = !formData.value.is_active
}

// Success message helper
const showSuccessMessage = (message: string) => {
  successMessage.value = message
  error.value = null // Clear any existing errors

  // Auto-hide success message after 3 seconds
  setTimeout(() => {
    successMessage.value = null
  }, 3000)

  console.log('SUCCESS:', message)
}

const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Hotel deletion with confirmation
const deleteHotel = async () => {
  if (!hotel.value || isNewHotel.value) return

  const confirmed = confirm(
    `"${hotel.value.name}" otelini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`
  )

  if (!confirmed) return

  isLoading.value = true
  error.value = null

  try {
    // Soft delete - set is_active to false instead of hard delete
    const { error: deleteError } = await supabase
      .from('hotels')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', hotel.value.id)

    if (deleteError) {
      throw deleteError
    }

    console.log('Hotel soft deleted successfully:', hotel.value.id)
    goBack()

  } catch (e: any) {
    console.error('Error deleting hotel:', e)
    error.value = `Otel silinirken bir hata oluştu: ${e.message}`
  } finally {
    isLoading.value = false
  }
}

// Image upload functionality
const uploadImage = async (file: File): Promise<string | null> => {
  try {
    const fileExt = file.name.split('.').pop()
    const fileName = `${Math.random()}.${fileExt}`
    const filePath = `hotels/${fileName}`

    const { error: uploadError } = await supabase.storage
      .from('hotel-images')
      .upload(filePath, file)

    if (uploadError) {
      throw uploadError
    }

    const { data } = supabase.storage
      .from('hotel-images')
      .getPublicUrl(filePath)

    return data.publicUrl
  } catch (e: any) {
    console.error('Error uploading image:', e)
    error.value = `Resim yüklenirken bir hata oluştu: ${e.message}`
    return null
  }
}

// Handle image file selection
const handleImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // Validate file type
  if (!file.type.startsWith('image/')) {
    error.value = 'Lütfen geçerli bir resim dosyası seçin.'
    return
  }

  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    error.value = 'Resim dosyası 5MB\'dan küçük olmalıdır.'
    return
  }

  isLoading.value = true
  const imageUrl = await uploadImage(file)

  if (imageUrl) {
    formData.value.main_image_url = imageUrl
  }

  isLoading.value = false
}

onMounted(async () => {
  if (hotelId) {
    try {
      isLoading.value = true
      const { data, error: fetchError } = await supabase
        .from('hotels')
        .select('*')
        .eq('id', hotelId)
        .single()
      
      if (fetchError) {
        throw fetchError
      }

      if (data) {
        hotel.value = data
        formData.value.name = data.name || ''
        formData.value.address = data.address || ''
        formData.value.city = data.city || ''
        formData.value.country = data.country || ''
        formData.value.description = data.description || ''
        formData.value.main_image_url = (data as any).main_image_url || ''
        formData.value.is_active = (data as any).is_active === null ? true : (data as any).is_active
      }
    } catch (e: any) {
      console.error('Error fetching hotel:', e)
      error.value = `Otel bilgileri alınamadı: ${e.message}`
    } finally {
      isLoading.value = false
    }
  } else {
    // New hotel
    isLoading.value = false
  }
})
</script> 