<template>
  <div class="p-6 bg-gray-50 dark:bg-navy-900 min-h-screen">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h1 class="text-2xl font-bold text-navy-700 dark:text-white">Housekeeping Görevleri</h1>
          <p class="text-gray-600 dark:text-gray-400">Tüm temizlik görevlerini yönetin ve takip edin</p>
        </div>
        <button
          @click="showTaskModal = true"
          class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          + <PERSON><PERSON> Görev
        </button>
      </div>

      <!-- KPI Cards -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Atanmamış</p>
              <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ tasksByStatus.unassigned.length }}</p>
            </div>
            <div class="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Devam Ediyor</p>
              <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ tasksByStatus.inProgress.length }}</p>
            </div>
            <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Kalite Kontrolü</p>
              <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ tasksByStatus.qualityCheck.length }}</p>
            </div>
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Tamamlandı</p>
              <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ tasksByStatus.completed.length }}</p>
            </div>
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-navy-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">İptal Edildi</p>
              <p class="text-2xl font-bold text-gray-600 dark:text-gray-400">{{ tasksByStatus.cancelled.length }}</p>
            </div>
            <div class="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="flex flex-wrap gap-4 mb-6">
        <select
          v-model="selectedPriority"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent"
        >
          <option value="">Tüm Öncelikler</option>
          <option value="URGENT">Acil</option>
          <option value="HIGH">Yüksek</option>
          <option value="MEDIUM">Orta</option>
          <option value="LOW">Düşük</option>
        </select>

        <select
          v-model="selectedStaff"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent"
        >
          <option value="">Tüm Personel</option>
          <option v-for="member in staff" :key="member.id" :value="member.id">
            {{ member.name }} {{ member.surname }}
          </option>
        </select>

        <input
          v-model="searchQuery"
          type="text"
          placeholder="Görev ara..."
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent"
        />
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="flex gap-6 overflow-x-auto">
      <!-- Atanmamış Column -->
      <div class="flex-shrink-0 w-80">
        <div class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="bg-red-50 dark:bg-red-900/20 px-4 py-3 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
            <h3 class="font-semibold text-red-700 dark:text-red-400">Atanmamış ({{ filteredTasksByStatus.unassigned.length }})</h3>
          </div>
          <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
            <TaskCard
              v-for="task in filteredTasksByStatus.unassigned"
              :key="task.id"
              :task="task"
              :staff="staff"
              @update-status="updateTaskStatus"
              @assign-staff="assignTaskToStaff"
            />
          </div>
        </div>
      </div>

      <!-- Devam Ediyor Column -->
      <div class="flex-shrink-0 w-80">
        <div class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="bg-yellow-50 dark:bg-yellow-900/20 px-4 py-3 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
            <h3 class="font-semibold text-yellow-700 dark:text-yellow-400">Devam Ediyor ({{ filteredTasksByStatus.inProgress.length }})</h3>
          </div>
          <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
            <TaskCard
              v-for="task in filteredTasksByStatus.inProgress"
              :key="task.id"
              :task="task"
              :staff="staff"
              @update-status="updateTaskStatus"
              @assign-staff="assignTaskToStaff"
            />
          </div>
        </div>
      </div>

      <!-- Kalite Kontrolü Column -->
      <div class="flex-shrink-0 w-80">
        <div class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="bg-blue-50 dark:bg-blue-900/20 px-4 py-3 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
            <h3 class="font-semibold text-blue-700 dark:text-blue-400">Kalite Kontrolü ({{ filteredTasksByStatus.qualityCheck.length }})</h3>
          </div>
          <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
            <TaskCard
              v-for="task in filteredTasksByStatus.qualityCheck"
              :key="task.id"
              :task="task"
              :staff="staff"
              @update-status="updateTaskStatus"
              @assign-staff="assignTaskToStaff"
            />
          </div>
        </div>
      </div>

      <!-- Tamamlandı Column -->
      <div class="flex-shrink-0 w-80">
        <div class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="bg-green-50 dark:bg-green-900/20 px-4 py-3 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
            <h3 class="font-semibold text-green-700 dark:text-green-400">Tamamlandı ({{ filteredTasksByStatus.completed.length }})</h3>
          </div>
          <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
            <TaskCard
              v-for="task in filteredTasksByStatus.completed"
              :key="task.id"
              :task="task"
              :staff="staff"
              @update-status="updateTaskStatus"
              @assign-staff="assignTaskToStaff"
            />
          </div>
        </div>
      </div>

      <!-- İptal Edildi Column -->
      <div class="flex-shrink-0 w-80">
        <div class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
            <h3 class="font-semibold text-gray-700 dark:text-gray-400">İptal Edildi ({{ filteredTasksByStatus.cancelled.length }})</h3>
          </div>
          <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
            <TaskCard
              v-for="task in filteredTasksByStatus.cancelled"
              :key="task.id"
              :task="task"
              :staff="staff"
              @update-status="updateTaskStatus"
              @assign-staff="assignTaskToStaff"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Task Creation Modal -->
    <div v-if="showTaskModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Yeni Görev Oluştur</h3>
        
        <form @submit.prevent="createTask" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Başlık</label>
            <input
              v-model="newTask.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              placeholder="Görev başlığı..."
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Açıklama</label>
            <textarea
              v-model="newTask.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              placeholder="Görev açıklaması..."
            ></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Oda Numarası</label>
            <input
              v-model="newTask.roomNumber"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              placeholder="Örn: 205"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Öncelik</label>
            <select
              v-model="newTask.priority"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            >
              <option value="LOW">Düşük</option>
              <option value="MEDIUM">Orta</option>
              <option value="HIGH">Yüksek</option>
              <option value="URGENT">Acil</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tahmini Süre (dakika)</label>
            <input
              v-model.number="newTask.estimatedTime"
              type="number"
              min="15"
              step="15"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Son Teslim</label>
            <input
              v-model="newTask.deadline"
              type="datetime-local"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notlar</label>
            <textarea
              v-model="newTask.notes"
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
              placeholder="Ek notlar..."
            ></textarea>
          </div>

          <div class="flex gap-3 pt-4">
            <button
              type="button"
              @click="showTaskModal = false"
              class="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              class="flex-1 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
            >
              Oluştur
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { TaskStatus, TaskPriority } from '@/types/housekeeping'
import TaskCard from '@/components/cards/TaskCard.vue'

const housekeepingStore = useHousekeepingStore()

// Reactive data
const selectedPriority = ref('')
const selectedStaff = ref('')
const searchQuery = ref('')
const showTaskModal = ref(false)

// New task form data
const newTask = ref({
  title: '',
  description: '',
  roomNumber: '',
  priority: 'MEDIUM' as TaskPriority,
  estimatedTime: 45,
  deadline: '',
  notes: ''
})

// Computed properties
const { tasksByStatus, staff } = housekeepingStore

const filteredTasksByStatus = computed(() => {
  const filterTasks = (tasks: any[]) => {
    return tasks.filter(task => {
      const matchesPriority = !selectedPriority.value || task.priority === selectedPriority.value
      const matchesStaff = !selectedStaff.value || task.assignedTo === selectedStaff.value
      const matchesSearch = !searchQuery.value || 
        task.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        task.description.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        task.roomNumber.includes(searchQuery.value)
      
      return matchesPriority && matchesStaff && matchesSearch
    })
  }

  return {
    unassigned: filterTasks(tasksByStatus.unassigned),
    inProgress: filterTasks(tasksByStatus.inProgress),
    qualityCheck: filterTasks(tasksByStatus.qualityCheck),
    completed: filterTasks(tasksByStatus.completed),
    cancelled: filterTasks(tasksByStatus.cancelled)
  }
})

// Methods
const updateTaskStatus = (taskId: string, newStatus: TaskStatus) => {
  housekeepingStore.updateTaskStatus(taskId, newStatus)
}

const assignTaskToStaff = (taskId: string, staffId: string) => {
  housekeepingStore.assignTaskToStaff(taskId, staffId)
}

const createTask = () => {
  housekeepingStore.addNewTask({
    ...newTask.value,
    status: 'UNASSIGNED' as TaskStatus
  })
  
  // Reset form
  newTask.value = {
    title: '',
    description: '',
    roomNumber: '',
    priority: 'MEDIUM' as TaskPriority,
    estimatedTime: 45,
    deadline: '',
    notes: ''
  }
  
  showTaskModal.value = false
}
</script> 