import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { User } from '@supabase/supabase-js'
import { supabase, type Database } from '@hotelexia/shared-supabase-client'
import router from '../router'

type UserRole = Database['public']['Enums']['user_role']

export interface UserProfile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: UserRole | null
  hotel_id: string | null
  created_at: string | null
  updated_at: string | null
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const profile = ref<UserProfile | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!user.value && !!profile.value)
  const userRole = computed(() => profile.value?.role || null)
  const userHotelId = computed(() => profile.value?.hotel_id || null)
  const userDisplayName = computed(() => profile.value?.full_name || profile.value?.email || 'User')

  const isSuperAdmin = computed(() => userRole.value === 'SUPER_ADMIN' as UserRole)
  const isHotelAdmin = computed(() => userRole.value === 'HOTEL_ADMIN' as UserRole)
  const isStaff = computed(() => userRole.value === 'STAFF' as UserRole)
  const isGuest = computed(() => userRole.value === 'GUEST' as UserRole)

  const hasHotelAccess = computed(() => 
    isHotelAdmin.value || 
    isStaff.value
  )

  const hasManagementAccess = computed(() => isSuperAdmin.value)

  // Actions
  const _setUser = (newUser: User | null) => {
    user.value = newUser
  }

  const _setProfile = (newProfile: UserProfile | null) => {
    profile.value = newProfile
  }

  const _clearAuth = () => {
    user.value = null
    profile.value = null
    error.value = null
  }

  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (fetchError) {
        console.error('Error fetching user profile:', fetchError)
        return null
      }
      return data as UserProfile
    } catch (err) {
      console.error('Unexpected error fetching profile:', err)
      return null
    }
  }

  // Portal access validation
  const validatePortalAccess = (portal: 'guest' | 'hotel' | 'management'): boolean => {
    if (!isAuthenticated.value || !userRole.value) {
      return false
    }

    switch (portal) {
      case 'guest':
        return isGuest.value
      case 'hotel':
        return hasHotelAccess.value
      case 'management':
        return hasManagementAccess.value
      default:
        return false
    }
  }

  // Session persistence optimization
  const persistSession = () => {
    if (user.value && profile.value) {
      const sessionData = {
        user: user.value,
        profile: profile.value,
        timestamp: Date.now()
      }
      localStorage.setItem('hotelexia_session_cache', JSON.stringify(sessionData))
    }
  }

  const clearPersistedSession = () => {
    localStorage.removeItem('hotelexia_session_cache')
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    isLoading.value = true
    error.value = null
    try {
      const { data, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (authError || !data.user) {
        error.value = getAuthErrorMessage(authError?.message)
        _clearAuth()
        return false
      }
      
      // onAuthStateChange will handle setting user and profile
      return true
    } catch (err) {
      console.error('Login error:', err)
      error.value = 'An unexpected error occurred during login.'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const signOut = async () => {
    isLoading.value = true
    error.value = null
    const { error: signOutError } = await supabase.auth.signOut()
    if (signOutError) {
      console.error('Sign out error:', signOutError)
      error.value = 'An error occurred during sign out.'
    }
    // onAuthStateChange will handle clearing auth state
    _clearAuth()
    router.push({ name: 'home' })
  }
  
  const redirectToDashboard = () => {
    if (!isAuthenticated.value || !userRole.value) {
      router.push({ name: 'home' })
      return
    }

    switch (userRole.value as UserRole) {
      case 'SUPER_ADMIN' as UserRole:
        router.push({ name: 'management-dashboard' })
        break
      case 'HOTEL_ADMIN' as UserRole:
      case 'STAFF' as UserRole:
        router.push({ name: 'hotel-dashboard' })
        break
      case 'GUEST' as UserRole:
        router.push({ name: 'guest-dashboard' })
        break
      default:
        router.push({ name: 'home' })
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>): Promise<boolean> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return false
    }

    isLoading.value = true
    error.value = null
    try {
      const { data, error: updateError } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.value.id)
        .select()
        .single()

      if (updateError) {
        throw updateError
      }
      _setProfile(data as UserProfile)
      return true
    } catch (err: any) {
      console.error('Error updating profile:', err)
      error.value = 'Failed to update profile.'
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Auto-refresh token handling and enhanced auth listener
  const setupAuthListener = () => {
    supabase.auth.onAuthStateChange(async (event, session) => {
      isLoading.value = true
      const currentUser = session?.user
      _setUser(currentUser ?? null)

      if (currentUser) {
        const profile = await fetchUserProfile(currentUser.id)
        _setProfile(profile)
        persistSession()

        // Initialize hotel context after successful authentication
        // Note: This will be handled by the hotel store's watcher
      } else {
        _setProfile(null)
        clearPersistedSession()
      }

      // Handle specific auth events
      switch (event) {
        case 'SIGNED_IN':
          console.log('User signed in successfully')
          break
        case 'SIGNED_OUT':
          console.log('User signed out')
          _clearAuth()
          break
        case 'TOKEN_REFRESHED':
          console.log('Token refreshed successfully')
          persistSession()
          break
        case 'USER_UPDATED':
          console.log('User profile updated')
          break
      }

      isLoading.value = false
    })
  }

  // Hotel context switching for multi-tenant support
  const switchHotelContext = async (hotelId: string): Promise<boolean> => {
    if (!isSuperAdmin.value) {
      console.error('Only super admins can switch hotel context')
      return false
    }

    try {
      // Update user profile with new hotel context
      const { error } = await supabase
        .from('profiles')
        .update({ hotel_id: hotelId })
        .eq('id', user.value?.id!)

      if (error) {
        console.error('Error switching hotel context:', error)
        return false
      }

      // Refresh profile data
      if (user.value) {
        const updatedProfile = await fetchUserProfile(user.value.id)
        _setProfile(updatedProfile)
        persistSession()
      }

      return true
    } catch (err) {
      console.error('Unexpected error switching hotel context:', err)
      return false
    }
  }

  const getAuthErrorMessage = (message?: string): string => {
    if (!message) return 'An unknown authentication error occurred.'
    if (message.includes('Invalid login credentials')) {
      return 'Invalid email or password.'
    }
    if (message.includes('Email not confirmed')) {
      return 'Please confirm your email address.'
    }
    return 'An authentication error occurred.'
  }

  // Initialize
  setupAuthListener()

  return {
    // State
    user: readonly(user),
    profile: readonly(profile),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    isAuthenticated,
    userRole,
    userHotelId,
    userDisplayName,
    isSuperAdmin,
    isHotelAdmin,
    isStaff,
    isGuest,
    hasHotelAccess,
    hasManagementAccess,

    // Actions
    login,
    signOut,
    redirectToDashboard,
    updateProfile,
    fetchUserProfile,
    validatePortalAccess,
    switchHotelContext,
    persistSession,
    clearPersistedSession,
  }
}) 