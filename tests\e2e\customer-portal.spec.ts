import { test, expect } from '@playwright/test'

test.describe('Customer Portal', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to customer portal
    await page.goto('http://localhost:3001')
  })

  test('should load customer portal successfully', async ({ page }) => {
    // Check if the page loads
    await expect(page).toHaveTitle(/Customer Portal|Müşteri|Hotelexia/)
    
    // Check for main portal elements
    await expect(page.locator('body')).toBeVisible()
  })

  test('should be mobile-first responsive', async ({ page }) => {
    // Test mobile view (primary target)
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('body')).toBeVisible()
    
    // Test larger mobile
    await page.setViewportSize({ width: 414, height: 896 })
    await expect(page.locator('body')).toBeVisible()
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('body')).toBeVisible()
    
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('body')).toBeVisible()
  })

  test('should handle guest authentication', async ({ page }) => {
    // Check for guest login or room access form
    const guestForm = page.locator('form, input[type="text"], input[placeholder*="oda"], input[placeholder*="room"]').first()
    const guestContent = page.locator('[data-testid="guest-portal"], .guest-portal, main').first()
    
    const hasGuestForm = await guestForm.isVisible()
    const hasGuestContent = await guestContent.isVisible()
    
    // Either should have guest form or guest content
    expect(hasGuestForm || hasGuestContent).toBe(true)
    
    if (hasGuestForm) {
      // Test guest form elements
      const roomInput = page.locator('input[placeholder*="oda"], input[placeholder*="room"], input[type="text"]').first()
      if (await roomInput.isVisible()) {
        await expect(roomInput).toBeVisible()
      }
    }
  })

  test('should display guest services', async ({ page }) => {
    // Look for common guest services
    const serviceButtons = page.locator('button, .service, .card')
    const serviceLinks = page.locator('a[href*="service"], a[href*="servis"]')
    
    const hasServices = await serviceButtons.count() > 0 || await serviceLinks.count() > 0
    
    if (hasServices) {
      // Check for common services
      const commonServices = [
        'Room Service', 'Oda Servisi',
        'Housekeeping', 'Temizlik',
        'Concierge', 'Konsiyerj',
        'Restaurant', 'Restoran',
        'Spa', 'Wellness'
      ]
      
      let foundService = false
      for (const service of commonServices) {
        const serviceElement = page.locator(`text=${service}`).first()
        if (await serviceElement.isVisible()) {
          foundService = true
          break
        }
      }
      
      // If we have service elements, at least one should be a recognized service
      if (await serviceButtons.count() > 0) {
        expect(foundService || await serviceButtons.count() > 0).toBe(true)
      }
    }
  })

  test('should handle touch interactions', async ({ page }) => {
    // Set mobile viewport for touch testing
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Look for interactive elements
    const buttons = page.locator('button')
    const links = page.locator('a')
    
    if (await buttons.count() > 0) {
      const firstButton = buttons.first()
      if (await firstButton.isVisible()) {
        // Test touch interaction
        await firstButton.tap()
        // Should not throw error
      }
    }
    
    if (await links.count() > 0) {
      const firstLink = links.first()
      if (await firstLink.isVisible()) {
        // Check if link is touchable
        await expect(firstLink).toBeVisible()
      }
    }
  })

  test('should load without critical errors', async ({ page }) => {
    const errors: string[] = []
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    await page.goto('http://localhost:3001')
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle')
    
    // Check for critical errors (ignore minor warnings)
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('WebSocket')
    )
    
    expect(criticalErrors).toHaveLength(0)
  })

  test('should handle offline scenarios', async ({ page }) => {
    // Test basic offline handling
    await page.goto('http://localhost:3001')
    
    // Go offline
    await page.context().setOffline(true)
    
    // Try to interact with the page
    const body = page.locator('body')
    await expect(body).toBeVisible()
    
    // Go back online
    await page.context().setOffline(false)
    
    // Page should still be functional
    await expect(body).toBeVisible()
  })

  test('should display room information', async ({ page }) => {
    // Look for room-related information
    const roomInfo = page.locator('[data-testid="room-info"], .room-info, .room-details')
    const roomNumber = page.locator('text=/Oda|Room|#[0-9]+/')
    
    if (await roomInfo.isVisible() || await roomNumber.isVisible()) {
      // Should display room information
      expect(await roomInfo.isVisible() || await roomNumber.isVisible()).toBe(true)
    }
  })
})
