<template>
  <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-navy-700 hover:shadow-md transition-shadow duration-200">
    <!-- Task Header -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-center space-x-2">
        <div :class="priorityIconClass" class="p-1.5 rounded-lg">
          <component :is="priorityIcon" class="w-4 h-4 text-white" />
        </div>
        <div>
          <h3 class="font-semibold text-navy-700 dark:text-white text-sm">{{ task.title }}</h3>
          <p class="text-xs text-gray-500 dark:text-gray-400">{{ task.roomNumber }}</p>
        </div>
      </div>
      <div :class="statusBadgeClass" class="px-2 py-1 rounded-full text-xs font-medium">
        {{ statusText }}
      </div>
    </div>

    <!-- Task Description -->
    <p class="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
      {{ task.description }}
    </p>

    <!-- Task Details -->
    <div class="space-y-2 mb-4">
      <!-- Assigned Staff -->
      <div v-if="task.assignedTo" class="flex items-center space-x-2">
        <UserIcon class="w-4 h-4 text-gray-400" />
        <span class="text-xs text-gray-600 dark:text-gray-300">{{ assignedStaffName }}</span>
      </div>

      <!-- Due Time -->
      <div class="flex items-center space-x-2">
        <ClockIcon class="w-4 h-4 text-gray-400" />
        <span class="text-xs text-gray-600 dark:text-gray-300">{{ formattedDueTime }}</span>
      </div>

      <!-- Estimated Duration -->
      <div class="flex items-center space-x-2">
        <CalendarIcon class="w-4 h-4 text-gray-400" />
        <span class="text-xs text-gray-600 dark:text-gray-300">{{ task.estimatedTime }} dakika</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex space-x-2">
      <button
        v-if="canAssign"
        @click="$emit('assign', task.id)"
        class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-3 rounded-lg transition-colors duration-200"
      >
        Ata
      </button>
      <button
        v-if="canStart"
        @click="$emit('start', task.id)"
        class="flex-1 bg-green-500 hover:bg-green-600 text-white text-xs py-2 px-3 rounded-lg transition-colors duration-200"
      >
        Başlat
      </button>
      <button
        v-if="canComplete"
        @click="$emit('complete', task.id)"
        class="flex-1 bg-teal-500 hover:bg-teal-600 text-white text-xs py-2 px-3 rounded-lg transition-colors duration-200"
      >
        Tamamla
      </button>
      <button
        v-if="canCancel"
        @click="$emit('cancel', task.id)"
        class="flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-2 px-3 rounded-lg transition-colors duration-200"
      >
        İptal
      </button>
      <button
        @click="$emit('view-details', task.id)"
        class="px-3 py-2 border border-gray-300 dark:border-navy-600 text-gray-700 dark:text-gray-300 text-xs rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors duration-200"
      >
        Detay
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  ExclamationTriangleIcon, 
  FireIcon, 
  ArrowUpIcon,
  UserIcon,
  ClockIcon,
  CalendarIcon
} from '@heroicons/vue/24/outline'
import type { HousekeepingTask } from '@/types/housekeeping'

interface Props {
  task: HousekeepingTask
}

const props = defineProps<Props>()

defineEmits<{
  assign: [taskId: string]
  start: [taskId: string]
  complete: [taskId: string]
  cancel: [taskId: string]
  'view-details': [taskId: string]
}>()

// Priority icon and styling
const priorityIcon = computed(() => {
  switch (props.task.priority) {
    case 'URGENT':
      return FireIcon
    case 'HIGH':
      return ExclamationTriangleIcon
    case 'MEDIUM':
      return ArrowUpIcon
    default:
      return ArrowUpIcon
  }
})

const priorityIconClass = computed(() => {
  switch (props.task.priority) {
    case 'URGENT':
      return 'bg-red-500'
    case 'HIGH':
      return 'bg-orange-500'
    case 'MEDIUM':
      return 'bg-yellow-500'
    default:
      return 'bg-blue-500'
  }
})

// Status badge styling
const statusBadgeClass = computed(() => {
  switch (props.task.status) {
    case 'NEW':
      return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
    case 'IN_PROGRESS':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
    case 'QUALITY_CHECK':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300'
    case 'COMPLETED':
      return 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
    case 'CANCELLED':
      return 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-700'
  }
})

const statusText = computed(() => {
  switch (props.task.status) {
    case 'NEW':
      return 'Atanmamış'
    case 'IN_PROGRESS':
      return 'Devam Ediyor'
    case 'QUALITY_CHECK':
      return 'Kalite Kontrolü'
    case 'COMPLETED':
      return 'Tamamlandı'
    case 'CANCELLED':
      return 'İptal Edildi'
    default:
      return 'Bilinmiyor'
  }
})

// Action button visibility
const canAssign = computed(() => props.task.status === 'NEW')
const canStart = computed(() => props.task.status === 'NEW' && props.task.assignedTo)
const canComplete = computed(() => props.task.status === 'IN_PROGRESS' || props.task.status === 'QUALITY_CHECK')
const canCancel = computed(() => props.task.status !== 'COMPLETED' && props.task.status !== 'CANCELLED')

// Assigned staff name (we'll need to get this from store)
const assignedStaffName = computed(() => {
  // For now, return the ID, later we can lookup the actual name from staff store
  return props.task.assignedTo || 'Atanmamış'
})

// Formatted due time
const formattedDueTime = computed(() => {
  const date = new Date(props.task.deadline)
  return date.toLocaleString('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 