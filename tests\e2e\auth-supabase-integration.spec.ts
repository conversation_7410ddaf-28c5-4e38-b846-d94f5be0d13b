import { test, expect } from '@playwright/test'

test.describe('Authentication & Supabase Integration', () => {
  test('should handle role-based authentication flow', async ({ page }) => {
    // Test Management Portal authentication
    await page.goto('http://localhost:3000/management/auth/login')
    
    // Check if login form is present
    const emailInput = page.locator('input[type="email"], input[name="email"]')
    const passwordInput = page.locator('input[type="password"], input[name="password"]')
    const submitButton = page.locator('button[type="submit"], button:has-text("Giriş")')
    
    await expect(emailInput).toBeVisible({ timeout: 10000 })
    await expect(passwordInput).toBeVisible()
    await expect(submitButton).toBeVisible()
    
    // Test form validation
    await submitButton.click()
    
    // Should show validation errors or prevent submission
    const hasValidationErrors = await page.locator('.error, .invalid, [class*="error"]').count() > 0
    const emailInvalid = await emailInput.evaluate(el => !el.checkValidity())
    
    expect(hasValidationErrors || emailInvalid).toBe(true)
  })

  test('should handle Hotel Dashboard authentication', async ({ page }) => {
    await page.goto('http://localhost:5002/auth/login')
    
    // Check if login form is present
    const emailInput = page.locator('input[type="email"], input[name="email"]')
    const passwordInput = page.locator('input[type="password"], input[name="password"]')
    
    await expect(emailInput).toBeVisible({ timeout: 10000 })
    await expect(passwordInput).toBeVisible()
    
    // Check for role-specific messaging
    const roleMessage = page.locator('text=/otel personeli/i, text=/hotel staff/i')
    if (await roleMessage.count() > 0) {
      expect(await roleMessage.isVisible()).toBe(true)
    }
  })

  test('should redirect based on user role after authentication', async ({ page }) => {
    // Test direct navigation to protected routes
    const protectedRoutes = [
      { url: 'http://localhost:3000/management/dashboard', expectedRole: 'SUPER_ADMIN' },
      { url: 'http://localhost:5002/housekeeping/rooms', expectedRole: 'HOTEL_STAFF' },
      { url: 'http://localhost:3001', expectedRole: 'GUEST' }
    ]
    
    for (const route of protectedRoutes) {
      await page.goto(route.url)
      
      // Should either show login form or content (depending on auth state)
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      const hasContent = await page.locator('main, .dashboard, .portal, [data-testid="content"]').count() > 0
      const hasAuthRedirect = page.url().includes('login') || page.url().includes('auth')
      
      expect(hasLoginForm || hasContent || hasAuthRedirect).toBe(true)
      console.log(`Route ${route.url}: hasLogin=${hasLoginForm}, hasContent=${hasContent}, hasRedirect=${hasAuthRedirect}`)
    }
  })

  test('should handle session persistence across page reloads', async ({ page }) => {
    // Test session persistence in Hotel Dashboard
    await page.goto('http://localhost:5002')
    
    // Check initial state
    const initialHasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const initialHasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
    
    // Reload page
    await page.reload()
    await page.waitForTimeout(2000)
    
    // Check state after reload
    const reloadHasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
    const reloadHasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
    
    // State should be consistent
    expect(reloadHasLogin).toBe(initialHasLogin)
    expect(reloadHasContent).toBe(initialHasContent)
  })

  test('should handle authentication state across multiple tabs', async ({ context }) => {
    // Create two pages (tabs)
    const page1 = await context.newPage()
    const page2 = await context.newPage()
    
    // Navigate both to protected areas
    await page1.goto('http://localhost:5002')
    await page2.goto('http://localhost:3000/management/dashboard')
    
    await page1.waitForTimeout(2000)
    await page2.waitForTimeout(2000)
    
    // Check auth state consistency
    const page1HasLogin = await page1.locator('input[type="email"], input[type="password"]').count() > 0
    const page2HasLogin = await page2.locator('input[type="email"], input[type="password"]').count() > 0
    
    const page1HasContent = await page1.locator('main, .dashboard, [data-testid="content"]').count() > 0
    const page2HasContent = await page2.locator('main, .dashboard, [data-testid="content"]').count() > 0
    
    console.log(`Page1: hasLogin=${page1HasLogin}, hasContent=${page1HasContent}`)
    console.log(`Page2: hasLogin=${page2HasLogin}, hasContent=${page2HasContent}`)
    
    // Both tabs should handle auth consistently
    expect(page1HasLogin || page1HasContent).toBe(true)
    expect(page2HasLogin || page2HasContent).toBe(true)
    
    await page1.close()
    await page2.close()
  })

  test('should handle logout flow properly', async ({ page }) => {
    // Start from authenticated area
    await page.goto('http://localhost:5002')
    await page.waitForTimeout(2000)
    
    // Look for logout button
    const logoutButton = page.locator('button:has-text("Çıkış"), button:has-text("Logout"), a:has-text("Çıkış"), a:has-text("Logout")')
    
    if (await logoutButton.count() > 0 && await logoutButton.first().isVisible()) {
      await logoutButton.first().click()
      await page.waitForTimeout(3000)
      
      // Should redirect to login or home
      const currentUrl = page.url()
      const isLoginPage = currentUrl.includes('login') || currentUrl.includes('auth')
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      
      expect(isLoginPage || hasLoginForm).toBe(true)
    }
  })

  test('should validate user profile data integration', async ({ page }) => {
    // Test if user profile data is properly fetched and displayed
    await page.goto('http://localhost:5002')
    await page.waitForTimeout(3000)
    
    // Look for user profile information
    const userProfile = page.locator('[data-testid="user-profile"], .user-info, .profile-info')
    const userAvatar = page.locator('[data-testid="user-avatar"], .avatar, .user-avatar')
    const userName = page.locator('[data-testid="user-name"], .user-name, .username')
    
    // If authenticated, should have some user context
    const hasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
    
    if (hasContent) {
      // Should have some form of user identification
      const hasUserContext = await userProfile.count() > 0 || 
                            await userAvatar.count() > 0 || 
                            await userName.count() > 0
      
      if (hasUserContext) {
        console.log('User context found in dashboard')
      }
    }
  })

  test('should handle authentication errors gracefully', async ({ page }) => {
    // Monitor console errors
    const errors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    // Test authentication error handling
    await page.goto('http://localhost:3000/management/auth/login')
    await page.waitForTimeout(2000)
    
    const emailInput = page.locator('input[type="email"], input[name="email"]')
    const passwordInput = page.locator('input[type="password"], input[name="password"]')
    const submitButton = page.locator('button[type="submit"], button:has-text("Giriş")')
    
    if (await emailInput.isVisible() && await passwordInput.isVisible()) {
      // Try invalid credentials
      await emailInput.fill('<EMAIL>')
      await passwordInput.fill('wrongpassword')
      await submitButton.click()
      
      await page.waitForTimeout(3000)
      
      // Should show error message or stay on login page
      const hasErrorMessage = await page.locator('.error, .alert, [class*="error"]').count() > 0
      const stillOnLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
      
      expect(hasErrorMessage || stillOnLogin).toBe(true)
    }
    
    // Check for critical authentication errors
    const criticalErrors = errors.filter(error => 
      error.includes('auth') || 
      error.includes('supabase') ||
      error.includes('login')
    ).filter(error => 
      !error.includes('favicon') && 
      !error.includes('404')
    )
    
    if (criticalErrors.length > 0) {
      console.warn('Authentication errors found:', criticalErrors)
    }
  })

  test('should handle role-based access control', async ({ page }) => {
    // Test access control for different user roles
    const restrictedRoutes = [
      'http://localhost:3000/management/users',
      'http://localhost:3000/management/hotels',
      'http://localhost:5002/housekeeping/rooms',
      'http://localhost:5002/maintenance/tasks'
    ]
    
    for (const route of restrictedRoutes) {
      await page.goto(route)
      await page.waitForTimeout(2000)
      
      // Should either show login form, content, or access denied
      const hasLoginForm = await page.locator('input[type="email"], input[type="password"]').count() > 0
      const hasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
      const hasAccessDenied = await page.locator('text=/access denied/i, text=/yetkisiz/i, .unauthorized').count() > 0
      const isRedirected = !page.url().includes(new URL(route).pathname)
      
      expect(hasLoginForm || hasContent || hasAccessDenied || isRedirected).toBe(true)
      console.log(`Route ${route}: login=${hasLoginForm}, content=${hasContent}, denied=${hasAccessDenied}, redirected=${isRedirected}`)
    }
  })

  test('should handle Supabase connection errors', async ({ page }) => {
    // Monitor network requests
    const failedRequests: string[] = []
    page.on('response', response => {
      if (!response.ok() && response.url().includes('supabase')) {
        failedRequests.push(`${response.status()} ${response.url()}`)
      }
    })
    
    // Test different MFEs
    const testUrls = [
      'http://localhost:3000/management/dashboard',
      'http://localhost:5002/housekeeping/rooms',
      'http://localhost:3001'
    ]
    
    for (const url of testUrls) {
      await page.goto(url)
      await page.waitForTimeout(3000)
      
      // Should handle connection issues gracefully
      const hasErrorBoundary = await page.locator('.error-boundary, [data-testid="error-boundary"]').count() > 0
      const hasOfflineMessage = await page.locator('text=/offline/i, text=/connection/i').count() > 0
      const hasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
      
      // Should either show content or handle errors gracefully
      expect(hasContent || hasErrorBoundary || hasOfflineMessage).toBe(true)
    }
    
    if (failedRequests.length > 0) {
      console.warn('Failed Supabase requests:', failedRequests)
    }
  })
})
