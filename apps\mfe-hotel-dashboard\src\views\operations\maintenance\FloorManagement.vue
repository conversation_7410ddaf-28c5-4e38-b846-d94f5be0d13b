<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Bakı<PERSON> - <PERSON></h1>
      <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON><PERSON> katların bakım durumunu takip edin ve yönetin.</p>
    </div>

    <!-- Filter and Search Section -->
    <div class="bg-white dark:bg-navy-700 rounded-xl p-4 shadow-sm mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="flex-1 min-w-64">
          <input
            v-model="searchTerm"
            type="text"
            placeholder="Kat ara..."
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          />
        </div>
        <div class="flex gap-2">
          <button
            v-for="status in statusOptions"
            :key="status.value"
            @click="selectedStatus = selectedStatus === status.value ? null : status.value"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-all',
              selectedStatus === status.value 
                ? `${status.activeClass} text-white` 
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            ]"
          >
            {{ status.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div
        v-for="stat in floorStatistics"
        :key="stat.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ stat.value }}</p>
          </div>
          <div :class="[
            'w-12 h-12 rounded-xl flex items-center justify-center',
            stat.iconBg
          ]">
            <component :is="stat.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Floors Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="floor in filteredFloors"
        :key="floor.id"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
      >
        <!-- Floor Header -->
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white">{{ floor.name }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ floor.roomCount }} oda</p>
          </div>
          <div :class="[
            'px-3 py-1 rounded-full text-sm font-medium',
            getStatusClass(floor.status)
          ]">
            {{ getStatusText(floor.status) }}
          </div>
        </div>

        <!-- Floor Stats -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center p-3 bg-gray-50 dark:bg-navy-800 rounded-lg">
            <p class="text-2xl font-bold text-green-500">{{ floor.cleanRooms }}</p>
            <p class="text-xs text-gray-600 dark:text-gray-400">Temiz</p>
          </div>
          <div class="text-center p-3 bg-gray-50 dark:bg-navy-800 rounded-lg">
            <p class="text-2xl font-bold text-orange-500">{{ floor.maintenanceRooms }}</p>
            <p class="text-xs text-gray-600 dark:text-gray-400">Bakımda</p>
          </div>
        </div>

        <!-- Progress Bar -->
        <div class="mb-4">
          <div class="flex justify-between text-sm mb-1">
            <span class="text-gray-600 dark:text-gray-400">Tamamlanma Oranı</span>
            <span class="text-navy-700 dark:text-white font-medium">{{ floor.completionRate }}%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${floor.completionRate}%` }"
            ></div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex gap-2">
          <button
            @click="viewFloorDetails(floor)"
            class="flex-1 px-4 py-2 bg-brand-500 hover:bg-brand-600 text-white rounded-lg font-medium transition-colors"
          >
            Detayları Gör
          </button>
          <button
            @click="scheduleMaintenanceForFloor(floor)"
            class="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
          >
            Bakım Planla
          </button>
        </div>

        <!-- Last Maintenance -->
        <div v-if="floor.lastMaintenance" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <p class="text-xs text-gray-500 dark:text-gray-400">
            Son Bakım: {{ formatDate(floor.lastMaintenance) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredFloors.length === 0" class="text-center py-12">
      <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
        <component :is="BuildingOfficeIcon" class="w-12 h-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Kat bulunamadı</h3>
      <p class="text-gray-500 dark:text-gray-400">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import { 
  BuildingOfficeIcon,
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/vue/24/outline'

const managementStore = useManagementStore()

// Reactive data
const searchTerm = ref('')
const selectedStatus = ref<string | null>(null)

// Status options for filtering
const statusOptions = [
  { value: 'OPERATIONAL', label: 'Operasyonel', activeClass: 'bg-green-500' },
  { value: 'MAINTENANCE', label: 'Bakımda', activeClass: 'bg-orange-500' },
  { value: 'ISSUES', label: 'Sorunlu', activeClass: 'bg-red-500' },
  { value: 'INSPECTION', label: 'Kontrol', activeClass: 'bg-blue-500' }
]

// Computed properties
const filteredFloors = computed(() => {
  let floors = managementStore.maintenanceFloors

  if (searchTerm.value) {
    floors = floors.filter(floor => 
      floor.name.toLowerCase().includes(searchTerm.value.toLowerCase())
    )
  }

  if (selectedStatus.value) {
    floors = floors.filter(floor => floor.status === selectedStatus.value)
  }

  return floors
})

const floorStatistics = computed(() => {
  const floors = managementStore.maintenanceFloors
  const totalFloors = floors.length
  const operationalFloors = floors.filter(f => f.status === 'OPERATIONAL').length
  const maintenanceFloors = floors.filter(f => f.status === 'MAINTENANCE').length
  const issueFloors = floors.filter(f => f.status === 'ISSUES').length

  return [
    {
      title: 'Toplam Kat',
      value: totalFloors,
      icon: BuildingOfficeIcon,
      iconBg: 'bg-blue-500'
    },
    {
      title: 'Operasyonel',
      value: operationalFloors,
      icon: CheckCircleIcon,
      iconBg: 'bg-green-500'
    },
    {
      title: 'Bakımda',
      value: maintenanceFloors,
      icon: WrenchScrewdriverIcon,
      iconBg: 'bg-orange-500'
    },
    {
      title: 'Sorunlu',
      value: issueFloors,
      icon: ExclamationTriangleIcon,
      iconBg: 'bg-red-500'
    }
  ]
})

// Helper methods
const getStatusClass = (status: string) => {
  switch (status) {
    case 'OPERATIONAL':
      return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
    case 'MAINTENANCE':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100'
    case 'ISSUES':
      return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
    case 'INSPECTION':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'OPERATIONAL':
      return 'Operasyonel'
    case 'MAINTENANCE':
      return 'Bakımda'
    case 'ISSUES':
      return 'Sorunlu'
    case 'INSPECTION':
      return 'Kontrol'
    default:
      return 'Bilinmiyor'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Action methods
const viewFloorDetails = (floor: any) => {
  console.log('Viewing floor details:', floor)
  // TODO: Implement floor details view
}

const scheduleMaintenanceForFloor = (floor: any) => {
  console.log('Scheduling maintenance for floor:', floor)
  // TODO: Implement maintenance scheduling
}
</script> 