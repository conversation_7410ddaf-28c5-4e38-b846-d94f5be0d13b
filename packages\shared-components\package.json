{"name": "@hotelexia/shared-components", "version": "1.0.0", "description": "Shared UI components for Hotelexia platform with Horizon UI Chakra theme", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "build-with-types": "vue-tsc && vite build", "dev": "vite build --watch", "type-check": "vue-tsc --noEmit"}, "peerDependencies": {"vue": "^3.3.0", "vue-router": "^4.0.0"}, "dependencies": {"radix-vue": "^1.9.17", "@ark-ui/vue": "^5.11.0", "lucide-vue-next": "^0.411.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "vue-i18n": "^12.0.0-alpha.2", "@heroicons/vue": "^2.0.18"}, "devDependencies": {"@types/node": "^20.17.57", "@vitejs/plugin-vue": "^5.1.4", "typescript": "~5.4.0", "vite": "^5.4.19", "vite-plugin-dts": "^4.0.3", "vue-tsc": "^2.1.2"}}