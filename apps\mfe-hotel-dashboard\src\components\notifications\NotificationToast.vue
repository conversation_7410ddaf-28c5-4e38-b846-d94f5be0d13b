<template>
  <Transition
    enter-active-class="transform ease-out duration-300 transition"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
    leave-active-class="transition ease-in duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      class="max-w-sm w-full bg-white dark:bg-navy-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <component
              :is="iconComponent"
              :class="iconClass"
              class="h-6 w-6"
              aria-hidden="true"
            />
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ notification.title }}
            </p>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-300">
              {{ notification.message }}
            </p>
            <div class="mt-2 text-xs text-gray-400 dark:text-gray-500">
              {{ formatTime(notification.timestamp) }}
            </div>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="handleClose"
              class="bg-white dark:bg-navy-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <span class="sr-only">Close</span>
              <XMarkIcon class="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Progress bar for auto-dismiss -->
      <div
        v-if="autoDismiss && timeRemaining > 0"
        class="h-1 bg-gray-200 dark:bg-navy-700"
      >
        <div
          class="h-full transition-all duration-100 ease-linear"
          :class="progressBarClass"
          :style="{ width: `${(timeRemaining / duration) * 100}%` }"
        ></div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import type { RealtimeNotification } from '@/stores/realtimeStore'

interface Props {
  notification: RealtimeNotification
  autoDismiss?: boolean
  duration?: number
}

interface Emits {
  (e: 'close'): void
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  autoDismiss: true,
  duration: 5000
})

const emit = defineEmits<Emits>()

const visible = ref(true)
const timeRemaining = ref(props.duration)
let dismissTimer: NodeJS.Timeout | null = null
let progressTimer: NodeJS.Timeout | null = null

const iconComponent = computed(() => {
  switch (props.notification.type) {
    case 'success':
      return CheckCircleIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'error':
      return XCircleIcon
    case 'info':
    default:
      return InformationCircleIcon
  }
})

const iconClass = computed(() => {
  switch (props.notification.type) {
    case 'success':
      return 'text-green-400'
    case 'warning':
      return 'text-yellow-400'
    case 'error':
      return 'text-red-400'
    case 'info':
    default:
      return 'text-blue-400'
  }
})

const progressBarClass = computed(() => {
  switch (props.notification.type) {
    case 'success':
      return 'bg-green-500'
    case 'warning':
      return 'bg-yellow-500'
    case 'error':
      return 'bg-red-500'
    case 'info':
    default:
      return 'bg-blue-500'
  }
})

const formatTime = (timestamp: Date): string => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  
  if (diff < 60000) { // Less than 1 minute
    return 'Şimdi'
  } else if (diff < 3600000) { // Less than 1 hour
    const minutes = Math.floor(diff / 60000)
    return `${minutes} dakika önce`
  } else if (diff < 86400000) { // Less than 1 day
    const hours = Math.floor(diff / 3600000)
    return `${hours} saat önce`
  } else {
    return timestamp.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const handleClose = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300) // Wait for transition to complete
}

const startAutoDismiss = () => {
  if (!props.autoDismiss) return

  // Start countdown timer
  progressTimer = setInterval(() => {
    timeRemaining.value -= 100
    if (timeRemaining.value <= 0) {
      clearInterval(progressTimer!)
    }
  }, 100)

  // Auto dismiss after duration
  dismissTimer = setTimeout(() => {
    handleClose()
  }, props.duration)
}

const stopAutoDismiss = () => {
  if (dismissTimer) {
    clearTimeout(dismissTimer)
    dismissTimer = null
  }
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
}

onMounted(() => {
  startAutoDismiss()
})

onUnmounted(() => {
  stopAutoDismiss()
})

// Pause auto-dismiss on hover
const handleMouseEnter = () => {
  stopAutoDismiss()
}

const handleMouseLeave = () => {
  if (props.autoDismiss && timeRemaining.value > 0) {
    startAutoDismiss()
  }
}
</script>

<style scoped>
/* Additional custom styles if needed */
</style>
