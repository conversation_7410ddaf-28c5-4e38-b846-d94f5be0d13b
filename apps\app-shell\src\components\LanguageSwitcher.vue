<template>
  <div class="relative">
    <button
      @click="toggleDropdown"
      class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md"
    >
      <span class="text-lg">{{ currentLocaleFlag }}</span>
      <span>{{ currentLocaleLabel }}</span>
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <Transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="isOpen"
        class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
      >
        <div class="py-1">
          <button
            v-for="locale in availableLocales"
            :key="locale.code"
            @click="changeLocale(locale.code)"
            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            :class="{ 'bg-gray-50 text-gray-900': currentLocale === locale.code }"
          >
            <span class="text-lg mr-3">{{ locale.flag }}</span>
            <span>{{ locale.label }}</span>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { setLocale, type SupportedLocale } from '@hotelexia/shared-components'

const { locale } = useI18n()
const isOpen = ref(false)

const availableLocales = [
  { code: 'tr' as SupportedLocale, label: 'Türkçe', flag: '🇹🇷' },
  { code: 'en' as SupportedLocale, label: 'English', flag: '🇺🇸' }
]

const currentLocale = computed(() => locale.value as SupportedLocale)

const currentLocaleData = computed(() => 
  availableLocales.find(l => l.code === currentLocale.value) || availableLocales[0]
)

const currentLocaleFlag = computed(() => currentLocaleData.value.flag)
const currentLocaleLabel = computed(() => currentLocaleData.value.label)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const changeLocale = (newLocale: SupportedLocale) => {
  setLocale(newLocale)
  isOpen.value = false
}

const closeDropdown = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown)
})
</script>
