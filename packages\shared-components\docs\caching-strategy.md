# Caching Strategy Documentation

## Overview

This document outlines the comprehensive caching strategy implemented across all MFEs in the Hotelexia project. The caching system is designed to improve performance, reduce server load, and provide better user experience.

## Architecture

### Core Components

1. **DataCacheService** - Central caching service with TTL and invalidation
2. **useCacheManagement** - Vue composable for component-level cache management
3. **Enhanced Data Services** - Service layer with built-in caching
4. **Cross-MFE Cache Synchronization** - Ensures cache consistency across MFEs

### Cache Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Component Cache (useCacheManagement)                      │
│  - Component-specific cache keys                           │
│  - Automatic cleanup on unmount                            │
│  - Loading state integration                               │
├─────────────────────────────────────────────────────────────┤
│  Service Cache (DataCacheService)                          │
│  - API response caching                                    │
│  - Tag-based invalidation                                  │
│  - Stale-while-revalidate                                  │
├─────────────────────────────────────────────────────────────┤
│  Browser Cache                                             │
│  - HTTP cache headers                                      │
│  - Service worker cache (future)                           │
└─────────────────────────────────────────────────────────────┘
```

## Cache Configuration by MFE

### Hotel Dashboard
- **Default TTL**: 2 minutes
- **Max <PERSON>ache Size**: 100 entries
- **Key Prefix**: `hotel-dashboard`
- **Primary Data**: Dashboard stats, room data, maintenance tasks

### Management Portal
- **Default TTL**: 5 minutes
- **Max Cache Size**: 200 entries
- **Key Prefix**: `management-portal`
- **Primary Data**: Platform stats, hotel lists, user data

### Customer Portal
- **Default TTL**: 3 minutes
- **Max Cache Size**: 150 entries
- **Key Prefix**: `customer-portal`
- **Primary Data**: Reservations, orders, activities

### App Shell
- **Default TTL**: 10 minutes
- **Max Cache Size**: 50 entries
- **Key Prefix**: `app-shell`
- **Primary Data**: User profiles, global notifications

## Cache Strategies

### 1. Time-Based Expiration (TTL)
```typescript
// Short-lived data (real-time updates)
{ ttl: 30 * 1000 } // 30 seconds

// Medium-lived data (frequent updates)
{ ttl: 2 * 60 * 1000 } // 2 minutes

// Long-lived data (infrequent updates)
{ ttl: 10 * 60 * 1000 } // 10 minutes
```

### 2. Tag-Based Invalidation
```typescript
// Cache with tags for selective invalidation
cacheManagement.set('hotel-123', data, {
  tags: ['hotels', 'hotel-123', 'active-hotels']
})

// Invalidate all hotel-related cache
cacheManagement.invalidateByTag('hotels')
```

### 3. Stale-While-Revalidate
```typescript
// Return stale data immediately, fetch fresh data in background
cacheManagement.get('key', fetcher, {
  staleWhileRevalidate: true
})
```

## Implementation Examples

### Enhanced Store with Caching
```typescript
export const useEnhancedStore = defineStore('enhanced', () => {
  const cacheManagement = useCacheManagement({
    keyPrefix: 'store-name',
    defaultTTL: 5 * 60 * 1000,
    enableStats: true
  })

  const fetchData = async (id: string) => {
    return await cacheManagement.get(
      `data-${id}`,
      () => apiService.getData(id),
      { ttl: 2 * 60 * 1000 }
    )
  }
})
```

### Component-Level Caching
```vue
<script setup>
import { useCacheManagement } from '@hotelexia/shared-components'

const cache = useCacheManagement({
  keyPrefix: 'component-name',
  enableAutoCleanup: true
})

const loadData = async () => {
  const data = await cache.get('data-key', fetchFromAPI)
  // Use cached data
}
</script>
```

## Cache Invalidation Strategies

### 1. Automatic Invalidation
- **Time-based**: Automatic expiration based on TTL
- **Size-based**: LRU eviction when cache size limit reached
- **Memory-based**: Cleanup on low memory conditions

### 2. Manual Invalidation
- **Tag-based**: Invalidate related cache entries
- **Pattern-based**: Invalidate by key patterns
- **Complete**: Clear all cache entries

### 3. Cross-MFE Invalidation
- **Real-time events**: Invalidate cache on data changes
- **Broadcast channels**: Sync invalidation across MFEs
- **Event-driven**: Trigger invalidation on specific actions

## Performance Monitoring

### Cache Statistics
```typescript
const stats = cacheManagement.getStats()
// {
//   hitRate: 0.85,
//   missRate: 0.15,
//   totalEntries: 45,
//   cacheSize: 1024000
// }
```

### Performance Metrics
- **Hit Rate**: Percentage of cache hits vs total requests
- **Response Time**: Average response time for cached vs uncached requests
- **Memory Usage**: Current cache memory consumption
- **Eviction Rate**: Frequency of cache evictions

## Best Practices

### 1. Cache Key Design
```typescript
// Good: Hierarchical and descriptive
'hotel-dashboard:room-123:maintenance-tasks'

// Bad: Flat and ambiguous
'tasks123'
```

### 2. TTL Selection
- **Real-time data**: 30 seconds - 2 minutes
- **User-specific data**: 2-5 minutes
- **Reference data**: 10-30 minutes
- **Static content**: 1-24 hours

### 3. Cache Size Management
- Monitor cache size regularly
- Implement appropriate eviction policies
- Use compression for large objects
- Consider memory constraints

### 4. Error Handling
```typescript
try {
  const data = await cache.get(key, fetcher)
} catch (error) {
  // Fallback to direct API call
  const data = await fetcher()
}
```

## Future Enhancements

1. **Persistent Cache**: Browser storage for offline support
2. **Service Worker**: Advanced caching strategies
3. **Cache Warming**: Preload frequently accessed data
4. **Distributed Cache**: Redis integration for server-side caching
5. **Cache Analytics**: Detailed performance monitoring
6. **Smart Prefetching**: Predictive data loading

## Troubleshooting

### Common Issues
1. **Cache Misses**: Check TTL settings and key consistency
2. **Memory Leaks**: Ensure proper cleanup and size limits
3. **Stale Data**: Verify invalidation strategies
4. **Performance**: Monitor hit rates and response times

### Debug Tools
```typescript
// Enable cache debugging
const cache = useCacheManagement({
  enableStats: true,
  enableDebug: true
})

// View cache contents
console.log(cache.getStats())
console.log(cache.getAllKeys())
```
