<template>
      <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h1 class="text-2xl font-bold text-navy-700 dark:text-white">Oda Yönetimi</h1>
          <p class="text-gray-600 dark:text-gray-400">Tüm odaları yönetin ve durumlarını takip edin</p>
        </div>
        <div class="flex gap-3">
          <button
            @click="exportRoomData"
            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            📊 Dışa Aktar
          </button>
          <button
            @click="showMaintenanceModal = true"
            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            🔧 Bakım Raporu
          </button>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
        <div class="bg-white dark:bg-navy-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 text-center">
          <div class="text-lg font-bold text-green-600">{{ roomsByStatus.available.length }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">Müsait</div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 text-center">
          <div class="text-lg font-bold text-blue-600">{{ roomsByStatus.occupied.length }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">Dolu</div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 text-center">
          <div class="text-lg font-bold text-orange-600">{{ roomsByStatus.cleaning.length }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">Temizlik</div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 text-center">
          <div class="text-lg font-bold text-red-600">{{ roomsByStatus.maintenance.length }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">Bakım</div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 text-center">
          <div class="text-lg font-bold text-yellow-600">{{ roomsByStatus.checkout.length }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">Check-out</div>
        </div>
        <div class="bg-white dark:bg-navy-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 text-center">
          <div class="text-lg font-bold text-purple-600">{{ roomsByStatus.reserved.length }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">Rezerve</div>
        </div>
      </div>

      <!-- Filters -->
      <div class="flex flex-wrap gap-4 mb-6">
        <select
          v-model="selectedFloor"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-sm text-gray-900 dark:text-white"
        >
          <option value="">Tüm Katlar</option>
          <option v-for="floor in floors" :key="floor.id" :value="floor.id">
            {{ floor.name }}
          </option>
        </select>

        <select
          v-model="selectedStatus"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-sm text-gray-900 dark:text-white"
        >
          <option value="">Tüm Durumlar</option>
          <option value="AVAILABLE">Müsait</option>
          <option value="OCCUPIED">Dolu</option>
          <option value="CLEANING">Temizlik</option>
          <option value="MAINTENANCE">Bakım</option>
          <option value="CHECKOUT">Check-out</option>
          <option value="RESERVED">Rezerve</option>
        </select>

        <select
          v-model="selectedType"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-sm text-gray-900 dark:text-white"
        >
          <option value="">Tüm Tipler</option>
          <option value="Standard">Standard</option>
          <option value="Deluxe">Deluxe</option>
          <option value="Suite">Suite</option>
          <option value="Presidential">Presidential</option>
        </select>

        <input
          v-model="searchQuery"
          type="text"
          placeholder="Oda numarası veya misafir ara..."
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
        />

        <button
          @click="clearFilters"
          class="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm"
        >
          Filtreleri Temizle
        </button>
      </div>

      <!-- View Toggle -->
      <div class="flex gap-2 mb-6">
        <button
          @click="viewMode = 'grid'"
          :class="viewMode === 'grid' ? 'bg-brand-500 text-white' : 'bg-white dark:bg-navy-700 text-gray-700 dark:text-gray-300'"
          class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-sm font-medium transition-colors"
        >
          🔲 Kart Görünümü
        </button>
        <button
          @click="viewMode = 'table'"
          :class="viewMode === 'table' ? 'bg-brand-500 text-white' : 'bg-white dark:bg-navy-700 text-gray-700 dark:text-gray-300'"
          class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-sm font-medium transition-colors"
        >
          📋 Tablo Görünümü
        </button>
      </div>
    </div>

    <!-- Grid View -->
    <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <div
        v-for="room in filteredRooms"
        :key="room.id"
        @click="selectRoom(room)"
        class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 p-4 cursor-pointer hover:shadow-md transition-all"
      >
        <!-- Room Header -->
        <div class="flex justify-between items-start mb-3">
          <div>
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white">{{ room.number }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ room.type }}</p>
          </div>
          <span :class="getRoomStatusBadgeClass(room.status)" class="px-2 py-1 rounded-full text-xs font-medium">
            {{ getRoomStatusText(room.status) }}
          </span>
        </div>

        <!-- Room Info -->
        <div class="space-y-2 text-sm">
          <div v-if="room.guestName" class="flex items-center gap-2">
            <UserIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-300">{{ room.guestName }}</span>
          </div>
          
          <div v-if="room.checkIn" class="flex items-center gap-2">
            <CalendarIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-300">{{ formatDate(room.checkIn) }}</span>
          </div>

          <div v-if="room.assignedStaff" class="flex items-center gap-2">
            <UserGroupIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-300">{{ room.assignedStaff }}</span>
          </div>

          <div v-if="room.lastCleaned" class="flex items-center gap-2">
            <SparklesIcon class="w-4 h-4 text-gray-400" />
            <span class="text-gray-700 dark:text-gray-300">{{ formatDate(room.lastCleaned) }}</span>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex gap-2 mt-4">
          <button
            v-if="room.status === 'CLEANING'"
            @click.stop="markRoomClean(room.id)"
            class="flex-1 px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors"
          >
            ✓ Temiz
          </button>
          <button
            v-if="room.status === 'AVAILABLE'"
            @click.stop="markRoomForCleaning(room.id)"
            class="flex-1 px-2 py-1 bg-orange-500 text-white text-xs rounded hover:bg-orange-600 transition-colors"
          >
            🧹 Temizlik
          </button>
          <button
            @click.stop="reportMaintenance(room.id)"
            class="flex-1 px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
          >
            🔧 Bakım
          </button>
        </div>
      </div>
    </div>

    <!-- Table View -->
    <div v-if="viewMode === 'table'" class="bg-white dark:bg-navy-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Oda
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Misafir
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Check-in/out
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Atanan Personel
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Son Temizlik
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr
              v-for="room in filteredRooms"
              :key="room.id"
              class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ room.number }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ room.type }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getRoomStatusBadgeClass(room.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getRoomStatusText(room.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ room.guestName || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                <div v-if="room.checkIn">
                  <div>Giriş: {{ formatDate(room.checkIn) }}</div>
                  <div v-if="room.checkOut">Çıkış: {{ formatDate(room.checkOut) }}</div>
                </div>
                <div v-else>-</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ room.assignedStaff || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ room.lastCleaned ? formatDate(room.lastCleaned) : '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  v-if="room.status === 'CLEANING'"
                  @click="markRoomClean(room.id)"
                  class="text-green-600 hover:text-green-900 transition-colors"
                >
                  Temiz
                </button>
                <button
                  v-if="room.status === 'AVAILABLE'"
                  @click="markRoomForCleaning(room.id)"
                  class="text-orange-600 hover:text-orange-900 transition-colors"
                >
                  Temizlik
                </button>
                <button
                  @click="reportMaintenance(room.id)"
                  class="text-red-600 hover:text-red-900 transition-colors"
                >
                  Bakım
                </button>
                <button
                  @click="selectRoom(room)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                >
                  Detay
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="filteredRooms.length === 0" class="text-center py-12">
        <HomeIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Oda bulunamadı</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Filtreleri değiştirmeyi deneyin.</p>
      </div>
    </div>

    <!-- Room Details Modal -->
    <div v-if="selectedRoom" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-start mb-4">
                      <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Oda {{ selectedRoom.number }} - Detaylı Bilgiler</h3>
          <button
            @click="selectedRoom = null"
            class="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Basic Info -->
          <div class="space-y-4">
            <h4 class="font-medium text-gray-900 dark:text-white">Temel Bilgiler</h4>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Oda Numarası</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.number }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Oda Tipi</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.type }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Durum</label>
              <span :class="getRoomStatusBadgeClass(selectedRoom.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getRoomStatusText(selectedRoom.status) }}
              </span>
            </div>

            <div v-if="selectedRoom.guestName">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Misafir</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.guestName }}</p>
            </div>

            <div v-if="selectedRoom.assignedStaff">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Atanan Personel</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ selectedRoom.assignedStaff }}</p>
            </div>
          </div>

          <!-- Dates & History -->
          <div class="space-y-4">
            <h4 class="font-medium text-gray-900 dark:text-white">Tarihler & Geçmiş</h4>
            
            <div v-if="selectedRoom.checkIn">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Check-in</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.checkIn) }}</p>
            </div>

            <div v-if="selectedRoom.checkOut">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Check-out</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.checkOut) }}</p>
            </div>

            <div v-if="selectedRoom.lastCleaned">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Son Temizlik</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.lastCleaned) }}</p>
            </div>

            <div v-if="selectedRoom.lastMaintenance">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Son Bakım</label>
              <p class="text-sm text-gray-900 dark:text-white">{{ formatDate(selectedRoom.lastMaintenance) }}</p>
            </div>
          </div>
        </div>

        <div v-if="selectedRoom.notes" class="mt-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notlar</label>
          <p class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">{{ selectedRoom.notes }}</p>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3 pt-6 border-t border-gray-200 dark:border-gray-600 mt-6">
          <button
            v-if="selectedRoom.status === 'CLEANING'"
            @click="markRoomClean(selectedRoom.id)"
            class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            ✓ Temizlik Tamamlandı
          </button>
          <button
            v-if="selectedRoom.status === 'AVAILABLE'"
            @click="markRoomForCleaning(selectedRoom.id)"
            class="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            🧹 Temizlik Gerekli
          </button>
          <button
            @click="reportMaintenance(selectedRoom.id)"
            class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            🔧 Bakım Raporu
          </button>
          <button
            @click="selectedRoom = null"
            class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors ml-auto"
          >
            Kapat
          </button>
        </div>
      </div>
    </div>

    <!-- Maintenance Report Modal -->
    <div v-if="showMaintenanceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold mb-4">Bakım Raporu</h3>
        
        <form @submit.prevent="submitMaintenanceReport" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Oda</label>
            <select
              v-model="maintenanceReport.roomId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg"
            >
              <option value="">Oda seçin...</option>
              <option v-for="room in allRooms" :key="room.id" :value="room.id">
                {{ room.number }} - {{ room.type }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Sorun Türü</label>
            <select
              v-model="maintenanceReport.issueType"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg"
            >
              <option value="">Sorun türü seçin...</option>
              <option value="Elektrik">Elektrik</option>
              <option value="Tesisat">Tesisat</option>
              <option value="Klima">Klima</option>
              <option value="Mobilya">Mobilya</option>
              <option value="Elektronik">Elektronik</option>
              <option value="Diğer">Diğer</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Açıklama</label>
            <textarea
              v-model="maintenanceReport.description"
              rows="3"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg"
              placeholder="Sorun detaylarını açıklayın..."
            ></textarea>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Öncelik</label>
            <select
              v-model="maintenanceReport.priority"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg"
            >
              <option value="LOW">Düşük</option>
              <option value="MEDIUM">Orta</option>
              <option value="HIGH">Yüksek</option>
              <option value="URGENT">Acil</option>
            </select>
          </div>

          <div class="flex gap-3 pt-4">
            <button
              type="button"
              @click="showMaintenanceModal = false"
              class="flex-1 px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
            >
              İptal
            </button>
            <button
              type="submit"
              class="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Rapor Et
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { RoomStatus, Room, TaskPriority } from '@/types/housekeeping'
import {
  UserIcon,
  CalendarIcon,
  UserGroupIcon,
  SparklesIcon,
  HomeIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const housekeepingStore = useHousekeepingStore()

// Reactive data
const selectedFloor = ref('')
const selectedStatus = ref('')
const selectedType = ref('')
const searchQuery = ref('')
const viewMode = ref<'grid' | 'table'>('grid')
const selectedRoom = ref<Room | null>(null)
const showMaintenanceModal = ref(false)

// Maintenance report form
const maintenanceReport = ref({
  roomId: '',
  issueType: '',
  description: '',
  priority: 'MEDIUM' as TaskPriority
})

// Computed properties
const { floors } = housekeepingStore

const allRooms = computed(() => {
  return floors.flatMap(floor => floor.rooms)
})

const roomsByStatus = computed(() => {
  const available = allRooms.value.filter(r => r.status === 'AVAILABLE')
  const occupied = allRooms.value.filter(r => r.status === 'OCCUPIED')
  const cleaning = allRooms.value.filter(r => r.status === 'CLEANING')
  const maintenance = allRooms.value.filter(r => r.status === 'MAINTENANCE')
  const checkout = allRooms.value.filter(r => r.status === 'CHECKOUT')
  const reserved = allRooms.value.filter(r => r.status === 'RESERVED')

  return { available, occupied, cleaning, maintenance, checkout, reserved }
})

const filteredRooms = computed(() => {
  return allRooms.value.filter(room => {
    const matchesFloor = !selectedFloor.value || room.floorId === selectedFloor.value
    const matchesStatus = !selectedStatus.value || room.status === selectedStatus.value
    const matchesType = !selectedType.value || room.type === selectedType.value
    const matchesSearch = !searchQuery.value || 
      room.number.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (room.guestName && room.guestName.toLowerCase().includes(searchQuery.value.toLowerCase()))
    
    return matchesFloor && matchesStatus && matchesType && matchesSearch
  })
})

// Methods
const clearFilters = () => {
  selectedFloor.value = ''
  selectedStatus.value = ''
  selectedType.value = ''
  searchQuery.value = ''
}

const selectRoom = (room: Room) => {
  selectedRoom.value = room
}

const markRoomClean = (roomId: string) => {
  housekeepingStore.updateRoomStatus(roomId, 'AVAILABLE')
  if (selectedRoom.value?.id === roomId) {
    selectedRoom.value = null
  }
}

const markRoomForCleaning = (roomId: string) => {
  housekeepingStore.updateRoomStatus(roomId, 'CLEANING')
  if (selectedRoom.value?.id === roomId) {
    selectedRoom.value = null
  }
}

const reportMaintenance = (roomId: string) => {
  maintenanceReport.value.roomId = roomId
  showMaintenanceModal.value = true
  if (selectedRoom.value?.id === roomId) {
    selectedRoom.value = null
  }
}

const submitMaintenanceReport = () => {
  // Update room status to maintenance
  housekeepingStore.updateRoomStatus(maintenanceReport.value.roomId, 'MAINTENANCE')
  
  // Here you would typically send the maintenance report to the backend
  console.log('Maintenance report submitted:', maintenanceReport.value)
  
  // Reset form
  maintenanceReport.value = {
    roomId: '',
    issueType: '',
    description: '',
    priority: 'MEDIUM' as TaskPriority
  }
  
  showMaintenanceModal.value = false
}

const exportRoomData = () => {
  // Simulate data export
  console.log('Exporting room data...')
  alert('Oda verileri dışa aktarılıyor...')
}

// Helper functions
const getRoomStatusBadgeClass = (status: RoomStatus) => {
  switch (status) {
    case 'AVAILABLE':
      return 'bg-green-100 text-green-800'
    case 'OCCUPIED':
      return 'bg-blue-100 text-blue-800'
    case 'CLEANING':
      return 'bg-orange-100 text-orange-800'
    case 'MAINTENANCE':
      return 'bg-red-100 text-red-800'
    case 'CHECKOUT':
      return 'bg-yellow-100 text-yellow-800'
    case 'RESERVED':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getRoomStatusText = (status: RoomStatus) => {
  switch (status) {
    case 'AVAILABLE':
      return 'Müsait'
    case 'OCCUPIED':
      return 'Dolu'
    case 'CLEANING':
      return 'Temizlik'
    case 'MAINTENANCE':
      return 'Bakım'
    case 'CHECKOUT':
      return 'Check-out'
    case 'RESERVED':
      return 'Rezerve'
    default:
      return 'Bilinmiyor'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script> 