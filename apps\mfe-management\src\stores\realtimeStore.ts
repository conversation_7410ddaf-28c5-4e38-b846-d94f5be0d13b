import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { 
  crossMFERealtimeService,
  CrossMFEEventType,
  type CrossMFEEvent 
} from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'
import { useManagementStore } from './managementStore'

export interface ManagementNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  read: boolean
  data?: any
}

export const useRealtimeStore = defineStore('realtime', () => {
  // Get auth store for user context
  const authStore = useAuthStore()
  const managementStore = useManagementStore()

  // State
  const notifications = ref<ManagementNotification[]>([])
  const isConnected = ref(false)
  const error = ref<string | null>(null)
  const unsubscribeFunctions = ref<Array<() => void>>([])

  // Computed
  const unreadCount = computed(() =>
    notifications.value.filter(n => !n.read).length
  )

  const recentNotifications = computed(() =>
    notifications.value
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10)
  )

  const addNotification = (notification: Omit<ManagementNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: ManagementNotification = {
      ...notification,
      id: crypto.randomUUID(),
      timestamp: new Date(),
      read: false
    }
    
    notifications.value.unshift(newNotification)
    
    // Keep only last 50 notifications
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markAsRead = (notificationId: string) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (notificationId: string) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // Initialize cross-MFE subscriptions
  const initializeSubscriptions = () => {
    console.log('Initializing Management Portal cross-MFE subscriptions')

    // Cleanup any existing subscriptions first
    cleanup()

    // Subscribe to relevant cross-MFE events
    const unsubscribers = [
      crossMFERealtimeService.subscribe(CrossMFEEventType.HOTEL_UPDATED, handleHotelUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.HOTEL_STATUS_CHANGED, handleHotelUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.USER_PROFILE_UPDATED, handleUserUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.USER_ROLE_CHANGED, handleUserUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.TASK_STATUS_UPDATED, handleTaskUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.MAINTENANCE_TASK_UPDATED, handleTaskUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.SERVICE_REQUEST_UPDATED, handleServiceRequestUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.ORDER_CREATED, handleOrderUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.ORDER_STATUS_UPDATED, handleOrderUpdate),
      crossMFERealtimeService.subscribe(CrossMFEEventType.ACTIVITY_UPDATED, handleActivityUpdate)
    ]

    unsubscribeFunctions.value = unsubscribers
    isConnected.value = true

    // Add initialization notification
    addNotification({
      title: 'Gerçek Zamanlı Bildirimler Aktif',
      message: 'Management Portal için gerçek zamanlı bildirimler başlatıldı',
      type: 'success'
    })
  }

  // Handle hotel updates
  const handleHotelUpdate = (event: CrossMFEEvent) => {
    console.log('Management Portal received hotel update:', event)
    
    if (event.type === CrossMFEEventType.HOTEL_STATUS_CHANGED) {
      addNotification({
        title: 'Otel Durumu Değişti',
        message: `Bir otelin durumu güncellendi`,
        type: 'info',
        data: {
          type: 'hotel_status_change',
          hotelId: event.payload.hotelId,
          eventType: event.type
        }
      })
    } else {
      addNotification({
        title: 'Otel Bilgileri Güncellendi',
        message: `Otel bilgilerinde değişiklik yapıldı`,
        type: 'info',
        data: {
          type: 'hotel_update',
          hotelId: event.payload.hotelId,
          eventType: event.type
        }
      })
    }

    // Refresh hotel data in management store
    managementStore.fetchHotels()
  }

  // Handle user updates
  const handleUserUpdate = (event: CrossMFEEvent) => {
    console.log('Management Portal received user update:', event)
    
    if (event.type === CrossMFEEventType.USER_ROLE_CHANGED) {
      addNotification({
        title: 'Kullanıcı Yetkileri Değişti',
        message: `Bir kullanıcının rol yetkileri güncellendi`,
        type: 'warning',
        data: {
          type: 'user_role_change',
          userId: event.payload.userId,
          eventType: event.type
        }
      })
    } else {
      addNotification({
        title: 'Kullanıcı Profili Güncellendi',
        message: `Bir kullanıcının profil bilgileri değiştirildi`,
        type: 'info',
        data: {
          type: 'user_profile_update',
          userId: event.payload.userId,
          eventType: event.type
        }
      })
    }

    // Refresh user data in management store
    managementStore.fetchUsers()
  }

  // Handle task updates
  const handleTaskUpdate = (event: CrossMFEEvent) => {
    console.log('Management Portal received task update:', event)
    
    addNotification({
      title: 'Görev Durumu Güncellendi',
      message: `Bir görevin durumu değiştirildi`,
      type: 'info',
      data: {
        type: 'task_update',
        taskId: event.payload.id,
        hotelId: event.payload.hotelId,
        eventType: event.type
      }
    })
  }

  // Handle service request updates
  const handleServiceRequestUpdate = (event: CrossMFEEvent) => {
    console.log('Management Portal received service request update:', event)
    
    addNotification({
      title: 'Servis Talebi Güncellendi',
      message: `Bir servis talebinin durumu değiştirildi`,
      type: 'info',
      data: {
        type: 'service_request_update',
        requestId: event.payload.id,
        hotelId: event.payload.hotelId,
        eventType: event.type
      }
    })
  }

  // Handle order updates
  const handleOrderUpdate = (event: CrossMFEEvent) => {
    console.log('Management Portal received order update:', event)
    
    if (event.type === CrossMFEEventType.ORDER_CREATED) {
      addNotification({
        title: 'Yeni Sipariş Alındı',
        message: `Sistemde yeni bir sipariş oluşturuldu`,
        type: 'success',
        data: {
          type: 'new_order',
          orderId: event.payload.id,
          hotelId: event.payload.hotelId,
          eventType: event.type
        }
      })
    } else {
      addNotification({
        title: 'Sipariş Durumu Güncellendi',
        message: `Bir siparişin durumu değiştirildi`,
        type: 'info',
        data: {
          type: 'order_status_update',
          orderId: event.payload.id,
          hotelId: event.payload.hotelId,
          eventType: event.type
        }
      })
    }

    // Refresh platform stats
    managementStore.fetchPlatformStats()
  }

  // Handle activity updates
  const handleActivityUpdate = (event: CrossMFEEvent) => {
    console.log('Management Portal received activity update:', event)
    
    addNotification({
      title: 'Aktivite Güncellendi',
      message: `Bir aktivitede değişiklik yapıldı`,
      type: 'info',
      data: {
        type: 'activity_update',
        activityId: event.payload.id,
        hotelId: event.payload.hotelId,
        eventType: event.type
      }
    })
  }

  // Broadcast hotel updates when changes are made in Management Portal
  const broadcastHotelUpdate = (hotelId: string, hotelData: any, eventType: 'updated' | 'status_changed') => {
    const crossMFEEventType = eventType === 'status_changed' 
      ? CrossMFEEventType.HOTEL_STATUS_CHANGED 
      : CrossMFEEventType.HOTEL_UPDATED

    crossMFERealtimeService.createEvent(
      crossMFEEventType,
      hotelId,
      hotelData,
      'management-portal',
      { eventType, source: 'hotels' }
    )
  }

  const broadcastUserUpdate = (userId: string, userData: any, eventType: 'profile_updated' | 'role_changed') => {
    const crossMFEEventType = eventType === 'role_changed' 
      ? CrossMFEEventType.USER_ROLE_CHANGED 
      : CrossMFEEventType.USER_PROFILE_UPDATED

    crossMFERealtimeService.createEvent(
      crossMFEEventType,
      userId,
      userData,
      'management-portal',
      { eventType, source: 'user_profiles' }
    )
  }

  // Cleanup subscriptions
  const cleanup = () => {
    unsubscribeFunctions.value.forEach(unsubscribe => unsubscribe())
    unsubscribeFunctions.value = []
    isConnected.value = false
    error.value = null
  }

  return {
    // State
    notifications,
    isConnected,
    error,
    
    // Computed
    unreadCount,
    recentNotifications,
    
    // Actions
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    initializeSubscriptions,
    cleanup,
    
    // Cross-MFE broadcasting functions
    broadcastHotelUpdate,
    broadcastUserUpdate
  }
})
