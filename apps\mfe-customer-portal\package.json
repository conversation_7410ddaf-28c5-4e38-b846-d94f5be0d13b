{"name": "@hotelexia/mfe-customer-portal", "version": "1.0.0", "description": "Hotelexia Customer Portal - Mobile-first micro frontend for hotel guests", "type": "module", "scripts": {"dev": "vite --port 3001 --host", "build": "vue-tsc && vite build", "preview": "vite preview --port 3001 --strictPort", "serve": "concurrently \"vite build --watch\" \"vite preview --port 3001 --strictPort\"", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@hotelexia/shared-components": "workspace:*", "@hotelexia/shared-supabase-client": "workspace:*", "@supabase/supabase-js": "^2.49.10", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.5", "@testing-library/jest-dom": "^6.4.2", "@testing-library/vue": "^8.0.2", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vitejs/plugin-vue": "^5.0.3", "@vitest/coverage-v8": "^1.3.1", "@vue/test-utils": "^2.4.4", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "jsdom": "^24.0.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.11", "vitest": "^1.3.1", "vue-tsc": "^1.8.27"}}