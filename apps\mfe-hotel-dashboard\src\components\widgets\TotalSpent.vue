<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h4 class="text-lg font-bold text-navy-700 dark:text-white">Toplam Harcama</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">Bu Ay</p>
      </div>
      <div class="text-right">
        <h3 class="text-2xl font-bold text-navy-700 dark:text-white">₺34,252</h3>
        <p class="text-sm text-green-500 font-medium">+2.45%</p>
      </div>
    </div>

    <!-- Chart Placeholder -->
    <div class="h-64 bg-gradient-to-r from-brand-500 to-brand-600 rounded-lg flex items-center justify-center">
      <div class="text-white text-center">
        <ChartBarIcon class="w-12 h-12 mx-auto mb-2 opacity-80" />
        <p class="text-sm opacity-80">Bar Chart</p>
        <p class="text-xs opacity-60">Chart.js entegrasyonu</p>
      </div>
    </div>

    <!-- Chart Legend -->
    <div class="flex items-center justify-center mt-4 space-x-6">
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-brand-500 rounded-full"></div>
        <span class="text-sm text-gray-600 dark:text-gray-400">Bu Ay</span>
      </div>
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
        <span class="text-sm text-gray-600 dark:text-gray-400">Geçen Ay</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChartBarIcon } from '@heroicons/vue/24/outline'

// TODO: Chart.js integration will be added here
// import { Bar } from 'vue-chartjs'
// import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js'
</script>

<style scoped>
/* Chart specific styles */
</style> 