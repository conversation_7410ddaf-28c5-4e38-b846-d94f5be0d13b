<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Main Content -->
    <div class="flex flex-col items-center justify-center min-h-screen px-4 py-8">
      <div class="max-w-md mx-auto text-center">
        
        <!-- Error Illustration -->
        <div class="mb-8">
          <!-- Error Animation with Sad Face -->
          <div class="relative w-80 h-64 mx-auto">
            <!-- Background error effect -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-60 h-60 bg-gradient-to-r from-red-400 via-red-500 to-red-600 rounded-full opacity-20 animate-pulse"></div>
              <div class="absolute w-40 h-40 bg-gradient-to-r from-red-500 to-red-600 rounded-full opacity-30"></div>
            </div>
            
            <!-- Error Icons and Elements -->
            <div class="relative z-10 flex items-center justify-center h-full">
              <div class="text-center">
                <!-- Main Error Icon -->
                <div class="w-24 h-24 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                  <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"/>
                  </svg>
                </div>
                
                <!-- Credit Card with X -->
                <div class="relative">
                  <img 
                    src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop&crop=center" 
                    alt="Payment Failed Illustration" 
                    class="w-64 h-40 object-contain mx-auto filter grayscale contrast-75"
                  />
                  <!-- Red X Overlay -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                      <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M6 18L18 6M6 6l12 12"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Floating Error Elements -->
            <div class="absolute top-4 left-8 animate-bounce" style="animation-delay: 0.5s;">
              <div class="w-3 h-3 bg-red-400 rounded-full opacity-60"></div>
            </div>
            <div class="absolute top-8 right-12 animate-bounce" style="animation-delay: 1s;">
              <div class="w-2 h-2 bg-red-500 rounded-full opacity-70"></div>
            </div>
            <div class="absolute bottom-12 left-4 animate-bounce" style="animation-delay: 1.5s;">
              <div class="w-4 h-4 bg-red-300 rounded-full opacity-50"></div>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-textDark mb-4">
            Sipariş Başarısız!
          </h1>
          <p class="text-lg text-textMedium mb-2">
            {{ errorMessage || 'Ödeme işleminiz gerçekleştirilemedi.' }}
          </p>
          <p class="text-base text-textLight">
            Lütfen ödeme bilgilerinizi kontrol ederek tekrar deneyiniz.
          </p>
        </div>

        <!-- Error Details (if available) -->
        <div v-if="errorCode" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-sm text-red-600">
            <span class="font-medium">Hata Kodu:</span> {{ errorCode }}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
          <!-- Retry Button -->
          <button 
            @click="retryOrder"
            class="w-full bg-gradient-to-r from-primary to-red-500 text-white py-4 px-8 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
          >
            <div class="flex items-center justify-center space-x-3">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              <span>Tekrar Dene</span>
            </div>
          </button>

          <!-- Back to Menu Button -->
          <button 
            @click="goToMenu"
            class="w-full bg-white text-textDark py-4 px-8 rounded-xl font-medium text-lg border-2 border-gray-200 hover:border-primary hover:text-primary transition-all duration-300"
          >
            <div class="flex items-center justify-center space-x-3">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"/>
              </svg>
              <span>Menüye Dön</span>
            </div>
          </button>

          <!-- Back to Dashboard Button -->
          <button 
            @click="goToDashboard"
            class="w-full bg-transparent text-textMedium py-3 px-8 rounded-xl font-medium hover:text-primary transition-colors duration-300"
          >
            Ana Sayfaya Dön
          </button>
        </div>

        <!-- Help Section -->
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
          <p class="text-sm text-textMedium mb-2">
            Sorun devam ederse lütfen resepsiyonla iletişime geçiniz.
          </p>
          <div class="flex items-center justify-center space-x-4">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              <span class="text-sm font-medium text-primary">Resepsiyon: 0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// Error data
const errorMessage = ref<string>('')
const errorCode = ref<string>('')

// Get error details from query params
onMounted(() => {
  errorMessage.value = (route.query.message as string) || 'Ödeme işleminiz gerçekleştirilemedi.'
  errorCode.value = (route.query.code as string) || ''
})

// Actions
const retryOrder = () => {
  // Go back to cart to retry
  router.push('/cart')
}

const goToMenu = () => {
  // Go to room service menu
  router.push('/room-service')
}

const goToDashboard = () => {
  // Go to main dashboard
  router.push('/dashboard')
}
</script>

<style scoped>
.animate-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    opacity: 0.2;
    transform: scale(0.95);
  }
  to {
    opacity: 0.4;
    transform: scale(1.05);
  }
}
</style> 