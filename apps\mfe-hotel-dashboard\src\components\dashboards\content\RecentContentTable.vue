<template>
  <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card p-6 border border-gray-200 dark:border-navy-700">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-bold text-navy-700 dark:text-white">
        Son <PERSON>en İçerikler
      </h3>
      <ClockIcon class="w-5 h-5 text-brand-500" />
    </div>
    
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-200 dark:border-navy-700">
            <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">
              <PERSON>ç<PERSON>k Adı
            </th>
            <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">
              Türü
            </th>
            <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">
              <PERSON>
            </th>
            <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">
              Durum
            </th>
            <th class="text-right py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">
              İşlemler
            </th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="item in recentContent" 
            :key="item.id"
            class="border-b border-gray-100 dark:border-navy-700 hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors duration-200"
          >
            <td class="py-4 px-4">
              <div class="flex items-center">
                <component :is="getTypeIcon(item.type)" class="w-5 h-5 mr-3" :class="getTypeIconColor(item.type)" />
                <span class="font-medium text-gray-900 dark:text-white">
                  {{ item.name }}
                </span>
              </div>
            </td>
            <td class="py-4 px-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getTypeBadgeClass(item.type)">
                {{ item.type }}
              </span>
            </td>
            <td class="py-4 px-4">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {{ formatRelativeTime(item.modifiedAt) }}
              </span>
            </td>
            <td class="py-4 px-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getStatusBadgeClass(item.status)">
                {{ item.status }}
              </span>
            </td>
            <td class="py-4 px-4 text-right">
              <button
                @click="handleEdit(item)"
                class="text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 text-sm font-medium"
              >
                Düzenle
              </button>
            </td>
          </tr>
        </tbody>
      </table>
      
      <!-- Empty state -->
      <div v-if="recentContent.length === 0" class="text-center py-8">
        <DocumentIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Henüz içerik yok</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Yeni içerik eklemeye başlayın.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ClockIcon,
  DocumentIcon,
  CalendarIcon,
  CubeIcon,
  HeartIcon,
  TagIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()

interface RecentContentItem {
  id: string
  name: string
  type: string
  modifiedAt: Date
  status: string
}

interface Props {
  recentContent: RecentContentItem[]
}

const props = defineProps<Props>()

const getTypeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'Oda Servisi': CubeIcon,
    'Aktivite': CalendarIcon,
    'Hizmet': HeartIcon,
    'Promosyon': TagIcon
  }
  return iconMap[type] || DocumentIcon
}

const getTypeIconColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'Oda Servisi': 'text-blue-500',
    'Aktivite': 'text-green-500',
    'Hizmet': 'text-purple-500',
    'Promosyon': 'text-orange-500'
  }
  return colorMap[type] || 'text-gray-500'
}

const getTypeBadgeClass = (type: string) => {
  const classMap: Record<string, string> = {
    'Oda Servisi': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    'Aktivite': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    'Hizmet': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    'Promosyon': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
  }
  return classMap[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
}

const getStatusBadgeClass = (status: string) => {
  const classMap: Record<string, string> = {
    'Aktif': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    'Pasif': 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    'Yayında': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    'Taslak': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
}

const formatRelativeTime = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Az önce'
  if (diffInMinutes < 60) return `${diffInMinutes} dakika önce`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours} saat önce`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays} gün önce`
  
  return date.toLocaleDateString('tr-TR')
}

const handleEdit = (item: RecentContentItem) => {
  const routeMap: Record<string, string> = {
    'Oda Servisi': '/hotel/content/room-service/management',
    'Aktivite': '/hotel/content/activities/management',
    'Hizmet': '/hotel/content/guest-services/management',
    'Promosyon': '/hotel/content/room-service/management'
  }
  
  const route = routeMap[item.type]
  if (route) {
    router.push(route)
  }
}
</script> 