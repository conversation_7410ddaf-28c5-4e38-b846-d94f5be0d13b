<template>
  <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 safe-area-pb">
    <div class="max-w-md mx-auto">
      <div class="flex justify-around items-center">
        <router-link 
          to="/dashboard" 
          class="flex flex-col items-center py-2 px-3 text-textMedium hover:text-primary transition-colors"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <span class="text-xs font-medium">Ana Sayfa</span>
        </router-link>
        
        <router-link 
          to="/room-service-menu" 
          :class="{ 'text-primary': currentPage === 'room-service', 'text-textMedium hover:text-primary': currentPage !== 'room-service' }"
          class="flex flex-col items-center py-2 px-3 transition-colors relative"
        >
          <div class="relative">
            <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H3m4 10v6a1 1 0 001 1h9a1 1 0 001-1v-6M7 13v6a1 1 0 001 1h2M17 13v2"/>
            </svg>
            <!-- Cart Badge -->
            <div 
              v-if="cartStore.cartItemCount > 0" 
              class="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center"
            >
              {{ cartStore.cartItemCount > 9 ? '9+' : cartStore.cartItemCount }}
            </div>
          </div>
          <span class="text-xs font-medium">Oda Servisi</span>
        </router-link>
        
        <router-link 
          to="/activities" 
          :class="{ 'text-primary': currentPage === 'activities', 'text-textMedium hover:text-primary': currentPage !== 'activities' }"
          class="flex flex-col items-center py-2 px-3 transition-colors"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
          </svg>
          <span class="text-xs font-medium">Aktiviteler</span>
        </router-link>
        
        <router-link 
          to="/services" 
          :class="{ 'text-primary': currentPage === 'services', 'text-textMedium hover:text-primary': currentPage !== 'services' }"
          class="flex flex-col items-center py-2 px-3 transition-colors"
        >
          <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"/>
          </svg>
          <span class="text-xs font-medium">Hizmetler</span>
        </router-link>
        
        <router-link 
          to="/notifications" 
          :class="{ 'text-primary': currentPage === 'notifications', 'text-textMedium hover:text-primary': currentPage !== 'notifications' }"
          class="flex flex-col items-center py-2 px-3 transition-colors relative"
        >
          <div class="relative">
            <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 2h7v5H4V2zm7 7h5v5h-5V9zM4 9h5v5H4V9z"/>
            </svg>
            <!-- Notification Badge -->
            <div 
              v-if="notificationStore.unreadCount > 0" 
              class="absolute -top-2 -right-2 bg-primary text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center animate-pulse"
            >
              {{ notificationStore.unreadCount > 9 ? '9+' : notificationStore.unreadCount }}
            </div>
          </div>
          <span class="text-xs font-medium">Bildirimler</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCartStore } from '../stores/cartStore'
import { useNotificationStore } from '../stores/notificationStore'

interface Props {
  currentPage?: string
}

defineProps<Props>()

// Cart store
const cartStore = useCartStore()

// Notification store
const notificationStore = useNotificationStore()
</script>

<style scoped>
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}
</style> 