<template>
  <div class="bg-white rounded-xl shadow-lg p-4 mb-4">
    <!-- Order Header -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center">
        <span class="text-2xl mr-3">🍽️</span>
        <div>
          <h3 class="text-lg font-semibold text-navy-700">
            Sipariş #{{ order.id.slice(-6) }}
          </h3>
          <p class="text-sm text-gray-600">
            {{ formatDate(order.created_at || '') }}
          </p>
        </div>
      </div>
      
      <!-- Order Status -->
      <div class="text-right">
        <span 
          :class="getStatusClass(order.status || '')"
          class="px-3 py-1 rounded-full text-xs font-medium"
        >
          {{ getStatusText(order.status || '') }}
        </span>
      </div>
    </div>

    <!-- Order Items -->
    <div v-if="order.order_items && order.order_items.length > 0" class="mb-3">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Sipariş Detayları:</h4>
      <div class="space-y-2">
        <div 
          v-for="item in order.order_items" 
          :key="item.id"
          class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center">
            <img
              :src="getItemImage(item.menu_item?.name || 'food')"
              :alt="item.menu_item?.name"
              class="w-10 h-10 rounded-lg object-cover mr-3"
              @error="handleImageError"
            />
            <div>
              <p class="font-medium text-sm">{{ item.menu_item?.name || 'Bilinmeyen Ürün' }}</p>
              <p class="text-xs text-gray-600">{{ item.quantity }} adet</p>
            </div>
          </div>
          <p class="font-semibold text-sm">
            {{ formatPrice(item.price_at_time_of_order * item.quantity) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Order Total and Notes -->
    <div class="border-t pt-3">
      <div class="flex items-center justify-between mb-2">
        <span class="font-semibold text-navy-700">Toplam:</span>
        <span class="font-bold text-lg text-brand-600">
          {{ formatPrice(order.total_amount) }}
        </span>
      </div>
      
      <div v-if="order.special_instructions" class="mb-3">
        <p class="text-sm text-gray-600">
          <span class="font-medium">Not:</span> {{ order.special_instructions }}
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-2 mt-3">
        <!-- Cancel Button (only for pending orders) -->
        <button
          v-if="order.status === 'pending'"
          @click="handleCancelOrder"
          :disabled="isLoading"
          class="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
        >
          <span v-if="isLoading">İptal Ediliyor...</span>
          <span v-else>Siparişi İptal Et</span>
        </button>

        <!-- Reorder Button -->
        <button
          v-if="order.status === 'delivered'"
          @click="handleReorder"
          class="flex-1 bg-brand-500 hover:bg-brand-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
        >
          Tekrar Sipariş Ver
        </button>

        <!-- Track Order Button -->
        <button
          v-if="order.status === 'pending' || order.status === 'preparing'"
          @click="emit('track', order.id)"
          class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium"
        >
          Siparişi Takip Et
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue'
import type { Order } from '@hotelexia/shared-supabase-client'
import { getSafeImageUrl, handleImageError as handleImgError } from '@/utils/imageUtils'

interface Props {
  order: Order
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const emit = defineEmits<{
  cancel: [orderId: string]
  reorder: [order: Order]
  track: [orderId: string]
}>()

function handleCancelOrder() {
  if (confirm('Bu siparişi iptal etmek istediğinizden emin misiniz?')) {
    emit('cancel', props.order.id)
  }
}

function handleReorder() {
  emit('reorder', props.order)
}

function getStatusClass(status: string) {
  const statusClasses = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'preparing': 'bg-blue-100 text-blue-800',
    'delivering': 'bg-purple-100 text-purple-800',
    'completed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-red-100 text-red-800'
  }
  return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string) {
  const statusTexts = {
    'pending': 'Bekliyor',
    'preparing': 'Hazırlanıyor',
    'delivering': 'Yolda',
    'completed': 'Teslim Edildi',
    'cancelled': 'İptal Edildi'
  }
  return statusTexts[status as keyof typeof statusTexts] || 'Bilinmiyor'
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

function formatPrice(price: number) {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(price)
}

function getItemImage(_itemName: string) {
  return getSafeImageUrl(undefined, 'food')
}

function handleImageError(event: Event) {
  handleImgError(event, 'food')
}
</script> 