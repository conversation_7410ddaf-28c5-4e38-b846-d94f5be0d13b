<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Main Content -->
    <div class="flex flex-col items-center justify-center min-h-screen px-4 py-8">
      <div class="max-w-md mx-auto text-center">
        
        <!-- Success Illustration -->
        <div class="mb-8 relative">
          <!-- Credit Cards with Burst Effect Illustration -->
          <div class="relative w-80 h-64 mx-auto">
            <!-- Background burst effect -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-60 h-60 bg-gradient-to-r from-orange-400 via-orange-500 to-red-500 rounded-full opacity-20 animate-pulse"></div>
              <div class="absolute w-40 h-40 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-30 animate-ping"></div>
            </div>
            
            <!-- Main illustration container -->
            <div class="relative z-10 w-full h-full flex items-center justify-center">
              <!-- Laptop base -->
              <div class="relative">
                                 <img 
                   src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center" 
                   alt="Payment Success Illustration" 
                   class="w-72 h-48 object-contain mx-auto filter drop-shadow-lg"
                 />
                
                <!-- Floating elements animation -->
                <div class="absolute -top-4 -left-4 w-8 h-8 bg-orange-400 rounded-full opacity-70 animate-bounce"></div>
                <div class="absolute -top-2 -right-6 w-6 h-6 bg-yellow-400 rounded-full opacity-60 animate-bounce" style="animation-delay: 0.5s;"></div>
                <div class="absolute -bottom-2 -left-6 w-4 h-4 bg-red-400 rounded-full opacity-50 animate-bounce" style="animation-delay: 1s;"></div>
                <div class="absolute -bottom-4 -right-4 w-6 h-6 bg-teal-400 rounded-full opacity-60 animate-bounce" style="animation-delay: 1.5s;"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Success Message -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-textDark mb-4">
            Siparişiniz İçin<br/>Teşekkürler!
          </h1>
          <p class="text-textMedium text-lg leading-relaxed">
            Siparişiniz zamanında teslim edilecektir.<br/>
            Teşekkür ederiz!
          </p>
        </div>

        <!-- Order Details Summary (Optional) -->
        <div v-if="orderDetails" class="bg-white rounded-xl p-4 mb-8 text-left shadow-sm">
          <h3 class="font-semibold text-textDark mb-3">Sipariş Özeti</h3>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-textMedium">Sipariş No:</span>
              <span class="font-medium text-textDark">#{{ orderDetails.orderNumber }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-textMedium">Toplam:</span>
              <span class="font-bold text-primary">₺{{ orderDetails.total.toFixed(2) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-textMedium">Tahmini Teslimat:</span>
              <span class="font-medium text-textDark">{{ orderDetails.estimatedDelivery }}</span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
          <!-- Continue Shopping Button -->
          <button 
            @click="continueShopping"
            class="w-full bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white py-4 px-6 rounded-xl font-semibold text-lg shadow-lg transition-all duration-200"
          >
            Alışverişe Devam Et
          </button>
          
          <!-- View Orders Button -->
          <button 
            @click="viewOrders"
            class="w-full bg-white border-2 border-teal-500 text-teal-500 hover:bg-teal-50 py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200"
          >
            Siparişleri Görüntüle
          </button>
        </div>

        <!-- Bottom Spacer for Navigation -->
        <div class="h-20"></div>
      </div>
    </div>

    <!-- Celebration Animation (Optional) -->
    <div v-if="showCelebration" class="fixed inset-0 pointer-events-none z-50">
      <div class="absolute inset-0 flex items-center justify-center">
        <!-- Confetti or celebration effect could go here -->
        <div class="animate-pulse">
          <div class="w-32 h-32 bg-green-500/20 rounded-full animate-ping"></div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useCartStore } from '../stores/cartStore'

const router = useRouter()
const route = useRoute()

// Cart store for clearing cart after successful order
const cartStore = useCartStore()

// State
const showCelebration = ref(true)
const orderDetails = ref<any>(null)

// Methods
const continueShopping = () => {
  router.push('/room-service-menu')
}

const viewOrders = () => {
  router.push('/my-orders')
}

// Generate dummy order details
const generateOrderDetails = () => {
  const orderNumber = Math.floor(100000 + Math.random() * 900000)
  const now = new Date()
  const estimatedTime = new Date(now.getTime() + 30 * 60000) // 30 minutes from now
  
  return {
    orderNumber: orderNumber.toString(),
    total: route.query.total ? parseFloat(route.query.total as string) : cartStore.cartTotal,
    estimatedDelivery: estimatedTime.toLocaleTimeString('tr-TR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
}

// Lifecycle
onMounted(() => {
  // Generate order details
  orderDetails.value = generateOrderDetails()
  
  // Clear cart after successful order
  cartStore.clearCart()
  
  // Hide celebration animation after 3 seconds
  setTimeout(() => {
    showCelebration.value = false
  }, 3000)
  
  // Auto redirect to menu after 10 seconds (optional)
  setTimeout(() => {
    // Uncomment if you want auto redirect
    // router.push('/room-service-menu')
  }, 10000)
})
</script>

<style scoped>
@keyframes confetti {
  0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
  100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

.confetti {
  animation: confetti 3s ease-out infinite;
}
</style> 