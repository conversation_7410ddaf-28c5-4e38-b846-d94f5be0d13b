<template>
  <div class="space-y-6">
    <!-- Guest Search -->
    <div class="bg-white dark:bg-navy-800 rounded-lg shadow-card p-6">
      <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-4"><PERSON><PERSON><PERSON><PERSON></h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <PERSON><PERSON><PERSON><PERSON> Adı veya Oda Numarası
          </label>
          <input
            type="text"
            v-model="searchQuery"
            @input="searchGuests"
            placeholder="Misa<PERSON>r adı veya oda numarası girin..."
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <PERSON><PERSON><PERSON><PERSON>
          </label>
          <select
            v-model="selectedGuest"
            @change="loadGuestHistory"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
          >
            <option value="">Misafir seçin...</option>
            <option v-for="guest in filteredGuests" :key="guest.id" :value="guest.id">
              {{ guest.name }} - Oda {{ guest.roomNumber }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tarih Filtresi
          </label>
          <select
            v-model="dateFilter"
            @change="loadGuestHistory"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
          >
            <option value="all">Tüm Zamanlar</option>
            <option value="today">Bugün</option>
            <option value="week">Son 7 Gün</option>
            <option value="month">Son 30 Gün</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Guest Info Card -->
    <div v-if="selectedGuestInfo" class="bg-white dark:bg-navy-800 rounded-lg shadow-card p-6">
      <div class="flex items-center space-x-4">
        <div class="w-16 h-16 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-xl">{{ selectedGuestInfo.name.charAt(0) }}</span>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">{{ selectedGuestInfo.name }}</h3>
          <p class="text-gray-600 dark:text-gray-300">Oda {{ selectedGuestInfo.roomNumber }}</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">Check-in: {{ selectedGuestInfo.checkIn }}</p>
        </div>
        <div class="ml-auto">
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-brand-600 dark:text-brand-400">{{ notificationStats.total }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Toplam</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ notificationStats.read }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Okundu</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ notificationStats.unread }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Okunmadı</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notification Timeline -->
    <div v-if="guestNotifications.length > 0" class="bg-white dark:bg-navy-800 rounded-lg shadow-card p-6">
      <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Bildirim Geçmişi</h3>
      
      <div class="space-y-6">
        <div v-for="notification in guestNotifications" :key="notification.id" class="relative">
          <!-- Timeline line -->
          <div v-if="guestNotifications.indexOf(notification) !== guestNotifications.length - 1" 
               class="absolute left-4 top-10 bottom-0 w-px bg-gray-200 dark:bg-gray-700"></div>
          
          <div class="flex items-start space-x-4">
            <!-- Timeline dot -->
            <div class="relative flex-shrink-0">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                   :class="getNotificationDotClass(notification)">
                <component :is="getNotificationIcon(notification)" class="w-4 h-4" />
              </div>
            </div>
            
            <!-- Notification content -->
            <div class="flex-1 min-w-0">
              <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-4">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="text-sm font-semibold text-navy-700 dark:text-white mb-1">
                      {{ notification.title }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      {{ notification.message }}
                    </p>
                    <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>{{ formatDate(notification.sentAt) }}</span>
                      <span class="flex items-center">
                        <component :is="getChannelIcon(notification.channel)" class="w-3 h-3 mr-1" />
                        {{ getChannelName(notification.channel) }}
                      </span>
                      <span class="flex items-center">
                        <div class="w-2 h-2 rounded-full mr-1" :class="getStatusDotClass(notification.status)"></div>
                        {{ getStatusText(notification.status) }}
                      </span>
                    </div>
                  </div>
                  <div class="flex-shrink-0 ml-4">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                          :class="getNotificationBadgeClass(notification)">
                      {{ notification.status === 'read' ? 'Okundu' : 'Okunmadı' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="selectedGuest" class="bg-white dark:bg-navy-800 rounded-lg shadow-card p-12 text-center">
      <BellSlashIcon class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Bildirim bulunamadı</h3>
      <p class="text-gray-500 dark:text-gray-400">
        Seçilen misafir için {{ dateFilter === 'all' ? 'hiç' : 'bu tarih aralığında' }} bildirim geçmişi mevcut değil.
      </p>
    </div>

    <!-- Initial State -->
    <div v-else class="bg-white dark:bg-navy-800 rounded-lg shadow-card p-12 text-center">
      <UserIcon class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Misafir Seçin</h3>
      <p class="text-gray-500 dark:text-gray-400">
        Bildirim geçmişini görüntülemek için yukarıdan bir misafir seçin.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  UserIcon,
  BellSlashIcon,
  BellIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

const searchQuery = ref('')
const selectedGuest = ref('')
const dateFilter = ref('all')

// Sample guest data
const guests = ref([
  { id: '1', name: 'Ahmet Yılmaz', roomNumber: '101', checkIn: '10 Oca 2024' },
  { id: '2', name: 'Fatma Özkan', roomNumber: '205', checkIn: '12 Oca 2024' },
  { id: '3', name: 'Mehmet Kaya', roomNumber: '308', checkIn: '14 Oca 2024' },
  { id: '4', name: 'Ayşe Demir', roomNumber: '412', checkIn: '15 Oca 2024' },
  { id: '5', name: 'Ali Şahin', roomNumber: '501', checkIn: '16 Oca 2024' }
])

// Sample notification data
const notificationHistory = ref([
  {
    id: '1',
    guestId: '1',
    title: 'Hoş Geldiniz',
    message: 'Otelimize hoş geldiniz! Konforlu bir konaklama diliyoruz.',
    sentAt: new Date('2024-01-10T15:30:00'),
    channel: 'in-app',
    status: 'read'
  },
  {
    id: '2',
    guestId: '1',
    title: 'Oda Servisi Menüsü',
    message: 'Yeni oda servisi menümüzü inceleyerek lezzetli yemeklerimizi keşfedin.',
    sentAt: new Date('2024-01-11T09:15:00'),
    channel: 'sms',
    status: 'read'
  },
  {
    id: '3',
    guestId: '1',
    title: 'Spa Randevusu Hatırlatması',
    message: 'Yarın saat 14:00\'da spa randevunuz bulunmaktadır.',
    sentAt: new Date('2024-01-12T18:00:00'),
    channel: 'whatsapp',
    status: 'unread'
  }
])

const filteredGuests = computed(() => {
  if (!searchQuery.value) return guests.value
  
  const query = searchQuery.value.toLowerCase()
  return guests.value.filter(guest => 
    guest.name.toLowerCase().includes(query) ||
    guest.roomNumber.includes(query)
  )
})

const selectedGuestInfo = computed(() => {
  if (!selectedGuest.value) return null
  return guests.value.find(g => g.id === selectedGuest.value)
})

const guestNotifications = computed(() => {
  if (!selectedGuest.value) return []
  
  let notifications = notificationHistory.value.filter(n => n.guestId === selectedGuest.value)
  
  // Apply date filter
  if (dateFilter.value !== 'all') {
    const now = new Date()
    const filterDate = new Date()
    
    switch (dateFilter.value) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        filterDate.setDate(now.getDate() - 7)
        break
      case 'month':
        filterDate.setDate(now.getDate() - 30)
        break
    }
    
    notifications = notifications.filter(n => new Date(n.sentAt) >= filterDate)
  }
  
  return notifications.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())
})

const notificationStats = computed(() => {
  const notifications = notificationHistory.value.filter(n => n.guestId === selectedGuest.value)
  return {
    total: notifications.length,
    read: notifications.filter(n => n.status === 'read').length,
    unread: notifications.filter(n => n.status === 'unread').length
  }
})

const searchGuests = () => {
  // Search functionality is handled by computed property
}

const loadGuestHistory = () => {
  // History loading is handled by computed property
}

const getNotificationIcon = (notification: any) => {
  return notification.status === 'read' ? CheckCircleIcon : BellIcon
}

const getNotificationDotClass = (notification: any) => {
  return notification.status === 'read' 
    ? 'bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-400'
    : 'bg-orange-100 text-orange-600 dark:bg-orange-800 dark:text-orange-400'
}

const getNotificationBadgeClass = (notification: any) => {
  return notification.status === 'read'
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    : 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
}

const getChannelIcon = (channel: string) => {
  const icons = {
    'in-app': BellIcon,
    'sms': DevicePhoneMobileIcon,
    'email': EnvelopeIcon,
    'whatsapp': ChatBubbleLeftRightIcon
  }
  return icons[channel] || BellIcon
}

const getChannelName = (channel: string) => {
  const names = {
    'in-app': 'Uygulama',
    'sms': 'SMS',
    'email': 'E-posta',
    'whatsapp': 'WhatsApp'
  }
  return names[channel] || channel
}

const getStatusDotClass = (status: string) => {
  return status === 'read' ? 'bg-green-400' : 'bg-orange-400'
}

const getStatusText = (status: string) => {
  return status === 'read' ? 'Okundu' : 'Okunmadı'
}

const formatDate = (date: Date | string) => {
  const d = new Date(date)
  return d.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script> 