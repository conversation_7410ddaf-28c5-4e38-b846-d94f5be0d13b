import { ref, watch } from 'vue'

// Mock types and services for build compatibility
interface CrossMFEEventType {
  type: string
  payload: any
  source: string
  timestamp: number
}
type CrossMFEEventListener = (event: CrossMFEEventType) => void

const crossMFERealtimeService = {
  subscribe: (eventType: string, callback: CrossMFEEventListener) => () => {},
  unsubscribe: (eventType: string, callback: CrossMFEEventListener) => {},
  emit: (type: string, data: any) => {},
  initialize: (userId: string, hotelId?: string) => {},
  startRealtimeSubscriptions: () => {},
  stopRealtimeSubscriptions: () => {},
  getSubscriptionStatus: () => ({ connected: false, subscriptions: [] }),
  triggerEvent: (event: CrossMFEEventType) => {}
}

export interface CrossMFESyncOptions {
  autoStart?: boolean
  userId?: string
  hotelId?: string
  eventTypes?: string[]
  onEvent?: (event: CrossMFEEventType) => void
  onError?: (error: Error) => void
}

export function useCrossMFESync(options: CrossMFESyncOptions = {}) {
  const {
    autoStart = true,
    userId,
    hotelId,
    eventTypes = [],
    onEvent,
    onError
  } = options

  // State
  const isActive = ref(false)
  const lastEvent = ref<CrossMFEEventType | null>(null)
  const eventHistory = ref<CrossMFEEventType[]>([])
  const error = ref<Error | null>(null)

  // Event listeners map
  const listeners = new Map<string, CrossMFEEventListener>()

  // Initialize the service
  const initialize = (initUserId?: string, initHotelId?: string) => {
    try {
      const targetUserId = initUserId || userId
      const targetHotelId = initHotelId || hotelId
      
      if (!targetUserId) {
        throw new Error('User ID is required to initialize Cross-MFE sync')
      }

      crossMFERealtimeService.initialize(targetUserId, targetHotelId)
      console.log('Cross-MFE sync initialized', { userId: targetUserId, hotelId: targetHotelId })
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj
      if (onError) {
        onError(errorObj)
      }
      console.error('Failed to initialize Cross-MFE sync:', errorObj)
    }
  }

  // Start realtime subscriptions
  const start = () => {
    try {
      crossMFERealtimeService.startRealtimeSubscriptions()
      isActive.value = true
      error.value = null
      console.log('Cross-MFE sync started')
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj
      if (onError) {
        onError(errorObj)
      }
      console.error('Failed to start Cross-MFE sync:', errorObj)
    }
  }

  // Stop realtime subscriptions
  const stop = () => {
    try {
      // Unsubscribe all listeners
      listeners.forEach((listener, eventType) => {
        crossMFERealtimeService.unsubscribe(eventType, listener)
      })
      listeners.clear()

      crossMFERealtimeService.stopRealtimeSubscriptions()
      isActive.value = false
      console.log('Cross-MFE sync stopped')
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj
      if (onError) {
        onError(errorObj)
      }
      console.error('Failed to stop Cross-MFE sync:', errorObj)
    }
  }

  // Subscribe to specific event types
  const subscribeToEvents = (types: string[]) => {
    types.forEach(eventType => {
      if (listeners.has(eventType)) {
        console.warn(`Already subscribed to ${eventType}`)
        return
      }

      const listener: CrossMFEEventListener = (event) => {
        lastEvent.value = event
        eventHistory.value.unshift(event)
        
        // Keep only last 50 events
        if (eventHistory.value.length > 50) {
          eventHistory.value = eventHistory.value.slice(0, 50)
        }

        // Call custom event handler
        if (onEvent) {
          onEvent(event)
        }

        console.log(`Cross-MFE event received: ${event.type}`, event)
      }

      crossMFERealtimeService.subscribe(eventType, listener)
      listeners.set(eventType, listener)
    })
  }

  // Unsubscribe from specific event types
  const unsubscribeFromEvents = (types: string[]) => {
    types.forEach(eventType => {
      const listener = listeners.get(eventType)
      if (listener) {
        crossMFERealtimeService.unsubscribe(eventType, listener)
        listeners.delete(eventType)
      }
    })
  }

  // Get subscription status
  const getStatus = () => {
    return {
      ...crossMFERealtimeService.getSubscriptionStatus(),
      isActive: isActive.value,
      subscribedEvents: Array.from(listeners.keys()),
      lastEvent: lastEvent.value,
      eventCount: eventHistory.value.length,
      error: error.value
    }
  }

  // Clear event history
  const clearHistory = () => {
    eventHistory.value = []
    lastEvent.value = null
  }

  // Manually trigger an event (for testing)
  const triggerEvent = (event: CrossMFEEventType) => {
    crossMFERealtimeService.triggerEvent(event)
  }

  // Watch for user/hotel changes
  watch([() => userId, () => hotelId], ([newUserId, newHotelId]) => {
    if (newUserId && isActive.value) {
      // Reinitialize with new context
      stop()
      initialize(newUserId, newHotelId)
      start()
      if (eventTypes.length > 0) {
        subscribeToEvents(eventTypes)
      }
    }
  })

  // Lifecycle yönetimi build için devre dışı - component'lar initialize/start çağırmalı

  return {
    // State
    isActive,
    lastEvent,
    eventHistory,
    error,

    // Actions
    initialize,
    start,
    stop,
    subscribeToEvents,
    unsubscribeFromEvents,
    getStatus,
    clearHistory,
    triggerEvent
  }
}

// Specialized hooks for specific use cases

/**
 * Hook for hotel dashboard to sync task and room status changes
 */
export function useHotelDashboardSync(userId: string, hotelId: string) {
  return useCrossMFESync({
    userId,
    hotelId,
    eventTypes: [
      'task_status_change',
      'room_status_change',
      'service_request_change'
    ],
    onEvent: (event) => {
      console.log('Hotel Dashboard sync event:', event.type, event.payload)
    }
  })
}

/**
 * Hook for management portal to sync hotel and user changes
 */
export function useManagementPortalSync(userId: string) {
  return useCrossMFESync({
    userId,
    eventTypes: [
      'hotel_data_change',
      'user_profile_change',
      'task_status_change'
    ],
    onEvent: (event) => {
      console.log('Management Portal sync event:', event.type, event.payload)
    }
  })
}

/**
 * Hook for customer portal to sync service requests and notifications
 */
export function useCustomerPortalSync(userId: string, hotelId?: string) {
  return useCrossMFESync({
    userId,
    hotelId,
    eventTypes: [
      'service_request_change',
      'notification_change',
      'room_status_change'
    ],
    onEvent: (event) => {
      console.log('Customer Portal sync event:', event.type, event.payload)
    }
  })
}

/**
 * Hook for app shell to handle global synchronization
 */
export function useAppShellSync(userId: string, hotelId?: string) {
  return useCrossMFESync({
    userId,
    hotelId,
    eventTypes: [
      'user_profile_change',
      'notification_change'
    ],
    onEvent: (event) => {
      console.log('App Shell sync event:', event.type, event.payload)
      
      // Handle global events that affect all MFEs
      if (event.type === 'user_profile_change' && event.payload.userId === userId) {
        // User's own profile changed - might need to update auth state
        console.log('Current user profile changed, consider refreshing auth state')
      }
    }
  })
}
