<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-gradient-to-r from-primary to-teal-600 text-white px-4 py-6">
      <div class="max-w-md mx-auto">
        <div class="flex items-center justify-between mb-4">
          <button @click="goBack" class="p-2 hover:bg-white/20 rounded-lg transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-xl font-bold">Oda Servisi</h1>
          <button @click="showCart = true" class="relative p-2 hover:bg-white/20 rounded-lg transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 1.5M7 13l-1.5-1.5M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z" />
            </svg>
            <span v-if="roomServiceStore.cartItemsCount > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
              {{ roomServiceStore.cartItemsCount }}
            </span>
          </button>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-green-400 rounded-full"></div>
          <p class="text-teal-100 text-sm">{{ guestAuthStore.currentGuest?.room_number || 'N/A' }} numaralı odaya teslimat</p>
        </div>
      </div>
    </div>

    <!-- Category Tabs -->
    <div class="max-w-md mx-auto px-4 -mt-3">
      <div class="bg-white rounded-xl shadow-lg p-3">
        <div class="flex overflow-x-auto space-x-2">
          <button 
            v-for="category in categories" 
            :key="category.key"
            @click="activeCategory = category.key"
            :class="[
              'flex-shrink-0 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200',
              activeCategory === category.key 
                ? 'bg-gradient-to-r from-primary to-teal-600 text-white shadow-lg' 
                : 'text-textMedium hover:bg-gray-100 hover:text-textDark'
            ]"
          >
            <span class="mr-2">{{ category.emoji }}</span>
            {{ category.name }}
          </button>
        </div>
      </div>
    </div>

    <!-- Menu Items -->
    <div class="max-w-md mx-auto px-4 py-6 pb-24">
      <!-- Loading State -->
      <div v-if="roomServiceStore.isLoading" class="space-y-4">
        <div v-for="i in 3" :key="i" class="bg-white rounded-2xl p-4 shadow-sm animate-pulse">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="h-5 bg-gray-300 rounded-lg mb-3"></div>
              <div class="h-3 bg-gray-200 rounded-lg mb-2 w-3/4"></div>
              <div class="h-4 bg-gray-200 rounded-lg w-1/2"></div>
            </div>
            <div class="w-20 h-20 bg-gray-300 rounded-xl ml-4"></div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="roomServiceStore.error" class="bg-red-50 border border-red-200 rounded-2xl p-6 text-center">
        <div class="text-4xl mb-4">😕</div>
        <p class="text-red-600 mb-4 font-medium">{{ roomServiceStore.error }}</p>
        <button 
          @click="loadMenu"
          class="bg-gradient-to-r from-primary to-teal-600 hover:from-primary/90 hover:to-teal-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200"
        >
          Tekrar Dene
        </button>
      </div>

      <!-- Menu Items -->
      <div v-else class="space-y-4">
        <div 
          v-for="item in currentCategoryItems" 
          :key="item.id" 
          class="bg-white rounded-2xl p-5 shadow-sm hover:shadow-lg transition-all duration-200"
        >
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <h3 class="font-bold text-textDark text-lg">{{ item.name }}</h3>
                <span v-if="item.is_new" class="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  YENİ
                </span>
              </div>
              <p class="text-textMedium text-sm mb-3 leading-relaxed">{{ item.description }}</p>
              <div class="flex items-center justify-between">
                <div>
                  <span class="text-primary font-bold text-xl">{{ roomServiceStore.formatPrice(item.price) }}</span>
                  <span v-if="item.portion_size" class="text-textLight text-sm ml-2">{{ item.portion_size }}</span>
                </div>
                <span v-if="!item.is_available" class="text-red-500 text-xs bg-red-50 px-3 py-1 rounded-full font-medium">
                  Tükendi
                </span>
              </div>
            </div>
            <div class="w-20 h-20 bg-gray-100 rounded-2xl ml-4 overflow-hidden shadow-sm">
              <img v-if="item.image_url" :src="item.image_url" :alt="item.name" class="w-full h-full object-cover">
              <div v-else class="w-full h-full flex items-center justify-center text-3xl">
                {{ getCategoryEmoji(activeCategory) }}
              </div>
            </div>
          </div>

          <!-- Add to Cart Controls -->
          <div v-if="item.is_available" class="mt-4">
            <div v-if="roomServiceStore.getCartItemQuantity(item.id) === 0">
              <button 
                @click="roomServiceStore.addToCart(item)"
                class="w-full bg-gradient-to-r from-primary to-teal-600 hover:from-primary/90 hover:to-teal-700 text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 shadow-lg"
              >
                Sepete Ekle
              </button>
            </div>
            <div v-else class="flex items-center justify-center space-x-4">
              <button 
                @click="roomServiceStore.updateCartItemQuantity(item.id, roomServiceStore.getCartItemQuantity(item.id) - 1)"
                class="w-10 h-10 bg-gray-200 hover:bg-gray-300 rounded-xl flex items-center justify-center transition-colors"
              >
                <svg class="w-5 h-5 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                </svg>
              </button>
              <div class="bg-backgroundLight px-4 py-2 rounded-xl">
                <span class="font-bold text-textDark text-lg min-w-[3rem] text-center block">
                  {{ roomServiceStore.getCartItemQuantity(item.id) }}
                </span>
              </div>
              <button 
                @click="roomServiceStore.updateCartItemQuantity(item.id, roomServiceStore.getCartItemQuantity(item.id) + 1)"
                class="w-10 h-10 bg-gradient-to-r from-primary to-teal-600 text-white rounded-xl flex items-center justify-center transition-all duration-200 shadow-lg"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="currentCategoryItems.length === 0" class="text-center py-16">
          <div class="text-6xl mb-6">🍽️</div>
          <h3 class="text-xl font-bold text-textDark mb-3">Bu kategoride ürün bulunamadı</h3>
          <p class="text-textMedium">Diğer kategorileri kontrol edebilirsiniz.</p>
        </div>
      </div>
    </div>

    <!-- Fixed Bottom Cart Button -->
    <div v-if="roomServiceStore.cartItemsCount > 0" class="fixed bottom-6 left-4 right-4 z-20">
      <div class="max-w-md mx-auto">
        <button 
          @click="showCart = true"
          class="w-full bg-gradient-to-r from-primary to-teal-600 hover:from-primary/90 hover:to-teal-700 text-white py-4 px-6 rounded-2xl shadow-xl font-bold flex items-center justify-between transition-all duration-200"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
              <span class="text-sm font-bold">{{ roomServiceStore.cartItemsCount }}</span>
            </div>
            <span>Sepeti Görüntüle</span>
          </div>
          <span>{{ roomServiceStore.formatPrice(roomServiceStore.cartTotal) }}</span>
        </button>
      </div>
    </div>

    <!-- Cart Modal -->
    <div v-if="showCart" class="fixed inset-0 bg-black/50 z-50 flex items-end">
      <div class="bg-white w-full max-h-[90vh] rounded-t-3xl overflow-hidden">
        <!-- Cart Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-2xl font-bold text-textDark">Sepetim</h2>
          <button @click="showCart = false" class="p-2 hover:bg-gray-100 rounded-xl transition-colors">
            <svg class="w-6 h-6 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Cart Items -->
        <div class="p-6 overflow-y-auto max-h-96">
          <div v-if="roomServiceStore.isCartEmpty" class="text-center py-12">
            <div class="text-6xl mb-4">🛒</div>
            <p class="text-textMedium text-lg">Sepetiniz boş</p>
          </div>
          
          <div v-else class="space-y-4">
            <div 
              v-for="item in roomServiceStore.cart" 
              :key="item.id"
              class="flex items-center justify-between p-4 bg-backgroundLight rounded-2xl"
            >
              <div class="flex-1">
                <h4 class="font-bold text-textDark">{{ item.name }}</h4>
                <p class="text-textMedium text-sm">{{ roomServiceStore.formatPrice(item.price) }} x {{ item.quantity }}</p>
              </div>
              <div class="flex items-center space-x-3">
                <button 
                  @click="roomServiceStore.updateCartItemQuantity(item.id, item.quantity - 1)"
                  class="w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center transition-colors"
                >
                  <svg class="w-4 h-4 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                  </svg>
                </button>
                <span class="font-bold text-textDark min-w-[2rem] text-center">{{ item.quantity }}</span>
                <button 
                  @click="roomServiceStore.updateCartItemQuantity(item.id, item.quantity + 1)"
                  class="w-8 h-8 bg-gradient-to-r from-primary to-teal-600 text-white rounded-full flex items-center justify-center transition-all duration-200"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Notes -->
        <div v-if="!roomServiceStore.isCartEmpty" class="px-6 py-4 border-t border-gray-200">
          <textarea 
            v-model="orderNotes"
            placeholder="Özel talepleriniz (örn: az tuzlu, çok baharatlı...)"
            class="w-full p-4 border border-gray-300 rounded-2xl resize-none text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none transition-all"
            rows="3"
          ></textarea>
        </div>

        <!-- Cart Total & Checkout -->
        <div v-if="!roomServiceStore.isCartEmpty" class="p-6 border-t border-gray-200 bg-backgroundLight">
          <div class="flex justify-between items-center mb-6">
            <span class="text-xl font-bold text-textDark">Toplam:</span>
            <span class="text-2xl font-bold text-primary">{{ roomServiceStore.formatPrice(roomServiceStore.cartTotal) }}</span>
          </div>
          <button 
            @click="proceedToCheckout"
            :disabled="isPlacingOrder"
            class="w-full bg-gradient-to-r from-primary to-teal-600 hover:from-primary/90 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed text-white py-4 px-6 rounded-2xl font-bold transition-all duration-200 shadow-lg"
          >
            <span v-if="isPlacingOrder" class="flex items-center justify-center">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
              Sipariş Veriliyor...
            </span>
            <span v-else>Sipariş Ver</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Success Modal -->
    <div v-if="showOrderSuccess" class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-3xl p-8 max-w-sm w-full text-center">
        <div class="text-6xl mb-6">🎉</div>
        <h3 class="text-2xl font-bold text-textDark mb-3">Siparişiniz Alındı!</h3>
        <p class="text-textMedium mb-6 leading-relaxed">Siparişiniz hazırlanıyor. Odanıza en kısa sürede teslim edilecektir.</p>
        <div class="space-y-3">
          <button 
            @click="closeSuccessModal"
            class="w-full bg-gradient-to-r from-primary to-teal-600 hover:from-primary/90 hover:to-teal-700 text-white py-3 px-6 rounded-2xl font-bold transition-all duration-200"
          >
            Tamam
          </button>
          <button 
            @click="goToDashboard"
            class="w-full text-primary hover:text-teal-600 py-3 px-6 font-bold transition-colors"
          >
            Ana Sayfaya Dön
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGuestAuthStore } from '../stores/guestAuthStore'
import { useRoomServiceStore } from '../stores/roomServiceStore'
import { storeToRefs } from 'pinia'

// Router and stores
const router = useRouter()
const guestAuthStore = useGuestAuthStore()
const roomServiceStore = useRoomServiceStore()

// Use storeToRefs for reactive access
const { currentGuest } = storeToRefs(guestAuthStore)

// Local reactive data
const activeCategory = ref<string>('Food')
const showCart = ref(false)
const orderNotes = ref('')
const isPlacingOrder = ref(false)
const showOrderSuccess = ref(false)

// Enhanced categories with emojis
const categories = [
  { key: 'Food', name: 'Yemekler', emoji: '🍽️' },
  { key: 'Beverages', name: 'İçecekler', emoji: '🥤' },
  { key: 'Desserts', name: 'Tatlılar', emoji: '🍰' },
  { key: 'Extras', name: 'Ekstralar', emoji: '🎁' }
]

// Computed properties
const currentCategoryItems = computed(() => {
  // Get items from store
  const categoryItems = roomServiceStore.menuByCategory[activeCategory.value as keyof typeof roomServiceStore.menuByCategory]
  return categoryItems || []
})

// Methods
const goBack = () => {
  router.back()
}

const goToDashboard = () => {
  showOrderSuccess.value = false
  router.push('/dashboard')
}

const getCategoryEmoji = (category: string) => {
  const emojiMap: Record<string, string> = {
    'Food': '🍽️',
    'Beverages': '🥤',
    'Desserts': '🍰',
    'Extras': '🎁'
  }
  return emojiMap[category] || '🍽️'
}




const loadMenu = async () => {
  if (!currentGuest.value) {
    console.error('No guest session found')
    return
  }

  try {
    await roomServiceStore.fetchMenu(currentGuest.value.hotel_id)
  } catch (error) {
    console.error('Failed to load menu:', error)
  }
}

const proceedToCheckout = async () => {
  if (roomServiceStore.isCartEmpty || isPlacingOrder.value) return

  isPlacingOrder.value = true
  
  try {
    await roomServiceStore.placeOrder(orderNotes.value || undefined)
    
    // Show success modal
    showCart.value = false
    showOrderSuccess.value = true
    orderNotes.value = ''
    
  } catch (error: any) {
    console.error('Order placement failed:', error)
    alert(`Sipariş verilemedi: ${error.message}`)
  } finally {
    isPlacingOrder.value = false
  }
}

const closeSuccessModal = () => {
  showOrderSuccess.value = false
}

// Component lifecycle
onMounted(async () => {
  console.log('Room Service view mounted')
  
  // Check authentication
  if (!guestAuthStore.isAuthenticated) {
    console.log('Guest not authenticated, redirecting to login...')
    router.push('/')
    return
  }

  // Load cart from storage
  roomServiceStore.loadCartFromStorage()
  
  // Load menu for current hotel
  await loadMenu()
})
</script>

<style scoped>
/* Smooth scrolling for category tabs */
.overflow-x-auto {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.overflow-x-auto::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly interactions */
button {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Card hover effects */
.bg-white:hover {
  transform: translateY(-2px);
  transition: all 0.2s ease-out;
}

/* Modal slide up animation */
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.fixed .bg-white {
  animation: slideUp 0.3s ease-out;
}

/* Gradient button hover effects */
.bg-gradient-to-r:hover {
  transform: translateY(-1px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Focus states */
button:focus,
textarea:focus {
  outline: none;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, transform, box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style> 