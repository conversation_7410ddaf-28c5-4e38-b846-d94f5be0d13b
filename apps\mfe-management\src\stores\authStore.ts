import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { User } from '@supabase/supabase-js'
import { supabase, type Database } from '@hotelexia/shared-supabase-client'

type UserRole = Database['public']['Enums']['user_role']

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: UserRole | null
  hotel_id: string | null
  created_at: string | null
  updated_at: string | null
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const profile = ref<UserProfile | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Session management
  const INACTIVITY_TIMEOUT = 30 * 60 * 1000 // 30 minutes
  let inactivityTimer: NodeJS.Timeout | null = null
  let lastActivity = Date.now()

  // Computed
  const isAuthenticated = computed(() => !!user.value && !!profile.value)
  const userRole = computed(() => profile.value?.role || null)
  const userDisplayName = computed(() => profile.value?.full_name || profile.value?.email || 'User')
  const isSuperAdmin = computed(() => userRole.value === ('SUPER_ADMIN' as UserRole))

  // Actions
  const login = async (email: string, password: string): Promise<boolean> => {
    isLoading.value = true
    error.value = null

    try {
      // Sign in with Supabase
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        throw new Error(authError.message)
      }

      if (!authData.user) {
        throw new Error('Kullanıcı bilgileri alınamadı.')
      }

      // Set user
      user.value = authData.user

      // Fetch user profile
      const userProfile = await fetchUserProfile(authData.user.id)
      if (!userProfile) {
        throw new Error('Kullanıcı profili alınamadı.')
      }

      profile.value = userProfile

      // Check if user has SUPER_ADMIN role
      if (!isSuperAdmin.value) {
        await signOut()
        throw new Error('Bu panele erişim yetkiniz bulunmuyor. Sadece Süper Admin kullanıcıları giriş yapabilir.')
      }

      // Store session data
      persistSession()

      // Start inactivity timer
      startInactivityTimer()

      return true
    } catch (err: any) {
      console.error('Login error:', err)
      error.value = err.message || 'Giriş sırasında bir hata oluştu.'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const signOut = async (): Promise<void> => {
    try {
      await supabase.auth.signOut()
      clearSession()
    } catch (err) {
      console.error('Sign out error:', err)
    }
  }

  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return null
      }

      return data as UserProfile
    } catch (err) {
      console.error('Unexpected error fetching user profile:', err)
      return null
    }
  }

  const persistSession = () => {
    if (user.value && profile.value) {
      const sessionData = {
        user: user.value,
        profile: profile.value,
        timestamp: Date.now()
      }
      sessionStorage.setItem('hotelexia_management_user', JSON.stringify(sessionData))
    }
  }

  const clearSession = () => {
    user.value = null
    profile.value = null
    error.value = null
    sessionStorage.removeItem('hotelexia_management_user')
    sessionStorage.removeItem('hotelexia_management_redirect')
    clearInactivityTimer()
  }

  const restoreSession = async (): Promise<boolean> => {
    try {
      // First check if there's an active Supabase session
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        // No active Supabase session, clear any stored session
        clearSession()
        return false
      }

      const sessionData = sessionStorage.getItem('hotelexia_management_user')
      if (!sessionData) {
        // Supabase session exists but no local session data
        // Try to fetch user profile and create session
        const userProfile = await fetchUserProfile(session.user.id)
        if (userProfile && userProfile.role === 'SUPER_ADMIN') {
          user.value = session.user
          profile.value = userProfile
          persistSession()
          startInactivityTimer()
          console.log('Session restored from Supabase:', userProfile)
          return true
        } else {
          // User doesn't have SUPER_ADMIN role
          await supabase.auth.signOut()
          return false
        }
      }

      const parsed = JSON.parse(sessionData)

      // Check if session is not too old (24 hours)
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      if (Date.now() - parsed.timestamp > maxAge) {
        clearSession()
        return false
      }

      // Verify the session user matches Supabase session
      if (parsed.user.id !== session.user.id) {
        clearSession()
        return false
      }

      user.value = parsed.user
      profile.value = parsed.profile

      // Start inactivity timer after restoring session
      startInactivityTimer()
      console.log('Session restored from storage:', parsed.profile)

      return true
    } catch (err) {
      console.error('Error restoring session:', err)
      clearSession()
      return false
    }
  }

  // Inactivity timer functions
  const startInactivityTimer = () => {
    clearInactivityTimer()
    lastActivity = Date.now()
    
    inactivityTimer = setTimeout(() => {
      console.log('User inactive for too long, signing out...')
      signOut()
    }, INACTIVITY_TIMEOUT)
  }

  const clearInactivityTimer = () => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
      inactivityTimer = null
    }
  }

  const resetInactivityTimer = () => {
    if (isAuthenticated.value) {
      startInactivityTimer()
    }
  }

  const trackUserActivity = () => {
    lastActivity = Date.now()
    resetInactivityTimer()
  }

  // Initialize auth listener
  const setupAuthListener = () => {
    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        clearSession()
      } else if (event === 'SIGNED_IN' && session.user) {
        user.value = session.user
        
        // Fetch profile if not already loaded
        if (!profile.value) {
          const userProfile = await fetchUserProfile(session.user.id)
          if (userProfile) {
            profile.value = userProfile
            persistSession()
            startInactivityTimer()
          }
        }
      } else if (event === 'TOKEN_REFRESHED') {
        // Reset inactivity timer on token refresh
        resetInactivityTimer()
      }
    })
  }

  // Setup activity listeners
  const setupActivityListeners = () => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    
    const handleActivity = () => {
      trackUserActivity()
    }

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true)
    })

    // Cleanup function
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true)
      })
    }
  }

  // Initialize
  setupAuthListener()
  setupActivityListeners()

  // Try to restore session on initialization (async)
  restoreSession().catch(err => {
    console.error('Failed to restore session on initialization:', err)
  })

  return {
    // State
    user: readonly(user),
    profile: readonly(profile),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    isAuthenticated,
    userRole,
    userDisplayName,
    isSuperAdmin,

    // Actions
    login,
    signOut,
    fetchUserProfile,
    persistSession,
    clearSession,
    restoreSession,
    trackUserActivity,
    resetInactivityTimer
  }
})
