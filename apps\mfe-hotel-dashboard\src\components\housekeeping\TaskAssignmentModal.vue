<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click="closeModal"
  >
    <div
      @click.stop
      class="bg-white dark:bg-navy-900 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto"
    >
      <!-- Header -->
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-bold text-gray-800 dark:text-white">
            {{ mode === 'create' ? 'Yeni Görev Oluştur' : 'Görev Düzenle' }}
          </h2>
          <button
            @click="closeModal"
            class="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          >
            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Başlık -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Görev Başlığı <span class="text-red-500">*</span>
          </label>
          <input
            v-model="form.title"
            type="text"
            required
            placeholder="Örn: Oda 101 Genel Temizlik"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>

        <!-- Açıklama -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Açıklama <span class="text-red-500">*</span>
          </label>
          <textarea
            v-model="form.description"
            required
            rows="3"
            placeholder="Görev detaylarını açıklayın..."
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
          ></textarea>
        </div>

        <!-- Oda Numarası ve Öncelik -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Oda Numarası <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.roomNumber"
              required
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
            >
              <option value="">Oda Seçin</option>
              <option v-for="room in availableRooms" :key="room.number" :value="room.number">
                {{ room.number }} - {{ room.type }}
              </option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Öncelik <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.priority"
              required
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
            >
              <option value="">Öncelik Seçin</option>
              <option value="LOW">Düşük</option>
              <option value="MEDIUM">Orta</option>
              <option value="HIGH">Yüksek</option>
              <option value="URGENT">Acil</option>
            </select>
          </div>
        </div>

        <!-- Tahmini Süre ve Son Tarih -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tahmini Süre (dakika) <span class="text-red-500">*</span>
            </label>
            <input
              v-model.number="form.estimatedTime"
              type="number"
              required
              min="5"
              max="480"
              placeholder="45"
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Son Tarih <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.deadline"
              type="datetime-local"
              required
              :min="minDeadline"
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
            />
          </div>
        </div>

        <!-- Personel Atama -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Personel Atama
          </label>
          <select
            v-model="form.assignedTo"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          >
            <option value="">Atanmamış (Daha sonra atanacak)</option>
            <option v-for="staff in availableStaff" :key="staff.id" :value="staff.id">
              {{ staff.name }} {{ staff.surname }} - {{ staff.role }}
              <span v-if="staff.status === 'BUSY'" class="text-yellow-500">(Meşgul)</span>
              <span v-if="staff.status === 'AVAILABLE'" class="text-green-500">(Müsait)</span>
            </option>
          </select>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Görev şimdi atanmazsa, daha sonra mevcut personelden birine atanabilir.
          </p>
        </div>

        <!-- Notlar -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ek Notlar
          </label>
          <textarea
            v-model="form.notes"
            rows="2"
            placeholder="Ek bilgiler, özel talimatlar..."
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
          ></textarea>
        </div>

        <!-- Durum (Sadece edit modunda) -->
        <div v-if="mode === 'edit'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Görev Durumu
          </label>
          <select
            v-model="form.status"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          >
            <option value="NEW">Atanmamış</option>
            <option value="IN_PROGRESS">Devam Ediyor</option>
            <option value="QUALITY_CHECK">Kalite Kontrolü</option>
            <option value="COMPLETED">Tamamlandı</option>
            <option value="CANCELLED">İptal Edildi</option>
          </select>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="closeModal"
            class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors font-medium"
          >
            İptal
          </button>
          <button
            type="submit"
            :disabled="!isFormValid"
            class="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg transition-colors font-medium"
          >
            {{ mode === 'create' ? 'Görev Oluştur' : 'Güncelle' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { HousekeepingTask, TaskPriority, TaskStatus } from '@/types/housekeeping'

interface Props {
  isOpen: boolean
  mode: 'create' | 'edit'
  task?: HousekeepingTask
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', task: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const housekeepingStore = useHousekeepingStore()

// Form data
const form = ref({
  title: '',
  description: '',
  roomNumber: '',
  priority: '' as TaskPriority | '',
  estimatedTime: 45,
  deadline: '',
  assignedTo: '',
  notes: '',
      status: 'NEW' as TaskStatus
})

// Computed
const availableRooms = computed(() => {
  const allRooms: Array<{number: string, type: string}> = []
  housekeepingStore.floors.forEach(floor => {
    floor.rooms.forEach(room => {
      allRooms.push({ number: room.number, type: room.type })
    })
  })
  return allRooms.sort((a, b) => a.number.localeCompare(b.number))
})

const availableStaff = computed(() => {
  return housekeepingStore.staff.filter(s => 
    s.status === 'AVAILABLE' || s.status === 'BUSY'
  )
})

const minDeadline = computed(() => {
  const now = new Date()
  now.setMinutes(now.getMinutes() + 30) // En az 30 dakika sonra
  return now.toISOString().slice(0, 16)
})

const isFormValid = computed(() => {
  return form.value.title.trim() &&
         form.value.description.trim() &&
         form.value.roomNumber &&
         form.value.priority &&
         form.value.estimatedTime > 0 &&
         form.value.deadline
})

// Methods
const closeModal = () => {
  emit('close')
}

const handleSubmit = () => {
  if (!isFormValid.value) return

  const taskData = {
    ...form.value,
    title: form.value.title.trim(),
    description: form.value.description.trim(),
    notes: form.value.notes?.trim() || undefined,
    assignedTo: form.value.assignedTo || undefined
  }

  emit('submit', taskData)
  resetForm()
  closeModal()
}

const resetForm = () => {
  form.value = {
    title: '',
    description: '',
    roomNumber: '',
    priority: '',
    estimatedTime: 45,
    deadline: '',
    assignedTo: '',
    notes: '',
    status: 'NEW'
  }
}

// Watch for prop changes
watch(() => props.task, (task) => {
  if (task && props.mode === 'edit') {
    form.value = {
      title: task.title,
      description: task.description,
      roomNumber: task.roomNumber,
      priority: task.priority,
      estimatedTime: task.estimatedTime,
      deadline: task.deadline,
      assignedTo: task.assignedTo || '',
      notes: task.notes || '',
      status: task.status
    }
  }
}, { immediate: true })

watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.mode === 'create') {
    resetForm()
    // Set default deadline to 4 hours from now
    const defaultDeadline = new Date()
    defaultDeadline.setHours(defaultDeadline.getHours() + 4)
    form.value.deadline = defaultDeadline.toISOString().slice(0, 16)
  }
})
</script> 