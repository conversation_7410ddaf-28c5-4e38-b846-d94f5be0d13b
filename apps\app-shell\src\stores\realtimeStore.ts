import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'
import {
  crossMFERealtimeService,
  type CrossMFEEventType,
  type CrossMFEEvent
} from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'

export const useRealtimeStore = defineStore('realtime', () => {
  // Get auth store for user context
  const authStore = useAuthStore()

  // State
  const isConnected = ref(false)
  const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected')
  const lastEvent = ref<CrossMFEEvent | null>(null)
  const eventHistory = ref<CrossMFEEvent[]>([])
  const subscriptionStatus = ref<{
    isInitialized: boolean
    activeChannels: string[]
    eventListeners: string[]
    currentUserId: string | null
    currentHotelId: string | null
  }>({
    isInitialized: false,
    activeChannels: [],
    eventListeners: [],
    currentUserId: null,
    currentHotelId: null
  })
  const unsubscribeFunctions = ref<Array<() => void>>([])

  // Actions
  const initialize = async () => {
    connectionStatus.value = 'connecting'

    try {
      // Get user context from auth store
      const hotelId = authStore.userHotelId
      const userId = authStore.user?.id

      if (!hotelId || !userId) {
        throw new Error('User context not available for realtime initialization')
      }

      // Initialize the cross-MFE realtime service
      await crossMFERealtimeService.initialize(hotelId, userId)

      // Subscribe to all critical event types for coordination
      const unsubscribers = [
        crossMFERealtimeService.subscribe('hotel_data_change', handleHotelUpdate),
        crossMFERealtimeService.subscribe('user_profile_change', handleUserUpdate),
        crossMFERealtimeService.subscribe('notification_change', handleNotificationUpdate),
        crossMFERealtimeService.subscribe('task_status_change', handleTaskUpdate),
        crossMFERealtimeService.subscribe('service_request_change', handleServiceRequestUpdate),
        crossMFERealtimeService.subscribe('room_status_change', handleRoomUpdate)
      ]

      unsubscribeFunctions.value = unsubscribers

      connectionStatus.value = 'connected'
      isConnected.value = true
      updateSubscriptionStatus()

      console.log('App Shell cross-MFE realtime coordination initialized')
    } catch (error) {
      console.error('Failed to initialize realtime coordination:', error)
      connectionStatus.value = 'error'
      isConnected.value = false
    }
  }

  const handleTaskUpdate = (event: CrossMFEEvent) => {
    console.log('Cross-MFE Task Update:', event)
    recordEvent(event)

    // Broadcast to other MFEs for coordination
    broadcastToMFEs('task-update', event.payload)

    // Trigger specific actions based on task updates
    if (event.type === 'task_status_change') {
      // Notify relevant stores or components about maintenance task changes
      broadcastToMFEs('maintenance-task-update', event.payload)
    }
  }

  const handleNotificationUpdate = (event: CrossMFEEvent) => {
    console.log('Cross-MFE Notification Update:', event)
    recordEvent(event)

    // Update notification badge or trigger refresh in App Shell
    broadcastToMFEs('notification-update', event.payload)

    // Trigger notification store refresh if needed
    if (event.type === 'notification_change') {
      // New notification received - update notification count
      broadcastToMFEs('new-notification', event.payload)
    }
  }



  const handleServiceRequestUpdate = (event: CrossMFEEvent) => {
    console.log('Cross-MFE Service Request Update:', event)
    recordEvent(event)

    // Coordinate between Hotel Dashboard and Customer Portal
    broadcastToMFEs('service-request-update', event.payload)
  }

  const handleRoomUpdate = (event: CrossMFEEvent) => {
    console.log('Cross-MFE Room Update:', event)
    recordEvent(event)

    // Coordinate room status between Hotel Dashboard and Customer Portal
    broadcastToMFEs('room-update', event.payload)
  }



  const handleUserUpdate = (event: CrossMFEEvent) => {
    console.log('Cross-MFE User Update:', event)
    recordEvent(event)

    // Update user profile across all MFEs
    broadcastToMFEs('user-update', event.payload)

    // Handle role changes specifically
    if (event.type === 'user_profile_change') {
      // User role changed - may need to refresh permissions
      broadcastToMFEs('user-role-changed', event.payload)
    }
  }

  const handleHotelUpdate = (event: CrossMFEEvent) => {
    console.log('Cross-MFE Hotel Update:', event)
    recordEvent(event)

    // Update hotel information across Management Portal and App Shell
    broadcastToMFEs('hotel-update', event.payload)

    // Handle hotel status changes specifically
    if (event.type === 'hotel_data_change') {
      broadcastToMFEs('hotel-status-changed', event.payload)
    }
  }

  const recordEvent = (event: CrossMFEEvent) => {
    lastEvent.value = event
    eventHistory.value.unshift(event)
    
    // Keep only last 100 events
    if (eventHistory.value.length > 100) {
      eventHistory.value = eventHistory.value.slice(0, 100)
    }
  }

  const broadcastToMFEs = (eventType: string, payload: any) => {
    // Use postMessage to communicate with MFE iframes or windows
    if (typeof window !== 'undefined') {
      const message = {
        type: 'CROSS_MFE_EVENT',
        eventType,
        payload,
        timestamp: new Date().toISOString()
      }
      
      // Broadcast to all frames
      window.postMessage(message, '*')
      
      // If running in micro-frontend architecture, broadcast to parent/child frames
      if (window.parent !== window) {
        window.parent.postMessage(message, '*')
      }
      
      // Broadcast to all child frames
      Array.from(document.querySelectorAll('iframe')).forEach(iframe => {
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, '*')
        }
      })
    }
  }

  const updateSubscriptionStatus = () => {
    subscriptionStatus.value = crossMFERealtimeService.getSubscriptionStatus()
  }

  const reconnect = async () => {
    connectionStatus.value = 'connecting'

    try {
      // Cleanup existing connections first
      await disconnect()

      // Reinitialize
      await initialize()
    } catch (error) {
      console.error('Failed to reconnect realtime service:', error)
      connectionStatus.value = 'error'
      isConnected.value = false
    }
  }

  const disconnect = async () => {
    // Cleanup all subscriptions
    unsubscribeFunctions.value.forEach(unsubscribe => unsubscribe())
    unsubscribeFunctions.value = []

    // Cleanup the service
    crossMFERealtimeService.stopRealtimeSubscriptions()

    connectionStatus.value = 'disconnected'
    isConnected.value = false
    subscriptionStatus.value = {
      isInitialized: false,
      activeChannels: [],
      eventListeners: [],
      currentUserId: null,
      currentHotelId: null
    }
  }

  const triggerTestEvent = (eventType: string, data: any) => {
    const event: CrossMFEEventType = {
      type: eventType as any,
      payload: data,
      source: 'app-shell-test',
      timestamp: Date.now(),
      userId: authStore.user?.id,
      hotelId: authStore.userHotelId || undefined
    }
    crossMFERealtimeService.triggerEvent(event)
  }

  const broadcastEvent = (eventType: string, data: any) => {
    const event: CrossMFEEventType = {
      type: eventType as any,
      payload: data,
      source: 'app-shell',
      timestamp: Date.now(),
      userId: authStore.user?.id,
      hotelId: authStore.userHotelId || undefined
    }
    crossMFERealtimeService.triggerEvent(event)
  }

  const clearEventHistory = () => {
    eventHistory.value = []
    lastEvent.value = null
  }

  // Computed
  const recentEvents = readonly(eventHistory)
  const connectionStatusText = readonly(connectionStatus)

  return {
    // State
    isConnected: readonly(isConnected),
    connectionStatus: readonly(connectionStatus),
    lastEvent: readonly(lastEvent),
    subscriptionStatus: readonly(subscriptionStatus),
    recentEvents,
    connectionStatusText,

    // Actions
    initialize,
    reconnect,
    disconnect,
    triggerTestEvent,
    broadcastEvent,
    clearEventHistory,
    updateSubscriptionStatus
  }
})
