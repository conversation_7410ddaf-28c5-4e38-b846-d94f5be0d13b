<template>
  <div class="space-y-6">
    <!-- Enhanced Page Header with Live Status -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 shadow-lg text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold mb-2">Güvenlik & Denetim Merkezi</h1>
          <p class="text-blue-100">Sistem güvenliğini izleyin ve tüm admin aktivitelerini denetleyin</p>
        </div>
        <div class="text-right">
          <div class="flex items-center space-x-2 mb-1">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-sm">Canlı İzleme Aktif</span>
          </div>
          <div class="text-xs text-blue-100">Son güncelleme: {{ currentTime }}</div>
        </div>
      </div>
    </div>

    <!-- Statistics Dashboard -->
    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
      <div v-for="stat in statistics" :key="stat.title" 
           class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-sm border-l-4"
           :class="stat.borderColor">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">{{ stat.title }}</p>
            <p class="text-xl font-bold text-navy-700 dark:text-white">{{ stat.value }}</p>
            <div class="flex items-center mt-1">
              <component :is="stat.trend === 'up' ? ArrowUpIcon : ArrowDownIcon" 
                         :class="stat.trend === 'up' ? 'text-green-500' : 'text-red-500'"
                         class="h-3 w-3 mr-1" />
              <span :class="stat.trend === 'up' ? 'text-green-500' : 'text-red-500'" 
                    class="text-xs font-medium">{{ stat.change }}</span>
            </div>
          </div>
          <div class="w-10 h-10 rounded-lg flex items-center justify-center"
               :class="stat.iconBg">
            <component :is="stat.icon" class="h-5 w-5" :class="stat.iconColor" />
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Filter and Search Panel -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white">Gelişmiş Filtreleme</h2>
        <div class="flex space-x-2">
          <button @click="exportLogs" 
                  class="flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm">
            <ArrowDownTrayIcon class="h-4 w-4" />
            <span>Excel'e Aktar</span>
          </button>
          <button @click="clearFilters"
                  class="flex items-center space-x-2 px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm">
            <XMarkIcon class="h-4 w-4" />
            <span>Filtreleri Temizle</span>
          </button>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tarih Aralığı</label>
          <select v-model="filters.dateRange" class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brand-500">
            <option value="24h">Son 24 saat</option>
            <option value="7d">Son 7 gün</option>
            <option value="30d">Son 30 gün</option>
            <option value="90d">Son 3 ay</option>
            <option value="custom">Özel tarih</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Risk Seviyesi</label>
          <select v-model="filters.riskLevel" class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brand-500">
            <option value="">Tümü</option>
            <option value="critical">Kritik</option>
            <option value="high">Yüksek</option>
            <option value="medium">Orta</option>
            <option value="low">Düşük</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Aktivite Tipi</label>
          <select v-model="filters.activityType" class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brand-500">
            <option value="">Tümü</option>
            <option value="auth">Kimlik Doğrulama</option>
            <option value="hotel">Otel Yönetimi</option>
            <option value="mfe">MFE Yapılandırması</option>
            <option value="system">Sistem Ayarları</option>
            <option value="announcement">Duyuru Yönetimi</option>
            <option value="user">Kullanıcı İşlemleri</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Admin</label>
          <select v-model="filters.admin" class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brand-500">
            <option value="">Tüm adminler</option>
            <option value="admin1">Ahmet Yılmaz</option>
            <option value="admin2">Mehmet Kaya</option>
            <option value="admin3">Ayşe Demir</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IP Adresi</label>
          <input v-model="filters.ipAddress" 
                 type="text" 
                 placeholder="IP adresi filtrele..."
                 class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brand-500">
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Arama</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-4 w-4 text-gray-400" />
            </div>
            <input v-model="filters.search"
              type="search"
              placeholder="Aktivite, otel, admin ara..."
              class="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-brand-500"
            >
          </div>
        </div>
      </div>
      
      <!-- Quick Filter Tags -->
      <div class="mt-4 flex flex-wrap gap-2">
        <button v-for="tag in quickFilters" :key="tag.key"
                @click="applyQuickFilter(tag)"
                class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-brand-100 hover:text-brand-700 transition-colors">
          {{ tag.label }}
        </button>
      </div>
    </div>

    <!-- Audit Log Entries with Timeline -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white">Denetim Zaman Çizelgesi</h2>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">Kritik</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">Orta Risk</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">Normal</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <span class="text-xs text-gray-600 dark:text-gray-400">Başarılı</span>
          </div>
        </div>
      </div>
      
      <div class="space-y-4">
        <!-- Enhanced Audit Log Entry -->
        <div v-for="(log, index) in paginatedLogs" :key="log.id"
             class="border-l-4 p-4 rounded-r-lg cursor-pointer transition-all duration-200 hover:shadow-md"
             :class="[
               log.riskLevel === 'critical' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
               log.riskLevel === 'high' ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' :
               log.riskLevel === 'medium' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
               log.riskLevel === 'low' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' :
               'border-green-500 bg-green-50 dark:bg-green-900/20'
             ]"
             @click="expandLog(log.id)">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <span class="text-xs px-2 py-1 rounded-full font-medium"
                      :class="[
                        log.riskLevel === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200' :
                        log.riskLevel === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-200' :
                        log.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200' :
                        log.riskLevel === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200' :
                        'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200'
                      ]">
                  {{ getRiskLevelText(log.riskLevel) }}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ formatDateTime(log.timestamp) }}</span>
                <span v-if="log.sessionId" class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">
                  Oturum: {{ log.sessionId.slice(-6) }}
                </span>
              </div>
              <h3 class="font-semibold text-navy-700 dark:text-white mb-1">{{ log.title }}</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ log.description }}</p>
              
              <!-- Quick Info -->
              <div class="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400">
                <div class="flex items-center space-x-1">
                  <UserIcon class="h-3 w-3" />
                  <span>{{ log.admin }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <GlobeAltIcon class="h-3 w-3" />
                  <span>{{ log.ipAddress }}</span>
                </div>
                <div v-if="log.hotelId" class="flex items-center space-x-1">
                  <BuildingOfficeIcon class="h-3 w-3" />
                  <span>Otel #{{ log.hotelId }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <ClockIcon class="h-3 w-3" />
                  <span>{{ getTimeAgo(log.timestamp) }}</span>
                </div>
              </div>
              
              <!-- Expanded Details -->
              <div v-if="expandedLogs.includes(log.id)" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 class="text-sm font-semibold text-navy-700 dark:text-white mb-2">Teknik Detaylar</h4>
                    <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                      <div><strong>İşlem ID:</strong> {{ log.id }}</div>
                      <div><strong>Kullanıcı Ajanı:</strong> {{ log.userAgent }}</div>
                      <div><strong>Referrer:</strong> {{ log.referrer || 'Doğrudan' }}</div>
                      <div><strong>İşlem Süresi:</strong> {{ log.duration }}ms</div>
                    </div>
                  </div>
                  <div>
                    <h4 class="text-sm font-semibold text-navy-700 dark:text-white mb-2">Değişiklik Detayları</h4>
                    <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                      <div v-if="log.changes"><strong>Değişiklik:</strong> {{ log.changes }}</div>
                      <div v-if="log.targetResource"><strong>Hedef Kaynak:</strong> {{ log.targetResource }}</div>
                      <div v-if="log.affectedRecords"><strong>Etkilenen Kayıt:</strong> {{ log.affectedRecords }}</div>
                      <div><strong>Sonuç:</strong> {{ log.result }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="mt-4 flex space-x-2">
                  <button @click.stop="viewRelatedLogs(log)" 
                          class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-3 py-1 rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
                    İlgili Kayıtları Gör
                  </button>
                  <button @click.stop="exportSingleLog(log)" 
                          class="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-3 py-1 rounded hover:bg-green-200 dark:hover:bg-green-800 transition-colors">
                    Detayları İndir
                  </button>
                  <button v-if="log.riskLevel === 'critical'" 
                          @click.stop="flagForReview(log)" 
                          class="text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 px-3 py-1 rounded hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
                    İncelemeye Al
                  </button>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <component :is="getRiskIcon(log.riskLevel)" 
                         :class="getRiskIconColor(log.riskLevel)"
                         class="h-5 w-5" />
              <ChevronDownIcon v-if="!expandedLogs.includes(log.id)"
                               class="h-4 w-4 text-gray-400 transition-transform" />
              <ChevronUpIcon v-else
                             class="h-4 w-4 text-gray-400 transition-transform" />
            </div>
          </div>
        </div>


      </div>

      <!-- Enhanced Pagination -->
      <div class="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Gösterilen: {{ ((currentPage - 1) * pageSize) + 1 }}-{{ Math.min(currentPage * pageSize, totalLogs) }} / {{ totalLogs }} kayıt
        </div>
        <div class="flex items-center space-x-2">
          <select v-model="pageSize" class="px-2 py-1 text-xs bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded">
            <option :value="10">10</option>
            <option :value="25">25</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
          </select>
          <span class="text-xs text-gray-500 dark:text-gray-400">/ sayfa</span>
        </div>
        <div class="flex space-x-1">
          <button @click="currentPage = 1" 
                  :disabled="currentPage === 1"
                  class="px-2 py-1 text-xs border border-gray-200 dark:border-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-navy-700">
            İlk
          </button>
          <button @click="currentPage--" 
                  :disabled="currentPage === 1"
                  class="px-3 py-1 text-sm border border-gray-200 dark:border-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-navy-700">
            Önceki
          </button>
          <button v-for="page in visiblePages" :key="page"
                  @click="currentPage = page"
                  :class="page === currentPage ? 'bg-brand-500 text-white' : 'border border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-navy-700'"
                  class="px-3 py-1 text-sm rounded transition-colors">
            {{ page }}
          </button>
          <button @click="currentPage++" 
                  :disabled="currentPage === totalPages"
                  class="px-3 py-1 text-sm border border-gray-200 dark:border-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-navy-700">
            Sonraki
          </button>
          <button @click="currentPage = totalPages" 
                  :disabled="currentPage === totalPages"
                  class="px-2 py-1 text-xs border border-gray-200 dark:border-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-navy-700">
            Son
          </button>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import {
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  UserIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  GlobeAltIcon,
  BuildingOfficeIcon,
  ClockIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  FireIcon,
  ShieldCheckIcon,
  EyeIcon,
  ComputerDesktopIcon
} from '@heroicons/vue/24/outline'

// Types
interface AuditLog {
  id: string
  timestamp: Date
  admin: string
  adminEmail: string
  title: string
  description: string
  riskLevel: 'critical' | 'high' | 'medium' | 'low' | 'success'
  activityType: 'auth' | 'hotel' | 'mfe' | 'system' | 'announcement' | 'user'
  ipAddress: string
  userAgent: string
  sessionId?: string
  hotelId?: number
  changes?: string
  targetResource?: string
  affectedRecords?: string
  result: string
  duration: number
  referrer?: string
}

interface Filters {
  dateRange: string
  riskLevel: string
  activityType: string
  admin: string
  ipAddress: string
  search: string
}

interface QuickFilter {
  key: string
  label: string
  filter: Partial<Filters>
}

interface Statistic {
  title: string
  value: string | number
  change: string
  trend: 'up' | 'down'
  icon: any
  iconColor: string
  iconBg: string
  borderColor: string
}

// Reactive data
const currentTime = ref(new Date().toLocaleString('tr-TR'))
const expandedLogs = ref<string[]>([])
const currentPage = ref(1)
const pageSize = ref(25)

const filters = reactive<Filters>({
  dateRange: '24h',
  riskLevel: '',
  activityType: '',
  admin: '',
  ipAddress: '',
  search: ''
})

// Statistics data
const statistics = computed((): Statistic[] => [
  {
    title: 'Bugün Toplam',
    value: auditLogs.value.filter(log => isToday(log.timestamp)).length,
    change: '+12%',
    trend: 'up',
    icon: ClipboardDocumentListIcon,
    iconColor: 'text-blue-600 dark:text-blue-400',
    iconBg: 'bg-blue-100 dark:bg-blue-900/30',
    borderColor: 'border-blue-500'
  },
  {
    title: 'Kritik Risk',
    value: auditLogs.value.filter(log => log.riskLevel === 'critical').length,
    change: '-2%',
    trend: 'down',
    icon: FireIcon,
    iconColor: 'text-red-600 dark:text-red-400',
    iconBg: 'bg-red-100 dark:bg-red-900/30',
    borderColor: 'border-red-500'
  },
  {
    title: 'Güvenlik Olayı',
    value: auditLogs.value.filter(log => log.activityType === 'auth' && log.riskLevel !== 'success').length,
    change: '0%',
    trend: 'up',
    icon: ShieldCheckIcon,
    iconColor: 'text-yellow-600 dark:text-yellow-400',
    iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
    borderColor: 'border-yellow-500'
  },
  {
    title: 'Başarılı İşlem',
    value: auditLogs.value.filter(log => log.riskLevel === 'success').length,
    change: '+5%',
    trend: 'up',
    icon: CheckCircleIcon,
    iconColor: 'text-green-600 dark:text-green-400',
    iconBg: 'bg-green-100 dark:bg-green-900/30',
    borderColor: 'border-green-500'
  },
  {
    title: 'Sistem Değişikliği',
    value: auditLogs.value.filter(log => log.activityType === 'system').length,
    change: '+8%',
    trend: 'up',
    icon: ComputerDesktopIcon,
    iconColor: 'text-purple-600 dark:text-purple-400',
    iconBg: 'bg-purple-100 dark:bg-purple-900/30',
    borderColor: 'border-purple-500'
  },
  {
    title: 'Aktif Oturum',
    value: 3,
    change: '+1',
    trend: 'up',
    icon: UsersIcon,
    iconColor: 'text-indigo-600 dark:text-indigo-400',
    iconBg: 'bg-indigo-100 dark:bg-indigo-900/30',
    borderColor: 'border-indigo-500'
  }
])

// Quick filters
const quickFilters = computed((): QuickFilter[] => [
  { key: 'critical', label: 'Kritik Riskli', filter: { riskLevel: 'critical' } },
  { key: 'today', label: 'Bugün', filter: { dateRange: '24h' } },
  { key: 'auth', label: 'Kimlik Doğrulama', filter: { activityType: 'auth' } },
  { key: 'hotel', label: 'Otel İşlemleri', filter: { activityType: 'hotel' } },
  { key: 'system', label: 'Sistem Değişiklikleri', filter: { activityType: 'system' } },
  { key: 'failed', label: 'Başarısız İşlemler', filter: { riskLevel: 'high' } }
])

// Dummy audit logs data
const auditLogs = ref<AuditLog[]>([
  {
    id: 'log_001',
    timestamp: new Date('2024-01-15T14:32:00'),
    admin: 'Ahmet Yılmaz',
    adminEmail: '<EMAIL>',
    title: 'Otel hesabı devre dışı bırakıldı',
    description: '"Kapadokya Taş Otel" hesabı ödeme sorunları nedeniyle devre dışı bırakıldı',
    riskLevel: 'critical',
    activityType: 'hotel',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    sessionId: 'sess_2024_15_001',
    hotelId: 3,
    changes: 'status: Aktif → Pasif',
    targetResource: 'Hotel #3',
    affectedRecords: '1 otel hesabı',
    result: 'Başarılı',
    duration: 1250,
    referrer: '/hotels'
  },
  {
    id: 'log_002',
    timestamp: new Date('2024-01-15T13:15:00'),
    admin: 'Mehmet Kaya',
    adminEmail: '<EMAIL>',
    title: 'MFE konfigürasyonu değiştirildi',
    description: '"Ege Palas İzmir" için SPA & Wellness modülü etkinleştirildi',
    riskLevel: 'medium',
    activityType: 'mfe',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    sessionId: 'sess_2024_15_002',
    hotelId: 1,
    changes: 'spaWellness: false → true',
    targetResource: 'MFE Config',
    affectedRecords: '1 modül konfigürasyonu',
    result: 'Başarılı',
    duration: 890,
    referrer: '/hotels/1/mfes'
  },
  {
    id: 'log_003',
    timestamp: new Date('2024-01-15T12:45:00'),
    admin: 'Ayşe Demir',
    adminEmail: '<EMAIL>',
    title: 'Otel bilgileri görüntülendi',
    description: '"Antalya Sahil Resort" otel detayları incelendi',
    riskLevel: 'low',
    activityType: 'hotel',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    sessionId: 'sess_2024_15_003',
    hotelId: 2,
    targetResource: 'Hotel #2',
    affectedRecords: '1 otel görüntüleme',
    result: 'Başarılı',
    duration: 350
  },
  {
    id: 'log_004',
    timestamp: new Date('2024-01-15T11:30:00'),
    admin: 'Ahmet Yılmaz',
    adminEmail: '<EMAIL>',
    title: 'Global duyuru gönderildi',
    description: '"Platform v2.1 Güncellemesi" duyurusu tüm otellere başarıyla gönderildi',
    riskLevel: 'success',
    activityType: 'announcement',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    sessionId: 'sess_2024_15_001',
    changes: 'Yeni duyuru oluşturuldu',
    targetResource: 'Global Announcement',
    affectedRecords: '127 otel',
    result: 'Başarılı',
    duration: 2150
  },
  {
    id: 'log_005',
    timestamp: new Date('2024-01-15T09:00:00'),
    admin: 'Mehmet Kaya',
    adminEmail: '<EMAIL>',
    title: 'Süper admin girişi',
    description: 'Başarılı giriş yapıldı ve yeni oturum başlatıldı',
    riskLevel: 'success',
    activityType: 'auth',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/120.0.0.0',
    sessionId: 'sess_2024_15_002',
    targetResource: 'Authentication System',
    affectedRecords: '1 oturum',
    result: 'Başarılı',
    duration: 450
  },
  {
    id: 'log_006',
    timestamp: new Date('2024-01-15T08:45:00'),
    admin: 'Sistem',
    adminEmail: '<EMAIL>',
    title: 'Otomatik güvenlik taraması',
    description: 'Günlük güvenlik taraması tamamlandı, 0 tehdit tespit edildi',
    riskLevel: 'success',
    activityType: 'system',
    ipAddress: '127.0.0.1',
    userAgent: 'System Process',
    targetResource: 'Security Scanner',
    affectedRecords: '1247 dosya tarandı',
    result: 'Temiz',
    duration: 15000
  },
  {
    id: 'log_007',
    timestamp: new Date('2024-01-14T23:30:00'),
    admin: 'Ahmet Yılmaz',
    adminEmail: '<EMAIL>',
    title: 'Şüpheli giriş denemesi',
    description: 'Bilinmeyen IP adresinden süper admin hesabına giriş denemesi',
    riskLevel: 'high',
    activityType: 'auth',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
    targetResource: 'Authentication System',
    affectedRecords: '1 başarısız giriş',
    result: 'Engellendi',
    duration: 100
  },
  {
    id: 'log_008',
    timestamp: new Date('2024-01-14T18:20:00'),
    admin: 'Ayşe Demir',
    adminEmail: '<EMAIL>',
    title: 'Toplu otel güncellemesi',
    description: '15 otelin abonelik planı Premium\'a yükseltildi',
    riskLevel: 'medium',
    activityType: 'hotel',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    sessionId: 'sess_2024_14_001',
    changes: 'Plan: Basic → Premium',
    targetResource: 'Bulk Hotel Update',
    affectedRecords: '15 otel',
    result: 'Başarılı',
    duration: 3450
  }
])

// Computed properties
const filteredLogs = computed(() => {
  let logs = auditLogs.value

  // Apply filters
  if (filters.dateRange) {
    const now = new Date()
    const filterDate = new Date()
    
    switch (filters.dateRange) {
      case '24h':
        filterDate.setHours(now.getHours() - 24)
        break
      case '7d':
        filterDate.setDate(now.getDate() - 7)
        break
      case '30d':
        filterDate.setDate(now.getDate() - 30)
        break
      case '90d':
        filterDate.setDate(now.getDate() - 90)
        break
    }
    
    if (filters.dateRange !== 'custom') {
      logs = logs.filter(log => log.timestamp >= filterDate)
    }
  }

  if (filters.riskLevel) {
    logs = logs.filter(log => log.riskLevel === filters.riskLevel)
  }

  if (filters.activityType) {
    logs = logs.filter(log => log.activityType === filters.activityType)
  }

  if (filters.admin) {
    logs = logs.filter(log => log.adminEmail.includes(filters.admin))
  }

  if (filters.ipAddress) {
    logs = logs.filter(log => log.ipAddress.includes(filters.ipAddress))
  }

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase()
    logs = logs.filter(log => 
      log.title.toLowerCase().includes(searchTerm) ||
      log.description.toLowerCase().includes(searchTerm) ||
      log.admin.toLowerCase().includes(searchTerm)
    )
  }

  return logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

const totalLogs = computed(() => filteredLogs.value.length)
const totalPages = computed(() => Math.ceil(totalLogs.value / pageSize.value))

const visiblePages = computed(() => {
  const pages: number[] = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const expandLog = (logId: string) => {
  const index = expandedLogs.value.indexOf(logId)
  if (index > -1) {
    expandedLogs.value.splice(index, 1)
  } else {
    expandedLogs.value.push(logId)
  }
}

const getRiskLevelText = (level: string): string => {
  const texts = {
    critical: 'Kritik',
    high: 'Yüksek Risk',
    medium: 'Orta Risk',
    low: 'Düşük Risk',
    success: 'Başarılı'
  }
  return texts[level as keyof typeof texts] || level
}

const getRiskIcon = (level: string) => {
  const icons = {
    critical: FireIcon,
    high: ExclamationTriangleIcon,
    medium: ExclamationCircleIcon,
    low: InformationCircleIcon,
    success: CheckCircleIcon
  }
  return icons[level as keyof typeof icons] || InformationCircleIcon
}

const getRiskIconColor = (level: string): string => {
  const colors = {
    critical: 'text-red-500',
    high: 'text-orange-500',
    medium: 'text-yellow-500',
    low: 'text-blue-500',
    success: 'text-green-500'
  }
  return colors[level as keyof typeof colors] || 'text-gray-500'
}

const formatDateTime = (date: Date): string => {
  return date.toLocaleString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getTimeAgo = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMins < 1) return 'Az önce'
  if (diffMins < 60) return `${diffMins} dakika önce`
  if (diffHours < 24) return `${diffHours} saat önce`
  if (diffDays < 7) return `${diffDays} gün önce`
  return formatDateTime(date)
}

const isToday = (date: Date): boolean => {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

const clearFilters = () => {
  Object.assign(filters, {
    dateRange: '24h',
    riskLevel: '',
    activityType: '',
    admin: '',
    ipAddress: '',
    search: ''
  })
  currentPage.value = 1
}

const applyQuickFilter = (filter: QuickFilter) => {
  Object.assign(filters, filter.filter)
  currentPage.value = 1
}

const exportLogs = () => {
  // Simulate export functionality
  const data = filteredLogs.value.map(log => ({
    Tarih: formatDateTime(log.timestamp),
    Admin: log.admin,
    Başlık: log.title,
    Açıklama: log.description,
    Risk: getRiskLevelText(log.riskLevel),
    Tip: log.activityType,
    IP: log.ipAddress,
    Sonuç: log.result
  }))
  
  console.log('Exporting logs:', data)
  alert('Denetim günlükleri Excel formatında indirildi!')
}

const exportSingleLog = (log: AuditLog) => {
  console.log('Exporting single log:', log)
  alert(`"${log.title}" kaydının detayları indirildi!`)
}

const viewRelatedLogs = (log: AuditLog) => {
  // Filter by same admin or same hotel
  if (log.hotelId) {
    filters.search = `#${log.hotelId}`
  } else {
    filters.admin = log.adminEmail.split('@')[0]
  }
  currentPage.value = 1
}

const flagForReview = (log: AuditLog) => {
  console.log('Flagging for review:', log)
  alert(`"${log.title}" kaydı güvenlik incelemesi için işaretlendi!`)
}

const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('tr-TR')
}

// Lifecycle
let timeInterval: ReturnType<typeof setInterval>

onMounted(() => {
  timeInterval = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script> 