<template>
  <div id="customer-portal-app" class="min-h-screen bg-gray-50">
    <!-- Mobile-first layout container -->
    <main class="mx-auto max-w-md lg:max-w-4xl pb-24">
      <router-view />
    </main>
    
    <!-- Centralized Bottom Navigation -->
    <BottomNavigation v-if="$route.meta.showBottomNav" />
    
    <!-- Global Notification Toast System -->
    <NotificationToast />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import BottomNavigation from '@/components/BottomNavigation.vue'
import NotificationToast from '@/components/NotificationToast.vue'

onMounted(() => {
  // Any initialization logic for customer portal
  console.log('Customer Portal MFE mounted with notification system')
})
</script>

<style scoped>
#customer-portal-app {
  /* Ensure proper touch scrolling on mobile */
  -webkit-overflow-scrolling: touch;
}
</style> 