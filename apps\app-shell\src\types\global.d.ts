// Module Federation type declarations
declare module 'mfe_auth/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'customer_portal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'hotel_portal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'manager_portal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Environment variables
interface ImportMetaEnv {
  readonly VITE_SUPABASE_URL: string
  readonly VITE_SUPABASE_ANON_KEY: string
  readonly VITE_APP_ENV: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_VERSION: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_MFE_AUTH_URL: string
  readonly VITE_MFE_CUSTOMER_URL: string
  readonly VITE_MFE_HOTEL_URL: string
  readonly VITE_MFE_MANAGER_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
} 