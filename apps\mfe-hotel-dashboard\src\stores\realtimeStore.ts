import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import {
  supabase,
  crossMFERealtimeService
} from '@hotelexia/shared-supabase-client'
import type { RealtimeChannel } from '@supabase/supabase-js'
import { useStaffNotificationStore } from './staffNotificationStore'
import { useAuthStore } from './authStore'

// Temporary local type definitions until module resolution is fixed
interface CrossMFEEvent {
  type: string
  payload: any
  source: string
  timestamp: number
  userId?: string
  hotelId?: string
}

type CrossMFEEventType = CrossMFEEvent

export interface RealtimeNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  read: boolean
  data?: any
}

export const useRealtimeStore = defineStore('realtime', () => {
  // State
  const notifications = ref<RealtimeNotification[]>([])
  const isConnected = ref(false)
  const error = ref<string | null>(null)

  // Realtime channels
  let maintenanceTasksChannel: RealtimeChannel | null = null
  let serviceRequestsChannel: RealtimeChannel | null = null

  // Get auth store for hotel context
  const authStore = useAuthStore()

  // Computed
  const unreadCount = computed(() =>
    notifications.value.filter(n => !n.read).length
  )

  const recentNotifications = computed(() =>
    notifications.value
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10)
  )

  const currentHotelId = computed(() => authStore.userHotelId)

  const addNotification = (notification: Omit<RealtimeNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: RealtimeNotification = {
      ...notification,
      id: crypto.randomUUID(),
      timestamp: new Date(),
      read: false
    }
    
    notifications.value.unshift(newNotification)
    
    // Keep only last 50 notifications
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markAsRead = (notificationId: string) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (notificationId: string) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // Realtime subscription setup
  const setupMaintenanceTasksSubscription = () => {
    if (!currentHotelId.value) {
      console.warn('Cannot setup maintenance tasks subscription: hotel ID not set')
      return
    }

    try {
      maintenanceTasksChannel = supabase
        .channel('maintenance_tasks_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'maintenance_tasks',
            filter: `hotel_id=eq.${currentHotelId.value}`
          },
          (payload) => {
            console.log('Maintenance task change:', payload)
            handleMaintenanceTaskChange(payload)
          }
        )
        .subscribe((status) => {
          console.log('Maintenance tasks subscription status:', status)
          if (status === 'SUBSCRIBED') {
            isConnected.value = true
          }
        })
    } catch (err) {
      console.error('Error setting up maintenance tasks subscription:', err)
      error.value = 'Failed to setup maintenance tasks subscription'
    }
  }

  const setupServiceRequestsSubscription = () => {
    if (!currentHotelId.value) {
      console.warn('Cannot setup service requests subscription: hotel ID not set')
      return
    }

    try {
      serviceRequestsChannel = supabase
        .channel('service_requests_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'service_requests',
            filter: `hotel_id=eq.${currentHotelId.value}`
          },
          (payload) => {
            console.log('Service request change:', payload)
            handleServiceRequestChange(payload)
          }
        )
        .subscribe((status) => {
          console.log('Service requests subscription status:', status)
        })
    } catch (err) {
      console.error('Error setting up service requests subscription:', err)
      error.value = 'Failed to setup service requests subscription'
    }
  }

  // Event handlers with hotel ID filtering
  const handleMaintenanceTaskChange = (payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload

    // Filter by hotel ID - only process events for current hotel
    const recordHotelId = newRecord?.hotel_id || oldRecord?.hotel_id
    if (recordHotelId !== currentHotelId.value) {
      console.log(`Ignoring maintenance task event for different hotel: ${recordHotelId}`)
      return
    }

    // Process the event and generate formatted notification
    const messageData = formatTaskMessage(newRecord, eventType, oldRecord)

    addNotification({
      title: messageData.title,
      message: messageData.message,
      type: messageData.type,
      data: {
        taskId: newRecord?.id || oldRecord?.id,
        type: 'maintenance_task',
        eventType,
        roomId: newRecord?.room_id || oldRecord?.room_id
      }
    })
  }

  const handleServiceRequestChange = (payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload

    // Filter by hotel ID - only process events for current hotel
    const recordHotelId = newRecord?.hotel_id || oldRecord?.hotel_id
    if (recordHotelId !== currentHotelId.value) {
      console.log(`Ignoring service request event for different hotel: ${recordHotelId}`)
      return
    }

    // Process the event and generate formatted notification
    const messageData = formatServiceRequestMessage(newRecord, eventType, oldRecord)

    addNotification({
      title: messageData.title,
      message: messageData.message,
      type: messageData.type,
      data: {
        requestId: newRecord?.id || oldRecord?.id,
        type: 'service_request',
        eventType,
        roomId: newRecord?.room_id || oldRecord?.room_id,
        serviceType: newRecord?.service_type || oldRecord?.service_type
      }
    })
  }

  // Helper functions for message processing
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'PENDING': return 'beklemede'
      case 'IN_PROGRESS': return 'devam ediyor'
      case 'COMPLETED': return 'tamamlandı'
      case 'CANCELLED': return 'iptal edildi'
      default: return status.toLowerCase()
    }
  }

  const getPriorityText = (priority: string): string => {
    switch (priority) {
      case 'LOW': return 'düşük'
      case 'MEDIUM': return 'orta'
      case 'HIGH': return 'yüksek'
      case 'URGENT': return 'acil'
      default: return priority.toLowerCase()
    }
  }

  const formatTaskMessage = (task: any, eventType: string, oldRecord?: any): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {
    switch (eventType) {
      case 'INSERT':
        return {
          title: 'Yeni Bakım Görevi',
          message: `"${task.title}" görevi oluşturuldu (Öncelik: ${getPriorityText(task.priority)})`,
          type: 'info'
        }

      case 'UPDATE':
        if (oldRecord?.status !== task.status) {
          const statusText = getStatusText(task.status)
          return {
            title: 'Görev Durumu Güncellendi',
            message: `"${task.title}" görevi ${statusText} olarak güncellendi`,
            type: task.status === 'COMPLETED' ? 'success' : 'info'
          }
        }

        if (oldRecord?.assigned_to !== task.assigned_to && task.assigned_to) {
          return {
            title: 'Görev Atandı',
            message: `"${task.title}" görevi size atandı`,
            type: 'info'
          }
        }

        if (oldRecord?.priority !== task.priority) {
          return {
            title: 'Görev Önceliği Güncellendi',
            message: `"${task.title}" görevinin önceliği ${getPriorityText(task.priority)} olarak değiştirildi`,
            type: task.priority === 'URGENT' ? 'warning' : 'info'
          }
        }

        return {
          title: 'Görev Güncellendi',
          message: `"${task.title}" görevi güncellendi`,
          type: 'info'
        }

      case 'DELETE':
        return {
          title: 'Görev Silindi',
          message: `"${task.title || 'Bakım görevi'}" silindi`,
          type: 'warning'
        }

      default:
        return {
          title: 'Görev Değişikliği',
          message: `Görev güncellemesi alındı`,
          type: 'info'
        }
    }
  }

  const formatServiceRequestMessage = (request: any, eventType: string, oldRecord?: any): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {
    switch (eventType) {
      case 'INSERT':
        return {
          title: 'Yeni Servis Talebi',
          message: `Yeni ${request.service_type || 'servis'} talebi alındı`,
          type: 'info'
        }

      case 'UPDATE':
        if (oldRecord?.status !== request.status) {
          const statusText = getStatusText(request.status)
          return {
            title: 'Servis Talebi Güncellendi',
            message: `${request.service_type || 'Servis'} talebi ${statusText}`,
            type: request.status === 'COMPLETED' ? 'success' : 'info'
          }
        }

        return {
          title: 'Servis Talebi Güncellendi',
          message: `${request.service_type || 'Servis'} talebi güncellendi`,
          type: 'info'
        }

      case 'DELETE':
        return {
          title: 'Servis Talebi İptal Edildi',
          message: `${request.service_type || 'Servis'} talebi iptal edildi`,
          type: 'warning'
        }

      default:
        return {
          title: 'Servis Talebi Değişikliği',
          message: `Servis talebi güncellemesi alındı`,
          type: 'info'
        }
    }
  }

  // Initialize subscriptions
  const initializeSubscriptions = () => {
    const currentHotel = currentHotelId.value
    console.log('Initializing realtime subscriptions for hotel:', currentHotel)

    // Cleanup any existing subscriptions first
    cleanup()

    if (!currentHotel) {
      console.error('Hotel ID is required for realtime subscriptions')
      return
    }

    setupMaintenanceTasksSubscription()
    setupServiceRequestsSubscription()
    setupCrossMFESubscriptions()

    // Initialize staff notifications
    const staffNotificationStore = useStaffNotificationStore()
    staffNotificationStore.initializeSubscriptions()

    // Add a test notification to verify the system is working
    addNotification({
      title: 'Gerçek Zamanlı Bildirimler Aktif',
      message: `${currentHotel} oteli için gerçek zamanlı bildirimler başlatıldı`,
      type: 'success'
    })
  }

  // Setup cross-MFE event subscriptions
  const setupCrossMFESubscriptions = () => {
    console.log('Setting up cross-MFE event subscriptions for Hotel Dashboard')

    // Subscribe to relevant cross-MFE events
    crossMFERealtimeService.subscribe(CrossMFEEventType.HOTEL_UPDATED, handleCrossMFEHotelUpdate)
    crossMFERealtimeService.subscribe(CrossMFEEventType.HOTEL_STATUS_CHANGED, handleCrossMFEHotelUpdate)
    crossMFERealtimeService.subscribe(CrossMFEEventType.USER_PROFILE_UPDATED, handleCrossMFEUserUpdate)
    crossMFERealtimeService.subscribe(CrossMFEEventType.USER_ROLE_CHANGED, handleCrossMFEUserUpdate)
    crossMFERealtimeService.subscribe(CrossMFEEventType.ORDER_CREATED, handleCrossMFEOrderUpdate)
    crossMFERealtimeService.subscribe(CrossMFEEventType.ORDER_STATUS_UPDATED, handleCrossMFEOrderUpdate)
  }

  // Handle cross-MFE hotel updates
  const handleCrossMFEHotelUpdate = (event: CrossMFEEvent) => {
    console.log('Hotel Dashboard received hotel update:', event)

    // Add notification about hotel changes
    addNotification({
      title: 'Otel Bilgileri Güncellendi',
      message: `Otel bilgilerinde değişiklik yapıldı`,
      type: 'info',
      data: {
        type: 'hotel_update',
        hotelId: event.payload.hotelId,
        eventType: event.type
      }
    })

    // Broadcast to local components that hotel data may have changed
    broadcastLocalEvent('hotel-updated', event.payload.data)
  }

  // Handle cross-MFE user updates
  const handleCrossMFEUserUpdate = (event: CrossMFEEvent) => {
    console.log('Hotel Dashboard received user update:', event)

    if (event.type === CrossMFEEventType.USER_ROLE_CHANGED) {
      addNotification({
        title: 'Kullanıcı Yetkileri Güncellendi',
        message: `Bir kullanıcının yetkileri değiştirildi`,
        type: 'warning',
        data: {
          type: 'user_role_change',
          userId: event.payload.userId,
          eventType: event.type
        }
      })
    }

    // Broadcast to local components
    broadcastLocalEvent('user-updated', event.payload.data)
  }

  // Handle cross-MFE order updates
  const handleCrossMFEOrderUpdate = (event: CrossMFEEvent) => {
    console.log('Hotel Dashboard received order update:', event)

    if (event.type === CrossMFEEventType.ORDER_CREATED) {
      addNotification({
        title: 'Yeni Sipariş Alındı',
        message: `Müşteriden yeni bir sipariş geldi`,
        type: 'success',
        data: {
          type: 'new_order',
          orderId: event.payload.id,
          eventType: event.type
        }
      })
    } else if (event.type === CrossMFEEventType.ORDER_STATUS_UPDATED) {
      addNotification({
        title: 'Sipariş Durumu Güncellendi',
        message: `Bir siparişin durumu değiştirildi`,
        type: 'info',
        data: {
          type: 'order_status_update',
          orderId: event.payload.id,
          eventType: event.type
        }
      })
    }

    // Broadcast to local components
    broadcastLocalEvent('order-updated', event.payload.data)
  }

  // Broadcast events to local components
  const broadcastLocalEvent = (eventType: string, data: any) => {
    const customEvent = new CustomEvent(`hotel-dashboard-${eventType}`, {
      detail: { data, timestamp: Date.now() }
    })
    window.dispatchEvent(customEvent)
  }

  // Broadcast cross-MFE events when local data changes
  const broadcastTaskUpdate = (taskId: string, taskData: any, eventType: 'created' | 'updated' | 'deleted') => {
    const crossMFEEventType = eventType === 'created' || eventType === 'updated'
      ? CrossMFEEventType.MAINTENANCE_TASK_UPDATED
      : CrossMFEEventType.TASK_STATUS_UPDATED

    crossMFERealtimeService.createEvent(
      crossMFEEventType,
      taskId,
      taskData,
      'hotel-dashboard',
      { eventType, source: 'maintenance_tasks' }
    )
  }

  const broadcastServiceRequestUpdate = (requestId: string, requestData: any, eventType: 'created' | 'updated' | 'deleted') => {
    crossMFERealtimeService.createEvent(
      CrossMFEEventType.SERVICE_REQUEST_UPDATED,
      requestId,
      requestData,
      'hotel-dashboard',
      { eventType, source: 'service_requests' }
    )
  }

  const broadcastRoomUpdate = (roomId: string, roomData: any, eventType: 'status_changed' | 'updated') => {
    const crossMFEEventType = eventType === 'status_changed'
      ? CrossMFEEventType.ROOM_STATUS_CHANGED
      : CrossMFEEventType.ROOM_UPDATED

    crossMFERealtimeService.createEvent(
      crossMFEEventType,
      roomId,
      roomData,
      'hotel-dashboard',
      { eventType, source: 'rooms' }
    )
  }

  // Cleanup subscriptions
  const cleanup = () => {
    console.log('Cleaning up realtime subscriptions')

    if (maintenanceTasksChannel) {
      try {
        supabase.removeChannel(maintenanceTasksChannel)
        console.log('Maintenance tasks channel removed')
      } catch (err) {
        console.error('Error removing maintenance tasks channel:', err)
      }
      maintenanceTasksChannel = null
    }

    if (serviceRequestsChannel) {
      try {
        supabase.removeChannel(serviceRequestsChannel)
        console.log('Service requests channel removed')
      } catch (err) {
        console.error('Error removing service requests channel:', err)
      }
      serviceRequestsChannel = null
    }

    // Cleanup staff notifications
    const staffNotificationStore = useStaffNotificationStore()
    staffNotificationStore.cleanup()

    isConnected.value = false
    error.value = null
  }

  // Test function to simulate events for testing
  const testNotificationSystem = () => {
    if (!currentHotelId.value) {
      console.warn('Cannot test notifications: no hotel ID set')
      return
    }

    // Test maintenance task notification
    addNotification({
      title: 'Test Bakım Görevi',
      message: 'Bu bir test bildirimidir - Oda 101 temizlik görevi oluşturuldu',
      type: 'info',
      data: {
        taskId: 'test-task-1',
        type: 'maintenance_task',
        eventType: 'INSERT',
        roomId: 'test-room-101'
      }
    })

    // Test service request notification
    setTimeout(() => {
      addNotification({
        title: 'Test Servis Talebi',
        message: 'Bu bir test bildirimidir - Oda 205 için havlu talebi',
        type: 'warning',
        data: {
          requestId: 'test-request-1',
          type: 'service_request',
          eventType: 'INSERT',
          roomId: 'test-room-205'
        }
      })
    }, 2000)

    console.log('Test notifications sent')
  }

  return {
    // State
    notifications,
    isConnected,
    error,
    currentHotelId,

    // Computed
    unreadCount,
    recentNotifications,

    // Actions
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    initializeSubscriptions,
    cleanup,
    testNotificationSystem,

    // Cross-MFE broadcasting functions
    broadcastTaskUpdate,
    broadcastServiceRequestUpdate,
    broadcastRoomUpdate
  }
})
