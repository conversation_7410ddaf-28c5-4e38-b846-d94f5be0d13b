import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/onboarding',
    name: 'Onboarding',
    component: () => import('../views/OnboardingView.vue'),
    meta: {
      title: 'Welcome - Hotelexia',
      public: true
    }
  },
  {
    path: '/',
    name: 'GuestLogin',
    redirect: '/login', // Redirect to guest login
    meta: {
      public: true
    }
  },
  {
    path: '/login',
    name: 'GuestLoginForm',
    component: () => import('../views/GuestLoginView.vue'),
    meta: {
      title: 'Guest Login - Hotelexia',
      public: true
    }
  },
  {
    path: '/dashboard',
    name: 'GuestDashboard',
    component: () => import('../views/GuestDashboardView.vue'),
    meta: {
      title: 'Dashboard - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/room-service-menu',
    name: 'RoomServiceMenu',
    component: () => import('../views/RoomServiceMenuView.vue'),
    meta: {
      title: 'Oda Servisi Menü - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/category/:category',
    name: 'RoomServiceCategory',
    component: () => import('../views/RoomServiceCategoryView.vue'),
    meta: {
      title: 'Kategori Menü - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/item/:id',
    name: 'RoomServiceItemDetail',
    component: () => import('../views/RoomServiceItemDetailView.vue'),
    meta: {
      title: 'Ürün Detayı - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/room-service',
    name: 'RoomService',
    component: () => import('../views/RoomServiceView.vue'),
    meta: {
      title: 'Oda Servisi - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/service-request',
    name: 'ServiceRequest',
    component: () => import('../views/ServiceRequestView.vue'),
    meta: {
      title: 'Servis Talebi - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/cart',
    name: 'Cart',
    component: () => import('../views/CartView.vue'),
    meta: {
      title: 'Sepetim - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/my-orders',
    name: 'MyOrders',
    component: () => import('../views/MyOrdersView.vue'),
    meta: {
      title: 'Siparişlerim - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/my-requests',
    name: 'MyRequests',
    component: () => import('../views/MyRequestsView.vue'),
    meta: {
      title: 'Servis Taleplerim - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/order-success',
    name: 'OrderSuccess',
    component: () => import('../views/OrderSuccessView.vue'),
    meta: {
      title: 'Sipariş Başarılı - Hotelexia',
      requiresGuestAuth: true
    }
  },
  {
    path: '/order-failed',
    name: 'OrderFailed',
    component: () => import('../views/OrderFailedView.vue'),
    meta: {
      title: 'Sipariş Başarısız - Hotelexia',
      requiresGuestAuth: true
    }
  },
  {
    path: '/activities',
    name: 'Activities',
    component: () => import('../views/ActivitiesView.vue'),
    meta: {
      title: 'Günün Programı - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/activity-detail/:id',
    name: 'ActivityDetail',
    component: () => import('../views/ActivityDetailView.vue'),
    meta: {
      title: 'Aktivite Detayı - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  // Services Module Routes
  {
    path: '/services',
    name: 'Services',
    component: () => import('../views/ServicesHomeView.vue'),
    meta: {
      title: 'Hizmetler - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/service-category/:slug',
    name: 'ServiceCategory',
    component: () => import('../views/ServiceCategoryView.vue'),
    meta: {
      title: 'Hizmet Kategorisi - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/service/:id',
    name: 'ServiceDetail',
    component: () => import('../views/ServiceDetailView.vue'),
    meta: {
      title: 'Hizmet Detayı - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  },
  {
    path: '/notifications',
    name: 'Notifications',
    component: () => import('../views/NotificationsView.vue'),
    meta: {
      title: 'Bildirimler - Hotelexia',
      requiresGuestAuth: true,
      showBottomNav: true
    }
  }
]

const router = createRouter({
  history: createWebHistory('/guest/'),
  routes
})

// Navigation guard
router.beforeEach(async (to, _from, next) => {
  // Update document title
  document.title = (to.meta?.title as string) || 'Hotelexia Customer Portal'

  // Check if onboarding is needed (except when already going to onboarding)
  if (to.name !== 'Onboarding') {
    const hasSeenOnboarding = localStorage.getItem('hotelexia_onboarding_completed')

    if (!hasSeenOnboarding) {
      // Redirect to onboarding if not seen
      next('/onboarding')
      return
    }
  }

  next()
})

export default router 