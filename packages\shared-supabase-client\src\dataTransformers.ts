/**
 * Data transformation utilities for converting between Supabase data and frontend interfaces
 * This ensures consistent data shapes across all MFEs while maintaining type safety
 */

import type { Database } from './types'

// Type aliases for cleaner code
type DbRoom = Database['public']['Tables']['rooms']['Row']
type DbTask = Database['public']['Tables']['housekeeping_tasks']['Row']
type DbUserProfile = Database['public']['Tables']['user_profiles']['Row']
type DbHotel = Database['public']['Tables']['hotels']['Row']
type DbMenuItem = Database['public']['Tables']['menu_items']['Row']
type DbServiceRequest = Database['public']['Tables']['service_requests']['Row']

// Hotel Dashboard Transformers
export namespace HotelDashboard {
  
  export interface Room {
    id: string
    number: string
    type: string
    floor: number
    status: 'DIRTY' | 'CLEANING' | 'CLEAN' | 'INSPECTED' | 'OUT_OF_ORDER' | 'MAINTENANCE'
    lastCleaned: string
    nextInspection: string
    currentGuest?: string
    guestName?: string
    checkOutTime?: string
    checkInTime?: string
    notes?: string
    assignedStaff?: string
  }

  export function transformRoom(dbRoom: DbRoom): Room {
    return {
      id: dbRoom.id,
      number: dbRoom.room_number,
      type: dbRoom.room_type || 'Standard',
      floor: dbRoom.floor || 1,
      status: mapRoomStatus(dbRoom.status),
      lastCleaned: dbRoom.last_cleaned ? new Date(dbRoom.last_cleaned).toISOString() : '',
      nextInspection: dbRoom.last_inspection ? new Date(dbRoom.last_inspection).toISOString() : '',
      currentGuest: dbRoom.current_guest_id || undefined,
      checkOutTime: dbRoom.check_out_time ? new Date(dbRoom.check_out_time).toISOString() : undefined,
      checkInTime: dbRoom.check_in_time ? new Date(dbRoom.check_in_time).toISOString() : undefined,
      notes: dbRoom.housekeeping_notes || undefined,
      assignedStaff: dbRoom.assigned_housekeeper_id || undefined
    }
  }

  export interface HousekeepingTask {
    id: string
    title: string
    description: string
    roomNumber: string
    status: 'NEW' | 'ASSIGNED' | 'IN_PROGRESS' | 'QUALITY_CHECK' | 'COMPLETED' | 'CANCELLED'
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
    assignedTo?: string
    estimatedTime: number
    deadline: string
    notes?: string
    createdAt: string
    completedAt?: string
    roomId?: string
  }

  export function transformTask(dbTask: DbTask, roomNumber?: string): HousekeepingTask {
    return {
      id: dbTask.id,
      title: dbTask.description || 'Housekeeping Task', // Use description as title
      description: dbTask.description || '',
      roomNumber: roomNumber || '',
      status: mapTaskStatus(dbTask.status),
      priority: mapTaskPriority(dbTask.priority),
      assignedTo: dbTask.assigned_to_user_id || undefined,
      estimatedTime: 30, // Default estimated time
      deadline: dbTask.due_date ? new Date(dbTask.due_date).toISOString() : '',
      notes: undefined, // Not available in current schema
      createdAt: new Date(dbTask.created_at || '').toISOString(),
      completedAt: undefined, // Not available in current schema
      roomId: dbTask.room_id || undefined
    }
  }

  export interface StaffMember {
    id: string
    name: string
    surname: string
    role: string
    status: 'AVAILABLE' | 'BUSY' | 'ON_BREAK' | 'OFF_DUTY'
    avatar: string
    shift: string
    performanceScore: number
    tasksCompleted: number
    averageTime: number
    phone: string
    email: string
    startDate: string
    currentTasks: string[]
  }

  export function transformStaffMember(dbUser: DbUserProfile): StaffMember {
    const fullName = dbUser.full_name || ''
    const nameParts = fullName.split(' ')
    const firstName = nameParts[0] || ''
    const lastName = nameParts.slice(1).join(' ') || ''

    return {
      id: dbUser.id,
      name: firstName,
      surname: lastName,
      role: dbUser.role || 'STAFF',
      status: 'AVAILABLE', // Default status since we don't have this field
      avatar: dbUser.avatar_url || '',
      shift: 'Day Shift', // Default shift
      performanceScore: 85, // Default performance score
      tasksCompleted: 0, // Default tasks completed
      averageTime: 45, // Default average time
      phone: '', // Not available in current schema
      email: dbUser.full_name || '', // Using full_name as fallback for email display
      startDate: dbUser.created_at ? new Date(dbUser.created_at).toISOString() : '',
      currentTasks: []
    }
  }

  export interface MenuItem {
    id: string
    name: string
    description: string
    price: number
    category: string
    isAvailable: boolean
    image?: string
    preparationTime?: number
  }

  export function transformMenuItem(dbItem: DbMenuItem): MenuItem {
    return {
      id: dbItem.id,
      name: dbItem.name,
      description: dbItem.description || '',
      price: Number(dbItem.price || 0),
      category: dbItem.category || 'main',
      isAvailable: dbItem.is_available ?? true,
      image: dbItem.image_url || undefined,
      preparationTime: undefined // Not available in current schema
    }
  }

  export interface HotelEvent {
    id: string
    title: string
    description: string
    date: string
    time: string
    location: string
    image: string
    maxAttendees?: number
    currentAttendees?: number
    isActive: boolean
    createdAt: Date
    updatedAt: Date
  }

  export function transformHotelEvent(dbActivity: any): HotelEvent {
    const startTime = new Date(dbActivity.start_time_utc)
    return {
      id: dbActivity.id,
      title: dbActivity.title,
      description: dbActivity.description || '',
      date: startTime.toISOString().split('T')[0],
      time: startTime.toTimeString().slice(0, 5),
      location: dbActivity.location || '',
      image: dbActivity.image_url || 'https://picsum.photos/600/400?random=1',
      maxAttendees: dbActivity.capacity || undefined,
      currentAttendees: 0, // Would need to calculate from registrations
      isActive: dbActivity.is_active ?? true,
      createdAt: new Date(dbActivity.created_at),
      updatedAt: new Date(dbActivity.updated_at || dbActivity.created_at)
    }
  }

  export interface RoomServiceOrder {
    id: string
    guestUserId: string
    guestName: string
    roomNumber: string
    status: 'NEW' | 'PREPARING' | 'ON_THE_WAY' | 'DELIVERED' | 'CANCELLED'
    totalPrice: number
    notes?: string
    items: Array<{
      id: string
      menuItemId: string
      menuItemName: string
      quantity: number
      priceAtOrder: number
    }>
    createdAt: Date
  }

  export function transformRoomServiceOrder(dbOrder: any): RoomServiceOrder {
    return {
      id: dbOrder.id,
      guestUserId: dbOrder.customer_id || '',
      guestName: dbOrder.user_profiles?.full_name || 'Unknown Guest',
      roomNumber: dbOrder.rooms?.room_number || '',
      status: mapOrderStatus(dbOrder.status),
      totalPrice: Number(dbOrder.total_amount || 0),
      notes: dbOrder.special_instructions || undefined,
      items: (dbOrder.order_items || []).map((item: any) => ({
        id: item.id,
        menuItemId: item.menu_item_id,
        menuItemName: item.menu_items?.name || 'Unknown Item',
        quantity: item.quantity,
        priceAtOrder: Number(item.price_at_order || 0)
      })),
      createdAt: new Date(dbOrder.created_at)
    }
  }

  export interface HotelActivity {
    id: string
    title: string
    description: string
    imageUrl: string
    startTimeUtc: string
    location: string
    capacity: number
    isActive: boolean
    tags: string[]
  }

  export function transformHotelActivity(dbActivity: any): HotelActivity {
    return {
      id: dbActivity.id,
      title: dbActivity.title,
      description: dbActivity.description || '',
      imageUrl: dbActivity.image_url || 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=600',
      startTimeUtc: dbActivity.start_time_utc,
      location: dbActivity.location || '',
      capacity: dbActivity.capacity || 0,
      isActive: dbActivity.is_active ?? true,
      tags: dbActivity.tags || []
    }
  }

  export interface MaintenanceTask {
    id: string
    title: string
    description: string
    roomNumber: string
    status: 'NEW' | 'ASSIGNED' | 'IN_PROGRESS' | 'QUALITY_CHECK' | 'COMPLETED' | 'CANCELLED'
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
    assignedTo?: string
    estimatedTime: number
    deadline: string
    notes?: string
    createdAt: string
    completedAt?: string
    roomId?: string
  }

  export function transformMaintenanceTask(dbTask: any): MaintenanceTask {
    return {
      id: dbTask.id,
      title: dbTask.description || 'Maintenance Task',
      description: dbTask.description || '',
      roomNumber: dbTask.rooms?.room_number || '',
      status: mapTaskStatus(dbTask.status),
      priority: mapTaskPriority(dbTask.priority),
      assignedTo: dbTask.user_profiles?.full_name || undefined,
      estimatedTime: 60, // Default estimated time
      deadline: dbTask.due_date ? new Date(dbTask.due_date).toISOString() : '',
      notes: undefined,
      createdAt: new Date(dbTask.created_at || '').toISOString(),
      completedAt: undefined,
      roomId: dbTask.room_id || undefined
    }
  }

  export interface ServiceCategory {
    id: string
    name: string
    image_url: string
    type: 'ROOM_SERVICE' | 'GUEST_SERVICE' | 'ACTIVITY'
    display_order?: number
    isActive?: boolean
    description?: string
    icon?: string
    createdAt?: Date
  }

  export function transformServiceCategory(dbCategory: any): ServiceCategory {
    return {
      id: dbCategory.id,
      name: dbCategory.name,
      image_url: dbCategory.image_url || '',
      type: dbCategory.type || 'GUEST_SERVICE',
      display_order: dbCategory.display_order || 0,
      isActive: dbCategory.is_active ?? true,
      description: dbCategory.description || undefined,
      icon: dbCategory.icon || undefined,
      createdAt: dbCategory.created_at ? new Date(dbCategory.created_at) : undefined
    }
  }
}

// Helper functions for status mapping
function mapRoomStatus(status: string | null): 'DIRTY' | 'CLEANING' | 'CLEAN' | 'INSPECTED' | 'OUT_OF_ORDER' | 'MAINTENANCE' {
  switch (status) {
    case 'dirty': return 'DIRTY'
    case 'cleaning': return 'CLEANING'
    case 'clean': return 'CLEAN'
    case 'inspected': return 'INSPECTED'
    case 'out_of_order': return 'OUT_OF_ORDER'
    case 'maintenance': return 'MAINTENANCE'
    default: return 'DIRTY'
  }
}

function mapTaskStatus(status: string | null): 'NEW' | 'ASSIGNED' | 'IN_PROGRESS' | 'QUALITY_CHECK' | 'COMPLETED' | 'CANCELLED' {
  switch (status) {
    case 'new': return 'NEW'
    case 'assigned': return 'ASSIGNED'
    case 'in_progress': return 'IN_PROGRESS'
    case 'quality_check': return 'QUALITY_CHECK'
    case 'completed': return 'COMPLETED'
    case 'cancelled': return 'CANCELLED'
    default: return 'NEW'
  }
}

function mapTaskPriority(priority: string | null): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' {
  switch (priority) {
    case 'low': return 'LOW'
    case 'medium': return 'MEDIUM'
    case 'high': return 'HIGH'
    case 'urgent': return 'URGENT'
    default: return 'MEDIUM'
  }
}

function mapOrderStatus(status: string | null): 'NEW' | 'PREPARING' | 'ON_THE_WAY' | 'DELIVERED' | 'CANCELLED' {
  switch (status) {
    case 'pending': return 'NEW'
    case 'confirmed': return 'NEW'
    case 'preparing': return 'PREPARING'
    case 'ready': return 'ON_THE_WAY'
    case 'delivered': return 'DELIVERED'
    case 'cancelled': return 'CANCELLED'
    default: return 'NEW'
  }
}

// Management Portal Transformers
export namespace ManagementPortal {
  
  export interface Hotel {
    id: string
    name: string
    status: 'Aktif' | 'Pasif'
    plan: string
    registrationDate: string
    contactPerson: string
    email: string
    phone: string
    address: string
    mfeConfig: {
      housekeeping: boolean
      maintenance: boolean
      ordering: boolean
      events: boolean
      spaWellness: boolean
    }
  }

  export function transformHotel(dbHotel: DbHotel): Hotel {
    return {
      id: dbHotel.id,
      name: dbHotel.name,
      status: dbHotel.status === 'active' ? 'Aktif' : 'Pasif',
      plan: 'Basic', // Default plan since not in schema
      registrationDate: new Date(dbHotel.created_at || '').toLocaleDateString('tr-TR'),
      contactPerson: '', // Not available in current schema
      email: dbHotel.email || '',
      phone: dbHotel.phone || '',
      address: dbHotel.address || '',
      mfeConfig: {
        housekeeping: true,
        maintenance: true,
        ordering: true,
        events: true,
        spaWellness: false
      }
    }
  }

  export interface PlatformStats {
    totalHotels: number
    activeSubscriptions: number
    pendingRequests: number
    systemHealth: string
  }

  export function transformPlatformStats(data: any): PlatformStats {
    return {
      totalHotels: Number(data.total_hotels || 0),
      activeSubscriptions: Number(data.active_hotels || 0),
      pendingRequests: Number(data.pending_requests || 0),
      systemHealth: data.system_health || 'Excellent'
    }
  }

  export interface User {
    id: string
    fullName: string
    email: string
    role: string
    hotelName?: string
    hotelId?: string
    status: 'active' | 'inactive'
    createdAt: string
    lastLogin?: string
  }

  export function transformUser(dbUser: any): User {
    return {
      id: dbUser.id,
      fullName: dbUser.full_name || 'Unknown User',
      email: dbUser.email || '',
      role: dbUser.role || 'HOTEL_STAFF',
      hotelName: dbUser.hotels?.name || undefined,
      hotelId: dbUser.hotel_id || undefined,
      status: dbUser.is_active ? 'active' : 'inactive',
      createdAt: new Date(dbUser.created_at || '').toISOString(),
      lastLogin: dbUser.last_login ? new Date(dbUser.last_login).toISOString() : undefined
    }
  }

  export interface HotelComparison {
    id: string
    name: string
    occupancyRate: number
    revenue: number
    taskCompletionRate: number
    guestSatisfaction: number
    staffCount: number
    roomCount: number
    status: 'active' | 'inactive'
  }

  export function transformHotelComparison(data: any): HotelComparison {
    return {
      id: data.hotel_id || '',
      name: data.hotel_name || 'Unknown Hotel',
      occupancyRate: Number(data.occupancy_rate || 0),
      revenue: Number(data.total_revenue || 0),
      taskCompletionRate: Number(data.task_completion_rate || 0),
      guestSatisfaction: Number(data.guest_satisfaction || 0),
      staffCount: Number(data.staff_count || 0),
      roomCount: Number(data.room_count || 0),
      status: data.is_active ? 'active' : 'inactive'
    }
  }
}

// Customer Portal Transformers
export namespace CustomerPortal {

  export interface ServiceRequest {
    id: string
    type: string
    description: string
    status: string
    createdAt: string
    roomNumber?: string
    guestName?: string
  }

  export function transformServiceRequest(dbRequest: DbServiceRequest): ServiceRequest {
    return {
      id: dbRequest.id,
      type: dbRequest.request_type,
      description: dbRequest.description || '',
      status: dbRequest.status,
      createdAt: new Date(dbRequest.created_at).toISOString(),
      roomNumber: dbRequest.room_number || undefined,
      guestName: undefined // Not available in current schema
    }
  }

  export interface Reservation {
    id: string
    hotelName: string
    hotelAddress: string
    roomNumber: string
    roomType: string
    checkInDate: string
    checkOutDate: string
    totalPrice: number
    status: string
    reservationNumber: string
    guestEmail: string
    createdAt: string
  }

  export function transformReservation(dbReservation: any): Reservation {
    return {
      id: dbReservation.id,
      hotelName: dbReservation.hotels?.name || 'Unknown Hotel',
      hotelAddress: dbReservation.hotels?.address || '',
      roomNumber: dbReservation.rooms?.room_number || '',
      roomType: dbReservation.rooms?.room_type || 'Standard',
      checkInDate: dbReservation.check_in_date,
      checkOutDate: dbReservation.check_out_date,
      totalPrice: Number(dbReservation.total_price || 0),
      status: dbReservation.status || 'pending',
      reservationNumber: dbReservation.reservation_number || dbReservation.id,
      guestEmail: dbReservation.guest_email || '',
      createdAt: new Date(dbReservation.created_at).toISOString()
    }
  }

  export interface Order {
    id: string
    status: string
    totalAmount: number
    specialInstructions?: string
    items: Array<{
      id: string
      quantity: number
      priceAtOrder: number
      menuItemName: string
      menuItemDescription?: string
    }>
    createdAt: string
    deliveredAt?: string
  }

  export function transformOrder(dbOrder: any): Order {
    return {
      id: dbOrder.id,
      status: dbOrder.status || 'pending',
      totalAmount: Number(dbOrder.total_amount || 0),
      specialInstructions: dbOrder.special_instructions || undefined,
      items: (dbOrder.order_items || []).map((item: any) => ({
        id: item.id,
        quantity: item.quantity,
        priceAtOrder: Number(item.price_at_order || 0),
        menuItemName: item.menu_items?.name || 'Unknown Item',
        menuItemDescription: item.menu_items?.description || undefined
      })),
      createdAt: new Date(dbOrder.created_at).toISOString(),
      deliveredAt: dbOrder.delivered_at ? new Date(dbOrder.delivered_at).toISOString() : undefined
    }
  }

  export interface Activity {
    id: string
    title: string
    description: string
    location: string
    startTime: string
    endTime: string
    imageUrl?: string
    isPromoted?: boolean
    capacity?: number
    currentParticipants?: number
    tags?: string[]
    type: 'activity' | 'service'
  }

  export function transformActivity(dbActivity: any): Activity {
    const startTime = new Date(dbActivity.start_time_utc)
    const endTime = dbActivity.end_time_utc ? new Date(dbActivity.end_time_utc) : new Date(startTime.getTime() + 60 * 60 * 1000) // Default 1 hour duration

    return {
      id: dbActivity.id,
      title: dbActivity.title,
      description: dbActivity.description || '',
      location: dbActivity.location || '',
      startTime: startTime.toTimeString().slice(0, 5), // HH:mm format
      endTime: endTime.toTimeString().slice(0, 5), // HH:mm format
      imageUrl: dbActivity.image_url || undefined,
      isPromoted: dbActivity.is_promoted || false,
      capacity: dbActivity.capacity || undefined,
      currentParticipants: 0, // Would need to calculate from registrations
      tags: dbActivity.tags || [],
      type: 'activity'
    }
  }

  export interface Notification {
    id: number
    type: 'order' | 'service' | 'activity' | 'alert'
    title: string
    message: string
    timestamp: Date
    isRead: boolean
    linkTo?: string
  }

  export function transformNotification(dbNotification: any): Notification {
    return {
      id: Number(dbNotification.id),
      type: mapNotificationType(dbNotification.type),
      title: dbNotification.title || '',
      message: dbNotification.message || '',
      timestamp: new Date(dbNotification.created_at),
      isRead: dbNotification.is_read || false,
      linkTo: dbNotification.link_to || undefined
    }
  }

  export interface ServiceCategory {
    id: string
    slug: string
    name: string
    description: string
    image: string
    services: Service[]
  }

  export interface Service {
    id: number
    name: string
    description: string
    longDescription?: string
    price: number
    currency: string
    duration_minutes: number
    image: string
  }

  export function transformServiceCategory(dbCategory: any): ServiceCategory {
    return {
      id: dbCategory.id,
      slug: dbCategory.slug || dbCategory.name.toLowerCase().replace(/\s+/g, '-'),
      name: dbCategory.name,
      description: dbCategory.description || '',
      image: dbCategory.image_url || 'https://picsum.photos/800/600',
      services: [] // Services would be fetched separately
    }
  }
}

// Helper function for notification type mapping
function mapNotificationType(type: string | null): 'order' | 'service' | 'activity' | 'alert' {
  switch (type) {
    case 'order': return 'order'
    case 'service': return 'service'
    case 'activity': return 'activity'
    case 'alert': return 'alert'
    default: return 'alert'
  }
}

// App Shell Transformers
export namespace AppShell {
  
  export interface User {
    id: string
    name: string
    email: string
    role: string
    avatar?: string
    hotelId?: string
  }

  export function transformUser(dbUser: DbUserProfile & { email?: string }): User {
    return {
      id: dbUser.id,
      name: dbUser.full_name || '',
      email: dbUser.email || '', // Email comes from joined auth.users table
      role: dbUser.role || 'STAFF',
      avatar: dbUser.avatar_url || undefined,
      hotelId: dbUser.hotel_id || undefined
    }
  }

  export interface Notification {
    id: string
    title: string
    message: string
    type: string
    isRead: boolean
    timestamp: string
    priority?: string
    category?: string
    relatedId?: string
  }

  export function transformNotification(dbNotification: any, userRole?: string): Notification {
    // Handle both guest and staff notifications
    const isGuestNotification = userRole === 'GUEST' || dbNotification.guest_email

    return {
      id: dbNotification.id || '',
      title: dbNotification.title || '',
      message: dbNotification.message || '',
      type: dbNotification.notification_type || 'SYSTEM',
      isRead: isGuestNotification
        ? !!dbNotification.read_at
        : dbNotification.is_read || false,
      timestamp: new Date(dbNotification.created_at || '').toISOString(),
      priority: dbNotification.priority || 'MEDIUM',
      category: dbNotification.category || undefined,
      relatedId: dbNotification.related_task_id || dbNotification.related_order_id || dbNotification.related_request_id || undefined
    }
  }

  export interface Hotel {
    id: string
    name: string
    address: string
    phone?: string
    email?: string
    isActive: boolean
  }

  export function transformHotel(dbHotel: any): Hotel {
    return {
      id: dbHotel.id,
      name: dbHotel.name,
      address: dbHotel.address || '',
      phone: dbHotel.phone || undefined,
      email: dbHotel.email || undefined,
      isActive: dbHotel.is_active ?? true
    }
  }

  export interface HotelContext {
    currentHotel: Hotel | null
    availableHotels: Hotel[]
    canSwitchHotels: boolean
    userRole: string
  }

  export interface UserProfileUpdate {
    fullName?: string
    avatarUrl?: string
  }

  export interface GlobalStats {
    // Platform-wide stats (for super admin)
    totalHotels?: number
    totalUsers?: number
    totalOrders?: number

    // Hotel-specific stats (for hotel staff)
    totalRooms?: number
    totalTasks?: number

    // Common stats
    systemHealth: 'HEALTHY' | 'WARNING' | 'ERROR'
  }
}


