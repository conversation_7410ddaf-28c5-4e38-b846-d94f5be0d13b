<template>
  <div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
          Personel Bildirimleri
        </h1>
        <p class="text-gray-600 dark:text-gray-300">
          Gerçek zamanlı bildirimler ve görev takibi
        </p>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center space-x-3">
        <div class="flex items-center space-x-2 text-sm">
          <div class="flex items-center">
            <div 
              :class="[
                'w-2 h-2 rounded-full mr-2',
                staffNotificationStore.isConnected ? 'bg-green-500' : 'bg-red-500'
              ]"
            ></div>
            <span class="text-gray-600 dark:text-gray-300">
              {{ staffNotificationStore.isConnected ? 'Bağlı' : 'Bağlantı Yok' }}
            </span>
          </div>
        </div>
        
        <button
          @click="markAllAsRead"
          :disabled="staffNotificationStore.unreadCount === 0"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Tümünü Okundu İşaretle
        </button>
        
        <button
          @click="refreshNotifications"
          :disabled="staffNotificationStore.isLoading"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"
        >
          <ArrowPathIcon class="w-4 h-4" :class="{ 'animate-spin': staffNotificationStore.isLoading }" />
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <BellIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-gray-600 dark:text-gray-300">Toplam</p>
            <p class="text-xl font-bold text-navy-700 dark:text-white">
              {{ staffNotificationStore.notifications.length }}
            </p>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
            <ExclamationCircleIcon class="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-gray-600 dark:text-gray-300">Okunmamış</p>
            <p class="text-xl font-bold text-navy-700 dark:text-white">
              {{ staffNotificationStore.unreadCount }}
            </p>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
            <ExclamationTriangleIcon class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-gray-600 dark:text-gray-300">Acil</p>
            <p class="text-xl font-bold text-navy-700 dark:text-white">
              {{ staffNotificationStore.urgentNotifications.length }}
            </p>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <CheckCircleIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm text-gray-600 dark:text-gray-300">Eylem Gerekli</p>
            <p class="text-xl font-bold text-navy-700 dark:text-white">
              {{ staffNotificationStore.actionRequiredNotifications.length }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Filtrele:</label>
          <select 
            v-model="selectedFilter"
            class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
          >
            <option value="all">Tümü</option>
            <option value="unread">Okunmamış</option>
            <option value="urgent">Acil</option>
            <option value="action-required">Eylem Gerekli</option>
          </select>
        </div>
        
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Kategori:</label>
          <select 
            v-model="selectedCategory"
            class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
          >
            <option value="">Tüm Kategoriler</option>
            <option value="maintenance">Bakım</option>
            <option value="housekeeping">Kat Hizmetleri</option>
            <option value="service">Servis</option>
            <option value="order">Sipariş</option>
            <option value="system">Sistem</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
          Bildirimler ({{ filteredNotifications.length }})
        </h3>
      </div>
      
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div 
          v-if="staffNotificationStore.isLoading" 
          class="p-8 text-center text-gray-500 dark:text-gray-400"
        >
          <ArrowPathIcon class="w-8 h-8 animate-spin mx-auto mb-2" />
          Bildirimler yükleniyor...
        </div>
        
        <div 
          v-else-if="filteredNotifications.length === 0" 
          class="p-8 text-center text-gray-500 dark:text-gray-400"
        >
          <BellSlashIcon class="w-12 h-12 mx-auto mb-2 opacity-50" />
          Henüz bildirim bulunmuyor
        </div>
        
        <div
          v-for="notification in filteredNotifications"
          :key="notification.id"
          @click="handleNotificationClick(notification)"
          :class="[
            'p-4 hover:bg-gray-50 dark:hover:bg-navy-700 cursor-pointer transition-colors',
            !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
          ]"
        >
          <div class="flex items-start space-x-3">
            <!-- Icon -->
            <div class="flex-shrink-0">
              <div 
                :class="[
                  'w-10 h-10 rounded-full flex items-center justify-center',
                  getNotificationIconClass(notification)
                ]"
              >
                <component 
                  :is="getNotificationIcon(notification)" 
                  class="w-5 h-5" 
                />
              </div>
            </div>
            
            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-navy-700 dark:text-white truncate">
                  {{ notification.title }}
                </h4>
                <div class="flex items-center space-x-2">
                  <span 
                    v-if="notification.is_urgent"
                    class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full"
                  >
                    ACİL
                  </span>
                  <span 
                    v-if="notification.requires_action && !notification.action_taken"
                    class="px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 rounded-full"
                  >
                    EYLEM GEREKLİ
                  </span>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formatTime(notification.created_at) }}
                  </span>
                </div>
              </div>
              
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                {{ notification.message }}
              </p>
              
              <div class="flex items-center justify-between mt-2">
                <div class="flex items-center space-x-2">
                  <span 
                    v-if="notification.category"
                    class="px-2 py-1 text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded"
                  >
                    {{ getCategoryLabel(notification.category) }}
                  </span>
                  <span 
                    v-if="notification.priority"
                    :class="[
                      'px-2 py-1 text-xs rounded',
                      getPriorityClass(notification.priority)
                    ]"
                  >
                    {{ getPriorityLabel(notification.priority) }}
                  </span>
                </div>
                
                <div class="flex items-center space-x-2">
                  <button
                    v-if="!notification.is_read"
                    @click.stop="markAsRead(notification.id)"
                    class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  >
                    Okundu İşaretle
                  </button>
                  <button
                    v-if="notification.requires_action && !notification.action_taken"
                    @click.stop="markActionTaken(notification.id)"
                    class="text-xs text-green-600 hover:text-green-800 dark:text-green-400"
                  >
                    Eylem Tamamlandı
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStaffNotificationStore } from '@/stores/staffNotificationStore'
import {
  BellIcon,
  BellSlashIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  WrenchScrewdriverIcon,
  HomeIcon,
  ShoppingBagIcon,
  CogIcon,
  ClipboardDocumentListIcon
} from '@heroicons/vue/24/outline'

const staffNotificationStore = useStaffNotificationStore()

// Filters
const selectedFilter = ref('all')
const selectedCategory = ref('')

// Computed
const filteredNotifications = computed(() => {
  let filtered = staffNotificationStore.notifications
  
  // Apply filter
  switch (selectedFilter.value) {
    case 'unread':
      filtered = filtered.filter(n => !n.is_read)
      break
    case 'urgent':
      filtered = filtered.filter(n => n.is_urgent)
      break
    case 'action-required':
      filtered = filtered.filter(n => n.requires_action && !n.action_taken)
      break
  }
  
  // Apply category filter
  if (selectedCategory.value) {
    filtered = filtered.filter(n => n.category === selectedCategory.value)
  }
  
  return filtered
})

// Methods
const refreshNotifications = () => {
  staffNotificationStore.fetchNotifications()
}

const markAllAsRead = () => {
  staffNotificationStore.markAllAsRead()
}

const markAsRead = (notificationId: string) => {
  staffNotificationStore.markAsRead(notificationId)
}

const markActionTaken = (notificationId: string) => {
  staffNotificationStore.markActionTaken(notificationId)
}

const handleNotificationClick = (notification: any) => {
  if (!notification.is_read) {
    markAsRead(notification.id)
  }
  
  // Handle navigation based on notification type
  if (notification.related_task_id) {
    // Navigate to task details
    console.log('Navigate to task:', notification.related_task_id)
  } else if (notification.related_order_id) {
    // Navigate to order details
    console.log('Navigate to order:', notification.related_order_id)
  } else if (notification.related_request_id) {
    // Navigate to request details
    console.log('Navigate to request:', notification.related_request_id)
  }
}

const getNotificationIcon = (notification: any) => {
  switch (notification.category) {
    case 'maintenance':
      return WrenchScrewdriverIcon
    case 'housekeeping':
      return HomeIcon
    case 'order':
      return ShoppingBagIcon
    case 'system':
      return CogIcon
    default:
      return ClipboardDocumentListIcon
  }
}

const getNotificationIconClass = (notification: any) => {
  if (notification.is_urgent) {
    return 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
  }
  
  switch (notification.category) {
    case 'maintenance':
      return 'bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-400'
    case 'housekeeping':
      return 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
    case 'order':
      return 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
    case 'system':
      return 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-400'
    default:
      return 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
  }
}

const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    maintenance: 'Bakım',
    housekeeping: 'Kat Hizmetleri',
    service: 'Servis',
    order: 'Sipariş',
    system: 'Sistem'
  }
  return labels[category] || category
}

const getPriorityLabel = (priority: string) => {
  const labels: Record<string, string> = {
    low: 'Düşük',
    medium: 'Orta',
    high: 'Yüksek',
    urgent: 'Acil'
  }
  return labels[priority] || priority
}

const getPriorityClass = (priority: string) => {
  const classes: Record<string, string> = {
    low: 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300',
    medium: 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
    high: 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300',
    urgent: 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
  }
  return classes[priority] || classes.medium
}

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return 'Şimdi'
  if (minutes < 60) return `${minutes}dk önce`
  if (hours < 24) return `${hours}sa önce`
  if (days < 7) return `${days}g önce`
  
  return date.toLocaleDateString('tr-TR')
}

// Lifecycle
onMounted(() => {
  staffNotificationStore.initializeSubscriptions()
  staffNotificationStore.fetchNotifications()
})

onUnmounted(() => {
  staffNotificationStore.cleanup()
})
</script>
