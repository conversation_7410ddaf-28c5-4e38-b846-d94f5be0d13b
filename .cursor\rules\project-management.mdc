---
description: Hotelexia Project Management & Workflow
globs: 
alwaysApply: false
---
# Hotelexia Project Management & Workflow

## 1. Task Management
- All backend-related tasks, including schema changes, function development, and API endpoint planning, are tracked in `backend-tasklist.mdc`.
- Frontend tasks are derived from the backend task list and design requirements.

## 2. Code Quality
- **Linter:** ESLint with `@vue/eslint-config-typescript` and `@vue/eslint-config-prettier` is used to enforce a consistent code style.
- **Formatter:** Prettier is used for automatic code formatting.
- **Type Checking:** `vue-tsc --noEmit` is run as part of the build process to ensure type safety.

## 3. Build & Deployment
- **Workspace Command:** `pnpm --recursive build` or `npm run build` from the root directory builds all packages in the correct order.
- **CI/CD:** The project is structured for easy integration with CI/CD pipelines. The successful and fast build process makes it ideal for automated deployments.
- **Environment:** The application uses `.env` files for managing environment-specific variables like Supabase URL and keys.

