<template>
  <div class="space-y-2">
    <label 
      v-if="label" 
      :for="inputId" 
      :class="labelClasses"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>
    
    <div class="relative">
      <div v-if="$slots.prefix" class="absolute left-3 top-1/2 -translate-y-1/2 text-navy-500">
        <slot name="prefix" />
      </div>
      
      <input
        :id="inputId"
        v-model="inputValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :class="inputClasses"
        v-bind="$attrs"
        @blur="handleBlur"
        @focus="handleFocus"
        @input="handleInput"
      />
      
      <div v-if="$slots.suffix || showClearButton" class="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
        <button
          v-if="showClearButton && inputValue"
          @click="clearInput"
          type="button"
          class="text-navy-400 hover:text-navy-600 transition-colors"
        >
          <X class="w-4 h-4" />
        </button>
        <slot name="suffix" />
      </div>
    </div>
    
    <div v-if="error || hint" class="text-sm">
      <p v-if="error" class="text-red-600 flex items-center gap-1">
        <AlertCircle class="w-4 h-4" />
        {{ error }}
      </p>
      <p v-else-if="hint" class="text-navy-500">
        {{ hint }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, useSlots } from 'vue'
import { cva, type VariantProps } from 'class-variance-authority'
import { X, AlertCircle } from 'lucide-vue-next'
import { cn } from '../lib/utils'

const inputVariants = cva(
  "w-full rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-gray-300 bg-white focus:border-brand-500 focus:ring-brand-500/20",
        filled: "border-transparent bg-gray-100 focus:bg-white focus:border-brand-500 focus:ring-brand-500/20",
        flushed: "border-0 border-b-2 border-gray-300 rounded-none bg-transparent focus:border-brand-500 focus:ring-0",
        outline: "border-2 border-brand-200 bg-white focus:border-brand-500 focus:ring-brand-500/20",
      },
      size: {
        sm: "h-8 px-3 py-1 text-sm",
        md: "h-10 px-4 py-2",
        lg: "h-12 px-4 py-3 text-lg",
      },
      state: {
        default: "",
        error: "border-red-500 focus:border-red-500 focus:ring-red-500/20",
        success: "border-green-500 focus:border-green-500 focus:ring-green-500/20",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      state: "default",
    },
  }
)

interface InputProps extends /* @vue-ignore */ VariantProps<typeof inputVariants> {
  modelValue?: string | number
  label?: string
  placeholder?: string
  type?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  error?: string
  hint?: string
  clearable?: boolean
  id?: string
}

const props = withDefaults(defineProps<InputProps>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  clearable: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'blur': [event: FocusEvent]
  'focus': [event: FocusEvent]
  'input': [event: Event]
  'clear': []
}>()

const slots = useSlots()

const inputValue = computed({
  get: () => props.modelValue ?? '',
  set: (value) => emit('update:modelValue', value)
})

const inputId = computed(() => 
  props.id || `input-${Math.random().toString(36).substr(2, 9)}`
)

const showClearButton = computed(() => 
  props.clearable && !props.disabled && !props.readonly
)

const inputClasses = computed(() => {
  const state = props.error ? 'error' : 'default'
  const hasPrefix = !!slots.prefix
  const hasSuffix = !!slots.suffix || showClearButton.value
  
  return cn(
    inputVariants({ 
      variant: props.variant, 
      size: props.size, 
      state 
    }),
    hasPrefix && "pl-10",
    hasSuffix && "pr-10"
  )
})

const labelClasses = computed(() =>
  cn(
    "block text-sm font-medium text-navy-700",
    props.disabled && "opacity-50"
  )
)

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleInput = (event: Event) => {
  emit('input', event)
}

const clearInput = () => {
  inputValue.value = ''
  emit('clear')
}
</script> 