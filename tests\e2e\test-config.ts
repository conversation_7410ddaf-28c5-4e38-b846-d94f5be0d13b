/**
 * Test Configuration for E2E Tests
 * Centralized configuration for all test scenarios
 */

export const TEST_CONFIG = {
  // Base URLs for different MFEs
  urls: {
    appShell: 'http://localhost:5000',
    managementPortal: 'http://localhost:5001',
    hotelDashboard: 'http://localhost:5002',
    customerPortal: 'http://localhost:3001'
  },

  // Test timeouts
  timeouts: {
    navigation: 10000,
    dataLoad: 15000,
    authentication: 5000,
    apiResponse: 8000,
    pageStable: 3000
  },

  // Test user credentials (for testing purposes)
  testUsers: {
    superAdmin: {
      email: '<EMAIL>',
      password: 'test123',
      role: 'SUPER_ADMIN',
      expectedRedirect: '/management/dashboard'
    },
    hotelAdmin: {
      email: '<EMAIL>',
      password: 'test123',
      role: 'HOTEL_ADMIN',
      expectedRedirect: '/dashboard'
    },
    hotelStaff: {
      email: '<EMAIL>',
      password: 'test123',
      role: 'HOTEL_STAFF',
      expectedRedirect: '/dashboard'
    },
    guest: {
      email: '<EMAIL>',
      password: 'test123',
      role: 'GUEST',
      expectedRedirect: '/'
    }
  },

  // Routes for RBAC testing
  rbacRoutes: {
    superAdminOnly: [
      '/management/dashboard',
      '/management/hotels',
      '/management/users',
      '/management/system-settings',
      '/management/billing',
      '/management/analytics'
    ],
    hotelAdminStaff: [
      '/dashboard',
      '/housekeeping/rooms',
      '/maintenance/tasks',
      '/ordering/menu',
      '/events/calendar',
      '/spa-wellness/services'
    ],
    guestOnly: [
      '/',
      '/reservations',
      '/services',
      '/activities',
      '/profile'
    ],
    publicRoutes: [
      '/auth/login',
      '/auth/register',
      '/about',
      '/contact'
    ]
  },

  // Security test payloads
  securityPayloads: {
    xss: [
      '<script>alert("xss")</script>',
      '"><script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src=x onerror=alert("xss")>',
      '${alert("xss")}',
      '<svg onload=alert("xss")>',
      '<iframe src="javascript:alert(\'xss\')"></iframe>'
    ],
    sqlInjection: [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "admin'/*"
    ],
    pathTraversal: [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
      '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
    ]
  },

  // Data selectors for testing
  selectors: {
    auth: {
      emailInput: 'input[type="email"], input[name="email"]',
      passwordInput: 'input[type="password"], input[name="password"]',
      submitButton: 'button[type="submit"], button:has-text("Giriş"), button:has-text("Login")',
      logoutButton: 'button:has-text("Çıkış"), button:has-text("Logout"), [data-testid="logout"]',
      errorMessage: '.error, .invalid, [role="alert"], .auth-error'
    },
    navigation: {
      sidebar: 'nav, .sidebar, .navigation',
      mainContent: 'main, .main-content, [data-testid="content"]',
      breadcrumb: '.breadcrumb, .breadcrumbs, [data-testid="breadcrumb"]'
    },
    data: {
      loadingState: '.loading, .spinner, [data-testid="loading"]',
      errorState: '.error, .error-message, [data-testid="error"]',
      emptyState: '.empty, .no-data, [data-testid="empty"]',
      dataTable: 'table, .data-table, [data-testid="table"]',
      dataList: '.list, .data-list, [data-testid="list"]'
    },
    forms: {
      textInput: 'input[type="text"]',
      textarea: 'textarea',
      selectInput: 'select',
      checkbox: 'input[type="checkbox"]',
      radio: 'input[type="radio"]',
      submitButton: 'button[type="submit"], .submit-btn',
      cancelButton: 'button[type="button"]:has-text("Cancel"), .cancel-btn'
    },
    security: {
      accessDenied: 'text=/access denied/i, text=/yetkisiz/i, .unauthorized',
      sessionExpired: 'text=/session expired/i, text=/oturum süresi/i, .session-expired',
      invalidCredentials: 'text=/invalid/i, text=/incorrect/i, text=/hatalı/i'
    }
  },

  // Test data for different scenarios
  testData: {
    hotels: [
      {
        id: '1',
        name: 'Test Hotel 1',
        status: 'active',
        plan: 'premium'
      },
      {
        id: '2',
        name: 'Test Hotel 2',
        status: 'active',
        plan: 'basic'
      }
    ],
    tasks: [
      {
        title: 'Test Maintenance Task',
        description: 'Test task description',
        priority: 'high',
        type: 'maintenance'
      },
      {
        title: 'Test Housekeeping Task',
        description: 'Test housekeeping description',
        priority: 'medium',
        type: 'housekeeping'
      }
    ],
    reservations: [
      {
        guestName: 'Test Guest',
        checkIn: '2024-01-15',
        checkOut: '2024-01-20',
        roomType: 'standard'
      }
    ]
  },

  // Performance thresholds
  performance: {
    pageLoadTime: 5000,
    apiResponseTime: 3000,
    renderTime: 2000,
    interactionTime: 1000
  },

  // Browser configurations
  browsers: {
    desktop: ['chromium', 'firefox', 'webkit'],
    mobile: ['Mobile Chrome', 'Mobile Safari'],
    headless: true,
    slowMo: 0
  },

  // Test environment settings
  environment: {
    retries: 2,
    timeout: 30000,
    parallel: true,
    workers: 4,
    reporter: ['html', 'json', 'junit']
  }
}

/**
 * Helper function to get URL for specific MFE and route
 */
export function getTestUrl(mfe: keyof typeof TEST_CONFIG.urls, route: string = ''): string {
  const baseUrl = TEST_CONFIG.urls[mfe]
  return route ? `${baseUrl}${route}` : baseUrl
}

/**
 * Helper function to get test user by role
 */
export function getTestUser(role: string) {
  return Object.values(TEST_CONFIG.testUsers).find(user => user.role === role)
}

/**
 * Helper function to get routes for specific role
 */
export function getRoutesForRole(role: string): string[] {
  switch (role) {
    case 'SUPER_ADMIN':
      return TEST_CONFIG.rbacRoutes.superAdminOnly
    case 'HOTEL_ADMIN':
    case 'HOTEL_STAFF':
      return TEST_CONFIG.rbacRoutes.hotelAdminStaff
    case 'GUEST':
      return TEST_CONFIG.rbacRoutes.guestOnly
    default:
      return TEST_CONFIG.rbacRoutes.publicRoutes
  }
}

/**
 * Helper function to check if route should be accessible for role
 */
export function shouldHaveAccess(role: string, route: string): boolean {
  const allowedRoutes = getRoutesForRole(role)
  const publicRoutes = TEST_CONFIG.rbacRoutes.publicRoutes
  
  return allowedRoutes.some(allowed => route.includes(allowed)) || 
         publicRoutes.some(pub => route.includes(pub))
}

/**
 * Helper function to get security test payloads
 */
export function getSecurityPayloads(type: keyof typeof TEST_CONFIG.securityPayloads): string[] {
  return TEST_CONFIG.securityPayloads[type]
}

export default TEST_CONFIG
