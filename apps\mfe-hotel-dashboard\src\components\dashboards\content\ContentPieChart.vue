<template>
  <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card p-6 border border-gray-200 dark:border-navy-700">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-bold text-navy-700 dark:text-white">
        <PERSON>çerik Dağılımı
      </h3>
      <ChartPieIcon class="w-5 h-5 text-brand-500" />
    </div>
    
    <div class="flex items-center justify-center">
      <!-- Simple SVG Pie Chart -->
      <div class="relative w-48 h-48">
        <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="none"
            stroke="currentColor"
            stroke-width="8"
            class="text-gray-200 dark:text-navy-600"
          />
          <circle
            v-for="(segment, index) in pieSegments"
            :key="index"
            cx="50"
            cy="50"
            r="40"
            fill="none"
            :stroke="segment.color"
            stroke-width="8"
            :stroke-dasharray="`${segment.percentage * 2.51} 251.2`"
            :stroke-dashoffset="segment.offset"
            class="transition-all duration-1000 ease-in-out"
          />
        </svg>
        
        <!-- Center text -->
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="text-2xl font-bold text-navy-700 dark:text-white">
              {{ totalItems }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Toplam İçerik
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Legend -->
    <div class="mt-6 space-y-3">
      <div
        v-for="item in contentDistribution"
        :key="item.name"
        class="flex items-center justify-between"
      >
        <div class="flex items-center">
          <div 
            class="w-3 h-3 rounded-full mr-3"
            :style="{ backgroundColor: getItemColor(item.name) }"
          ></div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ item.name }}
          </span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm font-bold text-navy-700 dark:text-white">
            {{ item.value }}
          </span>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            ({{ getPercentage(item.value) }}%)
          </span>
        </div>
      </div>
    </div>
    
    <!-- Empty state -->
    <div v-if="totalItems === 0" class="text-center py-8">
      <ChartPieIcon class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Henüz veri yok</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">İçerik eklendiğinde görünecek.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChartPieIcon } from '@heroicons/vue/24/outline'

interface ContentDistributionItem {
  name: string
  value: number
}

interface Props {
  contentDistribution: ContentDistributionItem[]
}

const props = defineProps<Props>()

const colors = [
  '#6366F1', // Indigo (brand color)
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#8B5CF6', // Violet
  '#EF4444', // Red
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#F97316'  // Orange
]

const totalItems = computed(() => {
  return props.contentDistribution.reduce((sum, item) => sum + item.value, 0)
})

const pieSegments = computed(() => {
  let cumulativePercentage = 0
  
  return props.contentDistribution.map((item, index) => {
    const percentage = totalItems.value > 0 ? (item.value / totalItems.value) * 100 : 0
    const offset = -cumulativePercentage * 2.51
    
    cumulativePercentage += percentage
    
    return {
      percentage,
      offset,
      color: colors[index % colors.length]
    }
  })
})

const getItemColor = (name: string): string => {
  const index = props.contentDistribution.findIndex(item => item.name === name)
  return colors[index % colors.length]
}

const getPercentage = (value: number): string => {
  if (totalItems.value === 0) return '0'
  return Math.round((value / totalItems.value) * 100).toString()
}
</script> 