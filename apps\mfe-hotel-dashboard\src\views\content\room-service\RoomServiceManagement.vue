<template>
  <div class="flex gap-6 h-full min-h-[600px]">
    <!-- Left Column: Category Management (30%) -->
    <div class="w-[30%] bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
          Oda Servisi Kategorileri
        </h3>
        <button
          @click="showCategoryModal = true"
          class="bg-brand-500 hover:bg-brand-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center"
        >
          <PlusIcon class="w-4 h-4 mr-1" />
          Ekle
        </button>
      </div>

      <!-- Category List -->
      <div class="space-y-2">
        <!-- All Products Option -->
        <div
          @click="selectedCategoryId = null"
          :class="[
            'cursor-pointer px-4 py-3 rounded-lg transition-colors flex items-center justify-between group',
            selectedCategoryId === null
              ? 'bg-brand-50 border-l-4 border-brand-500 text-brand-700 dark:bg-brand-900/20 dark:text-brand-300'
              : 'hover:bg-gray-50 dark:hover:bg-navy-600 text-gray-700 dark:text-gray-300'
          ]"
        >
          <div class="flex items-center">
            <component :is="ListBulletIcon" class="w-5 h-5 mr-3" />
            <span class="font-medium">Tüm Ürünler</span>
          </div>
          <span class="text-sm text-gray-500 dark:text-gray-400">
            ({{ store.menuItems.length }})
          </span>
        </div>

        <!-- Room Service Categories -->
        <div
          v-for="category in roomServiceCategories"
          :key="category.id"
          @click="selectedCategoryId = category.id"
          :class="[
            'cursor-pointer px-4 py-3 rounded-lg transition-colors flex items-center justify-between group',
            selectedCategoryId === category.id
              ? 'bg-brand-50 border-l-4 border-brand-500 text-brand-700 dark:bg-brand-900/20 dark:text-brand-300'
              : 'hover:bg-gray-50 dark:hover:bg-navy-600 text-gray-700 dark:text-gray-300'
          ]"
        >
          <div class="flex items-center min-w-0 flex-1">
            <div class="w-8 h-8 mr-3 flex-shrink-0 rounded-lg overflow-hidden">
              <img 
                :src="category.image_url || 'https://via.placeholder.com/32x32.png?text=' + category.name.charAt(0)" 
                :alt="category.name"
                class="w-full h-full object-cover"
              />
            </div>
            <div class="min-w-0">
              <div class="font-medium truncate">{{ category.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                Sıra: {{ category.display_order }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2 ml-2">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              ({{ getProductsForCategory(category.id).length }})
            </span>
            <div class="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
              <button
                @click.stop="editCategory(category)"
                class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300 p-1"
                title="Düzenle"
              >
                <PencilIcon class="w-4 h-4" />
              </button>
              <button
                @click.stop="deleteCategory(category.id)"
                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1"
                title="Sil"
              >
                <TrashIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column: Product Management (70%) -->
    <div class="w-[70%] bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
      <!-- Header with Add Button -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-lg font-semibold text-navy-700 dark:text-white">
            {{ selectedCategoryName }} Ürünleri
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ selectedCategoryId ? 'Seçili kategorideki ürünleri yönetin' : 'Tüm oda servisi ürünlerini yönetin' }}
          </p>
        </div>
        <button
          @click="showProductModal = true"
          class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
        >
          <PlusIcon class="w-5 h-5 mr-2" />
          Yeni Ürün Ekle
        </button>
      </div>

      <!-- Products Table -->
      <div class="bg-gray-50 dark:bg-navy-600 rounded-lg overflow-hidden">
        <div v-if="filteredProducts.length > 0" class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-100 dark:bg-navy-500">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Görsel
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Ürün Bilgileri
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Kategori
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Fiyat & Süre
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Özellikler
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Durum
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
              <tr
                v-for="item in filteredProducts"
                :key="item.id"
                class="hover:bg-gray-50 dark:hover:bg-navy-600 transition-colors"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <img
                    :src="item.image"
                    :alt="item.name"
                    class="w-12 h-12 rounded-lg object-cover"
                  >
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ item.name }}
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 font-mono">
                    {{ (item as any).item_code || 'N/A' }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                    {{ item.description }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {{ item.category }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    ₺{{ item.price.toFixed(2) }}
                  </div>
                  <div v-if="(item as any).preparation_time_minutes" class="text-xs text-gray-500 dark:text-gray-400">
                    {{ (item as any).preparation_time_minutes }} dakika
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-wrap gap-1">
                    <span v-if="(item as any).is_vegetarian" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      🌱 Vejetaryen
                    </span>
                    <span v-if="(item as any).is_vegan" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      🌿 Vegan
                    </span>
                    <span v-if="(item as any).is_gluten_free" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                      🌾 Glutensiz
                    </span>
                  </div>
                  <div v-if="(item as any).tags && (item as any).tags.length > 0" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ (item as any).tags.slice(0, 2).join(', ') }}{{ (item as any).tags.length > 2 ? '...' : '' }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <button
                    @click="toggleItemAvailability(item.id)"
                    :disabled="isLoadingToggle === item.id"
                    class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 disabled:opacity-50"
                    :class="item.isAvailable ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
                  >
                    <span
                      v-if="isLoadingToggle === item.id"
                      class="absolute inset-0 flex items-center justify-center"
                    >
                      <svg class="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </span>
                    <span
                      v-else
                      class="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                      :class="(item as any).isAvailable ? 'translate-x-5' : 'translate-x-0'"
                    ></span>
                  </button>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    @click="editProduct(item)"
                    :disabled="store.isLoading"
                    class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300 disabled:opacity-50"
                  >
                    Düzenle
                  </button>
                  <button
                    @click="deleteProduct(item.id)"
                    :disabled="isLoadingDelete === item.id || store.isLoading"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                  >
                    <span v-if="isLoadingDelete === item.id">
                      <svg class="animate-spin inline h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Siliniyor...
                    </span>
                    <span v-else>Sil</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div
          v-else
          class="text-center py-12 bg-white dark:bg-navy-700"
        >
          <component :is="DocumentIcon" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            {{ selectedCategoryId ? 'Bu kategoride ürün bulunmuyor' : 'Henüz ürün bulunmuyor' }}
          </h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {{ selectedCategoryId ? 'Bu kategoriye yeni ürün ekleyerek başlayın.' : 'İlk ürününüzü ekleyerek başlayın.' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Category Modal -->
    <CategoryModal
      v-if="showCategoryModal"
      :category="editingCategory"
      :category-type="'ROOM_SERVICE'"
      @close="closeCategoryModal"
      @save="saveCategory"
    />

    <!-- Product Modal -->
    <MenuItemModal
      v-if="showProductModal"
      :item="editingProduct"
      @close="closeProductModal"
      @save="saveProduct"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  ListBulletIcon,
  DocumentIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import MenuItemModal from '@/components/modals/MenuItemModal.vue'
import CategoryModal from '@/components/modals/CategoryModal.vue'
import type { MenuItem, CreateMenuItemData, ServiceCategory } from '@/types/management'

const store = useManagementStore()

// State
const selectedCategoryId = ref<string | null>(null)
const showCategoryModal = ref(false)
const showProductModal = ref(false)
const editingCategory = ref<ServiceCategory | null>(null)
const editingProduct = ref<MenuItem | null>(null)
const isLoadingToggle = ref<string | null>(null)
const isLoadingDelete = ref<string | null>(null)

// Get current hotel ID from auth store profile
const currentHotelId = ref<string>('4dfec6ed-6ad4-4591-b60c-4f68c74ec150')

// Initialize data on mount
onMounted(async () => {
  if (currentHotelId.value) {
    try {
      await Promise.all([
        store.fetchCategories(currentHotelId.value),
        store.fetchMenuItems(currentHotelId.value)
      ])
    } catch (error) {
      console.error('Error loading room service data:', error)
    }
  }
})

// Computed properties
const roomServiceCategories = computed(() => 
  store.categories.filter(cat => cat.type === 'ROOM_SERVICE')
)

const selectedCategoryName = computed(() => {
  if (!selectedCategoryId.value) return 'Tüm'
  const category = roomServiceCategories.value.find(cat => cat.id === selectedCategoryId.value)
  return category?.name || 'Tüm'
})

const filteredProducts = computed(() => {
  if (!selectedCategoryId.value) return store.menuItems
  
  return store.menuItems.filter(item => (item as any).category_id === selectedCategoryId.value)
})

// Helper functions
const getProductsForCategory = (categoryId: string) => {
  return store.menuItems.filter(item => (item as any).category_id === categoryId)
}

// Category management
const editCategory = (category: ServiceCategory) => {
  editingCategory.value = category
  showCategoryModal.value = true
}

const deleteCategory = async (categoryId: string) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  const productsInCategory = getProductsForCategory(categoryId).length
  if (productsInCategory > 0) {
    alert(`Bu kategori silinemez. Bu kategoride ${productsInCategory} ürün bulunmaktadır. Önce ürünleri silin veya başka kategoriye taşıyın.`)
    return
  }
  
  if (confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')) {
    try {
      await store.deleteCategory(categoryId, currentHotelId.value)
      if (selectedCategoryId.value === categoryId) {
        selectedCategoryId.value = null
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('Kategori silinirken bir hata oluştu. Lütfen tekrar deneyin.')
    }
  }
}

const closeCategoryModal = () => {
  showCategoryModal.value = false
  editingCategory.value = null
}

const saveCategory = async (categoryData: Omit<ServiceCategory, 'id'>) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  try {
    if (editingCategory.value) {
      await store.updateCategory({ ...editingCategory.value, ...categoryData }, currentHotelId.value)
    } else {
      await store.addCategory(categoryData, currentHotelId.value)
    }
    closeCategoryModal()
  } catch (error) {
    console.error('Error saving category:', error)
    alert('Kategori kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}

// Product management with Supabase integration
const editProduct = (product: MenuItem) => {
  editingProduct.value = product
  showProductModal.value = true
}

const deleteProduct = async (productId: string) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  if (confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
    isLoadingDelete.value = productId
    try {
      await store.deleteMenuItem(productId, currentHotelId.value)
    } catch (error) {
      console.error('Error deleting menu item:', error)
      alert('Ürün silinirken bir hata oluştu. Lütfen tekrar deneyin.')
    } finally {
      isLoadingDelete.value = null
    }
  }
}

const toggleItemAvailability = async (productId: string) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  isLoadingToggle.value = productId
  try {
    await store.toggleMenuItemAvailability(productId, currentHotelId.value)
  } catch (error) {
    console.error('Error toggling menu item availability:', error)
    alert('Ürün durumu güncellenirken bir hata oluştu. Lütfen tekrar deneyin.')
  } finally {
    isLoadingToggle.value = null
  }
}

const closeProductModal = () => {
  showProductModal.value = false
  editingProduct.value = null
}

const saveProduct = async (productData: CreateMenuItemData) => {
  if (!currentHotelId.value) {
    alert('Otel bilgisi bulunamadı!')
    return
  }

  try {
    if (editingProduct.value) {
      await store.updateMenuItem(editingProduct.value.id, productData, currentHotelId.value)
    } else {
      await store.addMenuItem(productData, currentHotelId.value)
    }
    closeProductModal()
  } catch (error) {
    console.error('Error saving menu item:', error)
    alert('Ürün kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style> 