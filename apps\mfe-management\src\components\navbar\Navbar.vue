<template>
  <nav data-testid="navbar" class="bg-white dark:bg-navy-800 border-b border-gray-200 dark:border-gray-700 px-4 md:px-10 py-4">
    <div class="flex items-center justify-between">
      <!-- Left Side - Hamburger Menu + Breadcrumbs -->
      <div class="flex items-center space-x-3">
        <!-- Hamburger Menu -->
        <button 
          @click="store.toggleSidebar()"
          class="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
        >
          <Bars3Icon class="h-5 w-5" />
        </button>
        
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <span>Süper Admin</span>
          <span class="mx-2">/</span>
          <span class="text-navy-700 dark:text-white font-medium">{{ currentPageTitle }}</span>
        </div>
      </div>

      <!-- Right Side - Actions -->
      <div class="flex items-center space-x-4">
        <!-- Platform Status -->
        <div class="hidden md:flex items-center space-x-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span class="text-sm font-medium text-green-700 dark:text-green-300">Platform Aktif</span>
        </div>

        <!-- Search -->
        <div class="relative hidden md:block">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="search"
            placeholder="Otel ara..."
            class="w-64 pl-10 pr-4 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
        </div>

        <!-- Notifications -->
        <button class="relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors">
          <BellIcon class="h-5 w-5" />
          <span class="absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full"></span>
        </button>

        <!-- Dark Mode Toggle -->
        <button 
          @click="toggleDarkMode"
          class="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
        >
          <SunIcon v-if="store.darkMode" class="h-5 w-5" />
          <MoonIcon v-else class="h-5 w-5" />
        </button>

        <!-- User Profile -->
        <div class="relative">
          <button
            data-testid="user-menu-button"
            @click="showUserMenu = !showUserMenu"
            class="flex items-center space-x-3 p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
          >
            <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-medium">{{ userInitials }}</span>
            </div>
            <div class="hidden md:block text-left">
              <div class="text-sm font-medium text-navy-700 dark:text-white">{{ userDisplayName }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ userRole }}</div>
            </div>
          </button>

          <!-- User Dropdown Menu -->
          <div 
            v-if="showUserMenu"
            class="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-navy-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
          >
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700">Profil</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700">Sistem Ayarları</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700">Güvenlik</a>
            <hr class="my-1 border-gray-200 dark:border-gray-600">
            <button
              @click="handleLogout"
              class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-navy-700"
            >
              Çıkış Yap
            </button>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  MagnifyingGlassIcon,
  BellIcon,
  SunIcon,
  MoonIcon,
  Bars3Icon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import { useAuthStore } from '@/stores/authStore'

const store = useManagementStore()
const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()
const showUserMenu = ref(false)

// Get user info from session
const getUserInfo = () => {
  try {
    const userSession = sessionStorage.getItem('hotelexia_management_user')
    if (userSession) {
      return JSON.parse(userSession)
    }
  } catch (error) {
    console.error('Error parsing user session:', error)
  }
  return null
}

const userInfo = getUserInfo()

// Computed properties for user display
const userDisplayName = computed(() => {
  return userInfo?.fullName || userInfo?.email || 'Süper Admin'
})

const userRole = computed(() => {
  return userInfo?.role === 'SUPER_ADMIN' ? 'Platform Yöneticisi' : userInfo?.role || 'Yönetici'
})

const userInitials = computed(() => {
  if (userInfo?.fullName) {
    return userInfo.fullName
      .split(' ')
      .map((name: string) => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }
  if (userInfo?.email) {
    return userInfo.email.charAt(0).toUpperCase()
  }
  return 'SA'
})

// Computed
const currentPageTitle = computed(() => {
  switch (route.path) {
    case '/dashboard':
      return 'Dashboard'
    case '/hotels':
      return 'Oteller'
    case '/settings':
      return 'Ayarlar'
    default:
      if (route.path.includes('/hotels/')) {
        if (route.path.includes('/edit')) return 'Otel Düzenle'
        if (route.path.includes('/mfes')) return 'MFE Yönetimi'
      }
      return 'Dashboard'
  }
})

// Methods
const toggleDarkMode = () => {
  store.toggleDarkMode()
}

// Handle logout
const handleLogout = async () => {
  try {
    // Close user menu first
    showUserMenu.value = false

    // Use auth store's signOut method for proper cleanup
    await authStore.signOut()

    // Redirect to login
    router.push('/auth/login')

    console.log('Management Portal logout successful')
  } catch (error) {
    console.error('Logout error:', error)
    // Even if logout fails, ensure we redirect to login
    router.push('/auth/login')
  }
}

const closeUserMenu = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserMenu.value = false
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', closeUserMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', closeUserMenu)
})
</script> 