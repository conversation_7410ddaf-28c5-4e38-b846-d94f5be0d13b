<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full">
      <!-- Header Section -->
      <div class="text-center mb-8">
        <div class="mx-auto h-16 w-16 bg-white rounded-2xl flex items-center justify-center shadow-2xl mb-6">
          <svg class="h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm6 16H8a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h2v1a1 1 0 0 0 2 0V9h2v12a1 1 0 0 1-1 1z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-bold text-white mb-2">
          Otel Personeli Girişi
        </h1>
        <p class="text-blue-200 text-lg">
          Otel yönetim paneline erişmek için giriş yapın
        </p>
      </div>

      <!-- Login Card -->
      <div class="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8">
      
        <form class="space-y-6" @submit.prevent="handleLogin" data-testid="login-form">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-white mb-2">
              E-posta Adresi
            </label>
            <div class="relative">
              <input
                id="email"
                v-model="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                :disabled="isLoading"
                class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200 disabled:opacity-50"
                placeholder="<EMAIL>"
                data-testid="email-input"
              />
              <svg class="absolute right-3 top-3 h-5 w-5 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            </div>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-white mb-2">
              Şifre
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="password"
                name="password"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="current-password"
                required
                :disabled="isLoading"
                class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200 disabled:opacity-50 pr-12"
                placeholder="••••••••"
                data-testid="password-input"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                :disabled="isLoading"
                class="absolute right-3 top-3 text-white/40 hover:text-white/60 transition-colors"
              >
                <svg v-if="!showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="p-4 bg-red-500/20 border border-red-500/30 rounded-xl backdrop-blur-sm" data-testid="error-message">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-200">
                  Giriş Hatası
                </h3>
                <div class="mt-2 text-sm text-red-100">
                  {{ errorMessage }}
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] shadow-lg"
            :disabled="isLoading"
            data-testid="login-button"
          >
            <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            <span v-else class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="h-5 w-5 text-white/80 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </span>
            {{ isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap' }}
          </button>

        <!-- Development Mode Notice -->
        <div v-if="isDevelopment" class="mt-4 p-3 bg-yellow-100 border border-yellow-400 rounded-md">
          <p class="text-sm text-yellow-800">
            <strong>Geliştirme Modu:</strong> Test için aşağıdaki bilgileri kullanabilirsiniz:
          </p>
          <p class="text-xs text-yellow-700 mt-1">
            E-posta: <EMAIL><br>
            Şifre: password123
          </p>
        </div>
        </form>

        <!-- Portal Info -->
        <div class="mt-8 text-center">
          <p class="text-white/60 text-sm">
            Otel yönetim sistemine hoş geldiniz
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const router = useRouter()
const authStore = useAuthStore()

// Form state
const email = ref('')
const password = ref('')
const errorMessage = ref('')
const showPassword = ref(false)

// Development mode check
const isDevelopment = import.meta.env.DEV

// Use loading state from auth store
const isLoading = authStore.isLoading

// Handle login
const handleLogin = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = 'Lütfen e-posta ve şifre alanlarını doldurun.'
    return
  }

  errorMessage.value = ''

  const success = await authStore.login(email.value, password.value)

  if (success) {
    // Check for stored redirect path
    const redirectPath = sessionStorage.getItem('hotelexia_hotel_redirect')
    if (redirectPath) {
      sessionStorage.removeItem('hotelexia_hotel_redirect')
      router.push(redirectPath)
      return
    }

    // Default redirect to dashboard
    router.push('/dashboard')
  } else {
    // Map auth store errors to user-friendly messages
    const error = authStore.error
    if (error?.includes('Invalid login credentials')) {
      errorMessage.value = 'E-posta adresi veya şifre hatalı.'
    } else if (error?.includes('Email not confirmed')) {
      errorMessage.value = 'E-posta adresinizi doğrulamanız gerekiyor.'
    } else if (error?.includes('Too many requests')) {
      errorMessage.value = 'Çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.'
    } else {
      errorMessage.value = error || 'Giriş sırasında bir hata oluştu.'
    }
  }
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
