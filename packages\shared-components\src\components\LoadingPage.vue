<template>
  <div class="loading-page" :class="containerClasses">
    <!-- Main Loading Content -->
    <div class="loading-content">
      <!-- Logo/Brand -->
      <div v-if="showLogo" class="loading-logo">
        <div class="logo-placeholder">
          <h2 class="logo-text">Hotelexia</h2>
        </div>
      </div>
      
      <!-- Loading Animation -->
      <div class="loading-animation">
        <div v-if="variant === 'spinner'" class="spinner-container">
          <div class="spinner"></div>
        </div>
        
        <div v-else-if="variant === 'dots'" class="dots-container">
          <div class="dot" v-for="i in 3" :key="i" :style="{ animationDelay: `${i * 0.2}s` }"></div>
        </div>
        
        <div v-else-if="variant === 'pulse'" class="pulse-container">
          <div class="pulse-circle"></div>
        </div>
        
        <div v-else-if="variant === 'bars'" class="bars-container">
          <div class="bar" v-for="i in 4" :key="i" :style="{ animationDelay: `${i * 0.1}s` }"></div>
        </div>
        
        <!-- Default: Progress bar -->
        <div v-else class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
        </div>
      </div>
      
      <!-- Loading Message -->
      <div class="loading-message">
        <h2 v-if="title" class="loading-title">{{ title }}</h2>
        <p class="loading-text">{{ currentMessage }}</p>
        
        <!-- Progress indicator -->
        <div v-if="showProgress && variant === 'progress'" class="progress-info">
          <span class="progress-percentage">{{ Math.round(progress) }}%</span>
        </div>
      </div>
      
      <!-- Timeout Warning -->
      <div v-if="showTimeoutWarning" class="timeout-warning">
        <ExclamationTriangleIcon class="w-5 h-5 text-yellow-500 mr-2" />
        <span class="text-sm text-yellow-600 dark:text-yellow-400">
          Yükleme beklenenden uzun sürüyor...
        </span>
      </div>
      
      <!-- Action Buttons -->
      <div v-if="showActions" class="loading-actions">
        <button 
          v-if="showRetry"
          @click="handleRetry" 
          class="action-button retry-button"
          :disabled="isRetrying"
        >
          <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isRetrying }" />
          {{ isRetrying ? 'Deneniyor...' : 'Tekrar Dene' }}
        </button>
        
        <button 
          v-if="showCancel"
          @click="handleCancel" 
          class="action-button cancel-button"
        >
          <XMarkIcon class="w-4 h-4 mr-2" />
          İptal
        </button>
      </div>
    </div>
    
    <!-- Background Pattern (optional) -->
    <div v-if="showPattern" class="background-pattern">
      <div class="pattern-grid"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { AlertTriangle as ExclamationTriangleIcon, RotateCw as ArrowPathIcon, X as XMarkIcon } from 'lucide-vue-next'

interface Props {
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'progress'
  title?: string
  message?: string
  messages?: string[]
  progress?: number
  showProgress?: boolean
  showLogo?: boolean
  showPattern?: boolean
  showActions?: boolean
  showRetry?: boolean
  showCancel?: boolean
  fullscreen?: boolean
  timeoutWarning?: number // Show warning after X seconds
  messageRotationInterval?: number // Rotate messages every X seconds
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'spinner',
  message: 'Yükleniyor...',
  messages: () => [],
  progress: 0,
  showProgress: false,
  showLogo: true,
  showPattern: false,
  showActions: false,
  showRetry: false,
  showCancel: false,
  fullscreen: true,
  timeoutWarning: 10000, // 10 seconds
  messageRotationInterval: 3000 // 3 seconds
})

const emit = defineEmits<{
  retry: []
  cancel: []
  timeout: []
}>()

// State
const currentMessageIndex = ref(0)
const showTimeoutWarning = ref(false)
const isRetrying = ref(false)
const startTime = ref(Date.now())

// Computed
const containerClasses = computed(() => ({
  'loading-fullscreen': props.fullscreen,
  'loading-inline': !props.fullscreen,
  'with-pattern': props.showPattern
}))

const currentMessage = computed(() => {
  if (props.messages.length > 0) {
    return props.messages[currentMessageIndex.value] || props.message
  }
  return props.message
})

// Timers
let messageTimer: NodeJS.Timeout | null = null
let timeoutTimer: NodeJS.Timeout | null = null

// Message rotation
const startMessageRotation = () => {
  if (props.messages.length <= 1) return
  
  messageTimer = setInterval(() => {
    currentMessageIndex.value = (currentMessageIndex.value + 1) % props.messages.length
  }, props.messageRotationInterval)
}

const stopMessageRotation = () => {
  if (messageTimer) {
    clearInterval(messageTimer)
    messageTimer = null
  }
}

// Timeout warning
const startTimeoutWarning = () => {
  if (props.timeoutWarning <= 0) return
  
  timeoutTimer = setTimeout(() => {
    showTimeoutWarning.value = true
    emit('timeout')
  }, props.timeoutWarning)
}

const stopTimeoutWarning = () => {
  if (timeoutTimer) {
    clearTimeout(timeoutTimer)
    timeoutTimer = null
  }
}

// Event handlers
const handleRetry = () => {
  isRetrying.value = true
  showTimeoutWarning.value = false
  startTime.value = Date.now()
  
  // Reset timers
  stopTimeoutWarning()
  startTimeoutWarning()
  
  emit('retry')
  
  // Reset retry state after a delay
  setTimeout(() => {
    isRetrying.value = false
  }, 1000)
}

const handleCancel = () => {
  emit('cancel')
}

// Watchers
watch(() => props.messages, () => {
  currentMessageIndex.value = 0
  stopMessageRotation()
  startMessageRotation()
}, { immediate: true })

// Lifecycle
onMounted(() => {
  startMessageRotation()
  startTimeoutWarning()
})

onUnmounted(() => {
  stopMessageRotation()
  stopTimeoutWarning()
})
</script>

<style scoped>
.loading-page {
  @apply flex items-center justify-center relative overflow-hidden;
}

.loading-fullscreen {
  @apply fixed inset-0 bg-white dark:bg-navy-900 z-50;
}

.loading-inline {
  @apply min-h-64 bg-gray-50 dark:bg-navy-800 rounded-lg;
}

.loading-content {
  @apply relative z-10 text-center max-w-md mx-auto px-6;
}

.loading-logo {
  @apply mb-8;
}

.logo-placeholder {
  @apply flex items-center justify-center;
}

.logo-text {
  @apply text-2xl font-bold text-blue-600 dark:text-blue-400;
}

.loading-animation {
  @apply mb-6;
}

/* Spinner Animation */
.spinner-container {
  @apply flex justify-center;
}

.spinner {
  @apply w-12 h-12 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
}

/* Dots Animation */
.dots-container {
  @apply flex justify-center space-x-2;
}

.dot {
  @apply w-3 h-3 bg-blue-600 rounded-full;
  animation: dot-bounce 1.4s ease-in-out infinite both;
}

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Pulse Animation */
.pulse-container {
  @apply flex justify-center;
}

.pulse-circle {
  @apply w-12 h-12 bg-blue-600 rounded-full;
  animation: pulse-scale 2s ease-in-out infinite;
}

@keyframes pulse-scale {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* Bars Animation */
.bars-container {
  @apply flex justify-center space-x-1;
}

.bar {
  @apply w-2 h-8 bg-blue-600 rounded;
  animation: bar-stretch 1.2s ease-in-out infinite;
}

@keyframes bar-stretch {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

/* Progress Bar */
.progress-container {
  @apply w-full max-w-xs mx-auto;
}

.progress-bar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-blue-600 rounded-full transition-all duration-300 ease-out;
}

/* Loading Message */
.loading-message {
  @apply mb-6;
}

.loading-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.loading-text {
  @apply text-gray-600 dark:text-gray-300 text-sm leading-relaxed;
}

.progress-info {
  @apply mt-2;
}

.progress-percentage {
  @apply text-xs font-medium text-blue-600 dark:text-blue-400;
}

/* Timeout Warning */
.timeout-warning {
  @apply flex items-center justify-center mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg;
}

/* Action Buttons */
.loading-actions {
  @apply flex justify-center space-x-3;
}

.action-button {
  @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors;
}

.retry-button {
  @apply bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.cancel-button {
  @apply bg-gray-600 text-white hover:bg-gray-700;
}

/* Background Pattern */
.background-pattern {
  @apply absolute inset-0 opacity-5;
}

.pattern-grid {
  @apply w-full h-full;
  background-image: radial-gradient(circle, #3b82f6 1px, transparent 1px);
  background-size: 20px 20px;
  animation: pattern-move 20s linear infinite;
}

@keyframes pattern-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(20px, 20px);
  }
}

/* Dark mode adjustments */
.dark .spinner {
  @apply border-gray-600 border-t-blue-400;
}

.dark .dot {
  @apply bg-blue-400;
}

.dark .pulse-circle {
  @apply bg-blue-400;
}

.dark .bar {
  @apply bg-blue-400;
}

.dark .progress-fill {
  @apply bg-blue-400;
}
</style>
