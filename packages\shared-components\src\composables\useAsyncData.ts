import { ref, computed, watch } from 'vue'
import type { Ref } from 'vue'

// Mock dataCacheService until shared-supabase-client is properly built
const dataCacheService = {
  get: async <T>(key: string, fetcher: () => Promise<T>, options?: any): Promise<T> => {
    return await fetcher()
  },
  has: (key: string): boolean => false,
  delete: (key: string): void => {},
  clear: (): void => {}
}

export interface UseAsyncDataOptions<T> {
  immediate?: boolean
  resetOnExecute?: boolean
  shallow?: boolean
  server?: boolean
  default?: () => T | null
  transform?: (data: any) => T
  pick?: string[]
  watch?: Ref<any>[]
  retry?: number
  retryDelay?: number
  timeout?: number
  onError?: (error: Error) => void
  onSuccess?: (data: T) => void
  // Caching options
  cache?: boolean | any
  cacheKey?: string
}

export interface AsyncDataState<T> {
  data: Ref<T | null>
  pending: Ref<boolean>
  error: Ref<Error | null>
  refresh: () => Promise<void>
  execute: () => Promise<void>
  clear: () => void
  // Cache-related
  isCached: Ref<boolean>
  cacheKey: string | null
  invalidateCache: () => void
}

export function useAsyncData<T>(
  key: string,
  handler: () => Promise<T>,
  options: UseAsyncDataOptions<T> = {}
): AsyncDataState<T> {
  const {
    immediate = true,
    resetOnExecute = true,
    default: defaultFn,
    transform,
    retry = 0,
    retryDelay = 1000,
    timeout = 30000,
    onError,
    onSuccess,
    watch: watchSources = [],
    cache = false,
    cacheKey
  } = options

  // State
  const data = ref<T | null>(defaultFn ? defaultFn() : null) as Ref<T | null>
  const pending = ref(false)
  const error = ref<Error | null>(null)
  const isCached = ref(false)

  // Cache configuration
  const finalCacheKey = cacheKey || `async-data:${key}`
  const cacheOptions = typeof cache === 'boolean' ? {} : cache
  const shouldCache = cache !== false

  // Retry state
  let retryCount = 0
  let timeoutId: NodeJS.Timeout | null = null
  let abortController: AbortController | null = null

  // Execute the async operation
  const execute = async (): Promise<void> => {
    if (pending.value) return

    if (resetOnExecute) {
      error.value = null
    }

    pending.value = true
    retryCount = 0
    isCached.value = false

    try {
      if (shouldCache) {
        // Use cache service
        const result = await dataCacheService.get(
          finalCacheKey,
          handler,
          cacheOptions
        )

        // Check if data came from cache
        isCached.value = dataCacheService.has(finalCacheKey)

        // Transform data if needed
        const finalData = transform ? transform(result) : result
        data.value = finalData

        // Call success callback
        if (onSuccess) {
          onSuccess(finalData)
        }
      } else {
        // Execute without caching
        await executeWithRetry()
      }
    } catch (err) {
      const finalError = err instanceof Error ? err : new Error(String(err))
      error.value = finalError

      if (onError) {
        onError(finalError)
      } else {
        console.error(`AsyncData[${key}] error:`, finalError)
      }
    } finally {
      pending.value = false
      cleanup()
    }
  }

  // Execute with retry logic
  const executeWithRetry = async (): Promise<void> => {
    while (retryCount <= retry) {
      try {
        // Create abort controller for timeout
        abortController = new AbortController()
        
        // Set timeout
        if (timeout > 0) {
          timeoutId = setTimeout(() => {
            abortController?.abort()
          }, timeout)
        }

        // Execute the handler
        const result = await handler()
        
        // Transform data if needed
        const finalData = transform ? transform(result) : result
        
        // Update data
        data.value = finalData
        
        // Call success callback
        if (onSuccess) {
          onSuccess(finalData)
        }
        
        return
      } catch (err) {
        retryCount++
        
        // If this was the last retry, throw the error
        if (retryCount > retry) {
          throw err
        }
        
        // Wait before retrying
        if (retryDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, retryDelay))
        }
      } finally {
        cleanup()
      }
    }
  }

  // Cleanup function
  const cleanup = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    if (abortController) {
      abortController.abort()
      abortController = null
    }
  }

  // Refresh function (alias for execute)
  const refresh = execute

  // Clear function
  const clear = () => {
    data.value = defaultFn ? defaultFn() : null
    error.value = null
    pending.value = false
    isCached.value = false
    cleanup()
  }

  // Cache invalidation function
  const invalidateCache = () => {
    if (shouldCache) {
      dataCacheService.delete(finalCacheKey)
      isCached.value = false
      console.log(`Cache invalidated for key: ${finalCacheKey}`)
    }
  }

  // Watch for changes in watch sources
  if (watchSources.length > 0) {
    watch(watchSources, () => {
      if (!pending.value) {
        execute()
      }
    }, { deep: true })
  }

  // Execute immediately if requested
  if (immediate) {
    execute()
  }

  // Cleanup on unmount
  // Cleanup will be handled by the composable itself

  return {
    data,
    pending,
    error,
    refresh,
    execute,
    clear,
    isCached,
    cacheKey: finalCacheKey,
    invalidateCache
  }
}

// Specialized hook for Supabase queries
export function useSupabaseQuery<T>(
  key: string,
  queryFn: () => Promise<{ data: T | null; error: any }>,
  options: UseAsyncDataOptions<T> = {}
) {
  return useAsyncData(
    key,
    async () => {
      const { data, error } = await queryFn()

      if (error) {
        throw new Error(error.message || 'Database query failed')
      }

      return data
    },
    {
      retry: 2,
      retryDelay: 1000,
      cache: {
        ttl: 5 * 60 * 1000, // 5 minutes default TTL
        tags: ['supabase', key.split(':')[0]], // Tag with table name
        staleWhileRevalidate: true
      },
      ...options
    }
  )
}

// Hook for handling multiple async operations
export function useAsyncDataGroup<T extends Record<string, any>>(
  operations: Record<keyof T, () => Promise<any>>,
  options: { immediate?: boolean; onError?: (key: string, error: Error) => void } = {}
): any {
  const { immediate = true, onError } = options
  
  const data = ref<Partial<T>>({})
  const pending = ref<Record<keyof T, boolean>>({} as Record<keyof T, boolean>)
  const errors = ref<Record<keyof T, Error | null>>({} as Record<keyof T, Error | null>)
  
  // Initialize state
  Object.keys(operations).forEach(key => {
    pending.value[key as keyof T] = false
    errors.value[key as keyof T] = null
  })
  
  const isLoading = computed(() => Object.values(pending.value).some(Boolean))
  const hasErrors = computed(() => Object.values(errors.value).some(Boolean))
  
  const execute = async (keys?: (keyof T)[]) => {
    const keysToExecute = keys || Object.keys(operations) as (keyof T)[]
    
    const promises = keysToExecute.map(async (key) => {
      pending.value[key] = true
      errors.value[key] = null
      
      try {
        const result = await operations[key]()
        data.value[key] = result
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err))
        errors.value[key] = error
        
        if (onError) {
          onError(String(key), error)
        } else {
          console.error(`AsyncDataGroup[${String(key)}] error:`, error)
        }
      } finally {
        pending.value[key] = false
      }
    })
    
    await Promise.allSettled(promises)
  }
  
  const refresh = () => execute()
  
  const clear = () => {
    data.value = {}
    Object.keys(operations).forEach(key => {
      pending.value[key as keyof T] = false
      errors.value[key as keyof T] = null
    })
  }
  
  if (immediate) {
    execute()
  }
  
  return {
    data,
    pending,
    errors,
    isLoading,
    hasErrors,
    execute,
    refresh,
    clear
  }
}
