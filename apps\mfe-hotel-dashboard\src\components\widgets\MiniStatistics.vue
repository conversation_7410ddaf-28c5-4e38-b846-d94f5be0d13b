<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header with Icon and Growth -->
    <div class="flex items-center justify-between mb-4">
      <div v-if="startContent" class="flex-shrink-0">
        <component :is="startContent" />
      </div>
      <div v-if="growth" class="text-right">
        <span class="text-green-500 text-sm font-bold">{{ growth }}</span>
      </div>
      <div v-if="endContent" class="flex-shrink-0">
        <component :is="endContent" />
      </div>
    </div>

    <!-- Statistics -->
    <div class="space-y-1">
      <h3 class="text-2xl font-bold text-navy-700 dark:text-white">{{ value }}</h3>
      <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">{{ name }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'

interface Props {
  name: string
  value: string
  growth?: string
  startContent?: Component
  endContent?: Component
}

defineProps<Props>()
</script>

<style scoped>
/* Component specific styles */
</style> 