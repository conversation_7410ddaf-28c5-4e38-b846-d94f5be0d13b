<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Bakım - <PERSON><PERSON> Yönetimi</h1>
      <p class="text-gray-600 dark:text-gray-400">Otel<PERSON><PERSON> odaların bakım durumunu takip edin ve yönetin.</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
      <div
        v-for="stat in roomStatistics"
        :key="stat.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ stat.value }}</p>
          </div>
          <div :class="[
            'w-12 h-12 rounded-xl flex items-center justify-center',
            stat.iconBg
          ]">
            <component :is="stat.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-navy-700 rounded-xl p-4 shadow-sm mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <!-- Search -->
        <div class="flex-1 min-w-64">
          <input
            v-model="searchTerm"
            type="text"
            placeholder="Oda numarası veya açıklama ara..."
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          />
        </div>
        
        <!-- Floor Filter -->
        <div>
          <select
            v-model="selectedFloor"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500"
          >
            <option value="">Tüm Katlar</option>
            <option v-for="floor in uniqueFloors" :key="floor?.toString() || 'unknown'" :value="floor">{{ floor }}</option>
          </select>
        </div>

        <!-- Status Filter -->
        <div class="flex gap-2">
          <button
            v-for="status in statusOptions"
            :key="status.value"
            @click="selectedStatus = selectedStatus === status.value ? null : status.value"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-all',
              selectedStatus === status.value 
                ? `${status.activeClass} text-white` 
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            ]"
          >
            {{ status.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Rooms Table -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <!-- Table Header -->
      <div class="p-4 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
            Oda Listesi ({{ filteredRooms.length }} oda)
          </h3>
          <div class="flex items-center space-x-2">
            <input
              type="checkbox"
              :checked="selectedRooms.length === filteredRooms.length && filteredRooms.length > 0"
              @change="toggleAllSelection"
              class="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ selectedRooms.length }} seçili
            </span>
          </div>
        </div>
      </div>

      <!-- Table Content -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <input
                  type="checkbox"
                  :checked="selectedRooms.length === paginatedRooms.length && paginatedRooms.length > 0"
                  @change="togglePageSelection"
                  class="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                />
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Oda
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tip
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Kat
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Öncelik
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Son Bakım
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr
              v-for="room in paginatedRooms"
              :key="room.id"
              class="hover:bg-gray-50 dark:hover:bg-navy-800 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  :value="room.id"
                  v-model="selectedRooms"
                  class="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-navy-700 dark:text-white">{{ room.roomNumber }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-gray-300">{{ room.roomType }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-gray-300">{{ room.floor }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'px-2 py-1 text-xs font-medium rounded-full',
                  getStatusClass(room.status)
                ]">
                  {{ getStatusText(room.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'px-2 py-1 text-xs font-medium rounded-full',
                  getPriorityClass('medium')
                ]">
                  {{ getPriorityText('medium') }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                {{ room.lastMaintenanceDate }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="updateRoomStatus(room)"
                    class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300"
                  >
                    Durum
                  </button>
                  <button
                    @click="scheduleMaintenanceForRoom(room)"
                    class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                  >
                    Bakım
                  </button>
                  <button
                    @click="viewRoomHistory(room)"
                    class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    Geçmiş
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="px-6 py-3 border-t border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            {{ (currentPage - 1) * itemsPerPage + 1 }}-{{ Math.min(currentPage * itemsPerPage, filteredRooms.length) }} 
            arası, toplam {{ filteredRooms.length }} oda
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-navy-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Önceki
            </button>
            <span class="text-sm text-gray-700 dark:text-gray-300">
              {{ currentPage }} / {{ totalPages }}
            </span>
            <button
              @click="currentPage++"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-navy-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Sonraki
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredRooms.length === 0" class="text-center py-12">
      <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
        <HomeIcon class="w-12 h-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Oda bulunamadı</h3>
      <p class="text-gray-500 dark:text-gray-400">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  HomeIcon,
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  EyeIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import { useDashboardStore } from '@/stores/dashboardStore'
import { supabase } from '@hotelexia/shared-supabase-client'

const managementStore = useManagementStore()
const dashboardStore = useDashboardStore()

// Use store data instead of local state
const rooms = computed(() => managementStore.maintenanceRooms)
const loading = computed(() => managementStore.maintenanceRoomsLoading)
const error = computed(() => managementStore.maintenanceRoomsError)

// Reactive data
const searchTerm = ref('')
const selectedFloor = ref<number | string>('')
const selectedStatus = ref<string | null>(null)
const selectedRooms = ref<string[]>([])

const fetchRooms = async () => {
  // Dummy hotel ID for now - should be from user context
  const hotelId = 'hotel_001'
  await managementStore.fetchMaintenanceRooms(hotelId)
}

onMounted(() => {
  fetchRooms()
})

const roomStatistics = computed(() => {
  const total = rooms.value.length
  const available = rooms.value.filter((r: any) => r.status === 'CLEAN').length
  const occupied = rooms.value.filter((r: any) => r.status === 'OCCUPIED').length
  const needsCleaning = rooms.value.filter((r: any) => r.status === 'DIRTY').length
  const inMaintenance = rooms.value.filter((r: any) => r.status === 'MAINTENANCE').length

  return [
    { title: 'Toplam Oda', value: total, icon: HomeIcon, iconBg: 'bg-blue-500' },
    { title: 'Müsait Odalar', value: available, icon: CheckCircleIcon, iconBg: 'bg-green-500' },
    { title: 'Dolu Odalar', value: occupied, icon: EyeIcon, iconBg: 'bg-yellow-500' },
    { title: 'Temizlik Gereken', value: needsCleaning, icon: WrenchScrewdriverIcon, iconBg: 'bg-orange-500' },
    { title: 'Bakımda', value: inMaintenance, icon: ExclamationTriangleIcon, iconBg: 'bg-red-500' }
  ]
})

const uniqueFloors = computed(() => {
  const floors = rooms.value.map((r: any) => r.floor).filter((f: any) => f !== null && f !== undefined)
  return [...new Set(floors)].sort((a: any, b: any) => a - b)
})

// Status options for filtering
const statusOptions = [
  { value: 'CLEAN', label: 'Temiz', activeClass: 'bg-green-500' },
  { value: 'MAINTENANCE', label: 'Bakımda', activeClass: 'bg-orange-500' }
]

const filteredRooms = computed(() => {
  return rooms.value.filter((room: any) => {
    const searchTermMatch = !searchTerm.value ||
      room.room_number?.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      room.room_type?.toLowerCase().includes(searchTerm.value.toLowerCase())

    const floorMatch = !selectedFloor.value || room.floor === selectedFloor.value

    const statusMatch = !selectedStatus.value || room.status === selectedStatus.value

    return searchTermMatch && floorMatch && statusMatch
  })
})

const currentPage = ref(1)
const itemsPerPage = ref(10)

const paginatedRooms = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredRooms.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredRooms.value.length / itemsPerPage.value)
})

// Selection methods
const toggleAllSelection = () => {
  if (selectedRooms.value.length === filteredRooms.value.length) {
    selectedRooms.value = []
  } else {
    selectedRooms.value = filteredRooms.value.map(room => room.id)
  }
}

const togglePageSelection = () => {
  const pageRoomIds = paginatedRooms.value.map(room => room.id)
  const allSelected = pageRoomIds.every(id => selectedRooms.value.includes(id))
  
  if (allSelected) {
    selectedRooms.value = selectedRooms.value.filter(id => !pageRoomIds.includes(id))
  } else {
    pageRoomIds.forEach(id => {
      if (!selectedRooms.value.includes(id)) {
        selectedRooms.value.push(id)
      }
    })
  }
}

// Helper methods
const getStatusText = (status: any) => {
  // Assuming a 'status' column that is not in the current schema.
  // This will need adjustment if the schema is different.
  const statusMap: { [key: string]: string } = {
    AVAILABLE: 'Müsait',
    DIRTY: 'Kirli',
    CLEAN: 'Temiz',
    MAINTENANCE: 'Bakımda',
  }
  return statusMap[status] || 'Bilinmiyor'
}

const getStatusClass = (status: any) => {
  const classMap: { [key: string]: string } = {
    AVAILABLE: 'bg-green-100 text-green-800',
    DIRTY: 'bg-yellow-100 text-yellow-800',
    CLEAN: 'bg-blue-100 text-blue-800',
    MAINTENANCE: 'bg-orange-100 text-orange-800',
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const getPriorityText = (priority: any) => {
  const priorityMap: { [key: string]: string } = {
    HIGH: 'Yüksek',
    MEDIUM: 'Orta',
    LOW: 'Düşük',
  }
  return priorityMap[priority] || '-'
}

const getPriorityClass = (priority: any) => {
  const classMap: { [key: string]: string } = {
    HIGH: 'bg-red-100 text-red-800',
    MEDIUM: 'bg-yellow-100 text-yellow-800',
    LOW: 'bg-green-100 text-green-800',
  }
  return classMap[priority] || 'bg-gray-100 text-gray-800'
}

// Action methods
const updateRoomStatus = async (room: any) => {
  try {
    const { error } = await supabase
      .from('rooms')
      .update({
        status: room.status === 'CLEAN' ? 'MAINTENANCE' : 'CLEAN',
        updated_at: new Date().toISOString()
      })
      .eq('id', room.id)

    if (error) throw error

    // Refresh rooms data
    await fetchRooms()
    console.log(`Room ${room.roomNumber} status updated successfully`)
  } catch (error: any) {
    console.error('Error updating room status:', error.message)
  }
}

const scheduleMaintenanceForRoom = async (room: any) => {
  try {
    // Create a maintenance task
    const { error: taskError } = await supabase
      .from('maintenance_tasks')
      .insert({
        room_id: room.id,
        title: `Bakım - Oda ${room.roomNumber}`,
        description: `${room.roomType} odası için rutin bakım`,
        priority: 'MEDIUM',
        status: 'PENDING',
        hotel_id: 'hotel_001', // Should be from user context
        created_at: new Date().toISOString()
      })

    if (taskError) throw taskError

    // Update room status to maintenance
    const { error: roomError } = await supabase
      .from('rooms')
      .update({
        status: 'MAINTENANCE',
        updated_at: new Date().toISOString()
      })
      .eq('id', room.id)

    if (roomError) throw roomError

    // Refresh rooms data
    await fetchRooms()
    console.log(`Maintenance scheduled for room ${room.roomNumber}`)
  } catch (error: any) {
    console.error('Error scheduling maintenance:', error.message)
  }
}

const viewRoomHistory = async (room: any) => {
  try {
    // Fetch maintenance tasks for this room
    const { data: tasks, error } = await supabase
      .from('maintenance_tasks')
      .select(`
        *,
        profiles:assigned_to(full_name)
      `)
      .eq('room_id', room.id)
      .order('created_at', { ascending: false })

    if (error) throw error

    console.log(`Room ${room.roomNumber} history:`, tasks)
    // Here you could open a modal or navigate to a detailed view
    // For now, just log the history
  } catch (error: any) {
    console.error('Error fetching room history:', error.message)
  }
}
</script>

<style scoped>
/* Add any specific styles for this component here */
</style>