import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, HotelDashboardService, HotelDashboard } from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'
import type { 
  MenuItem, 
  HotelEvent, 
  CreateMenuItemData, 
  CreateEventData, 
  ServiceCategory, 
  HotelActivity, 
  GuestService, 
  CreateGuestServiceData, 
  RoomServiceOrder, 
  GuestServiceRequest, 
  ActivityRegistration,
  MaintenanceFloor,
  MaintenanceRoom,
  MaintenanceStaff,
  CommunicationChannel,
  DirectMessage,
  ChatMessage
} from '@/types/management'

export const useManagementStore = defineStore('management', () => {
  // Get auth store for user context
  const authStore = useAuthStore()

  // State
  const menuItems = ref<HotelDashboard.MenuItem[]>([])
  const categories = ref<ServiceCategory[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Fetch menu items using the new data service
  const fetchMenuItems = async (hotelId?: string) => {
    if (isLoading.value) return

    isLoading.value = true
    error.value = null

    try {
      const targetHotelId = hotelId || (authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined)
      console.log('Fetching menu items for hotel:', targetHotelId)

      const response = await HotelDashboardService.getMenuItems(targetHotelId)

      if (response.error) {
        throw new Error(response.error)
      }

      if (!response.data) {
        console.warn('No menu data returned from service')
        menuItems.value = []
        return
      }

      console.log('Menu data fetched successfully:', response.data)

      // Use the transformed data directly
      menuItems.value = response.data

    } catch (err) {
      console.error('Error fetching menu items:', err)
      error.value = err instanceof Error ? err.message : 'Menu items yüklenirken bilinmeyen bir hata oluştu'
      menuItems.value = []
    } finally {
      isLoading.value = false
    }
  }

  // Fetch categories from Supabase
  const fetchCategories = async (hotelId: string) => {
    if (isLoading.value) return

    isLoading.value = true
    error.value = null

    try {
      console.log('Fetching categories for hotel:', hotelId)
      
      const { data, error: supabaseError } = await (supabase as any)
        .from('menu_item_categories')
        .select('*')
        .eq('hotel_id', hotelId)
        .order('display_order', { ascending: true })

      if (supabaseError) {
        console.error('Supabase error:', supabaseError)
        throw new Error(`Kategoriler yüklenirken hata oluştu: ${supabaseError.message}`)
      }

      if (!data) {
        console.warn('No category data returned from Supabase')
        categories.value = []
        return
      }

      console.log('Category data fetched successfully:', data)
      
      // Convert Supabase data to ServiceCategory format
      const fetchedCategories: ServiceCategory[] = data.map((cat: any) => ({
        id: cat.id,
        name: cat.name,
        image_url: cat.image_url || 'https://via.placeholder.com/300x200.png?text=' + encodeURIComponent(cat.name),
        type: 'ROOM_SERVICE' as const, // All menu categories are room service
        display_order: cat.display_order || 0
      }))
      
      categories.value = fetchedCategories

    } catch (err) {
      console.error('Error fetching categories:', err)
      error.value = err instanceof Error ? err.message : 'Kategoriler yüklenirken bilinmeyen bir hata oluştu'
      categories.value = []
    } finally {
      isLoading.value = false
    }
  }

  // Fetch maintenance rooms from Supabase
  const fetchMaintenanceRooms = async (hotelId: string) => {
    if (maintenanceRoomsLoading.value) return

    maintenanceRoomsLoading.value = true
    maintenanceRoomsError.value = null

    try {
      console.log('Fetching maintenance rooms for hotel:', hotelId)

      const { data, error: supabaseError } = await supabase
        .from('rooms')
        .select(`
          id,
          room_number,
          room_type,
          floor,
          status,
          last_cleaned,
          last_inspection,
          next_maintenance,
          maintenance_notes,
          out_of_order_reason,
          is_active
        `)
        .eq('hotel_id', hotelId)
        .eq('is_active', true)
        .order('floor', { ascending: true })
        .order('room_number', { ascending: true })

      if (supabaseError) {
        console.error('Supabase error:', supabaseError)
        throw new Error(`Maintenance rooms yüklenirken hata oluştu: ${supabaseError.message}`)
      }

      if (!data) {
        console.warn('No maintenance rooms data returned from Supabase')
        maintenanceRooms.value = []
        return
      }

      console.log('Maintenance rooms data fetched successfully:', data)

      // Convert Supabase data to MaintenanceRoom format
      const fetchedRooms: MaintenanceRoom[] = data.map((room: any) => ({
        id: room.id,
        roomNumber: room.room_number,
        number: room.room_number,
        roomType: room.room_type || 'Standart Oda',
        type: room.room_type || 'Standart Oda',
        floor: room.floor ? `${room.floor}. Kat` : 'Bilinmeyen Kat',
        status: room.status || 'CLEAN',
        lastMaintenanceDate: room.last_cleaned ? new Date(room.last_cleaned).toLocaleDateString('tr-TR') : 'N/A',
        lastMaintenance: room.last_cleaned || null,
        priority: determinePriority(room.status, room.next_maintenance),
        description: room.maintenance_notes || room.out_of_order_reason || '',
        activeRequest: room.status === 'MAINTENANCE' || room.status === 'OUT_OF_ORDER' ?
          (room.maintenance_notes || room.out_of_order_reason || 'Bakım gerekli') : undefined
      }))

      maintenanceRooms.value = fetchedRooms

    } catch (err) {
      console.error('Error fetching maintenance rooms:', err)
      maintenanceRoomsError.value = err instanceof Error ? err.message : 'Maintenance rooms yüklenirken bilinmeyen bir hata oluştu'
      maintenanceRooms.value = []
    } finally {
      maintenanceRoomsLoading.value = false
    }
  }

  // Helper function to determine priority based on room status and maintenance schedule
  const determinePriority = (status: string | null, nextMaintenance: string | null): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' => {
    if (status === 'OUT_OF_ORDER') return 'URGENT'
    if (status === 'MAINTENANCE') return 'HIGH'
    if (status === 'DIRTY') return 'MEDIUM'

    // Check if maintenance is overdue
    if (nextMaintenance) {
      const maintenanceDate = new Date(nextMaintenance)
      const now = new Date()
      const daysDiff = Math.floor((maintenanceDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      if (daysDiff < 0) return 'HIGH' // Overdue
      if (daysDiff <= 7) return 'MEDIUM' // Due within a week
    }

    return 'LOW'
  }

  // Fetch maintenance floors from Supabase (computed from rooms data)
  const fetchMaintenanceFloors = async (hotelId: string) => {
    if (maintenanceFloorsLoading.value) return

    maintenanceFloorsLoading.value = true
    maintenanceFloorsError.value = null

    try {
      console.log('Computing maintenance floors for hotel:', hotelId)

      // First fetch rooms data if not already loaded
      if (maintenanceRooms.value.length === 0) {
        await fetchMaintenanceRooms(hotelId)
      }

      // Group rooms by floor and compute statistics
      const floorStats = new Map<number, {
        totalRooms: number
        cleanRooms: number
        maintenanceRooms: number
        outOfOrderRooms: number
        lastMaintenance: string | null
      }>()

      maintenanceRooms.value.forEach(room => {
        const floorNumber = extractFloorNumber(room.floor)
        if (!floorStats.has(floorNumber)) {
          floorStats.set(floorNumber, {
            totalRooms: 0,
            cleanRooms: 0,
            maintenanceRooms: 0,
            outOfOrderRooms: 0,
            lastMaintenance: null
          })
        }

        const stats = floorStats.get(floorNumber)!
        stats.totalRooms++

        switch (room.status) {
          case 'CLEAN':
            stats.cleanRooms++
            break
          case 'MAINTENANCE':
            stats.maintenanceRooms++
            break
          case 'OUT_OF_ORDER':
            stats.outOfOrderRooms++
            break
        }

        // Update last maintenance date
        if (room.lastMaintenance && (!stats.lastMaintenance || room.lastMaintenance > stats.lastMaintenance)) {
          stats.lastMaintenance = room.lastMaintenance
        }
      })

      // Convert to MaintenanceFloor format
      const fetchedFloors: MaintenanceFloor[] = Array.from(floorStats.entries()).map(([floorNumber, stats]) => {
        const completionRate = stats.totalRooms > 0 ? Math.round((stats.cleanRooms / stats.totalRooms) * 100) : 0
        const activeMaintenanceRequests = stats.maintenanceRooms + stats.outOfOrderRooms

        return {
          id: floorNumber.toString(),
          name: getFloorName(floorNumber),
          totalRooms: stats.totalRooms,
          roomCount: stats.totalRooms,
          cleanRooms: stats.cleanRooms,
          maintenanceRooms: stats.maintenanceRooms,
          completionRate,
          activeMaintenanceRequests,
          status: (activeMaintenanceRequests > 2 ? 'MAINTENANCE' : 'OPERATIONAL') as 'MAINTENANCE' | 'INSPECTION' | 'OPERATIONAL' | 'ISSUES',
          lastMaintenance: stats.lastMaintenance || new Date().toISOString().split('T')[0],
          rooms: []
        }
      }).sort((a, b) => parseInt(a.id) - parseInt(b.id))

      maintenanceFloors.value = fetchedFloors
      console.log('Maintenance floors computed successfully:', fetchedFloors)

    } catch (err) {
      console.error('Error computing maintenance floors:', err)
      maintenanceFloorsError.value = err instanceof Error ? err.message : 'Maintenance floors hesaplanırken bilinmeyen bir hata oluştu'
      maintenanceFloors.value = []
    } finally {
      maintenanceFloorsLoading.value = false
    }
  }

  // Helper function to extract floor number from floor string
  const extractFloorNumber = (floorStr: string): number => {
    if (floorStr.includes('Zemin') || floorStr.includes('Lobi')) return 0
    const match = floorStr.match(/(\d+)/)
    return match ? parseInt(match[1]) : 0
  }

  // Helper function to get floor name from number
  const getFloorName = (floorNumber: number): string => {
    if (floorNumber === 0) return 'Zemin Kat'
    if (floorNumber === 1) return '1. Kat'
    if (floorNumber === 2) return '2. Kat'
    if (floorNumber === 3) return '3. Kat'
    if (floorNumber === 4) return '4. Kat (VIP)'
    return `${floorNumber}. Kat`
  }

  // Hotel Events - Supabase reactive data
  const events = ref<HotelEvent[]>([])
  const eventsLoading = ref(false)
  const eventsError = ref<string | null>(null)

  const fetchEvents = async () => {
    eventsLoading.value = true
    eventsError.value = null

    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const response = await HotelDashboardService.getHotelEvents(hotelId)

      if (response.error) {
        throw new Error(response.error)
      }

      events.value = response.data || []
    } catch (err) {
      eventsError.value = (err as Error).message
      console.error('Error fetching events:', err)
    } finally {
      eventsLoading.value = false
    }
  }

  // Maintenance Management Data - Supabase reactive data
  const maintenanceFloors = ref<MaintenanceFloor[]>([])
  const maintenanceFloorsLoading = ref(false)
  const maintenanceFloorsError = ref<string | null>(null)

  const maintenanceRooms = ref<MaintenanceRoom[]>([])
  const maintenanceRoomsLoading = ref(false)
  const maintenanceRoomsError = ref<string | null>(null)

  const maintenanceStaff = ref<MaintenanceStaff[]>([
    {
      id: '1',
      name: 'Ahmet Yılmaz',
      specialization: 'Sıhhi Tesisat',
      currentTaskCount: 3,
      status: 'BUSY',
      avatar: 'https://picsum.photos/64/64?random=11',
      phone: '+90 532 123 45 67',
      email: '<EMAIL>',
      experience: 8,
      completedTasks: 156,
      activeTasks: 3,
      rating: 4.8,
      lastActivity: '2024-01-20T14:30:00'
    },
    {
      id: '2',
      name: 'Mehmet Kaya',
      specialization: 'Elektrik',
      currentTaskCount: 2,
      status: 'AVAILABLE',
      avatar: 'https://picsum.photos/64/64?random=12',
      phone: '+90 532 234 56 78',
      email: '<EMAIL>',
      experience: 12,
      completedTasks: 203,
      activeTasks: 2,
      rating: 4.9,
      lastActivity: '2024-01-20T13:45:00'
    },
    {
      id: '3',
      name: 'İbrahim Özkan',
      specialization: 'Genel Bakım',
      currentTaskCount: 1,
      status: 'AVAILABLE',
      avatar: 'https://picsum.photos/64/64?random=13',
      phone: '+90 532 345 67 89',
      email: '<EMAIL>',
      experience: 5,
      completedTasks: 89,
      activeTasks: 1,
      rating: 4.6,
      lastActivity: '2024-01-20T15:20:00'
    },
    {
      id: '4',
      name: 'Mustafa Demir',
      specialization: 'Klima & Havalandırma',
      currentTaskCount: 4,
      status: 'BUSY',
      avatar: 'https://picsum.photos/64/64?random=14',
      phone: '+90 532 456 78 90',
      email: '<EMAIL>',
      experience: 15,
      completedTasks: 312,
      activeTasks: 4,
      rating: 4.7,
      lastActivity: '2024-01-20T16:10:00'
    },
    {
      id: '5',
      name: 'Ali Şahin',
      specialization: 'Asansör Teknisyeni',
      currentTaskCount: 0,
      status: 'OFF_DUTY',
      avatar: 'https://picsum.photos/64/64?random=15',
      phone: '+90 532 567 89 01',
      email: '<EMAIL>',
      experience: 10,
      completedTasks: 145,
      activeTasks: 0,
      rating: 4.4,
      lastActivity: '2024-01-19T17:00:00'
    },
    {
      id: '6',
      name: 'Hasan Çelik',
      specialization: 'Güvenlik Sistemleri',
      currentTaskCount: 1,
      status: 'AVAILABLE',
      avatar: 'https://picsum.photos/64/64?random=16',
      phone: '+90 532 678 90 12',
      email: '<EMAIL>',
      experience: 7,
      completedTasks: 98,
      activeTasks: 1,
      rating: 4.5,
      lastActivity: '2024-01-20T12:30:00'
    }
  ])

  const communicationChannels = ref<CommunicationChannel[]>([
    {
      id: 'maintenance_general',
      name: 'Genel Bakım',
      type: 'DEPARTMENT',
      participants: ['staff_001', 'staff_002', 'staff_003'],
      icon: 'wrench-screwdriver',
      memberCount: 8,
      unreadCount: 3,
      department: 'MAINTENANCE'
    },
    {
      id: 'maintenance_urgent',
      name: 'Acil Müdahaleler',
      type: 'DEPARTMENT',
      participants: ['staff_001', 'staff_004'],
      icon: 'exclamation-triangle',
      memberCount: 5,
      unreadCount: 1,
      department: 'MAINTENANCE'
    },
    {
      id: 'maintenance_electrical',
      name: 'Elektrik Ekibi',
      type: 'DEPARTMENT',
      participants: ['staff_002', 'staff_005'],
      icon: 'bolt',
      memberCount: 4,
      unreadCount: 0,
      department: 'MAINTENANCE'
    },
    {
      id: 'housekeeping_general',
      name: 'Temizlik Genel',
      type: 'DEPARTMENT',
      participants: ['staff_003', 'staff_006'],
      icon: 'sparkles',
      memberCount: 12,
      unreadCount: 2,
      department: 'HOUSEKEEPING'
    },
    {
      id: 'housekeeping_floors',
      name: 'Kat Temizlik',
      type: 'DEPARTMENT',
      participants: ['staff_004', 'staff_007'],
      icon: 'building-office',
      memberCount: 6,
      unreadCount: 4,
      department: 'HOUSEKEEPING'
    }
  ])

  const directMessages = ref<DirectMessage[]>([
    {
      id: 'dm_1',
      name: 'Ahmet Yılmaz',
      lastMessage: '105 odası tamamlandı, kontrole hazır',
      lastMessageTime: '14:30',
      unreadCount: 1,
      isOnline: true,
      department: 'MAINTENANCE'
    },
    {
      id: 'dm_2', 
      name: 'Mehmet Kaya',
      lastMessage: 'Yeni elektrik malzemeleri geldi mi?',
      lastMessageTime: '13:45',
      unreadCount: 0,
      isOnline: true,
      department: 'MAINTENANCE'
    },
    {
      id: 'dm_3',
      name: 'Fatma Yıldız',
      lastMessage: '203 odası temizlendi',
      lastMessageTime: '12:20',
      unreadCount: 2,
      isOnline: false,
      department: 'HOUSEKEEPING'
    },
    {
      id: 'dm_4',
      name: 'Zeynep Kara',
      lastMessage: 'Bugünün programı hazır',
      lastMessageTime: '11:15',
      unreadCount: 0,
      isOnline: true,
      department: 'HOUSEKEEPING'
    }
  ])

  const communicationMessages = ref<ChatMessage[]>([
    {
      id: 'msg_1',
      sender: 'Mehmet Kaya',
      senderName: 'Mehmet Kaya',
      text: 'Oda 205\'te klima sorunu var, müsait olan bakabilir mi?',
      message: 'Oda 205\'te klima sorunu var, müsait olan bakabilir mi?',
      timestamp: '2024-01-20 14:30',
      isOwn: false,
      isRead: true,
      department: 'MAINTENANCE',
      threadId: 'thread_1',
      channelId: 'maintenance_general',
      status: 'read'
    },
    {
      id: 'msg_2',
      sender: 'Ben',
      senderName: 'Ben',
      text: 'Hemen bakıyorum, 15 dakikada oradayım.',
      message: 'Hemen bakıyorum, 15 dakikada oradayım.',
      timestamp: '2024-01-20 14:32',
      isOwn: true,
      isRead: false,
      department: 'MAINTENANCE',
      threadId: 'thread_1',
      channelId: 'maintenance_general',
      status: 'delivered'
    },
    {
      id: 'msg_3',
      sender: 'Ali Demir',
      senderName: 'Ali Demir',
      text: 'Elektrik panosu kontrol edildi, sorun yok.',
      message: 'Elektrik panosu kontrol edildi, sorun yok.',
      timestamp: '2024-01-20 15:15',
      isOwn: false,
      isRead: true,
      department: 'MAINTENANCE',
      threadId: 'thread_2',
      channelId: 'maintenance_electrical',
      status: 'read'
    },
    {
      id: 'msg_4',
      sender: 'Fatma Yılmaz',
      senderName: 'Fatma Yılmaz',
      text: 'Acil bakım gerekli, ana su tesisatında sızıntı!',
      message: 'Acil bakım gerekli, ana su tesisatında sızıntı!',
      timestamp: '2024-01-20 16:45',
      isOwn: false,
      isRead: true,
      department: 'MAINTENANCE',
      threadId: 'thread_3',
      channelId: 'maintenance_urgent',
      status: 'read'
    },
    {
      id: 'msg_5',
      sender: 'Ayşe Kara',
      senderName: 'Ayşe Kara',
      text: '3. kat temizlik tamamlandı.',
      message: '3. kat temizlik tamamlandı.',
      timestamp: '2024-01-20 13:00',
      isOwn: false,
      isRead: true,
      department: 'HOUSEKEEPING',
      threadId: 'thread_4',
      channelId: 'housekeeping_general',
      status: 'read'
    },
    {
      id: 'msg_6',
      sender: 'Zeynep Öz',
      senderName: 'Zeynep Öz',
      text: 'Oda 315 misafir şikayeti - ekstra temizlik.',
      message: 'Oda 315 misafir şikayeti - ekstra temizlik.',
      timestamp: '2024-01-20 17:20',
      isOwn: false,
      isRead: true,
      department: 'HOUSEKEEPING',
      threadId: 'thread_5',
      channelId: 'housekeeping_floors',
      status: 'read'
    },
    {
      id: 'msg_7',
      sender: 'Murat Çelik',
      senderName: 'Murat Çelik',
      text: 'Yarınki vardiya değişikliği onaylandı.',
      message: 'Yarınki vardiya değişikliği onaylandı.',
      timestamp: '2024-01-20 09:15',
      isOwn: false,
      isRead: true,
      department: 'MAINTENANCE',
      directMessageId: 'dm_001',
      status: 'read'
    },
    {
      id: 'msg_8',
      sender: 'Seda Aydın',
      senderName: 'Seda Aydın',
      text: 'Malzeme siparişleri bugün gelecek.',
      message: 'Malzeme siparişleri bugün gelecek.',
      timestamp: '2024-01-20 11:30',
      isOwn: false,
      isRead: true,
      department: 'MAINTENANCE',
      directMessageId: 'dm_002',
      status: 'read'
    }
  ])

  // Room Service Orders - Supabase reactive data
  const roomServiceOrders = ref<RoomServiceOrder[]>([])
  const roomServiceOrdersLoading = ref(false)
  const roomServiceOrdersError = ref<string | null>(null)

  const fetchRoomServiceOrders = async () => {
    roomServiceOrdersLoading.value = true
    roomServiceOrdersError.value = null

    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const response = await HotelDashboardService.getRoomServiceOrders(hotelId)

      if (response.error) {
        throw new Error(response.error)
      }

      roomServiceOrders.value = response.data || []
    } catch (err) {
      roomServiceOrdersError.value = (err as Error).message
      console.error('Error fetching room service orders:', err)
    } finally {
      roomServiceOrdersLoading.value = false
    }
  }

  const guestServiceRequests = ref<GuestServiceRequest[]>([
    {
      id: 'req_1',
      guestUserId: 'guest_001',
      guestName: 'Ahmet Yılmaz',
      roomNumber: '204',
      serviceType: 'Ekstra Havlu',
      status: 'NEW',
      description: '2 adet büyük havlu talebi',
      createdAt: new Date(Date.now() - 10 * 60 * 1000) // 10 dakika önce
    },
    {
      id: 'req_2',
      guestUserId: 'guest_008',
      guestName: 'Selin Kartal',
      roomNumber: '307',
      serviceType: 'Klima Arızası',
      status: 'ASSIGNED',
      description: 'Klima çalışmıyor, oda çok sıcak',
      assignedStaffId: 'staff_002',
      assignedStaffName: 'Tekniker Burak',
      createdAt: new Date(Date.now() - 30 * 60 * 1000) // 30 dakika önce
    },
    {
      id: 'req_3',
      guestUserId: 'guest_009',
      guestName: 'Hasan Polat',
      roomNumber: '412',
      serviceType: 'Oda Temizliği',
      status: 'IN_PROGRESS',
      description: 'Oda ekstra temizlik talebi - parti sonrası',
      assignedStaffId: 'staff_003',
      assignedStaffName: 'Kat Görevlisi Ayşe',
      createdAt: new Date(Date.now() - 45 * 60 * 1000) // 45 dakika önce
    },
    {
      id: 'req_4',
      guestUserId: 'guest_010',
      guestName: 'Gamze Aksoy',
      roomNumber: '156',
      serviceType: 'Vale Hizmeti',
      status: 'COMPLETED',
      description: 'Araç park edilmesi ve anahtarların teslim alınması',
      assignedStaffId: 'staff_004',
      assignedStaffName: 'Vale Murat',
      createdAt: new Date(Date.now() - 120 * 60 * 1000) // 2 saat önce
    },
    {
      id: 'req_5',
      guestUserId: 'guest_011',
      guestName: 'Okan Şahin',
      roomNumber: '678',
      serviceType: 'Ekstra Yastık',
      status: 'NEW',
      description: 'Bebek için ekstra yumuşak yastık talebi',
      createdAt: new Date(Date.now() - 5 * 60 * 1000) // 5 dakika önce
    },
    {
      id: 'req_6',
      guestUserId: 'guest_012',
      guestName: 'Derya Kaya',
      roomNumber: '234',
      serviceType: 'Spa Rezervasyonu',
      status: 'ASSIGNED',
      description: 'Yarın saat 14:00 için masaj rezervasyonu',
      assignedStaffId: 'staff_005',
      assignedStaffName: 'Spa Müdürü Elif',
      notes: 'Hamile masajı talep edildi',
      createdAt: new Date(Date.now() - 80 * 60 * 1000) // 80 dakika önce
    },
    {
      id: 'req_7',
      guestUserId: 'guest_013',
      guestName: 'Serkan Aydın',
      roomNumber: '890',
      serviceType: 'TV Arızası',
      status: 'CANCELLED',
      description: 'Televizyon açılmıyor',
      notes: 'Misafir sorunu çözdü - remote pil değişimi yeterliydi',
      createdAt: new Date(Date.now() - 90 * 60 * 1000) // 90 dakika önce
    }
  ])

  const activityRegistrations = ref<ActivityRegistration[]>([
    {
      id: 'reg_1',
      activityId: 'act_1',
      activityTitle: 'Havuz Başında Canlı Gitar Dinletisi',
      guestUserId: 'guest_001',
      guestName: 'Ahmet Yılmaz',
      roomNumber: '204',
      status: 'REGISTERED',
      registeredAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 gün önce
    },
    {
      id: 'reg_2',
      activityId: 'act_1',
      activityTitle: 'Havuz Başında Canlı Gitar Dinletisi',
      guestUserId: 'guest_002',
      guestName: 'Fatma Kara',
      roomNumber: '315',
      status: 'ATTENDED',
      registeredAt: new Date(Date.now() - 23 * 60 * 60 * 1000)
    },
    {
      id: 'reg_3',
      activityId: 'act_2',
      activityTitle: 'Çocuklar için Hazine Avı',
      guestUserId: 'guest_014',
      guestName: 'Elif Özkan',
      roomNumber: '567',
      status: 'REGISTERED',
      registeredAt: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12 saat önce
    },
    {
      id: 'reg_4',
      activityId: 'act_4',
      activityTitle: 'Yoga ve Meditasyon Seansı',
      guestUserId: 'guest_015',
      guestName: 'Burak Çınar',
      roomNumber: '445',
      status: 'REGISTERED',
      registeredAt: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 saat önce
    },
    {
      id: 'reg_5',
      activityId: 'act_1',
      activityTitle: 'Havuz Başında Canlı Gitar Dinletisi',
      guestUserId: 'guest_016',
      guestName: 'Nesrin Yavuz',
      roomNumber: '123',
      status: 'CANCELLED',
      registeredAt: new Date(Date.now() - 48 * 60 * 60 * 1000) // 2 gün önce
    },
    {
      id: 'reg_6',
      activityId: 'act_4',
      activityTitle: 'Yoga ve Meditasyon Seansı',
      guestUserId: 'guest_017',
      guestName: 'Tolga Işık',
      roomNumber: '789',
      status: 'ATTENDED',
      registeredAt: new Date(Date.now() - 72 * 60 * 60 * 1000) // 3 gün önce
    },
    {
      id: 'reg_7',
      activityId: 'act_2',
      activityTitle: 'Çocuklar için Hazine Avı',
      guestUserId: 'guest_018',
      guestName: 'Pınar Aslan',
      roomNumber: '332',
      status: 'REGISTERED',
      registeredAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 saat önce
    },
    {
      id: 'reg_8',
      activityId: 'act_1',
      activityTitle: 'Havuz Başında Canlı Gitar Dinletisi',
      guestUserId: 'guest_019',
      guestName: 'Kaan Doğan',
      roomNumber: '654',
      status: 'REGISTERED',
      registeredAt: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 saat önce
    }
  ])

  // Hotel Activities - Supabase reactive data
  const activities = ref<HotelActivity[]>([])
  const activitiesLoading = ref(false)
  const activitiesError = ref<string | null>(null)

  const fetchActivities = async () => {
    activitiesLoading.value = true
    activitiesError.value = null

    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const response = await HotelDashboardService.getHotelActivities(hotelId)

      if (response.error) {
        throw new Error(response.error)
      }

      activities.value = response.data || []
    } catch (err) {
      activitiesError.value = (err as Error).message
      console.error('Error fetching activities:', err)
    } finally {
      activitiesLoading.value = false
    }
  }

  const guestServices = ref<GuestService[]>([
    {
      id: 'gs_1',
      name: 'Ekstra Havlu Talebi',
      description: 'Odaya temiz havlu seti gönderilir. Maksimum 4 adet havlu talep edilebilir.',
      categoryId: 'cat_gs_1',
      isActive: true,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-10')
    },
    {
      id: 'gs_2',
      name: 'Oda Temizliği',
      description: 'Misafirin belirlediği saatte oda temizliği yapılır. En az 1 saat öncesinden bildirilmelidir.',
      categoryId: 'cat_gs_1',
      isActive: true,
      createdAt: new Date('2024-01-11'),
      updatedAt: new Date('2024-01-11')
    },
    {
      id: 'gs_3',
      name: 'Ekstra Yastık Talebi',
      description: 'Odaya ek yastık gönderilir. Maksimum 2 adet yastık talep edilebilir.',
      categoryId: 'cat_gs_1',
      isActive: true,
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-12')
    },
    {
      id: 'gs_4',
      name: 'Klima Arızası Bildirimi',
      description: 'Teknik ekip klimayı kontrol etmek için yönlendirilir. 24 saat hizmet verilir.',
      categoryId: 'cat_gs_2',
      isActive: true,
      createdAt: new Date('2024-01-13'),
      updatedAt: new Date('2024-01-13')
    },
    {
      id: 'gs_5',
      name: 'TV Arızası Bildirimi',
      description: 'Televizyon sorunları için teknik destek sağlanır. Gerekirse yeni TV değişimi yapılır.',
      categoryId: 'cat_gs_2',
      isActive: true,
      createdAt: new Date('2024-01-14'),
      updatedAt: new Date('2024-01-14')
    },
    {
      id: 'gs_6',
      name: 'Banyo Arızası Bildirimi',
      description: 'Tesisat sorunları için acil müdahale ekibi gönderilir. Su ve elektrik arızaları dahil.',
      categoryId: 'cat_gs_2',
      isActive: true,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'gs_7',
      name: 'Vale Araç Talebi',
      description: 'Misafirin aracı vale tarafından lobi önüne getirilir. En az 30 dakika öncesinden bildirim gereklidir.',
      categoryId: 'cat_gs_3',
      isActive: true,
      createdAt: new Date('2024-01-16'),
      updatedAt: new Date('2024-01-16')
    },
    {
      id: 'gs_8',
      name: 'Havalimanı Transfer Talebi',
      description: 'Havalimanına transfer hizmeti rezervasyonu. 4 saat öncesinden bildirilmelidir.',
      categoryId: 'cat_gs_3',
      isActive: true,
      createdAt: new Date('2024-01-17'),
      updatedAt: new Date('2024-01-17')
    },
    {
      id: 'gs_9',
      name: 'Spa Randevu Talebi',
      description: 'Spa ve wellness merkezi için randevu alma hizmeti. Masaj, sauna ve terapi seçenekleri.',
      categoryId: 'cat_gs_4',
      isActive: true,
      createdAt: new Date('2024-01-18'),
      updatedAt: new Date('2024-01-18')
    },
    {
      id: 'gs_10',
      name: 'Masaj Rezervasyonu',
      description: 'Profesyonel masöz ile rahatlatıcı masaj seansı. İsveç, aromaterapi ve taş terapisi mevcut.',
      categoryId: 'cat_gs_4',
      isActive: false,
      createdAt: new Date('2024-01-19'),
      updatedAt: new Date('2024-01-20')
    }
  ])

  // Actions - Menu Item CRUD with Supabase
  const addMenuItem = async (itemData: CreateMenuItemData, hotelId: string) => {
    try {
      // Use any type to bypass TypeScript checks for new schema fields
      const insertData: any = {
        hotel_id: hotelId,
        name: itemData.name,
        description: itemData.description,
        price: itemData.price,
        category_id: itemData.category_id,
        image_url: typeof itemData.image === 'string' ? itemData.image : null,
        is_available: itemData.isAvailable,
        display_order: itemData.display_order,
        preparation_time_minutes: itemData.preparation_time_minutes,
        item_code: itemData.item_code,
        is_vegetarian: itemData.is_vegetarian,
        is_vegan: itemData.is_vegan,
        is_gluten_free: itemData.is_gluten_free,
        tags: itemData.tags
      }

      const { data, error } = await supabase
        .from('menu_items')
        .insert(insertData)
        .select()
        .single()

      if (error) {
        console.error('Error adding menu item:', error)
        throw error
      }

      // Refresh menu items after adding
      await fetchMenuItems(hotelId)
      return data
    } catch (err) {
      console.error('Failed to add menu item:', err)
      throw err
    }
  }

  const updateMenuItem = async (id: string, itemData: Partial<CreateMenuItemData>, hotelId: string) => {
    try {
      // Use any type to bypass TypeScript checks for new schema fields
      const updateData: any = {}
      
      if (itemData.name !== undefined) updateData.name = itemData.name
      if (itemData.category_id !== undefined) updateData.category_id = itemData.category_id
      if (itemData.description !== undefined) updateData.description = itemData.description
      if (itemData.price !== undefined) updateData.price = itemData.price
      if (itemData.image !== undefined) updateData.image_url = typeof itemData.image === 'string' ? itemData.image : null
      if (itemData.isAvailable !== undefined) updateData.is_available = itemData.isAvailable
      if (itemData.display_order !== undefined) updateData.display_order = itemData.display_order
      if (itemData.preparation_time_minutes !== undefined) updateData.preparation_time_minutes = itemData.preparation_time_minutes
      if (itemData.item_code !== undefined) updateData.item_code = itemData.item_code
      if (itemData.is_vegetarian !== undefined) updateData.is_vegetarian = itemData.is_vegetarian
      if (itemData.is_vegan !== undefined) updateData.is_vegan = itemData.is_vegan
      if (itemData.is_gluten_free !== undefined) updateData.is_gluten_free = itemData.is_gluten_free
      if (itemData.tags !== undefined) updateData.tags = itemData.tags

      const { data, error } = await supabase
        .from('menu_items')
        .update(updateData)
        .eq('id', id)
        .eq('hotel_id', hotelId)
        .select()
        .single()

      if (error) {
        console.error('Error updating menu item:', error)
        throw error
      }

      // Refresh menu items after updating
      await fetchMenuItems(hotelId)
      return data
    } catch (err) {
      console.error('Failed to update menu item:', err)
      throw err
    }
  }

  const deleteMenuItem = async (id: string, hotelId: string) => {
    try {
      const { error } = await supabase
        .from('menu_items')
        .delete()
        .eq('id', id)
        .eq('hotel_id', hotelId)

      if (error) {
        console.error('Error deleting menu item:', error)
        throw error
      }

      // Refresh menu items after deleting
      await fetchMenuItems(hotelId)
    } catch (err) {
      console.error('Failed to delete menu item:', err)
      throw err
    }
  }

  const toggleMenuItemAvailability = async (id: string, hotelId: string) => {
    try {
      // First get the current item to toggle its availability
      const currentItem = menuItems.value.find(item => item.id === id)
      if (!currentItem) {
        throw new Error('Menu item not found')
      }

      const { data, error } = await supabase
        .from('menu_items')
        .update({ is_available: !currentItem.isAvailable })
        .eq('id', id)
        .eq('hotel_id', hotelId)
        .select()
        .single()

      if (error) {
        console.error('Error toggling menu item availability:', error)
        throw error
      }

      // Refresh menu items after toggling
      await fetchMenuItems(hotelId)
      return data
    } catch (err) {
      console.error('Failed to toggle menu item availability:', err)
      throw err
    }
  }

  const addEvent = (eventData: CreateEventData) => {
    const newEvent: HotelEvent = {
      id: Date.now().toString(),
      ...eventData,
      image: eventData.image ? 'https://picsum.photos/600/400?random=' + Date.now() : 'https://picsum.photos/600/400?random=default',
      currentAttendees: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    events.value.push(newEvent)
  }

  const updateEvent = (id: string, eventData: Partial<CreateEventData>) => {
    const index = events.value.findIndex(event => event.id === id)
    if (index !== -1) {
      const updatedData = { ...eventData }
      if (updatedData.image) {
        updatedData.image = 'https://picsum.photos/600/400?random=' + Date.now()
      }
      events.value[index] = {
        ...events.value[index],
        ...updatedData,
        image: updatedData.image || events.value[index].image,
        updatedAt: new Date()
      }
    }
  }

  const deleteEvent = (id: string) => {
    const index = events.value.findIndex(event => event.id === id)
    if (index !== -1) {
      events.value.splice(index, 1)
    }
  }

  const toggleEventStatus = (id: string) => {
    const event = events.value.find(event => event.id === id)
    if (event) {
      event.isActive = !event.isActive
      event.updatedAt = new Date()
    }
  }

  // Category Actions - Updated with Supabase integration
  const addCategory = async (categoryData: Omit<ServiceCategory, 'id'>, hotelId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const { data, error: supabaseError } = await (supabase as any)
        .from('menu_item_categories')
        .insert([{
          hotel_id: hotelId,
          name: categoryData.name,
          image_url: categoryData.image_url,
          display_order: categoryData.display_order || 0
        }])
        .select()
        .single()

      if (supabaseError) {
        throw new Error(`Kategori oluşturulamadı: ${supabaseError.message}`)
      }

      // Add to local state
      const newCategory: ServiceCategory = {
        id: data.id,
        name: data.name,
        image_url: data.image_url,
        type: 'ROOM_SERVICE',
        display_order: data.display_order
      }
      categories.value.push(newCategory)

    } catch (err) {
      console.error('Error creating category:', err)
      error.value = err instanceof Error ? err.message : 'Kategori oluşturulamadı'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateCategory = async (category: ServiceCategory, hotelId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const { data, error: supabaseError } = await (supabase as any)
        .from('menu_item_categories')
        .update({
          name: category.name,
          image_url: category.image_url,
          display_order: category.display_order || 0
        })
        .eq('id', category.id)
        .eq('hotel_id', hotelId)
        .select()
        .single()

      if (supabaseError) {
        throw new Error(`Kategori güncellenemedi: ${supabaseError.message}`)
      }

      // Update local state
      const index = categories.value.findIndex(cat => cat.id === category.id)
      if (index !== -1) {
        categories.value[index] = {
          id: data.id,
          name: data.name,
          image_url: data.image_url,
          type: 'ROOM_SERVICE',
          display_order: data.display_order
        }
      }

    } catch (err) {
      console.error('Error updating category:', err)
      error.value = err instanceof Error ? err.message : 'Kategori güncellenemedi'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteCategory = async (categoryId: string, hotelId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const { error: supabaseError } = await (supabase as any)
        .from('menu_item_categories')
        .delete()
        .eq('id', categoryId)
        .eq('hotel_id', hotelId)

      if (supabaseError) {
        throw new Error(`Kategori silinemedi: ${supabaseError.message}`)
      }

      // Remove from local state
      const index = categories.value.findIndex(cat => cat.id === categoryId)
      if (index !== -1) {
        categories.value.splice(index, 1)
      }

    } catch (err) {
      console.error('Error deleting category:', err)
      error.value = err instanceof Error ? err.message : 'Kategori silinemedi. Lütfen önce bu kategoriye ait ürünleri silin.'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Activity Actions
  const addActivity = (activityData: Omit<HotelActivity, 'id'>) => {
    const newActivity: HotelActivity = {
      id: Date.now().toString(),
      ...activityData
    }
    activities.value.push(newActivity)
  }

  const updateActivity = (activity: HotelActivity) => {
    const index = activities.value.findIndex(act => act.id === activity.id)
    if (index !== -1) {
      activities.value[index] = activity
    }
  }

  const deleteActivity = (activityId: string) => {
    const index = activities.value.findIndex(act => act.id === activityId)
    if (index !== -1) {
      activities.value.splice(index, 1)
    }
  }

  const toggleActivityStatus = (activityId: string) => {
    const activity = activities.value.find(act => act.id === activityId)
    if (activity) {
      activity.isActive = !activity.isActive
    }
  }

  // Guest Service Actions
  const addGuestService = (serviceData: CreateGuestServiceData) => {
    const newService: GuestService = {
      id: Date.now().toString(),
      ...serviceData,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    guestServices.value.push(newService)
  }

  const updateGuestService = (id: string, serviceData: Partial<CreateGuestServiceData>) => {
    const index = guestServices.value.findIndex(service => service.id === id)
    if (index !== -1) {
      guestServices.value[index] = {
        ...guestServices.value[index],
        ...serviceData,
        updatedAt: new Date()
      }
    }
  }

  const deleteGuestService = (id: string) => {
    const index = guestServices.value.findIndex(service => service.id === id)
    if (index !== -1) {
      guestServices.value.splice(index, 1)
    }
  }

  const toggleGuestServiceStatus = (id: string) => {
    const service = guestServices.value.find(service => service.id === id)
    if (service) {
      service.isActive = !service.isActive
      service.updatedAt = new Date()
    }
  }

  // Operational Tracking Actions
  const updateOrderStatus = (orderId: string, newStatus: RoomServiceOrder['status']) => {
    const order = roomServiceOrders.value.find(order => order.id === orderId)
    if (order) {
      order.status = newStatus
    }
  }

  const updateServiceRequestStatus = (requestId: string, newStatus: GuestServiceRequest['status']) => {
    const request = guestServiceRequests.value.find(req => req.id === requestId)
    if (request) {
      request.status = newStatus
    }
  }

  const updateActivityRegistrationStatus = (registrationId: string, newStatus: ActivityRegistration['status']) => {
    const registration = activityRegistrations.value.find(r => r.id === registrationId)
    if (registration) {
      registration.status = newStatus
    }
  }

  // Service Category Actions (Activity kategorileri için)
  const addServiceCategory = (categoryData: Omit<ServiceCategory, 'id' | 'createdAt'>) => {
    const newCategory: ServiceCategory = {
      id: Date.now().toString(),
      ...categoryData
    }
    categories.value.push(newCategory)
  }

  const updateServiceCategory = (id: string, categoryData: Partial<ServiceCategory>) => {
    const index = categories.value.findIndex(cat => cat.id === id)
    if (index !== -1) {
      categories.value[index] = {
        ...categories.value[index],
        ...categoryData
      }
    }
  }

  const deleteServiceCategory = (id: string) => {
    const index = categories.value.findIndex(cat => cat.id === id)
    if (index !== -1) {
      categories.value.splice(index, 1)
    }
  }

  // Development-only: Set up mock staff session for testing
  const setStaffSessionForDevelopment = async (userId: string) => {
    isLoading.value = true
    error.value = null

    try {
      console.log('[Dev] Setting up staff session for user:', userId)
      
      const { data: userProfile, error: profileError } = await supabase
        .from('user_profiles')
        .select(`
          *,
          hotels!user_profiles_hotel_id_fkey(*)
        `)
        .eq('id', userId)
        .single()

      if (profileError) {
        throw new Error(`User profile could not be fetched: ${profileError.message}`)
      }

      if (!userProfile || !userProfile.hotels) {
        throw new Error('User profile or associated hotel not found.')
      }

      // Set the authenticated state (this would normally be handled by auth)
      console.log(`[Dev] Staff session for ${userProfile.full_name} at ${userProfile.hotels.name} initialized.`)
      
      // Fetch initial data for the hotel
      if (userProfile.hotel_id) {
        await fetchMenuItems(userProfile.hotel_id)
        await fetchCategories(userProfile.hotel_id)
      }

      return { 
        success: true, 
        user: userProfile, 
        hotel: userProfile.hotels 
      }

    } catch (err: any) {
      console.error('[Dev] Error during staff session setup:', err)
      error.value = `Failed to set staff session: ${err.message}`
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  // Service Categories computed
  const serviceCategories = computed(() => categories.value)

  // Initialize all data
  const initializeData = async () => {
    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined

      // Use Promise.allSettled to prevent one failure from stopping all data loading
      const results = await Promise.allSettled([
        fetchEvents(),
        fetchRoomServiceOrders(),
        fetchActivities(),
        fetchMenuItems(hotelId),
        fetchCategories(hotelId)
      ])

      // Log any failures for debugging
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const operations = ['fetchEvents', 'fetchRoomServiceOrders', 'fetchActivities', 'fetchMenuItems', 'fetchCategories']
          console.error(`Hotel Management Store: ${operations[index]} failed:`, result.reason)
        }
      })
    } catch (error) {
      console.error('Hotel Management Store: Critical error during data initialization:', error)
    }
  }

  // Note: initializeData is not called automatically to prevent blank pages
  // Components should call initializeData() when they're ready to load data

  return {
    // State
    menuItems,
    events,
    categories,
    serviceCategories,
    activities,
    guestServices,
    roomServiceOrders,
    guestServiceRequests,
    activityRegistrations,
    maintenanceFloors,
    maintenanceRooms,
    maintenanceStaff,
    communicationChannels,
    directMessages,
    communicationMessages,
    isLoading,
    error,

    // Maintenance loading states
    maintenanceFloorsLoading,
    maintenanceFloorsError,
    maintenanceRoomsLoading,
    maintenanceRoomsError,

    // New data loading states
    eventsLoading,
    eventsError,
    roomServiceOrdersLoading,
    roomServiceOrdersError,
    activitiesLoading,
    activitiesError,

    // Maintenance communication specific states
    maintenanceCommunicationChannels: communicationChannels,
    maintenanceDirectMessages: directMessages,
    maintenanceChatMessages: communicationMessages,

    // Fetch Actions
    fetchMenuItems,
    fetchCategories,
    fetchMaintenanceRooms,
    fetchMaintenanceFloors,
    fetchEvents,
    fetchRoomServiceOrders,
    fetchActivities,
    initializeData,

    // Menu Item Actions
    addMenuItem,
    updateMenuItem,
    deleteMenuItem,
    toggleMenuItemAvailability,
    
    // Event Actions
    addEvent,
    updateEvent,
    deleteEvent,
    toggleEventStatus,
    
    // Category Actions
    addCategory,
    updateCategory,
    deleteCategory,
    
    // Service Category Actions
    addServiceCategory,
    updateServiceCategory,
    deleteServiceCategory,
    
    // Activity Actions
    addActivity,
    updateActivity,
    deleteActivity,
    toggleActivityStatus,
    
    // Guest Service Actions
    addGuestService,
    updateGuestService,
    deleteGuestService,
    toggleGuestServiceStatus,
    
    // Operational Tracking Actions
    updateOrderStatus,
    updateServiceRequestStatus,
    updateActivityRegistrationStatus,
    setStaffSessionForDevelopment
  }
}) 