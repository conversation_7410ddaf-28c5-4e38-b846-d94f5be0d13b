import { ref, computed, readonly } from 'vue'
import { defineStore } from 'pinia'
import { supabase } from '@hotelexia/shared-supabase-client'
import type { MenuItem, CartItem, Order } from '@hotelexia/shared-supabase-client'
import { useGuestAuthStore } from './guestAuthStore'

export const useRoomServiceStore = defineStore('roomService', () => {
  // State
  const menuItems = ref<MenuItem[]>([])
  const cart = ref<CartItem[]>([])
  const orders = ref<Order[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const cancelling = ref<string | null>(null) // Track which order is being cancelled

  // Helper function to categorize menu items
  const categorizeMenuItem = (item: MenuItem): string => {
    const name = item.name?.toLowerCase() || ''
    const tags = item.tags || []

    // Check for beverages
    if (name.includes('kahve') || name.includes('çay') || name.includes('suyu') ||
        name.includes('içecek') || name.includes('smoothie') || name.includes('juice') ||
        tags.some((tag: string) => tag.toLowerCase().includes('içecek') || tag.toLowerCase().includes('sıcak'))) {
      return 'Beverages'
    }

    // Check for desserts
    if (name.includes('tatlı') || name.includes('baklava') || name.includes('tiramisu') ||
        name.includes('sufle') || name.includes('cheesecake') || name.includes('dondurma') ||
        tags.some((tag: string) => tag.toLowerCase().includes('tatlı') || tag.toLowerCase().includes('şerbet') || tag.toLowerCase().includes('krema'))) {
      return 'Desserts'
    }

    // Check for extras/appetizers
    if (name.includes('meze') || name.includes('humus') || name.includes('bruschetta') ||
        name.includes('ekmek') || name.includes('sepet') || name.includes('tabağı') ||
        tags.some((tag: string) => tag.toLowerCase().includes('mezze') || tag.toLowerCase().includes('aperitif'))) {
      return 'Extras'
    }

    // Default to Food for main dishes
    return 'Food'
  }

  // Getters
  const menuByCategory = computed(() => {
    const categories: Record<string, MenuItem[]> = {
      Food: [],
      Beverages: [],
      Desserts: [],
      Extras: []
    }

    menuItems.value.forEach(item => {
      if (item.is_available) {
        const category = categorizeMenuItem(item)
        categories[category].push(item)
      }
    })

    return categories
  })

  const cartTotal = computed(() => {
    return cart.value.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  })

  const cartItemsCount = computed(() => {
    return cart.value.reduce((count, item) => count + item.quantity, 0)
  })

  const isCartEmpty = computed(() => cart.value.length === 0)

  const currentOrders = computed(() => 
    orders.value.filter(order => ['pending', 'confirmed', 'in_progress'].includes(order.status || ''))
  )
  
  const pastOrders = computed(() => 
    orders.value.filter(order => ['delivered', 'canceled'].includes(order.status || ''))
  )

  // Actions
  async function fetchMenu(hotelId: string) {
    isLoading.value = true
    error.value = null

    try {
      const { data, error: dbError } = await supabase
        .from('menu_items')
        .select('*')
        .eq('hotel_id', hotelId)
        .eq('is_available', true)
        .order('category', { ascending: true })
        .order('name', { ascending: true })

      if (dbError) {
        console.error('Menu fetch error:', dbError)
        throw new Error('Menü yüklenirken hata oluştu.')
      }

      menuItems.value = data || []
    } catch (err: any) {
      console.error('Fetch menu error:', err)
      error.value = err.message || 'Menü yüklenemedi.'
    } finally {
      isLoading.value = false
    }
  }

  function addToCart(menuItem: MenuItem, quantity: number = 1) {
    const existingItem = cart.value.find(item => item.id === menuItem.id)
    
    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      cart.value.push({
        ...menuItem,
        quantity
      })
    }

    // Save cart to localStorage
    saveCartToStorage()
  }

  function removeFromCart(menuItemId: string) {
    const index = cart.value.findIndex(item => item.id === menuItemId)
    if (index > -1) {
      cart.value.splice(index, 1)
      saveCartToStorage()
    }
  }

  function updateCartItemQuantity(menuItemId: string, quantity: number) {
    const item = cart.value.find(item => item.id === menuItemId)
    if (item) {
      if (quantity <= 0) {
        removeFromCart(menuItemId)
      } else {
        item.quantity = quantity
        saveCartToStorage()
      }
    }
  }

  function clearCart() {
    cart.value = []
    localStorage.removeItem('hotelexia_cart')
  }

  function saveCartToStorage() {
    localStorage.setItem('hotelexia_cart', JSON.stringify(cart.value))
  }

  function loadCartFromStorage() {
    try {
      const storedCart = localStorage.getItem('hotelexia_cart')
      if (storedCart) {
        cart.value = JSON.parse(storedCart)
      }
    } catch (err) {
      console.error('Error loading cart from storage:', err)
      cart.value = []
    }
  }

  async function placeOrder(notes?: string) {
    const guestAuthStore = useGuestAuthStore()

    if (!guestAuthStore.isAuthenticated || cart.value.length === 0) {
      throw new Error('Sipariş vermek için giriş yapmalı ve sepetinizde ürün bulunmalıdır.')
    }

    isLoading.value = true
    error.value = null

    try {
      const guestSession = guestAuthStore.currentGuest!

      // Calculate totals
      const totalAmount = cartTotal.value

      // Create order in orders table
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .insert({
          hotel_id: guestSession.hotel_id,
          reservation_id: guestSession.id,
          room_number: guestSession.room_number,
          total_price: totalAmount,
          status: 'pending',
          notes: notes || null
        })
        .select()
        .single()

      if (orderError) {
        console.error('Order creation error:', orderError)
        throw new Error('Sipariş oluşturulurken hata oluştu.')
      }

      // Create order items
      const orderItems = cart.value.map(item => ({
        order_id: orderData.id,
        menu_item_id: item.id,
        quantity: item.quantity,
        price_at_time_of_order: item.price
      }))

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems)

      if (itemsError) {
        console.error('Order items creation error:', itemsError)
        // Try to delete the order if items failed
        await supabase.from('orders').delete().eq('id', orderData.id)
        throw new Error('Sipariş ürünleri oluşturulurken hata oluştu.')
      }

      // Clear cart after successful order
      clearCart()

      // Refresh orders
      await fetchOrders(guestSession.hotel_id)

      return { success: true, orderId: orderData.id, orderNumber: `ORD-${orderData.id.slice(-8)}` }
    } catch (err: any) {
      console.error('Place order error:', err)
      error.value = err.message || 'Sipariş verirken hata oluştu.'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const fetchOrders = async (hotelId: string) => {
    const guestAuthStore = useGuestAuthStore()

    if (!guestAuthStore.isAuthenticated) {
      return
    }

    isLoading.value = true
    error.value = null

    try {
      const guestSession = guestAuthStore.currentGuest!

      // Fetch orders for the current guest from orders table
      const { data: ordersData, error: ordersError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items:order_items(*)
        `)
        .eq('hotel_id', hotelId)
        .eq('room_number', guestSession.room_number)
        .order('created_at', { ascending: false })

      if (ordersError) {
        console.error('Orders fetch error:', ordersError)
        throw new Error('Siparişler yüklenirken hata oluştu.')
      }

      // Transform orders data to match Order interface
      const transformedOrders = (ordersData || []).map(order => ({
        id: order.id,
        hotel_id: order.hotel_id,
        reservation_id: order.reservation_id,
        room_id: null,
        order_number: `ORD-${order.id.slice(-8)}`,
        guest_name: guestSession.guest_name || 'Guest',
        room_number: order.room_number,
        guest_phone: guestSession.guest_phone || null,
        status: order.status,
        special_instructions: order.notes,
        dietary_notes: null,
        order_time: order.created_at,
        requested_delivery_time: null,
        estimated_delivery_time: null,
        actual_delivery_time: null,
        preparation_started_at: null,
        preparation_completed_at: null,
        subtotal: order.total_price,
        tax_amount: null,
        service_charge: null,
        delivery_fee: null,
        discount_amount: null,
        total_amount: order.total_price,
        payment_method: null,
        payment_status: null,
        tip_amount: null,
        assigned_kitchen_staff_id: null,
        assigned_delivery_staff_id: null,
        kitchen_notes: null,
        guest_rating: null,
        guest_feedback: null,
        delivery_rating: null,
        created_at: order.created_at,
        updated_at: order.updated_at,
        order_items: (order.order_items || []).map((item: any) => ({
          id: item.id,
          order_id: item.order_id,
          menu_item_id: item.menu_item_id,
          quantity: item.quantity,
          price_at_time_of_order: item.price_at_time_of_order,
          created_at: item.created_at,
          menu_item: item.menu_item
        }))
      }))

      orders.value = transformedOrders
    } catch (err) {
      console.error('Error fetching orders:', err)
      error.value = err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu'
    } finally {
      isLoading.value = false
    }
  }

  const cancelOrder = async (orderId: string): Promise<boolean> => {
    cancelling.value = orderId
    error.value = null
    
         try {
       // First check if order can be cancelled
      const { data: currentOrder, error: fetchError } = await supabase
        .from('orders')
        .select('status')
        .eq('id', orderId)
        .single()

      if (fetchError) {
        throw new Error(`Sipariş bilgisi alınamadı: ${fetchError.message}`)
      }

      if (!currentOrder || !['pending', 'confirmed'].includes(currentOrder.status || '')) {
        throw new Error('Bu sipariş artık iptal edilemez. Sadece bekleyen ve onaylanmış siparişler iptal edilebilir.')
      }

      // Update order status to 'canceled'
      const { data, error: updateError } = await supabase
        .from('orders')
        .update({
          status: 'canceled',
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()

      if (updateError) {
        console.error('Supabase update error:', updateError)
        throw new Error(`Sipariş iptal edilirken hata oluştu: ${updateError.message}`)
      }

      if (!data || data.length === 0) {
        throw new Error('Sipariş güncellenemedi. Lütfen tekrar deneyin.')
      }

      // Update local state
      const orderIndex = orders.value.findIndex(order => order.id === orderId)
      if (orderIndex !== -1) {
        orders.value[orderIndex] = { ...orders.value[orderIndex], status: 'canceled' }
      }

      return true
    } catch (err) {
      console.error('Error cancelling order:', err)
      error.value = err instanceof Error ? err.message : 'Sipariş iptal edilirken bilinmeyen bir hata oluştu'
      return false
    } finally {
      cancelling.value = null
    }
  }

  const clearError = () => {
    error.value = null
  }

  function getMenuItemById(id: string): MenuItem | undefined {
    return menuItems.value.find(item => item.id === id)
  }

  function getCartItemQuantity(menuItemId: string): number {
    const item = cart.value.find(item => item.id === menuItemId)
    return item ? item.quantity : 0
  }

  function formatPrice(price: number): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(price)
  }

  return {
    // State
    menuItems: readonly(menuItems),
    cart: readonly(cart),
    orders: readonly(orders),
    isLoading: readonly(isLoading),
    error: readonly(error),
    cancelling: readonly(cancelling),
    
    // Getters
    menuByCategory,
    cartTotal,
    cartItemsCount,
    isCartEmpty,
    currentOrders,
    pastOrders,
    
    // Actions
    fetchMenu,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    loadCartFromStorage,
    placeOrder,
    fetchOrders,
    cancelOrder,
    clearError,
    getMenuItemById,
    getCartItemQuantity,
    formatPrice
  }
}) 