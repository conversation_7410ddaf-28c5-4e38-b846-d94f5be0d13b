import { Page, expect } from '@playwright/test'

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Wait for Supabase data to load with timeout
   */
  async waitForSupabaseData(selector: string, timeout = 10000) {
    await this.page.waitForSelector(selector, { timeout })
    // Additional wait for data to populate
    await this.page.waitForTimeout(1000)
  }

  /**
   * Check if page has authentication form
   */
  async hasAuthForm(): Promise<boolean> {
    const emailInput = this.page.locator('input[type="email"], input[name="email"]')
    const passwordInput = this.page.locator('input[type="password"], input[name="password"]')
    
    return (await emailInput.count() > 0) && (await passwordInput.count() > 0)
  }

  /**
   * Check if page has main content
   */
  async hasMainContent(): Promise<boolean> {
    const contentSelectors = [
      'main',
      '.dashboard',
      '.portal',
      '[data-testid="content"]',
      '.app-content'
    ]
    
    for (const selector of contentSelectors) {
      if (await this.page.locator(selector).count() > 0) {
        return true
      }
    }
    return false
  }

  /**
   * Check for loading states
   */
  async hasLoadingState(): Promise<boolean> {
    const loadingSelectors = [
      '.loading',
      '.spinner',
      '[data-testid="loading"]',
      '.skeleton',
      'text=/yükleniyor/i',
      'text=/loading/i'
    ]
    
    for (const selector of loadingSelectors) {
      if (await this.page.locator(selector).count() > 0) {
        return true
      }
    }
    return false
  }

  /**
   * Check for error states
   */
  async hasErrorState(): Promise<boolean> {
    const errorSelectors = [
      '.error-boundary',
      '.error-message',
      '[data-testid="error"]',
      '.alert-error',
      'text=/hata/i',
      'text=/error/i'
    ]
    
    for (const selector of errorSelectors) {
      if (await this.page.locator(selector).count() > 0) {
        return true
      }
    }
    return false
  }

  /**
   * Wait for page to be in a stable state (not loading)
   */
  async waitForStableState(timeout = 15000) {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      const isLoading = await this.hasLoadingState()
      if (!isLoading) {
        // Wait a bit more to ensure stability
        await this.page.waitForTimeout(1000)
        const stillNotLoading = await this.hasLoadingState()
        if (!stillNotLoading) {
          return
        }
      }
      await this.page.waitForTimeout(500)
    }
    
    throw new Error(`Page did not reach stable state within ${timeout}ms`)
  }

  /**
   * Get data count from common list patterns
   */
  async getDataCount(dataType: 'hotels' | 'rooms' | 'users' | 'reservations' | 'tasks'): Promise<number> {
    const selectors = {
      hotels: ['table tbody tr', '.hotel-card', '[data-testid="hotel"]'],
      rooms: ['table tbody tr', '.room-card', '[data-testid="room"]', '.room-grid > div'],
      users: ['table tbody tr', '.user-card', '[data-testid="user"]'],
      reservations: ['table tbody tr', '.reservation-card', '[data-testid="reservation"]'],
      tasks: ['table tbody tr', '.task-card', '[data-testid="task"]']
    }
    
    for (const selector of selectors[dataType]) {
      const count = await this.page.locator(selector).count()
      if (count > 0) {
        return count
      }
    }
    
    return 0
  }

  /**
   * Check if data is from Supabase (not dummy data)
   */
  async validateSupabaseData(dataType: 'hotels' | 'rooms' | 'users'): Promise<boolean> {
    const count = await this.getDataCount(dataType)
    
    if (count === 0) {
      // Check for empty state
      const emptyStateSelectors = [
        '.empty-state',
        '.no-data',
        'text=/veri bulunamadı/i',
        'text=/no data/i'
      ]
      
      for (const selector of emptyStateSelectors) {
        if (await this.page.locator(selector).count() > 0) {
          return true // Empty state is valid
        }
      }
      return false
    }
    
    // If we have data, check for realistic patterns
    if (dataType === 'hotels' && count > 0) {
      // Check for hotel-specific data patterns
      const hotelNames = await this.page.locator('table tbody tr td:first-child, .hotel-card h3, .hotel-name').allTextContents()
      return hotelNames.some(name => name && name.length > 2 && !name.includes('Test Hotel'))
    }
    
    return count > 0
  }

  /**
   * Monitor console errors during test execution
   */
  async monitorConsoleErrors(): Promise<string[]> {
    const errors: string[] = []
    
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    return errors
  }

  /**
   * Filter critical errors from console logs
   */
  filterCriticalErrors(errors: string[]): string[] {
    return errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('WebSocket') &&
      !error.includes('Failed to load resource') &&
      !error.toLowerCase().includes('warning')
    )
  }

  /**
   * Test form validation
   */
  async testFormValidation(formSelector = 'form'): Promise<boolean> {
    const form = this.page.locator(formSelector)
    const submitButton = form.locator('button[type="submit"], button:has-text("Giriş"), button:has-text("Submit")')
    
    if (await submitButton.count() === 0) {
      return false
    }
    
    // Try to submit empty form
    await submitButton.click()
    await this.page.waitForTimeout(1000)
    
    // Check for validation errors
    const hasValidationErrors = await this.page.locator('.error, .invalid, [class*="error"]').count() > 0
    const hasInvalidInputs = await form.locator('input:invalid').count() > 0
    
    return hasValidationErrors || hasInvalidInputs
  }

  /**
   * Navigate and verify MFE loading
   */
  async navigateAndVerifyMFE(url: string, expectedContent?: string): Promise<boolean> {
    await this.page.goto(url)
    await this.page.waitForTimeout(3000)
    
    const hasAuth = await this.hasAuthForm()
    const hasContent = await this.hasMainContent()
    const hasError = await this.hasErrorState()
    
    if (expectedContent) {
      const hasExpectedContent = await this.page.locator(`text=${expectedContent}`).count() > 0
      return hasExpectedContent || hasAuth || hasContent
    }
    
    return hasAuth || hasContent || hasError
  }

  /**
   * Test data consistency between two pages
   */
  async compareDataConsistency(
    page1Selector: string, 
    page2Selector: string, 
    tolerance = 0
  ): Promise<{ consistent: boolean; page1Count: number; page2Count: number }> {
    const page1Count = await this.page.locator(page1Selector).count()
    
    // Navigate to second page or use second selector
    const page2Count = await this.page.locator(page2Selector).count()
    
    const difference = Math.abs(page1Count - page2Count)
    const consistent = difference <= tolerance
    
    return { consistent, page1Count, page2Count }
  }

  /**
   * Wait for real-time updates
   */
  async waitForRealtimeUpdate(selector: string, initialCount: number, timeout = 10000): Promise<boolean> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      const currentCount = await this.page.locator(selector).count()
      if (currentCount !== initialCount) {
        return true
      }
      await this.page.waitForTimeout(500)
    }
    
    return false
  }

  /**
   * Test offline/online behavior
   */
  async testOfflineBehavior(): Promise<{ hasOfflineHandling: boolean; maintainsData: boolean }> {
    // Get initial data count
    const initialDataCount = await this.getDataCount('hotels')

    // Go offline
    await this.page.context().setOffline(true)
    await this.page.reload()
    await this.page.waitForTimeout(3000)

    const hasOfflineMessage = await this.page.locator('text=/offline/i, text=/connection/i').count() > 0
    const hasErrorBoundary = await this.hasErrorState()
    const maintainsData = await this.getDataCount('hotels') > 0

    // Go back online
    await this.page.context().setOffline(false)
    await this.page.reload()
    await this.page.waitForTimeout(3000)

    const restoredDataCount = await this.getDataCount('hotels')

    return {
      hasOfflineHandling: hasOfflineMessage || hasErrorBoundary,
      maintainsData: maintainsData || (restoredDataCount >= initialDataCount)
    }
  }

  /**
   * Test RLS (Row Level Security) by attempting unauthorized access
   */
  async testRLSProtection(hotelId: string, unauthorizedHotelId: string): Promise<boolean> {
    const currentUrl = this.page.url()

    // Try to access another hotel's data
    const manipulatedUrl = currentUrl.replace(hotelId, unauthorizedHotelId)
    await this.page.goto(manipulatedUrl)
    await this.page.waitForTimeout(2000)

    // Check if access is denied
    const hasAccessDenied = await this.page.locator('text=/access denied/i, text=/yetkisiz/i, .unauthorized').count() > 0
    const isRedirected = !this.page.url().includes(unauthorizedHotelId)
    const hasAuthForm = await this.hasAuthForm()

    return hasAccessDenied || isRedirected || hasAuthForm
  }

  /**
   * Test RBAC (Role-Based Access Control) for specific routes
   */
  async testRBACAccess(routes: string[], expectedRole: string): Promise<{ route: string; hasAccess: boolean; reason: string }[]> {
    const results = []

    for (const route of routes) {
      await this.page.goto(route)
      await this.page.waitForTimeout(2000)

      const hasAuth = await this.hasAuthForm()
      const hasContent = await this.hasMainContent()
      const hasAccessDenied = await this.page.locator('text=/access denied/i, text=/yetkisiz/i').count() > 0
      const isRedirected = this.page.url() !== route

      let hasAccess = false
      let reason = ''

      if (hasContent && !hasAuth && !hasAccessDenied) {
        hasAccess = true
        reason = 'Content accessible'
      } else if (hasAuth) {
        reason = 'Authentication required'
      } else if (hasAccessDenied) {
        reason = 'Access denied'
      } else if (isRedirected) {
        reason = 'Redirected'
      } else {
        reason = 'Unknown state'
      }

      results.push({ route, hasAccess, reason })
    }

    return results
  }

  /**
   * Monitor and collect security-related network requests
   */
  async monitorSecurityRequests(): Promise<{ unauthorized: string[]; errors: string[]; suspicious: string[] }> {
    const unauthorized: string[] = []
    const errors: string[] = []
    const suspicious: string[] = []

    this.page.on('response', response => {
      const url = response.url()
      const status = response.status()

      if (status === 401 || status === 403) {
        unauthorized.push(`${status}: ${url}`)
      } else if (status >= 400 && status < 500) {
        errors.push(`${status}: ${url}`)
      }

      // Check for suspicious patterns
      if (url.includes('admin') || url.includes('system') || url.includes('config')) {
        suspicious.push(`Suspicious access: ${url}`)
      }
    })

    return { unauthorized, errors, suspicious }
  }

  /**
   * Test input sanitization and XSS prevention
   */
  async testInputSanitization(inputSelector: string, testPayloads: string[] = [
    '<script>alert("xss")</script>',
    '"><script>alert("xss")</script>',
    'javascript:alert("xss")',
    '<img src=x onerror=alert("xss")>',
    '${alert("xss")}'
  ]): Promise<{ payload: string; prevented: boolean }[]> {
    const results = []
    const alertDialogs: string[] = []

    // Monitor for alert dialogs
    this.page.on('dialog', dialog => {
      alertDialogs.push(dialog.message())
      dialog.dismiss()
    })

    for (const payload of testPayloads) {
      const input = this.page.locator(inputSelector)
      const inputExists = await input.count() > 0

      if (inputExists) {
        await input.fill(payload)
        await this.page.waitForTimeout(1000)

        // Check if XSS was prevented (no alert dialog)
        const prevented = alertDialogs.length === 0
        results.push({ payload, prevented })

        // Clear the input
        await input.fill('')
      }
    }

    return results
  }

  /**
   * Test session management and timeout handling
   */
  async testSessionManagement(): Promise<{ persistsOnReload: boolean; handlesTimeout: boolean }> {
    const initialAuthState = !(await this.hasAuthForm())

    if (!initialAuthState) {
      return { persistsOnReload: false, handlesTimeout: false }
    }

    // Test session persistence on reload
    await this.page.reload()
    await this.page.waitForTimeout(2000)
    const persistsOnReload = !(await this.hasAuthForm())

    // Test session timeout (simulate by clearing storage)
    await this.page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })

    await this.page.reload()
    await this.page.waitForTimeout(2000)
    const handlesTimeout = await this.hasAuthForm()

    return { persistsOnReload, handlesTimeout }
  }
}

/**
 * Common test data and selectors
 */
export const TestSelectors = {
  auth: {
    emailInput: 'input[type="email"], input[name="email"]',
    passwordInput: 'input[type="password"], input[name="password"]',
    submitButton: 'button[type="submit"], button:has-text("Giriş"), button:has-text("Login")',
    logoutButton: 'button:has-text("Çıkış"), button:has-text("Logout"), a:has-text("Çıkış")'
  },
  data: {
    hotels: 'table tbody tr, .hotel-card, [data-testid="hotel"]',
    rooms: 'table tbody tr, .room-card, [data-testid="room"]',
    users: 'table tbody tr, .user-card, [data-testid="user"]',
    reservations: 'table tbody tr, .reservation-card, [data-testid="reservation"]',
    tasks: 'table tbody tr, .task-card, [data-testid="task"]'
  },
  ui: {
    loading: '.loading, .spinner, [data-testid="loading"]',
    error: '.error-boundary, .error-message, [data-testid="error"]',
    content: 'main, .dashboard, .portal, [data-testid="content"]',
    notifications: '.notification-bell, [data-testid="notifications"], .notifications'
  }
}

/**
 * Test URLs for different MFEs
 */
export const TestURLs = {
  appShell: 'http://localhost:5000',
  management: {
    base: 'http://localhost:5001',
    login: 'http://localhost:5001/auth/login',
    dashboard: 'http://localhost:5001/dashboard',
    hotels: 'http://localhost:5001/hotels',
    users: 'http://localhost:5001/users'
  },
  hotel: {
    base: 'http://localhost:5002',
    login: 'http://localhost:5002/auth/login',
    dashboard: 'http://localhost:5002/dashboard',
    housekeeping: 'http://localhost:5002/housekeeping/rooms',
    maintenance: 'http://localhost:5002/maintenance/tasks'
  },
  customer: {
    base: 'http://localhost:3001',
    login: 'http://localhost:3001/auth/login',
    reservations: 'http://localhost:3001/reservations',
    roomService: 'http://localhost:3001/room-service'
  }
}
