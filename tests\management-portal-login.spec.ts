import { test, expect } from '@playwright/test'

test.describe('Management Portal Login', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing session storage
    await page.goto('http://localhost:5002/')
    await page.evaluate(() => {
      sessionStorage.clear()
      localStorage.clear()
    })
  })

  test('should redirect to login when not authenticated', async ({ page }) => {
    // Navigate to management portal root
    await page.goto('http://localhost:5002/')
    
    // Should be redirected to login page
    await expect(page).toHaveURL(/.*\/login/)
    
    // Check page title
    await expect(page).toHaveTitle(/Süper Admin G<PERSON>i/)
  })

  test('login page should not show sidebar and navbar', async ({ page }) => {
    // Navigate directly to login page
    await page.goto('http://localhost:5002/login')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Check that login form is visible
    await expect(page.locator('h2:has-text("Süper Admin <PERSON>")')).toBeVisible()
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    
    // Check that sidebar is NOT visible
    await expect(page.locator('[data-testid="sidebar"]')).not.toBeVisible()
    await expect(page.locator('nav[role="navigation"]')).not.toBeVisible()
    
    // Check that navbar is NOT visible
    await expect(page.locator('[data-testid="navbar"]')).not.toBeVisible()
    await expect(page.locator('header')).not.toBeVisible()
    
    // Check that only login content is visible
    await expect(page.locator('body')).toHaveClass(/bg-gradient-to-br/)
    
    // Verify login form elements
    await expect(page.locator('label:has-text("E-posta Adresi")')).toBeVisible()
    await expect(page.locator('label:has-text("Şifre")')).toBeVisible()
    await expect(page.locator('button:has-text("Giriş Yap")')).toBeVisible()
  })

  test('should show validation errors for invalid inputs', async ({ page }) => {
    await page.goto('http://localhost:5002/login')
    
    // Try to submit empty form
    await page.click('button:has-text("Giriş Yap")')
    
    // Check HTML5 validation (required fields)
    const emailInput = page.locator('input[type="email"]')
    const passwordInput = page.locator('input[type="password"]')
    
    await expect(emailInput).toHaveAttribute('required')
    await expect(passwordInput).toHaveAttribute('required')
    
    // Test invalid email format
    await emailInput.fill('invalid-email')
    await passwordInput.fill('short')
    
    // Check for validation messages (if implemented)
    await page.click('button:has-text("Giriş Yap")')
    
    // Should show email validation error
    await expect(page.locator('text=Geçerli bir e-posta adresi girin')).toBeVisible()
  })

  test('should handle login attempt with invalid credentials', async ({ page }) => {
    await page.goto('http://localhost:5002/login')
    
    // Fill in invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'wrongpassword')
    
    // Submit form
    await page.click('button:has-text("Giriş Yap")')
    
    // Should show loading state
    await expect(page.locator('button:has-text("Giriş yapılıyor...")')).toBeVisible()
    
    // Wait for error message
    await expect(page.locator('text=E-posta adresi veya şifre hatalı')).toBeVisible({ timeout: 10000 })
    
    // Should still be on login page
    await expect(page).toHaveURL(/.*\/login/)
    
    // Sidebar and navbar should still not be visible
    await expect(page.locator('[data-testid="sidebar"]')).not.toBeVisible()
    await expect(page.locator('[data-testid="navbar"]')).not.toBeVisible()
  })

  test('should login successfully with valid SUPER_ADMIN credentials', async ({ page }) => {
    await page.goto('http://localhost:5002/login')
    
    // Use development test credentials
    await page.click('button:has-text("Test Hesabı ile Doldur")')
    
    // Verify credentials are filled
    await expect(page.locator('input[type="email"]')).toHaveValue('<EMAIL>')
    await expect(page.locator('input[type="password"]')).toHaveValue('admin123')
    
    // Submit form
    await page.click('button:has-text("Giriş Yap")')
    
    // Should show loading state
    await expect(page.locator('button:has-text("Giriş yapılıyor...")')).toBeVisible()
    
    // Should redirect to dashboard (or show error if credentials don't exist)
    await page.waitForURL(/.*\/(dashboard|login)/, { timeout: 10000 })
    
    // If redirected to dashboard, check that sidebar and navbar are now visible
    if (page.url().includes('/dashboard')) {
      await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
      await expect(page.locator('[data-testid="navbar"]')).toBeVisible()
    }
  })

  test('should logout and redirect to login page', async ({ page }) => {
    // First, simulate being logged in by setting session storage
    await page.goto('http://localhost:5002/login')
    
    await page.evaluate(() => {
      sessionStorage.setItem('hotelexia_management_user', JSON.stringify({
        id: 'test-id',
        email: '<EMAIL>',
        fullName: 'Test Admin',
        role: 'SUPER_ADMIN',
        hotelId: null
      }))
    })
    
    // Navigate to dashboard
    await page.goto('http://localhost:5002/dashboard')
    
    // Should be on dashboard with sidebar and navbar visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
    await expect(page.locator('[data-testid="navbar"]')).toBeVisible()
    
    // Click logout button
    await page.click('[data-testid="user-menu-button"]')
    await page.click('button:has-text("Çıkış Yap")')
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/login/)
    
    // Sidebar and navbar should not be visible
    await expect(page.locator('[data-testid="sidebar"]')).not.toBeVisible()
    await expect(page.locator('[data-testid="navbar"]')).not.toBeVisible()
    
    // Login form should be visible
    await expect(page.locator('h2:has-text("Süper Admin Girişi")')).toBeVisible()
    
    // Session should be cleared
    const sessionData = await page.evaluate(() => sessionStorage.getItem('hotelexia_management_user'))
    expect(sessionData).toBeNull()
  })

  test('should handle direct navigation to protected routes', async ({ page }) => {
    // Try to navigate directly to a protected route
    await page.goto('http://localhost:5002/dashboard')
    
    // Should be redirected to login
    await expect(page).toHaveURL(/.*\/login/)
    
    // Should not show sidebar/navbar
    await expect(page.locator('[data-testid="sidebar"]')).not.toBeVisible()
    await expect(page.locator('[data-testid="navbar"]')).not.toBeVisible()
    
    // Try other protected routes
    await page.goto('http://localhost:5002/hotels')
    await expect(page).toHaveURL(/.*\/login/)
    
    await page.goto('http://localhost:5002/settings')
    await expect(page).toHaveURL(/.*\/login/)
  })

  test('should preserve redirect path after login', async ({ page }) => {
    // Try to access a specific protected route
    await page.goto('http://localhost:5002/hotels')
    
    // Should be redirected to login
    await expect(page).toHaveURL(/.*\/login/)
    
    // Check if redirect path is stored (this would need to be implemented)
    const redirectPath = await page.evaluate(() => 
      sessionStorage.getItem('hotelexia_management_redirect')
    )
    
    // If redirect functionality is implemented, it should store the original path
    if (redirectPath) {
      expect(redirectPath).toBe('/hotels')
    }
  })
})
