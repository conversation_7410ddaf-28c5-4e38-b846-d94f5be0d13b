<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-6">Sistem Ayarları</h1>
    
    <!-- Settings Sections -->
    <div class="space-y-6">
      <!-- Hotel Information Section -->
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Otel Bilgileri</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Otel Adı
            </label>
            <input
              type="text"
              v-model="hotelName"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
              placeholder="Otel adını giriniz"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Telefon
            </label>
            <input
              type="tel"
              v-model="hotelPhone"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
              placeholder="Telefon numarası"
            />
          </div>
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Adres
            </label>
            <textarea
              v-model="hotelAddress"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
              placeholder="Otel adresi"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Bildirim Ayarları</h2>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-navy-700 dark:text-white">E-posta Bildirimleri</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">Önemli güncellemeler için e-posta bildirimleri</p>
            </div>
            <button
              @click="toggleEmailNotifications"
              class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
              :class="emailNotifications ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                :class="emailNotifications ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-navy-700 dark:text-white">SMS Bildirimleri</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">Acil durumlar için SMS bildirimleri</p>
            </div>
            <button
              @click="toggleSmsNotifications"
              class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
              :class="smsNotifications ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                :class="smsNotifications ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
        </div>
      </div>

      <!-- System Preferences -->
      <div class="bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Sistem Tercihleri</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Dil
            </label>
            <select
              v-model="selectedLanguage"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
            >
              <option value="tr">Türkçe</option>
              <option value="en">English</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Saat Dilimi
            </label>
            <select
              v-model="selectedTimezone"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
            >
              <option value="Europe/Istanbul">İstanbul (UTC+3)</option>
              <option value="UTC">UTC</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Save Button -->
      <div class="flex justify-end">
        <button
          @click="saveSettings"
          class="bg-brand-500 hover:bg-brand-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
        >
          Ayarları Kaydet
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Hotel Information
const hotelName = ref('Demo Otel')
const hotelPhone = ref('+90 ************')
const hotelAddress = ref('Örnek Mahalle, Demo Sokak No:123, İstanbul')

// Notification Settings
const emailNotifications = ref(true)
const smsNotifications = ref(false)

// System Preferences
const selectedLanguage = ref('tr')
const selectedTimezone = ref('Europe/Istanbul')

const toggleEmailNotifications = () => {
  emailNotifications.value = !emailNotifications.value
}

const toggleSmsNotifications = () => {
  smsNotifications.value = !smsNotifications.value
}

const saveSettings = () => {
  // TODO: Implement save functionality with backend
  alert('Ayarlar kaydedildi! (Backend entegrasyonu yapılacak)')
}
</script> 