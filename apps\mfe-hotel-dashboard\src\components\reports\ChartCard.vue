<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h4 class="text-lg font-bold text-navy-700 dark:text-white">{{ title }}</h4>
        <p v-if="subtitle" class="text-sm text-gray-600 dark:text-gray-400">{{ subtitle }}</p>
      </div>
      <div v-if="$slots.actions" class="flex items-center space-x-2">
        <slot name="actions" />
      </div>
    </div>

    <!-- Chart Container -->
    <div :class="['relative', `h-${height}`]">
      <!-- Chart Placeholder -->
      <div v-if="!hasData" :class="[
        'h-full rounded-lg flex items-center justify-center',
        getChartGradient(type)
      ]">
        <div class="text-white text-center">
          <component :is="getChartIcon(type)" class="w-12 h-12 mx-auto mb-2 opacity-80" />
          <p class="text-sm opacity-80">{{ getChartLabel(type) }}</p>
          <p class="text-xs opacity-60">{{ placeholder || 'Veri yükleniyor...' }}</p>
        </div>
      </div>

      <!-- Actual Chart (when data is available) -->
      <div v-else class="h-full">
        <slot name="chart" />
      </div>
    </div>

    <!-- Chart Legend -->
    <div v-if="legend && legend.length > 0" class="flex items-center justify-center mt-4 space-x-6">
      <div
        v-for="item in legend"
        :key="item.label"
        class="flex items-center space-x-2"
      >
        <div :class="['w-3 h-3 rounded-full', item.color]"></div>
        <span class="text-sm text-gray-600 dark:text-gray-400">{{ item.label }}</span>
      </div>
    </div>

    <!-- Chart Summary -->
    <div v-if="summary" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div
          v-for="item in summary"
          :key="item.label"
          class="text-center"
        >
          <div class="text-lg font-bold text-navy-700 dark:text-white">{{ item.value }}</div>
          <div class="text-xs text-gray-600 dark:text-gray-400">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  ChartBarIcon,
  ChartPieIcon,
  ArrowTrendingUpIcon,
  PresentationChartLineIcon
} from '@heroicons/vue/24/outline'

interface LegendItem {
  label: string
  color: string
}

interface SummaryItem {
  label: string
  value: string | number
}

interface Props {
  title: string
  subtitle?: string
  type: 'line' | 'bar' | 'pie' | 'area'
  height?: string
  hasData?: boolean
  placeholder?: string
  legend?: LegendItem[]
  summary?: SummaryItem[]
}

const props = withDefaults(defineProps<Props>(), {
  height: '64',
  hasData: false
})

const getChartIcon = (type: string) => {
  const icons = {
    line: PresentationChartLineIcon,
    bar: ChartBarIcon,
    pie: ChartPieIcon,
    area: ArrowTrendingUpIcon
  }
  return icons[type as keyof typeof icons] || ChartBarIcon
}

const getChartLabel = (type: string) => {
  const labels = {
    line: 'Line Chart',
    bar: 'Bar Chart',
    pie: 'Pie Chart',
    area: 'Area Chart'
  }
  return labels[type as keyof typeof labels] || 'Chart'
}

const getChartGradient = (type: string) => {
  const gradients = {
    line: 'bg-gradient-to-br from-green-400 to-blue-500',
    bar: 'bg-gradient-to-br from-blue-400 to-purple-500',
    pie: 'bg-gradient-to-br from-purple-400 to-pink-500',
    area: 'bg-gradient-to-br from-orange-400 to-red-500'
  }
  return gradients[type as keyof typeof gradients] || 'bg-gradient-to-br from-gray-400 to-gray-500'
}
</script>

<style scoped>
/* Chart card specific styles */
</style>
