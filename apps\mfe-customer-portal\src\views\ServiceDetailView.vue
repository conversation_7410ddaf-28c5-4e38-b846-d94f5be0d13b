<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-6">
        <div class="flex items-center justify-between">
          <button @click="goBack" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <svg class="w-6 h-6 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          </button>
          <h1 class="text-xl font-bold text-textDark">Hizmet Detayı</h1>
          <div class="w-10"></div> <!-- Spacer for centering -->
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
      <div class="text-center">
        <div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-textMedium">Hizmet detayları yükleniyor...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="px-4 py-8">
      <div class="bg-red-50 border border-red-200 rounded-2xl p-6 text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-textDark mb-2">Hizmet Bulunamadı</h3>
        <p class="text-textMedium mb-4">{{ error }}</p>
        <button 
          @click="goBack"
          class="bg-primary text-white px-6 py-3 rounded-xl font-medium hover:bg-primary/90 transition-colors"
        >
          Geri Dön
        </button>
      </div>
    </div>

    <!-- Service Detail Content -->
    <div v-else-if="service" class="pb-32">
      <!-- Hero Image -->
      <div class="relative h-80 overflow-hidden">
        <img 
          :src="service.image" 
          :alt="service.name" 
          class="w-full h-full object-cover"
        >
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
        
        <!-- Price Badge -->
        <div class="absolute top-4 right-4">
          <div v-if="service.price > 0" class="bg-white text-primary px-4 py-2 rounded-full font-bold text-lg shadow-lg">
            ₺{{ service.price }}
          </div>
          <div v-else class="bg-green-500 text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
            Ücretsiz
          </div>
        </div>

        <!-- Service Basic Info -->
        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
          <h2 class="text-3xl font-bold mb-2">{{ service.name }}</h2>
          <p class="text-white/90 text-lg leading-relaxed">{{ service.description }}</p>
        </div>
      </div>

      <!-- Service Details -->
      <div class="max-w-md mx-auto px-4 py-6">
        <!-- Service Info Cards -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="bg-white rounded-2xl p-4 text-center shadow-sm">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="text-sm text-textMedium mb-1">Süre</div>
            <div class="font-bold text-textDark">
              {{ service.duration_minutes > 0 ? `${service.duration_minutes} dakika` : 'Belirsiz' }}
            </div>
          </div>

          <div class="bg-white rounded-2xl p-4 text-center shadow-sm">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
            </div>
            <div class="text-sm text-textMedium mb-1">Konum</div>
            <div class="font-bold text-textDark">Otel İçi</div>
          </div>
        </div>

        <!-- Service Description -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
          <h3 class="text-lg font-bold text-textDark mb-4">Hizmet Açıklaması</h3>
          <p class="text-textMedium leading-relaxed mb-4">{{ service.longDescription }}</p>
          
          <!-- Service Features -->
          <div class="border-t border-gray-100 pt-4">
            <h4 class="font-semibold text-textDark mb-3">Bu Hizmete Dahil:</h4>
            <div class="space-y-2">
              <div class="flex items-center text-textMedium text-sm">
                <svg class="w-4 h-4 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span>Profesyonel hizmet</span>
              </div>
              <div class="flex items-center text-textMedium text-sm">
                <svg class="w-4 h-4 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span>Gerekli ekipmanlar dahil</span>
              </div>
              <div class="flex items-center text-textMedium text-sm">
                <svg class="w-4 h-4 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span>Hijyenik ortam</span>
              </div>
              <div class="flex items-center text-textMedium text-sm">
                <svg class="w-4 h-4 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span>Misafir odanıza servis</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Booking Information -->
        <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-6 mb-6">
          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-amber-800 mb-2">Rezervasyon Bilgileri</h4>
              <ul class="text-amber-700 text-sm space-y-1">
                <li>• Rezervasyon en az 2 saat önceden yapılmalıdır</li>
                <li>• İptal işlemleri 1 saat öncesine kadar ücretsizdir</li>
                <li>• Ödeme hizmet sonrası odanıza tahsil edilir</li>
                <li>• Özel isteklerinizi rezervasyon sırasında belirtebilirsiniz</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Rating and Reviews Summary -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
          <h3 class="text-lg font-bold text-textDark mb-4">Misafir Değerlendirmeleri</h3>
          
          <div class="flex items-center space-x-4 mb-4">
            <div class="text-center">
              <div class="text-3xl font-bold text-primary">4.8</div>
              <div class="flex justify-center space-x-1 mb-1">
                <svg v-for="i in 5" :key="i" class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <div class="text-xs text-textMedium">156 değerlendirme</div>
            </div>
            
            <div class="flex-1">
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-textMedium w-12">5 yıldız</span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div class="bg-yellow-400 h-2 rounded-full" style="width: 75%"></div>
                  </div>
                  <span class="text-sm text-textMedium">75%</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-textMedium w-12">4 yıldız</span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div class="bg-yellow-400 h-2 rounded-full" style="width: 20%"></div>
                  </div>
                  <span class="text-sm text-textMedium">20%</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-textMedium w-12">3 yıldız</span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div class="bg-yellow-400 h-2 rounded-full" style="width: 5%"></div>
                  </div>
                  <span class="text-sm text-textMedium">5%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="border-t border-gray-100 pt-4">
            <p class="text-sm text-textMedium">
              "Harika bir deneyim! Profesyonel personel ve kaliteli hizmet." 
              <span class="text-primary font-medium">- Ayşe K.</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Fixed Bottom Booking Section -->
    <div v-if="service" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
      <div class="max-w-md mx-auto">
        <div class="flex items-center justify-between mb-4">
          <div>
            <div class="text-lg font-bold text-textDark">{{ service.name }}</div>
            <div class="flex items-center space-x-3">
              <div v-if="service.price > 0" class="text-2xl font-bold text-primary">
                ₺{{ service.price }}
              </div>
              <div v-else class="text-lg font-bold text-green-600">
                Ücretsiz
              </div>
              <div v-if="service.duration_minutes > 0" class="text-sm text-textMedium">
                ({{ service.duration_minutes }} dk)
              </div>
            </div>
          </div>
        </div>
        
        <button 
          @click="bookService"
          :disabled="bookingInProgress"
          class="w-full bg-gradient-to-r from-primary to-teal-600 hover:from-primary/90 hover:to-teal-700 disabled:from-gray-400 disabled:to-gray-500 text-white py-4 px-6 rounded-2xl text-lg font-bold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:transform-none disabled:shadow-none"
        >
          <span v-if="!bookingInProgress" class="flex items-center justify-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            Randevu Al
          </span>
          <span v-else class="flex items-center justify-center">
            <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            İşleniyor...
          </span>
        </button>
        
        <div class="text-center mt-2">
          <p class="text-xs text-textMedium">
            Rezervasyon concierge tarafından onaylanacaktır
          </p>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { serviceCategories, type Service } from '@/data/servicesData'

const router = useRouter()
const route = useRoute()

const loading = ref(true)
const error = ref('')
const service = ref<Service | null>(null)
const bookingInProgress = ref(false)

onMounted(() => {
  loadService()
})

const loadService = () => {
  loading.value = true
  error.value = ''
  
  try {
    const serviceId = parseInt(route.params.id as string)
    
    // Find service in all categories
    let foundService: Service | null = null
    for (const category of serviceCategories) {
      foundService = category.services.find(s => s.id === serviceId) || null
      if (foundService) break
    }
    
    if (!foundService) {
      error.value = `ID ${serviceId} numaralı hizmet bulunamadı. Lütfen geçerli bir hizmet seçin.`
      service.value = null
    } else {
      service.value = foundService
    }
  } catch (err) {
    error.value = 'Hizmet detayları yüklenirken bir hata oluştu.'
    service.value = null
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

const bookService = async () => {
  if (!service.value || bookingInProgress.value) return
  
  bookingInProgress.value = true
  
  try {
    // Simulate booking process
    console.log('Booking service:', service.value.name)
    
    // Show booking confirmation
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Show success message
    alert(`${service.value.name} için rezervasyon talebiniz alındı! Concierge ekibimiz en kısa sürede sizinle iletişime geçecektir.`)
    
    // Navigate back to services
    router.push('/services')
    
  } catch (error) {
    console.error('Booking error:', error)
    alert('Rezervasyon işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.')
  } finally {
    bookingInProgress.value = false
  }
}
</script>

<style scoped>
/* Component-specific styles */
</style> 