<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Aktivite Kategori Yönetimi</h1>
        <p class="text-gray-600 dark:text-gray-400">Otel aktivitelerinin kategorilerini yönetin ve düzenleyin</p>
      </div>
      <button
        @click="openCreateModal"
        class="px-6 py-3 bg-brand-500 hover:bg-brand-600 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
      >
        <PlusIcon class="w-5 h-5" />
        <span>Yeni <PERSON>gor<PERSON></span>
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Kategori</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ totalCategories }}</p>
          </div>
          <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
            <TagIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Aktif Kategori</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ activeCategories }}</p>
          </div>
          <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
            <CheckCircleIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Aktivite</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ totalActivities }}</p>
          </div>
          <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-xl">
            <CalendarIcon class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Ortalama Aktivite</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ averageActivitiesPerCategory }}</p>
          </div>
          <div class="p-3 bg-amber-100 dark:bg-amber-900/20 rounded-xl">
            <ChartBarIcon class="w-6 h-6 text-amber-600 dark:text-amber-400" />
          </div>
        </div>
      </div>
    </div>

    <!-- Categories Table -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Aktivite Kategorileri</h3>
          <div class="flex items-center space-x-4">
            <!-- Search Input -->
            <div class="relative">
              <MagnifyingGlassIcon class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Kategori ara..."
                class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Kategori
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Açıklama
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Aktivite Sayısı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Oluşturma Tarihi
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr v-for="category in filteredCategories" :key="category.id" class="hover:bg-gray-50 dark:hover:bg-navy-600">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-brand-400 to-brand-600 flex items-center justify-center">
                      <component :is="getIconForCategory(category.icon)" class="w-5 h-5 text-white" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ category.name }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-white">{{ category.description || 'Açıklama yok' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">{{ getActivityCountForCategory(category.id) }} aktivite</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                    category.isActive
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  ]"
                >
                  {{ category.isActive ? 'Aktif' : 'Pasif' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(category.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="editCategory(category)"
                    class="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300"
                  >
                    <PencilIcon class="w-4 h-4" />
                  </button>
                  <button
                    @click="toggleCategoryStatus(category)"
                    :class="[
                      'ml-2',
                      category.isActive
                        ? 'text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300'
                        : 'text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300'
                    ]"
                  >
                    <component :is="category.isActive ? EyeSlashIcon : EyeIcon" class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteCategory(category)"
                    class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 ml-2"
                  >
                    <TrashIcon class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="filteredCategories.length === 0" class="text-center py-12">
        <TagIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Kategori bulunamadı</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {{ searchQuery ? 'Arama kriterlerinize uygun kategori bulunamadı.' : 'Henüz hiç aktivite kategorisi eklenmemiş.' }}
        </p>
        <div class="mt-6">
          <button
            @click="openCreateModal"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-600 hover:bg-brand-700"
          >
            <PlusIcon class="-ml-1 mr-2 h-5 w-5" />
            Yeni Kategori Ekle
          </button>
        </div>
      </div>
    </div>

    <!-- Category Modal -->
    <CategoryModal
      v-if="showModal"
      :category="selectedCategory"
      category-type="ACTIVITY"
      @close="closeModal"
      @save="handleSaveCategory"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useManagementStore } from '../../../stores/managementStore'
import type { ServiceCategory } from '../../../types/management'
import CategoryModal from '../../../components/modals/CategoryModal.vue'
import {
  PlusIcon,
  TagIcon,
  CheckCircleIcon,
  CalendarIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  SparklesIcon,
  GlobeAltIcon,
  HeartIcon,
  AcademicCapIcon,
  MusicalNoteIcon,
  TrophyIcon,
  CameraIcon,
  StarIcon
} from '@heroicons/vue/24/outline'

const managementStore = useManagementStore()

// Reactive refs
const showModal = ref(false)
const selectedCategory = ref<ServiceCategory | null>(null)
const searchQuery = ref('')

// Computed properties
const activityCategories = computed(() => 
  managementStore.serviceCategories.filter(cat => cat.type === 'ACTIVITY')
)

const filteredCategories = computed(() => {
  if (!searchQuery.value) return activityCategories.value
  const query = searchQuery.value.toLowerCase()
  return activityCategories.value.filter(category =>
    category.name.toLowerCase().includes(query) ||
    (category.description && category.description.toLowerCase().includes(query))
  )
})

const totalCategories = computed(() => activityCategories.value.length)
const activeCategories = computed(() => activityCategories.value.filter(cat => cat.isActive).length)
const totalActivities = computed(() => managementStore.activities.length)
const averageActivitiesPerCategory = computed(() => {
  if (totalCategories.value === 0) return 0
  return Math.round(totalActivities.value / totalCategories.value)
})

// Icon mapping for categories
const iconMap: Record<string, any> = {
  'sparkles': SparklesIcon,
  'globe': GlobeAltIcon,
  'heart': HeartIcon,
  'academic': AcademicCapIcon,
  'music': MusicalNoteIcon,
  'trophy': TrophyIcon,
  'camera': CameraIcon,
  'star': StarIcon
}

// Methods
const getIconForCategory = (iconName: string | undefined) => {
  return iconMap[iconName || 'tag'] || TagIcon
}

const getActivityCountForCategory = (categoryId: string) => {
  return managementStore.activities.filter(activity => activity.categoryId === categoryId).length
}

const formatDate = (dateString: Date | string | undefined) => {
  if (!dateString) return 'Tarih belirtilmemiş'
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const openCreateModal = () => {
  selectedCategory.value = null
  showModal.value = true
}

const editCategory = (category: ServiceCategory) => {
  selectedCategory.value = { ...category }
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedCategory.value = null
}

const handleSaveCategory = (categoryData: Partial<ServiceCategory>) => {
  if (selectedCategory.value) {
    // Edit existing category
    managementStore.updateServiceCategory(selectedCategory.value.id, categoryData)
  } else {
    // Create new category
    managementStore.addServiceCategory({
      ...categoryData,
      type: 'ACTIVITY'
    } as Omit<ServiceCategory, 'id' | 'createdAt'>)
  }
  closeModal()
}

const toggleCategoryStatus = (category: ServiceCategory) => {
  managementStore.updateServiceCategory(category.id, {
    isActive: !category.isActive
  })
}

const deleteCategory = (category: ServiceCategory) => {
  // Check if category has activities
  const activityCount = getActivityCountForCategory(category.id)
  if (activityCount > 0) {
    alert(`Bu kategori ${activityCount} aktiviteye sahip. Önce aktiviteleri başka kategorilere taşıyın veya silin.`)
    return
  }

  if (confirm(`"${category.name}" kategorisini silmek istediğinizden emin misiniz?`)) {
    managementStore.deleteServiceCategory(category.id)
  }
}
</script> 