<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2"><PERSON><PERSON><PERSON><PERSON> Hizmet <PERSON>i Takibi</h1>
      <p class="text-gray-600 dark:text-gray-300">Misa<PERSON>r hizmet taleplerini gerçek zamanlı olarak takip edin ve yönetin.</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ newRequestsCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Yeni Talepler</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ assignedRequestsCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Atanmış</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-orange-100 dark:bg-orange-900">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ inProgressRequestsCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Devam Ediyor</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ completedRequestsCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Tamamlanmış</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ cancelledRequestsCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">İptal Edilmiş</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
      <!-- NEW Column -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Yeni Talepler</h3>
          <span class="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full text-sm font-medium">
            {{ newRequests.length }}
          </span>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <ServiceRequestCard
            v-for="request in newRequests"
            :key="request.id"
            :request="request"
            @update-status="updateRequestStatus"
          />
          <div v-if="newRequests.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <p class="text-sm">Yeni talep yok</p>
          </div>
        </div>
      </div>

      <!-- ASSIGNED Column -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Atanmış</h3>
          <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400 px-2 py-1 rounded-full text-sm font-medium">
            {{ assignedRequests.length }}
          </span>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <ServiceRequestCard
            v-for="request in assignedRequests"
            :key="request.id"
            :request="request"
            @update-status="updateRequestStatus"
          />
          <div v-if="assignedRequests.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <p class="text-sm">Atanmış talep yok</p>
          </div>
        </div>
      </div>

      <!-- IN_PROGRESS Column -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Devam Ediyor</h3>
          <span class="bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 px-2 py-1 rounded-full text-sm font-medium">
            {{ inProgressRequests.length }}
          </span>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <ServiceRequestCard
            v-for="request in inProgressRequests"
            :key="request.id"
            :request="request"
            @update-status="updateRequestStatus"
          />
          <div v-if="inProgressRequests.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <p class="text-sm">Devam eden talep yok</p>
          </div>
        </div>
      </div>

      <!-- COMPLETED Column -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Tamamlanmış</h3>
          <span class="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-sm font-medium">
            {{ completedRequests.length }}
          </span>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <ServiceRequestCard
            v-for="request in completedRequests"
            :key="request.id"
            :request="request"
            @update-status="updateRequestStatus"
            :is-completed="true"
          />
          <div v-if="completedRequests.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <p class="text-sm">Tamamlanmış talep yok</p>
          </div>
        </div>
      </div>

      <!-- CANCELLED Column -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">İptal Edilmiş</h3>
          <span class="bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 px-2 py-1 rounded-full text-sm font-medium">
            {{ cancelledRequests.length }}
          </span>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <ServiceRequestCard
            v-for="request in cancelledRequests"
            :key="request.id"
            :request="request"
            @update-status="updateRequestStatus"
            :is-cancelled="true"
          />
          <div v-if="cancelledRequests.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <p class="text-sm">İptal edilmiş talep yok</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import ServiceRequestCard from '@/components/cards/ServiceRequestCard.vue'
import type { GuestServiceRequest } from '@/types/management'

const managementStore = useManagementStore()

// Status-based filtered computed properties
const newRequests = computed(() => 
  managementStore.guestServiceRequests.filter(request => request.status === 'NEW')
)

const assignedRequests = computed(() => 
  managementStore.guestServiceRequests.filter(request => request.status === 'ASSIGNED')
)

const inProgressRequests = computed(() => 
  managementStore.guestServiceRequests.filter(request => request.status === 'IN_PROGRESS')
)

const completedRequests = computed(() => 
  managementStore.guestServiceRequests.filter(request => request.status === 'COMPLETED')
)

const cancelledRequests = computed(() => 
  managementStore.guestServiceRequests.filter(request => request.status === 'CANCELLED')
)

// Statistics computed properties
const newRequestsCount = computed(() => newRequests.value.length)
const assignedRequestsCount = computed(() => assignedRequests.value.length)
const inProgressRequestsCount = computed(() => inProgressRequests.value.length)
const completedRequestsCount = computed(() => completedRequests.value.length)
const cancelledRequestsCount = computed(() => cancelledRequests.value.length)

// Update request status
const updateRequestStatus = (requestId: string, newStatus: GuestServiceRequest['status']) => {
  managementStore.updateServiceRequestStatus(requestId, newStatus)
}
</script> 