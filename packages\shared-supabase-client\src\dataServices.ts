/**
 * Data service utilities for fetching and caching data from Supabase
 * Provides consistent error handling, loading states, and caching across all MFEs
 */

import { supabase } from './index'
import { HotelDashboard, ManagementPortal, CustomerPortal, AppShell } from './dataTransformers'

// Generic response type for all data services
export interface DataServiceResponse<T> {
  data: T | null
  error: string | null
  isLoading: boolean
}

// Simple in-memory cache with TTL
class SimpleCache {
  private cache = new Map<string, { data: any; expires: number }>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  set(key: string, data: any, ttl = this.defaultTTL) {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    })
  }

  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expires) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }

  clear() {
    this.cache.clear()
  }

  delete(key: string) {
    this.cache.delete(key)
  }
}

const cache = new SimpleCache()

// Hotel Dashboard Data Services
export namespace HotelDashboardService {
  
  export async function getRooms(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.Room[]>> {
    try {
      const cacheKey = `rooms_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('rooms')
        .select(`
          *,
          floors(floor_number)
        `)
        .eq('is_active', true)
        .order('room_number')

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((room: any) => HotelDashboard.transformRoom(room))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching rooms:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getTasks(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.HousekeepingTask[]>> {
    try {
      const cacheKey = `tasks_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('housekeeping_tasks')
        .select(`
          *,
          rooms(room_number)
        `)
        .order('created_at', { ascending: false })

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((task: any) =>
        HotelDashboard.transformTask(task, task.rooms?.room_number)
      )
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching tasks:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getStaffMembers(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.StaffMember[]>> {
    try {
      const cacheKey = `staff_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('user_profiles')
        .select('*')
        .order('full_name')

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((user: any) => HotelDashboard.transformStaffMember(user))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching staff members:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getMenuItems(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.MenuItem[]>> {
    try {
      const cacheKey = `menu_items_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('menu_items')
        .select('*')
        .eq('is_available', true)
        .order('display_order')

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((item: any) => HotelDashboard.transformMenuItem(item))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching menu items:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getHotelEvents(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.HotelEvent[]>> {
    try {
      const cacheKey = `hotel_events_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('hotel_activities' as any)
        .select('*')
        .eq('is_active', true)
        .order('start_time')

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((event: any) => HotelDashboard.transformHotelEvent(event))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching hotel events:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getRoomServiceOrders(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.RoomServiceOrder[]>> {
    try {
      const cacheKey = `room_service_orders_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('room_service_orders')
        .select(`
          *,
          order_items (
            id,
            quantity,
            price_at_order,
            menu_items (
              id,
              name
            )
          ),
          user_profiles (
            full_name
          ),
          rooms (
            room_number
          )
        `)
        .order('created_at', { ascending: false })

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((order: any) => HotelDashboard.transformRoomServiceOrder(order))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching room service orders:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getHotelActivities(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.HotelActivity[]>> {
    try {
      const cacheKey = `hotel_activities_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('hotel_activities' as any)
        .select('*')
        .eq('is_active', true)
        .order('start_time')

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((activity: any) => HotelDashboard.transformHotelActivity(activity))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching hotel activities:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getMaintenanceTasks(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.MaintenanceTask[]>> {
    try {
      const cacheKey = `maintenance_tasks_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('maintenance_tasks')
        .select(`
          *,
          rooms(room_number),
          user_profiles(full_name)
        `)
        .order('created_at', { ascending: false })

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map((task: any) => HotelDashboard.transformMaintenanceTask(task))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching maintenance tasks:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getServiceCategories(hotelId?: string): Promise<DataServiceResponse<HotelDashboard.ServiceCategory[]>> {
    try {
      const cacheKey = `service_categories_${hotelId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('service_categories' as any)
        .select('*')
        .eq('is_active', true)
        .order('display_order')

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map(category => HotelDashboard.transformServiceCategory(category))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching service categories:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }
}

// Management Portal Data Services
export namespace ManagementPortalService {
  
  export async function getHotels(): Promise<DataServiceResponse<ManagementPortal.Hotel[]>> {
    try {
      const cacheKey = 'hotels_all'
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await supabase
        .from('hotels')
        .select('*')
        .order('name')

      if (error) throw error

      const transformedData = (data || []).map(hotel => ManagementPortal.transformHotel(hotel))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching hotels:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getPlatformStats(): Promise<DataServiceResponse<ManagementPortal.PlatformStats>> {
    try {
      const cacheKey = 'platform_stats'
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await (supabase as any).rpc('get_management_platform_overview_secure')

      if (error) throw error

      const transformedData = ManagementPortal.transformPlatformStats(data?.[0] || {})
      cache.set(cacheKey, transformedData, 2 * 60 * 1000) // 2 minutes cache for stats

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching platform stats:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getUsers(): Promise<DataServiceResponse<ManagementPortal.User[]>> {
    try {
      const cacheKey = 'users_all'
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .select(`
          *,
          hotels(name)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      const transformedData = (data || []).map(user => ManagementPortal.transformUser(user))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching users:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getHotelComparison(): Promise<DataServiceResponse<ManagementPortal.HotelComparison[]>> {
    try {
      const cacheKey = 'hotel_comparison'
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await (supabase as any).rpc('get_management_hotel_comparison_secure')

      if (error) throw error

      const transformedData = (data || []).map((hotel: any) => ManagementPortal.transformHotelComparison(hotel))
      cache.set(cacheKey, transformedData, 5 * 60 * 1000) // 5 minutes cache

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching hotel comparison:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }
}

// Customer Portal Data Services
export namespace CustomerPortalService {

  export async function getServiceRequests(hotelId?: string): Promise<DataServiceResponse<CustomerPortal.ServiceRequest[]>> {
    try {
      let query = supabase
        .from('service_requests')
        .select('*')
        .order('created_at', { ascending: false })

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map(request => CustomerPortal.transformServiceRequest(request))

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching service requests:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getGuestReservations(guestId: string): Promise<DataServiceResponse<CustomerPortal.Reservation[]>> {
    try {
      const cacheKey = `guest_reservations_${guestId}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await supabase
        .from('reservations')
        .select(`
          *,
          hotels(name, address),
          rooms(room_number, room_type)
        `)
        .eq('profile_id', guestId)
        .order('created_at', { ascending: false })

      if (error) throw error

      const transformedData = (data || []).map(reservation => CustomerPortal.transformReservation(reservation))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching guest reservations:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getGuestOrders(guestId: string, hotelId?: string): Promise<DataServiceResponse<CustomerPortal.Order[]>> {
    try {
      const cacheKey = `guest_orders_${guestId}_${hotelId || 'all'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('room_service_orders')
        .select(`
          *,
          order_items(
            id,
            quantity,
            price_at_order,
            menu_items(name, description)
          )
        `)
        .eq('customer_id', guestId)
        .order('created_at', { ascending: false })

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map(order => CustomerPortal.transformOrder(order))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching guest orders:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getHotelActivities(hotelId: string): Promise<DataServiceResponse<CustomerPortal.Activity[]>> {
    try {
      const cacheKey = `hotel_activities_${hotelId}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await supabase
        .from('hotel_activities')
        .select('*')
        .eq('hotel_id', hotelId)
        .eq('is_active', true)
        .order('start_time')

      if (error) throw error

      const transformedData = (data || []).map(activity => CustomerPortal.transformActivity(activity))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching hotel activities:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getGuestNotifications(guestId: string, hotelId?: string): Promise<DataServiceResponse<CustomerPortal.Notification[]>> {
    try {
      const cacheKey = `guest_notifications_${guestId}_${hotelId || 'all'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      let query = supabase
        .from('guest_notifications')
        .select('*')
        .eq('guest_email', guestId)
        .order('created_at', { ascending: false })

      if (hotelId) {
        query = query.eq('hotel_id', hotelId)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedData = (data || []).map(notification => CustomerPortal.transformNotification(notification))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching guest notifications:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getServiceCategories(hotelId: string): Promise<DataServiceResponse<CustomerPortal.ServiceCategory[]>> {
    try {
      const cacheKey = `service_categories_${hotelId}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data, error } = await supabase
        .from('service_categories')
        .select('*')
        .eq('hotel_id', hotelId)
        .eq('is_active', true)
        .order('display_order')

      if (error) throw error

      const transformedData = (data || []).map(category => CustomerPortal.transformServiceCategory(category))
      cache.set(cacheKey, transformedData)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching service categories:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }
}

// App Shell Data Services
export namespace AppShellService {
  
  export async function getCurrentUserProfile(): Promise<DataServiceResponse<AppShell.User>> {
    try {
      const cacheKey = 'current_user_profile'
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) throw error

      const transformedData = AppShell.transformUser(data)
      cache.set(cacheKey, transformedData, 10 * 60 * 1000) // 10 minutes cache for user profile

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching user profile:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getNotifications(userId?: string): Promise<DataServiceResponse<AppShell.Notification[]>> {
    try {
      const cacheKey = `notifications_${userId || 'current'}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Get user profile to determine role and hotel context
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('role, hotel_id')
        .eq('id', user.id)
        .single()

      if (!profile) throw new Error('User profile not found')

      let notifications: any[] = []

      // Fetch notifications based on user role
      if (profile.role === 'GUEST') {
        // For guests, fetch guest notifications
        if (!user.email) throw new Error('Guest email is required for notifications')

        const { data: guestNotifications, error: guestError } = await supabase
          .from('guest_notifications')
          .select('*')
          .eq('guest_email', user.email)
          .order('created_at', { ascending: false })
          .limit(50)

        if (guestError) throw guestError
        notifications = guestNotifications || []
      } else {
        // For staff/admin, fetch staff notifications
        if (!profile.hotel_id) throw new Error('Hotel ID is required for staff notifications')

        const { data: staffNotifications, error: staffError } = await supabase
          .from('staff_notifications')
          .select('*')
          .or(`recipient_id.eq.${user.id},recipient_id.is.null`)
          .eq('hotel_id', profile.hotel_id)
          .order('created_at', { ascending: false })
          .limit(50)

        if (staffError) throw staffError
        notifications = staffNotifications || []
      }

      const transformedData = notifications.map(notification =>
        AppShell.transformNotification(notification, profile.role)
      )
      cache.set(cacheKey, transformedData, 2 * 60 * 1000) // 2 minutes cache for notifications

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching notifications:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getHotelContext(userId: string): Promise<DataServiceResponse<AppShell.HotelContext>> {
    try {
      const cacheKey = `hotel_context_${userId}`
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      // Get user profile to determine hotel context
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select(`
          *,
          hotels(
            id,
            name,
            address,
            phone,
            email,
            is_active
          )
        `)
        .eq('id', userId)
        .single()

      if (profileError) throw profileError

      let hotelContext: AppShell.HotelContext

      if (profile.role === 'SUPER_ADMIN') {
        // Super admin can access all hotels
        const { data: allHotels, error: hotelsError } = await supabase
          .from('hotels')
          .select('*')
          .eq('is_active', true)
          .order('name')

        if (hotelsError) throw hotelsError

        hotelContext = {
          currentHotel: null, // No specific hotel selected
          availableHotels: (allHotels || []).map(hotel => AppShell.transformHotel(hotel)),
          canSwitchHotels: true,
          userRole: profile.role
        }
      } else {
        // Hotel-specific user
        const hotel = profile.hotels
        hotelContext = {
          currentHotel: hotel ? AppShell.transformHotel(hotel) : null,
          availableHotels: hotel ? [AppShell.transformHotel(hotel)] : [],
          canSwitchHotels: false,
          userRole: profile.role
        }
      }

      cache.set(cacheKey, hotelContext, 5 * 60 * 1000) // 5 minutes cache

      return { data: hotelContext, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching hotel context:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function updateUserProfile(userId: string, updates: Partial<AppShell.UserProfileUpdate>): Promise<DataServiceResponse<AppShell.User>> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({
          full_name: updates.fullName,
          avatar_url: updates.avatarUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error

      const transformedData = AppShell.transformUser(data)

      // Clear cache to force refresh
      cache.delete('current_user_profile')
      cache.delete(`hotel_context_${userId}`)

      return { data: transformedData, error: null, isLoading: false }
    } catch (error) {
      console.error('Error updating user profile:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function markNotificationAsRead(notificationId: string): Promise<DataServiceResponse<boolean>> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Get user profile to determine role
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (!profile) throw new Error('User profile not found')

      if (profile.role === 'GUEST') {
        // Update guest notification
        const { error } = await supabase
          .from('guest_notifications')
          .update({
            read_at: new Date().toISOString(),
            in_app_status: 'READ'
          })
          .eq('id', notificationId)

        if (error) throw error
      } else {
        // Update staff notification
        const { error } = await supabase
          .from('staff_notifications')
          .update({
            is_read: true,
            read_at: new Date().toISOString()
          })
          .eq('id', notificationId)
          .eq('recipient_id', user.id)

        if (error) throw error
      }

      // Clear notifications cache to force refresh
      cache.delete(`notifications_${user.id}`)
      cache.delete('notifications_current')

      return { data: true, error: null, isLoading: false }
    } catch (error) {
      console.error('Error marking notification as read:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }

  export async function getGlobalStats(): Promise<DataServiceResponse<AppShell.GlobalStats>> {
    try {
      const cacheKey = 'global_stats'
      const cached = cache.get(cacheKey)
      if (cached) {
        return { data: cached, error: null, isLoading: false }
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // Get user profile to determine context
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('role, hotel_id')
        .eq('id', user.id)
        .single()

      if (!profile) throw new Error('User profile not found')

      let stats: AppShell.GlobalStats

      if (profile.role === 'SUPER_ADMIN') {
        // Platform-wide stats for super admin
        const [hotelsResult, usersResult, ordersResult] = await Promise.all([
          supabase.from('hotels').select('id', { count: 'exact' }),
          supabase.from('user_profiles').select('id', { count: 'exact' }),
          supabase.from('orders').select('id', { count: 'exact' })
        ])

        stats = {
          totalHotels: hotelsResult.count || 0,
          totalUsers: usersResult.count || 0,
          totalOrders: ordersResult.count || 0,
          systemHealth: 'HEALTHY'
        }
      } else if (profile.hotel_id) {
        // Hotel-specific stats for hotel staff
        const [roomsResult, ordersResult, tasksResult] = await Promise.all([
          supabase.from('rooms').select('id', { count: 'exact' }).eq('hotel_id', profile.hotel_id),
          supabase.from('orders').select('id', { count: 'exact' }).eq('hotel_id', profile.hotel_id),
          supabase.from('maintenance_tasks').select('id', { count: 'exact' }).eq('hotel_id', profile.hotel_id)
        ])

        stats = {
          totalRooms: roomsResult.count || 0,
          totalOrders: ordersResult.count || 0,
          totalTasks: tasksResult.count || 0,
          systemHealth: 'HEALTHY'
        }
      } else {
        // Default stats for guests or users without hotel context
        stats = {
          systemHealth: 'HEALTHY'
        }
      }

      cache.set(cacheKey, stats, 5 * 60 * 1000) // Cache for 5 minutes
      return { data: stats, error: null, isLoading: false }
    } catch (error) {
      console.error('Error fetching global stats:', error)
      return { data: null, error: (error as Error).message, isLoading: false }
    }
  }
}

// Cache management utilities
export const CacheManager = {
  clear: () => cache.clear(),
  delete: (key: string) => cache.delete(key),
  clearHotelData: (hotelId: string) => {
    const keysToDelete = [
      `rooms_${hotelId}`,
      `tasks_${hotelId}`,
      `staff_${hotelId}`,
      `menu_items_${hotelId}`,
      `guest_services_${hotelId}`,
      `activities_${hotelId}`,
      `service_requests_${hotelId}`
    ]
    keysToDelete.forEach(key => cache.delete(key))
  }
}
