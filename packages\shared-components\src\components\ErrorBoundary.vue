<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <div class="error-icon">
        <AlertTriangleIcon class="w-16 h-16 text-red-500" />
      </div>
      
      <div class="error-content">
        <h2 class="error-title">{{ $t('errors.somethingWentWrong', 'Something went wrong') }}</h2>
        <p class="error-message">{{ errorMessage }}</p>
        
        <div class="error-actions">
          <Button 
            @click="retry" 
            variant="primary"
            class="mr-3"
          >
            {{ $t('common.retry', 'Try Again') }}
          </Button>
          
          <Button 
            @click="goHome" 
            variant="outline"
          >
            {{ $t('navigation.goHome', 'Go Home') }}
          </Button>
        </div>
        
        <!-- Development error details -->
        <details v-if="isDevelopment && errorDetails" class="error-details">
          <summary class="error-details-summary">
            {{ $t('errors.technicalDetails', 'Technical Details') }}
          </summary>
          <pre class="error-stack">{{ errorDetails }}</pre>
        </details>
      </div>
    </div>
  </div>
  
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, computed } from 'vue'
import { AlertTriangleIcon } from 'lucide-vue-next'
import Button from './Button.vue'

// Mock router for build compatibility
const useRouter = () => ({
  push: (path: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = path
    }
  }
})

interface Props {
  fallbackMessage?: string
  showRetry?: boolean
  showGoHome?: boolean
  onError?: (error: Error, errorInfo: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  fallbackMessage: '',
  showRetry: true,
  showGoHome: true
})

const emit = defineEmits<{
  error: [error: Error, errorInfo: any]
  retry: []
}>()

const router = useRouter()

// State
const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')

// Computed
const isDevelopment = computed(() => {
  try {
    return import.meta.env?.DEV || false
  } catch {
    return process?.env?.NODE_ENV === 'development' || false
  }
})

// Error capture
onErrorCaptured((error: Error, instance, errorInfo) => {
  console.error('ErrorBoundary caught an error:', error)
  console.error('Error info:', errorInfo)
  console.error('Component instance:', instance)
  
  hasError.value = true
  errorMessage.value = props.fallbackMessage || error.message || 'An unexpected error occurred'
  
  if (isDevelopment.value) {
    errorDetails.value = `${error.stack}\n\nComponent: ${instance?.$options.name || 'Unknown'}\nError Info: ${errorInfo}`
  }
  
  // Call custom error handler
  if (props.onError) {
    props.onError(error, errorInfo)
  }
  
  // Emit error event
  emit('error', error, errorInfo)
  
  // Prevent the error from propagating further
  return false
})

// Actions
const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
  emit('retry')
}

const goHome = () => {
  hasError.value = false
  router.push('/')
}

// Global error handler for unhandled promise rejections
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    if (!hasError.value) {
      hasError.value = true
      errorMessage.value = 'A network or data loading error occurred'
      
      if (isDevelopment.value) {
        errorDetails.value = `Unhandled Promise Rejection: ${event.reason}`
      }
    }
    
    // Prevent default browser error handling
    event.preventDefault()
  })
}
</script>

<style scoped>
.error-boundary {
  @apply min-h-screen flex items-center justify-center bg-gray-50 dark:bg-navy-900 px-4;
}

.error-container {
  @apply max-w-md w-full text-center;
}

.error-icon {
  @apply flex justify-center mb-6;
}

.error-content {
  @apply bg-white dark:bg-navy-800 rounded-lg shadow-lg p-8;
}

.error-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-4;
}

.error-message {
  @apply text-gray-600 dark:text-gray-300 mb-6 leading-relaxed;
}

.error-actions {
  @apply flex justify-center items-center mb-6;
}

.error-details {
  @apply mt-6 text-left;
}

.error-details-summary {
  @apply cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.error-stack {
  @apply bg-gray-100 dark:bg-navy-700 p-4 rounded text-xs font-mono text-gray-800 dark:text-gray-200 overflow-auto max-h-40;
}
</style>
