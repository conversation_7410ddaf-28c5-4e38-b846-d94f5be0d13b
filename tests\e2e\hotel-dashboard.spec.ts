import { test, expect } from '@playwright/test'

test.describe('Hotel Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to hotel dashboard
    await page.goto('http://localhost:5002')
  })

  test('should load dashboard successfully', async ({ page }) => {
    // Check if the page loads
    await expect(page).toHaveTitle(/Hotel Dashboard|Hotelexia/)
    
    // Check for main dashboard elements
    await expect(page.locator('body')).toBeVisible()
  })

  test('should display navigation sidebar', async ({ page }) => {
    // Look for sidebar navigation
    const sidebar = page.locator('nav, aside, [role="navigation"]').first()
    
    if (await sidebar.isVisible()) {
      await expect(sidebar).toBeVisible()
      
      // Check for common navigation items
      const navItems = [
        'Dashboard', 'Gösterge', 'Ana Sayfa',
        'Rezervasyon', 'Booking', 'Reservation',
        'Oda', 'Room', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Customer', 'Guest'
      ]
      
      let foundNavItem = false
      for (const item of navItems) {
        const navItem = page.locator(`text=${item}`).first()
        if (await navItem.isVisible()) {
          foundNavItem = true
          break
        }
      }
      
      expect(foundNavItem).toBe(true)
    }
  })

  test('should handle authentication flow', async ({ page }) => {
    // Check if login form is present or if already authenticated
    const loginForm = page.locator('form, input[type="email"], input[type="password"]').first()
    const dashboardContent = page.locator('[data-testid="dashboard"], .dashboard, main').first()
    
    const hasLoginForm = await loginForm.isVisible()
    const hasDashboardContent = await dashboardContent.isVisible()
    
    // Either should have login form or dashboard content
    expect(hasLoginForm || hasDashboardContent).toBe(true)
    
    if (hasLoginForm) {
      // Test login form elements
      await expect(page.locator('input[type="email"], input[type="text"]')).toBeVisible()
      await expect(page.locator('input[type="password"]')).toBeVisible()
      await expect(page.locator('button[type="submit"], button:has-text("Giriş"), button:has-text("Login")')).toBeVisible()
    }
  })

  test('should be responsive', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('body')).toBeVisible()
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('body')).toBeVisible()
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('body')).toBeVisible()
  })

  test('should load without critical errors', async ({ page }) => {
    const errors: string[] = []
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    await page.goto('http://localhost:5002')
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle')
    
    // Check for critical errors (ignore minor warnings)
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('WebSocket')
    )
    
    expect(criticalErrors).toHaveLength(0)
  })

  test('should handle notification system', async ({ page }) => {
    // Look for notification bell or notification area
    const notificationBell = page.locator('[data-testid="notification-bell"], .notification-bell, button:has([class*="bell"])')
    const notificationArea = page.locator('[data-testid="notifications"], .notifications')
    
    if (await notificationBell.isVisible()) {
      await expect(notificationBell).toBeVisible()
      
      // Try clicking the notification bell
      await notificationBell.click()
      
      // Check if notification dropdown appears
      const dropdown = page.locator('[data-testid="notification-dropdown"], .notification-dropdown')
      if (await dropdown.isVisible()) {
        await expect(dropdown).toBeVisible()
      }
    } else if (await notificationArea.isVisible()) {
      await expect(notificationArea).toBeVisible()
    }
  })

  test('should display dashboard metrics', async ({ page }) => {
    // Look for dashboard cards or metrics
    const dashboardCards = page.locator('.card, .metric, .stat, [class*="dashboard"]')
    const charts = page.locator('canvas, svg[class*="chart"], .chart')
    
    // Should have either dashboard cards or charts
    const hasCards = await dashboardCards.count() > 0
    const hasCharts = await charts.count() > 0
    
    if (hasCards || hasCharts) {
      expect(hasCards || hasCharts).toBe(true)
    }
  })
})
