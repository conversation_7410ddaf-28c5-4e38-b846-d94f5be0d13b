import { ref, computed, watch } from 'vue'

// Mock implementations for build compatibility
const useRouter = () => ({
  push: (path: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = path
    }
  }
})

const useRoute = () => ({
  path: typeof window !== 'undefined' ? window.location.pathname : '/',
  fullPath: typeof window !== 'undefined' ? window.location.href : '/'
})

export interface AuthGuardOptions {
  requiresAuth?: boolean
  requiresGuest?: boolean
  allowedRoles?: string[]
  redirectTo?: string
  maxWaitTime?: number
  onAuthRequired?: () => void
  onAccessDenied?: (reason: string) => void
  onAuthTimeout?: () => void
}

export interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: any
  userRole: string | null
  error: string | null
}

export function useAuthGuard(authStore: any, options: AuthGuardOptions = {}) {
  const router = useRouter()
  const route = useRoute()
  
  const {
    requiresAuth = false,
    requiresGuest = false,
    allowedRoles = [],
    redirectTo = '/login',
    maxWaitTime = 5000,
    onAuthRequired,
    onAccessDenied,
    onAuthTimeout
  } = options

  // State
  const isChecking = ref(true)
  const hasTimedOut = ref(false)
  const accessDenied = ref(false)
  const denialReason = ref('')

  // Computed auth state
  const authState = computed<AuthState>(() => ({
    isAuthenticated: authStore.isAuthenticated || false,
    isLoading: authStore.isLoading || false,
    user: authStore.user || null,
    userRole: authStore.userRole || null,
    error: authStore.error || null
  }))

  // Computed access control
  const canAccess = computed(() => {
    if (hasTimedOut.value) return false
    if (accessDenied.value) return false
    
    const { isAuthenticated, isLoading, userRole } = authState.value
    
    // If still loading, don't allow access yet
    if (isLoading) return false
    
    // Guest-only routes
    if (requiresGuest && isAuthenticated) {
      return false
    }
    
    // Auth required routes
    if (requiresAuth && !isAuthenticated) {
      return false
    }
    
    // Role-based access
    if (allowedRoles.length > 0 && isAuthenticated) {
      return userRole ? allowedRoles.includes(userRole) : false
    }
    
    return true
  })

  const shouldShowLoading = computed(() => {
    return isChecking.value && authState.value.isLoading && !hasTimedOut.value
  })

  const shouldRedirect = computed(() => {
    if (isChecking.value || authState.value.isLoading || hasTimedOut.value) {
      return false
    }
    
    const { isAuthenticated } = authState.value
    
    // Redirect authenticated users away from guest-only pages
    if (requiresGuest && isAuthenticated) {
      return true
    }
    
    // Redirect unauthenticated users away from protected pages
    if (requiresAuth && !isAuthenticated) {
      return true
    }
    
    return false
  })

  // Timeout handling
  let timeoutId: NodeJS.Timeout | null = null

  const startTimeout = () => {
    if (timeoutId !== null) clearTimeout(timeoutId)
    
    timeoutId = setTimeout(() => {
      if (authState.value.isLoading) {
        console.warn('Auth guard timeout - proceeding without full auth check')
        hasTimedOut.value = true
        isChecking.value = false
        
        if (onAuthTimeout) {
          onAuthTimeout()
        }
      }
    }, maxWaitTime)
  }

  const clearAuthTimeout = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  // Access control logic
  const checkAccess = () => {
    const { isAuthenticated, isLoading, userRole } = authState.value
    
    // Reset denial state
    accessDenied.value = false
    denialReason.value = ''
    
    // Don't check if still loading (unless timed out)
    if (isLoading && !hasTimedOut.value) {
      return
    }
    
    // Check guest-only access
    if (requiresGuest && isAuthenticated) {
      accessDenied.value = true
      denialReason.value = 'Already authenticated'
      return
    }
    
    // Check auth requirement
    if (requiresAuth && !isAuthenticated) {
      accessDenied.value = true
      denialReason.value = 'Authentication required'
      
      if (onAuthRequired) {
        onAuthRequired()
      }
      return
    }
    
    // Check role-based access
    if (allowedRoles.length > 0 && isAuthenticated) {
      if (!userRole || !allowedRoles.includes(userRole)) {
        accessDenied.value = true
        denialReason.value = `Access denied for role: ${userRole || 'unknown'}`
        
        if (onAccessDenied) {
          onAccessDenied(denialReason.value)
        }
        return
      }
    }
    
    // Access granted
    isChecking.value = false
  }

  // Handle redirects
  const handleRedirect = () => {
    if (!shouldRedirect.value) return
    
    const { isAuthenticated } = authState.value
    
    if (requiresGuest && isAuthenticated) {
      // Redirect authenticated users to dashboard
      if (authStore.redirectToDashboard) {
        authStore.redirectToDashboard()
      } else {
        if (typeof window !== 'undefined') {
          window.location.href = '/'
        }
      }
    } else if (requiresAuth && !isAuthenticated) {
      // Store current route for post-login redirect
      if (route.path !== redirectTo) {
        sessionStorage.setItem('hotelexia_redirect_after_login', route.fullPath)
      }
      
      // Redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = redirectTo
      }
    }
  }

  // Watchers
  watch(authState, checkAccess, { immediate: true, deep: true })
  watch(shouldRedirect, (should) => {
    if (should) {
      handleRedirect()
    }
  })

  // Lifecycle - Bu build için devre dışı, runtime'da authStore tarafından yönetiliyor
  // Vue lifecycle hooks sadece component context'inde çalışır

  // Public API
  const retry = async () => {
    isChecking.value = true
    hasTimedOut.value = false
    accessDenied.value = false
    denialReason.value = ''
    
    startTimeout()
    
    // Trigger auth store refresh if available
    if (authStore.restoreSession) {
      try {
        await authStore.restoreSession()
      } catch (error) {
        console.error('Auth guard retry failed:', error)
      }
    }
  }

  const forceAllow = () => {
    isChecking.value = false
    hasTimedOut.value = false
    accessDenied.value = false
    denialReason.value = ''
    clearAuthTimeout()
  }

  return {
    // State
    authState,
    isChecking,
    hasTimedOut,
    accessDenied,
    denialReason,
    
    // Computed
    canAccess,
    shouldShowLoading,
    shouldRedirect,
    
    // Actions
    retry,
    forceAllow,
    checkAccess
  }
}

// Utility function for route-level auth guards
export function createRouteAuthGuard(authStore: any, options: AuthGuardOptions = {}) {
  return async (to: any, from: any, next: any) => {
    const guard = useAuthGuard(authStore, {
      ...options,
      onAuthRequired: () => {
        // Store intended destination
        if (to.path !== options.redirectTo) {
          sessionStorage.setItem('hotelexia_redirect_after_login', to.fullPath)
        }
        next(options.redirectTo || '/login')
      },
      onAccessDenied: (reason) => {
        console.warn('Route access denied:', reason)
        if (authStore.redirectToDashboard) {
          authStore.redirectToDashboard()
        } else {
          next('/')
        }
      },
      onAuthTimeout: () => {
        console.warn('Auth timeout in route guard')
        // Allow navigation but log the timeout
        next()
      }
    })
    
    // Wait for auth check to complete or timeout
    const maxWait = options.maxWaitTime || 5000
    const startTime = Date.now()
    
    while (guard.isChecking.value && (Date.now() - startTime) < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    if (guard.canAccess.value) {
      next()
    } else if (guard.shouldRedirect.value) {
      // Redirect will be handled by the guard
      return
    } else {
      // Fallback - allow navigation with warning
      console.warn('Auth guard inconclusive - allowing navigation')
      next()
    }
  }
}
