<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-xl font-bold text-navy-700 dark:text-white">
          {{ activity ? 'Aktiviteyi Düzenle' : 'Yeni Aktivite Oluştur' }}
        </h3>
        <button 
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Temel Bilgiler Section -->
        <div>
          <h4 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Temel Bilgiler</h4>
          
          <!-- Title -->
          <div class="mb-4">
            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Aktivite Adı *
            </label>
            <input
              id="title"
              v-model="formData.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              placeholder="Örn: Havuz Başında Canlı Müzik"
            >
          </div>

          <!-- Description -->
          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Açıklama *
            </label>
            <textarea
              id="description"
              v-model="formData.description"
              rows="4"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              placeholder="Aktivite hakkında detaylı bilgi verin..."
            ></textarea>
          </div>
        </div>

        <!-- Zamanlama ve Lokasyon Section -->
        <div>
          <h4 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Zamanlama ve Lokasyon</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Start Time -->
            <div>
              <label for="startTime" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Başlangıç Tarihi ve Saati *
              </label>
              <input
                id="startTime"
                v-model="formData.startTimeUtc"
                type="datetime-local"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              >
            </div>

            <!-- End Time -->
            <div>
              <label for="endTime" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Bitiş Tarihi ve Saati
              </label>
              <input
                id="endTime"
                v-model="formData.endTimeUtc"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              >
            </div>
          </div>

          <!-- Location -->
          <div class="mt-4">
            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Lokasyon *
            </label>
            <input
              id="location"
              v-model="formData.location"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              placeholder="Örn: Ana Havuz Alanı, Lobi Bar, Spa"
            >
          </div>
        </div>

        <!-- Detaylar Section -->
        <div>
          <h4 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Detaylar</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Capacity -->
            <div>
              <label for="capacity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Kapasite (Kişi)
              </label>
              <input
                id="capacity"
                v-model.number="formData.capacity"
                type="number"
                min="1"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
                placeholder="Maksimum katılımcı sayısı"
              >
            </div>

            <!-- Status -->
            <div class="flex items-center justify-between">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Durum
                </label>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Aktiviteyi misafirlere göster
                </p>
              </div>
              <button
                type="button"
                @click="formData.isActive = !formData.isActive"
                class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
                :class="formData.isActive ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
              >
                <span
                  class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                  :class="formData.isActive ? 'translate-x-6' : 'translate-x-1'"
                ></span>
              </button>
            </div>
          </div>

          <!-- Tags -->
          <div class="mt-4">
            <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Etiketler
            </label>
            <input
              id="tags"
              v-model="tagsInput"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              placeholder="Örn: Müzik, Aile, Yetişkin (virgülle ayırın)"
            >
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Etiketleri virgülle ayırarak yazın
            </p>
          </div>
        </div>

        <!-- Görsel Section -->
        <div>
          <h4 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Görsel</h4>
          
          <!-- Image URL -->
          <div>
            <label for="imageUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Görsel URL'si *
            </label>
            <input
              id="imageUrl"
              v-model="formData.imageUrl"
              type="url"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
              placeholder="https://example.com/image.jpg"
            >
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Gelecekte dosya yükleme özelliği eklenecek
            </p>
          </div>

          <!-- Image Preview -->
          <div v-if="formData.imageUrl" class="mt-4">
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Önizleme</p>
            <div class="w-full h-48 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
              <img 
                :src="formData.imageUrl" 
                :alt="formData.title"
                class="w-full h-full object-cover"
                @error="imageError = true"
                @load="imageError = false"
              >
              <div v-if="imageError" class="w-full h-full flex items-center justify-center">
                <p class="text-gray-500 dark:text-gray-400">Görsel yüklenemedi</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            İptal
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-brand-500 hover:bg-brand-600 rounded-lg transition-colors"
          >
            {{ activity ? 'Güncelle' : 'Oluştur' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { HotelActivity } from '@/types/management'

// Props
interface Props {
  activity?: HotelActivity | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [activityData: Omit<HotelActivity, 'id'>]
}>()

// State
const imageError = ref(false)
const tagsInput = ref('')

const formData = ref({
  title: '',
  description: '',
  imageUrl: '',
  startTimeUtc: '',
  endTimeUtc: '',
  location: '',
  capacity: undefined as number | undefined,
  isActive: true,
  tags: [] as string[]
})

// Computed
const isEditing = computed(() => !!props.activity)

// Watchers
watch(() => tagsInput.value, (newValue) => {
  formData.value.tags = newValue
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0)
})

// Methods
const handleSubmit = () => {
  // Convert datetime-local to ISO string
  const startTimeUtc = new Date(formData.value.startTimeUtc).toISOString()
  const endTimeUtc = formData.value.endTimeUtc 
    ? new Date(formData.value.endTimeUtc).toISOString() 
    : undefined

  const activityData: Omit<HotelActivity, 'id'> = {
    title: formData.value.title,
    description: formData.value.description,
    imageUrl: formData.value.imageUrl,
    startTimeUtc,
    endTimeUtc,
    location: formData.value.location,
    capacity: formData.value.capacity,
    isActive: formData.value.isActive,
    tags: formData.value.tags
  }

  emit('save', activityData)
}

const formatDateTimeLocal = (isoString: string) => {
  const date = new Date(isoString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day}T${hours}:${minutes}`
}

// Initialize form data
onMounted(() => {
  if (props.activity) {
    formData.value = {
      title: props.activity.title,
      description: props.activity.description,
      imageUrl: props.activity.imageUrl,
      startTimeUtc: formatDateTimeLocal(props.activity.startTimeUtc),
      endTimeUtc: props.activity.endTimeUtc ? formatDateTimeLocal(props.activity.endTimeUtc) : '',
      location: props.activity.location,
      capacity: props.activity.capacity,
      isActive: props.activity.isActive,
      tags: props.activity.tags || []
    }
    tagsInput.value = (props.activity.tags || []).join(', ')
  }
})
</script>

<style scoped>
/* Ensure modal is scrollable */
.max-h-[90vh] {
  max-height: 90vh;
}
</style> 