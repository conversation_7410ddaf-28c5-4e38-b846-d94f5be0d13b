@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Horizon UI Global Styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: -0.5px;
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Dark mode body background */
  .dark body {
    @apply bg-navy-900 text-white;
  }

  /* Light mode body background */
  body {
    @apply bg-secondaryGray-300 text-navy-700;
  }

  /* Form inputs global styling */
  input {
    @apply text-gray-700;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Dark mode scrollbar */
  .dark ::-webkit-scrollbar-track {
    @apply bg-navy-800;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-navy-600;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-navy-500;
  }
}

@layer components {
  /* Button base styles - Horizon UI uyumlu */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-brand-500 text-white hover:bg-brand-600 focus:ring-brand-500 shadow-lg;
  }

  .btn-secondary {
    @apply btn bg-white text-navy-700 border border-gray-200 hover:bg-gray-50 focus:ring-gray-500;
  }

  .btn-ghost {
    @apply btn bg-transparent text-navy-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  /* Card component - Horizon UI style */
  .card {
    @apply bg-white rounded-xl shadow-card border border-gray-100;
  }

  .card-dark {
    @apply bg-navy-800 border-navy-700;
  }

  /* Input field - Horizon UI style */
  .input-field {
    @apply w-full px-4 py-3 rounded-xl border border-gray-200 bg-white text-navy-700 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200;
  }

  .input-field-dark {
    @apply bg-navy-800 border-navy-600 text-white placeholder:text-gray-300;
  }

  /* Portal specific styles */
  .portal-customer {
    @apply bg-secondaryGray-300;
  }

  .portal-hotel {
    @apply bg-navy-900;
  }

  .portal-manager {
    @apply bg-gray-900;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* Gradient utilities - Horizon UI style */
  .gradient-brand {
    background: linear-gradient(135deg, #422AFB 0%, #7551FF 100%);
  }

  .gradient-blue {
    background: linear-gradient(135deg, #3965FF 0%, #00D4FF 100%);
  }

  .gradient-purple {
    background: linear-gradient(135deg, #7551FF 0%, #422AFB 100%);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .glass-dark {
    background: rgba(27, 37, 75, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Custom shadows */
  .shadow-brand {
    box-shadow: 0 0 40px rgba(66, 42, 251, 0.15);
  }

  .shadow-card {
    box-shadow: 0px 18px 40px rgba(112, 144, 176, 0.12);
  }
} 