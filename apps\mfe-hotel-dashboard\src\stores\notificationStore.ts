import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Guest {
  id: string
  name: string
  roomNumber: string
  checkIn: string
  email?: string
  phone?: string
}

export interface NotificationTemplate {
  id: string
  name: string
  title: string
  message: string
  channels: string[]
  targetType: string
}

export interface SentNotification {
  id: string
  title: string
  message: string
  targetType: string
  targetValue: string
  channels: string[]
  status: 'sent' | 'pending' | 'failed'
  sentAt: Date
  recipients: number
  readCount?: number
  deliveredCount?: number
}

export interface GuestNotification {
  id: string
  guestId: string
  title: string
  message: string
  channel: string
  status: 'read' | 'unread'
  sentAt: Date
}

export const useNotificationStore = defineStore('notifications', () => {
  // State
  const guests = ref<Guest[]>([
    {
      id: '1',
      name: '<PERSON><PERSON>',
      roomNumber: '101',
      checkIn: '10 Oca 2024',
      email: '<EMAIL>',
      phone: '+90 ************'
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      roomNumber: '205',
      checkIn: '12 Oca 2024',
      email: '<EMAIL>',
      phone: '+90 ************'
    },
    {
      id: '3',
      name: '<PERSON><PERSON><PERSON>a',
      roomNumber: '308',
      checkIn: '14 Oca 2024',
      email: '<EMAIL>',
      phone: '+90 ************'
    },
    {
      id: '4',
      name: 'Ayşe Demir',
      roomNumber: '412',
      checkIn: '15 Oca 2024',
      email: '<EMAIL>',
      phone: '+90 ************'
    },
    {
      id: '5',
      name: 'Ali Şahin',
      roomNumber: '501',
      checkIn: '16 Oca 2024',
      email: '<EMAIL>',
      phone: '+90 ************'
    }
  ])

  const notificationTemplates = ref<NotificationTemplate[]>([
    {
      id: '1',
      name: 'Hoş Geldin Mesajı',
      title: 'Hotelimize Hoş Geldiniz!',
      message: 'Sevgili {{guest_name}}, otelimize hoş geldiniz! Konforlu bir konaklama dileriz.',
      channels: ['in-app', 'sms'],
      targetType: 'specific'
    },
    {
      id: '2',
      name: 'Yeni Menü Duyurusu',
      title: 'Yeni Lezzetlerimizi Keşfedin!',
      message: 'Bugün itibariyle menümüze eklenen yeni lezzetli yemekleri inceleyebilirsiniz.',
      channels: ['in-app', 'email'],
      targetType: 'all'
    },
    {
      id: '3',
      name: 'Spa Randevu Hatırlatması',
      title: 'Spa Randevusu Hatırlatması',
      message: 'Sayın {{guest_name}}, yarın saat {{appointment_time}} spa randevunuz bulunmaktadır.',
      channels: ['whatsapp', 'sms'],
      targetType: 'specific'
    }
  ])

  const sentNotifications = ref<SentNotification[]>([
    {
      id: '1',
      title: 'Yeni Menü Duyurusu',
      message: 'Yeni lezzetlerimizi keşfedin! Bugün itibariyle menümüze eklenen özel yemekleri deneyebilirsiniz.',
      targetType: 'all',
      targetValue: '',
      channels: ['in-app', 'sms'],
      status: 'sent',
      sentAt: new Date('2024-01-15T14:30:00'),
      recipients: 45,
      readCount: 32,
      deliveredCount: 43
    },
    {
      id: '2',
      title: 'Spa İndirimi',
      message: 'Bu hafta spa hizmetlerimizde %20 indirim! Randevunuzu hemen alın.',
      targetType: 'service',
      targetValue: 'spa',
      channels: ['email', 'whatsapp'],
      status: 'sent',
      sentAt: new Date('2024-01-14T10:15:00'),
      recipients: 12,
      readCount: 8,
      deliveredCount: 11
    },
    {
      id: '3',
      title: 'Gece Aktivitesi',
      message: 'Bu akşam saat 21:00\'da canlı müzik etkinliğimiz başlıyor!',
      targetType: 'activity',
      targetValue: 'music',
      channels: ['in-app'],
      status: 'pending',
      sentAt: new Date('2024-01-16T18:00:00'),
      recipients: 25
    },
    {
      id: '4',
      title: 'Sistem Bakımı',
      message: 'Gece 02:00-04:00 arası sistem bakımı yapılacaktır.',
      targetType: 'all',
      targetValue: '',
      channels: ['sms', 'email'],
      status: 'failed',
      sentAt: new Date('2024-01-13T20:00:00'),
      recipients: 45
    }
  ])

  const guestNotificationHistory = ref<GuestNotification[]>([
    {
      id: '1',
      guestId: '1',
      title: 'Hoş Geldiniz',
      message: 'Otelimize hoş geldiniz! Konforlu bir konaklama diliyoruz.',
      channel: 'in-app',
      status: 'read',
      sentAt: new Date('2024-01-10T15:30:00')
    },
    {
      id: '2',
      guestId: '1',
      title: 'Oda Servisi Menüsü',
      message: 'Yeni oda servisi menümüzü inceleyerek lezzetli yemeklerimizi keşfedin.',
      channel: 'sms',
      status: 'read',
      sentAt: new Date('2024-01-11T09:15:00')
    },
    {
      id: '3',
      guestId: '1',
      title: 'Spa Randevusu Hatırlatması',
      message: 'Yarın saat 14:00\'da spa randevunuz bulunmaktadır.',
      channel: 'whatsapp',
      status: 'unread',
      sentAt: new Date('2024-01-12T18:00:00')
    },
    {
      id: '4',
      guestId: '2',
      title: 'Welcome Message',
      message: 'Hotelimize hoş geldiniz! İhtiyacınız olması durumunda resepsiyonla iletişime geçebilirsiniz.',
      channel: 'email',
      status: 'read',
      sentAt: new Date('2024-01-12T16:00:00')
    },
    {
      id: '5',
      guestId: '2',
      title: 'Havuz Bakımı',
      message: 'Yarın sabah 08:00-10:00 arası havuz bakımı yapılacaktır.',
      channel: 'in-app',
      status: 'unread',
      sentAt: new Date('2024-01-14T22:00:00')
    }
  ])

  // Computed properties
  const totalNotifications = computed(() => sentNotifications.value.length)
  const successfulNotifications = computed(() => 
    sentNotifications.value.filter(n => n.status === 'sent').length
  )
  const pendingNotifications = computed(() => 
    sentNotifications.value.filter(n => n.status === 'pending').length
  )
  const failedNotifications = computed(() => 
    sentNotifications.value.filter(n => n.status === 'failed').length
  )

  const notificationsByChannel = computed(() => {
    const channels = ['in-app', 'sms', 'email', 'whatsapp']
    return channels.map(channel => ({
      channel,
      count: sentNotifications.value.filter(n => n.channels.includes(channel)).length
    }))
  })

  // Actions
  const addNotification = (notification: Omit<SentNotification, 'id'>) => {
    const newNotification: SentNotification = {
      id: Date.now().toString(),
      ...notification
    }
    sentNotifications.value.unshift(newNotification)
  }

  const updateNotificationStatus = (id: string, status: SentNotification['status']) => {
    const notification = sentNotifications.value.find(n => n.id === id)
    if (notification) {
      notification.status = status
    }
  }

  const deleteNotification = (id: string) => {
    const index = sentNotifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      sentNotifications.value.splice(index, 1)
    }
  }

  const addTemplate = (template: Omit<NotificationTemplate, 'id'>) => {
    const newTemplate: NotificationTemplate = {
      id: Date.now().toString(),
      ...template
    }
    notificationTemplates.value.push(newTemplate)
  }

  const updateTemplate = (id: string, template: Partial<NotificationTemplate>) => {
    const existingTemplate = notificationTemplates.value.find(t => t.id === id)
    if (existingTemplate) {
      Object.assign(existingTemplate, template)
    }
  }

  const deleteTemplate = (id: string) => {
    const index = notificationTemplates.value.findIndex(t => t.id === id)
    if (index > -1) {
      notificationTemplates.value.splice(index, 1)
    }
  }

  const getGuestNotifications = (guestId: string) => {
    return guestNotificationHistory.value.filter(n => n.guestId === guestId)
  }

  const markGuestNotificationAsRead = (notificationId: string) => {
    const notification = guestNotificationHistory.value.find(n => n.id === notificationId)
    if (notification) {
      notification.status = 'read'
    }
  }

  const addGuestNotification = (notification: Omit<GuestNotification, 'id'>) => {
    const newNotification: GuestNotification = {
      id: Date.now().toString(),
      ...notification
    }
    guestNotificationHistory.value.unshift(newNotification)
  }

  const getNotificationStats = () => {
    const totalSent = sentNotifications.value.reduce((sum, n) => sum + n.recipients, 0)
    const totalRead = sentNotifications.value.reduce((sum, n) => sum + (n.readCount || 0), 0)
    
    return {
      totalSent,
      totalRead,
      readRate: totalSent > 0 ? Math.round((totalRead / totalSent) * 100) : 0,
      avgRecipientsPerNotification: totalNotifications.value > 0 ? 
        Math.round(totalSent / totalNotifications.value) : 0
    }
  }

  const searchGuests = (query: string) => {
    if (!query) return guests.value
    
    const lowerQuery = query.toLowerCase()
    return guests.value.filter(guest => 
      guest.name.toLowerCase().includes(lowerQuery) ||
      guest.roomNumber.includes(query) ||
      guest.email?.toLowerCase().includes(lowerQuery)
    )
  }

  const getGuestById = (id: string) => {
    return guests.value.find(g => g.id === id)
  }

  return {
    // State
    guests,
    notificationTemplates,
    sentNotifications,
    guestNotificationHistory,

    // Computed
    totalNotifications,
    successfulNotifications,
    pendingNotifications,
    failedNotifications,
    notificationsByChannel,

    // Actions
    addNotification,
    updateNotificationStatus,
    deleteNotification,
    addTemplate,
    updateTemplate,
    deleteTemplate,
    getGuestNotifications,
    markGuestNotificationAsRead,
    addGuestNotification,
    getNotificationStats,
    searchGuests,
    getGuestById
  }
}) 