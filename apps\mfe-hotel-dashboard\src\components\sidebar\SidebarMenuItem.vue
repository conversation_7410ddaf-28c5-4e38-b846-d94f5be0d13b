<template>
  <li>
    <!-- Single-level menu items (no children) -->
    <template v-if="!item.children && item.path">
      <router-link
        :to="item.path"
        class="flex items-center px-4 py-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700 rounded-xl transition-colors group"
        :class="[
          { 'bg-brand-50 text-brand-500 dark:bg-brand-500 dark:text-white': isRouteActive(item.path, item.exact) },
          isCollapsed ? 'justify-center px-2' : '',
          depth > 0 ? 'ml-4' : ''
        ]"
      >
        <component :is="item.icon" class="w-5 h-5 flex-shrink-0" :class="isCollapsed ? '' : 'mr-3'" />
        <span v-if="!isCollapsed" class="font-medium">{{ item.name }}</span>
      </router-link>
    </template>
    
    <!-- Multi-level menu items (with children) -->
    <template v-else>
      <div>
        <!-- Parent menu button -->
        <button
          @click="toggleMenu(item.name)"
          class="w-full flex items-center px-4 py-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700 rounded-xl transition-colors group"
          :class="[
            { 'bg-gray-100 dark:bg-navy-700': isMenuOpen(item.name) },
            isCollapsed ? 'justify-center px-2' : '',
            depth > 0 ? 'ml-4' : ''
          ]"
        >
          <component :is="item.icon" class="w-5 h-5 flex-shrink-0" :class="isCollapsed ? '' : 'mr-3'" />
          <span v-if="!isCollapsed" class="font-medium flex-1 text-left">{{ item.name }}</span>
          <ChevronDownIcon 
            v-if="!isCollapsed"
            class="w-4 h-4 transition-transform duration-200 flex-shrink-0"
            :class="{ 'rotate-180': isMenuOpen(item.name) }"
          />
        </button>
        
        <!-- Recursive submenu items -->
        <Transition
          enter-active-class="transition-all duration-300 ease-in-out"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-96"
          leave-active-class="transition-all duration-300 ease-in-out"
          leave-from-class="opacity-100 max-h-96"
          leave-to-class="opacity-0 max-h-0"
        >
          <ul 
            v-if="isMenuOpen(item.name) && !isCollapsed"
            class="mt-2 space-y-1 overflow-hidden"
          >
            <SidebarMenuItem
              v-for="child in item.children"
              :key="child.name"
              :item="child"
              :is-collapsed="isCollapsed"
              :open-menus="openMenus"
              :depth="depth + 1"
              @toggle-menu="$emit('toggle-menu', $event)"
            />
          </ul>
        </Transition>
      </div>
    </template>
  </li>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'

interface MenuItem {
  name: string
  path?: string
  icon: any
  exact?: boolean
  children?: MenuItem[]
}

interface Props {
  item: MenuItem
  isCollapsed: boolean
  openMenus: Record<string, boolean>
  depth?: number
}

const props = withDefaults(defineProps<Props>(), {
  depth: 0
})

const emit = defineEmits<{
  'toggle-menu': [menuName: string]
}>()

const route = useRoute()

// Toggle menu open/closed state
const toggleMenu = (menuName: string) => {
  emit('toggle-menu', menuName)
}

// Check if menu is open
const isMenuOpen = (menuName: string) => {
  return props.openMenus[menuName] || false
}

// Check if route is active
const isRouteActive = (path: string | undefined, exact: boolean = false) => {
  if (!path) return false
  
  if (exact) {
    return route.path === path
  }
  return route.path.startsWith(path)
}
</script> 