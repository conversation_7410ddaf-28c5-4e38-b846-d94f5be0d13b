<template>
  <div :class="cardClasses">
    <div v-if="$slots.header" :class="headerClasses">
      <slot name="header" />
    </div>
    
    <div :class="contentClasses">
      <slot />
    </div>
    
    <div v-if="$slots.footer" :class="footerClasses">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../lib/utils'

const cardVariants = cva(
  "bg-white rounded-xl border overflow-hidden transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-gray-200 shadow-sm hover:shadow-md",
        elevated: "border-gray-200 shadow-md hover:shadow-lg",
        brand: "border-brand-200 shadow-brand bg-gradient-to-br from-white to-brand-50",
        gradient: "border-transparent shadow-lg bg-gradient-to-br from-brand-500 to-purple-600 text-white",
        outline: "border-2 border-brand-200 shadow-none hover:border-brand-300",
        ghost: "border-transparent shadow-none bg-gray-50 hover:bg-gray-100",
      },
      size: {
        sm: "p-4",
        md: "p-6",
        lg: "p-8",
        xl: "p-10",
      },
      hover: {
        true: "hover:scale-[1.02] cursor-pointer",
        false: "",
      },
      interactive: {
        true: "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-500 focus-visible:ring-offset-2",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      hover: false,
      interactive: false,
    },
  }
)

interface CardProps extends /* @vue-ignore */ VariantProps<typeof cardVariants> {
  as?: string
  headerPadding?: boolean
  footerPadding?: boolean
  noPadding?: boolean
}

const props = withDefaults(defineProps<CardProps>(), {
  as: 'div',
  headerPadding: true,
  footerPadding: true,
  noPadding: false,
})

const cardClasses = computed(() => 
  cn(
    cardVariants({ 
      variant: props.variant, 
      size: props.noPadding ? undefined : props.size,
      hover: props.hover,
      interactive: props.interactive,
    }),
    props.noPadding && "p-0"
  )
)

const headerClasses = computed(() =>
  cn(
    "border-b border-gray-200",
    props.headerPadding && props.size === 'sm' && "px-4 py-3",
    props.headerPadding && props.size === 'md' && "px-6 py-4",
    props.headerPadding && props.size === 'lg' && "px-8 py-5",
    props.headerPadding && props.size === 'xl' && "px-10 py-6",
    !props.headerPadding && "p-0",
  )
)

const contentClasses = computed(() =>
  cn(
    "flex-1",
    props.noPadding && "p-0"
  )
)

const footerClasses = computed(() =>
  cn(
    "border-t border-gray-200 bg-gray-50",
    props.footerPadding && props.size === 'sm' && "px-4 py-3",
    props.footerPadding && props.size === 'md' && "px-6 py-4",
    props.footerPadding && props.size === 'lg' && "px-8 py-5",
    props.footerPadding && props.size === 'xl' && "px-10 py-6",
    !props.footerPadding && "p-0",
  )
)
</script> 