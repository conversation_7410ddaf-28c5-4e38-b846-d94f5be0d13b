import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { AppShellService, AppShell } from '@hotelexia/shared-supabase-client'

export const useGlobalStatsStore = defineStore('globalStats', () => {
  // State
  const stats = ref<AppShell.GlobalStats | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const systemHealth = computed(() => stats.value?.systemHealth || 'HEALTHY')
  
  const platformStats = computed(() => {
    if (!stats.value) return null
    return {
      totalHotels: stats.value.totalHotels,
      totalUsers: stats.value.totalUsers,
      totalOrders: stats.value.totalOrders
    }
  })

  const hotelStats = computed(() => {
    if (!stats.value) return null
    return {
      totalRooms: stats.value.totalRooms,
      totalTasks: stats.value.totalTasks,
      totalOrders: stats.value.totalOrders
    }
  })

  const isHealthy = computed(() => systemHealth.value === 'HEALTHY')
  const hasWarning = computed(() => systemHealth.value === 'WARNING')
  const hasError = computed(() => systemHealth.value === 'ERROR')

  // Actions
  const fetchStats = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await AppShellService.getGlobalStats()
      
      if (response.error) {
        throw new Error(response.error)
      }

      stats.value = response.data
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error fetching global stats:', err)
    } finally {
      isLoading.value = false
    }
  }

  const refreshStats = () => {
    return fetchStats()
  }

  const clearStats = () => {
    stats.value = null
    error.value = null
  }

  // Initialize stats on store creation
  fetchStats()

  return {
    // State
    stats: readonly(stats),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Computed
    systemHealth,
    platformStats,
    hotelStats,
    isHealthy,
    hasWarning,
    hasError,

    // Actions
    fetchStats,
    refreshStats,
    clearStats
  }
})
