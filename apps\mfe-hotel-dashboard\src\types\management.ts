export interface MenuItem {
  id: string
  name: string
  category: string // For display purposes
  category_id: string // For database relationship
  description: string
  price: number
  image: string
  isAvailable: boolean
  display_order: number
  preparation_time_minutes: number | null
  item_code: string
  is_vegetarian: boolean
  is_vegan: boolean
  is_gluten_free: boolean
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

export interface HotelEvent {
  id: string
  title: string
  description: string
  date: string
  time: string
  location: string
  image: string
  maxAttendees?: number
  currentAttendees?: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateMenuItemData {
  name: string
  category: string
  category_id: string
  description: string
  price: number
  image?: File | string
  isAvailable: boolean
  display_order: number
  preparation_time_minutes: number | null
  item_code: string
  is_vegetarian: boolean
  is_vegan: boolean
  is_gluten_free: boolean
  tags: string[]
}

export interface CreateEventData {
  title: string
  description: string
  date: string
  time: string
  location: string
  image?: File | string
  maxAttendees?: number
  isActive: boolean
}

export interface ServiceCategory {
  id: string // uuid
  name: string
  image_url: string // Category image URL
  type: 'ROOM_SERVICE' | 'GUEST_SERVICE' | 'ACTIVITY'
  display_order?: number
  isActive?: boolean
  description?: string
  icon?: string
  createdAt?: Date
}

export interface GuestService {
  id: string
  name: string
  description: string
  categoryId: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateGuestServiceData {
  name: string
  description: string
  categoryId: string
  isActive: boolean
}

export interface HotelActivity {
  id: string // uuid
  title: string
  description: string
  imageUrl: string
  startTimeUtc: string // ISO 8601 format string
  endTimeUtc?: string // ISO 8601 format string
  location: string
  capacity?: number
  isActive: boolean
  tags?: string[]
  categoryId?: string
}

// Operational Tracking Interfaces
export interface OrderItem {
  id: string
  menuItemId: string
  menuItemName: string
  quantity: number
  priceAtOrder: number
}

export interface RoomServiceOrder {
  id: string
  guestUserId: string
  guestName: string
  roomNumber: string
  status: 'NEW' | 'PREPARING' | 'ON_THE_WAY' | 'DELIVERED' | 'CANCELLED'
  totalPrice: number
  notes?: string
  items: OrderItem[]
  createdAt: Date
}

export interface GuestServiceRequest {
  id: string
  guestUserId: string
  guestName: string
  roomNumber: string
  serviceType: string
  status: 'NEW' | 'ASSIGNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  description: string
  notes?: string
  assignedStaffId?: string
  assignedStaffName?: string
  createdAt: Date
}

export interface ActivityRegistration {
  id: string
  activityId: string
  activityTitle: string
  guestUserId: string
  guestName: string
  roomNumber: string
  status: 'REGISTERED' | 'ATTENDED' | 'CANCELLED'
  registeredAt: Date
}

// Maintenance Management Interfaces
export interface MaintenanceFloor {
  id: string
  name: string
  totalRooms: number
  roomCount: number
  cleanRooms: number
  maintenanceRooms: number
  completionRate: number
  activeMaintenanceRequests: number
  status: 'OPERATIONAL' | 'MAINTENANCE' | 'ISSUES' | 'INSPECTION'
  lastMaintenance?: string
  rooms: Room[]
  supervisor?: string
  supervisorId?: string
}

export interface MaintenanceRoom {
  id: string
  roomNumber: string
  number: string
  roomType: string
  type: string
  floor: string
  status: 'CLEAN' | 'MAINTENANCE' | 'OUT_OF_ORDER' | 'INSPECTION'
  lastMaintenanceDate: string
  lastMaintenance?: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  description?: string
  activeRequest?: string
}

export interface MaintenanceStaff {
  id: string
  name: string
  specialization: string
  currentTaskCount: number
  status: 'AVAILABLE' | 'BUSY' | 'OFF_DUTY'
  avatar?: string
  phone: string
  email: string
  experience: number
  completedTasks: number
  activeTasks: number
  rating: number
  lastActivity?: string
  position?: string
}

export interface CommunicationChannel {
  id: string
  name: string
  type: 'GENERAL' | 'DEPARTMENT' | 'DIRECT'
  participants: string[]
  lastMessage?: ChatMessage
  unreadCount: number
  icon?: string
  memberCount?: number
  department?: 'MAINTENANCE' | 'HOUSEKEEPING'
}

// Housekeeping Interfaces
export type RoomStatus = 'DIRTY' | 'CLEANING' | 'CLEAN' | 'INSPECTED' | 'OUT_OF_ORDER' | 'MAINTENANCE'
export type TaskStatus = 'NEW' | 'ASSIGNED' | 'IN_PROGRESS' | 'QUALITY_CHECK' | 'COMPLETED' | 'CANCELLED'
export type TaskPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
export type StaffStatus = 'AVAILABLE' | 'BUSY' | 'ON_BREAK' | 'OFF_DUTY'

export interface Room {
  id: string
  number: string
  type: string
  floor: number
  status: RoomStatus
  lastCleaned: string
  nextInspection: string
  currentGuest?: string
  guestName?: string
  checkOutTime?: string
  checkInTime?: string
  checkIn?: string
  checkOut?: string
  notes?: string
  assignedStaff?: string
  floorId?: string
  lastMaintenance?: string
}

export interface HousekeepingTask {
  id: string
  title: string
  description: string
  roomNumber: string
  status: TaskStatus
  priority: TaskPriority
  assignedTo?: string
  estimatedTime: number
  deadline: string
  notes?: string
  createdAt: string
  completedAt?: string
  roomId?: string
}

export interface StaffMember {
  id: string
  name: string
  surname: string
  role: string
  position?: string
  status: StaffStatus
  avatar: string
  shift: string
  performanceScore: number
  tasksCompleted: number
  averageTime: number
  phone: string
  email: string
  startDate: string
  hireDate?: string
  currentTasks: string[]
  completedTasks?: number
  averageTaskTime?: string
}

export interface Floor {
  id: string
  name: string
  rooms: Room[]
  supervisor?: string
  supervisorId?: string
}

export interface StaffRequest {
  id: string
  requesterName: string
  type: string
  title: string
  description: string
  priority: TaskPriority
  status: TaskStatus
  requestDate: string
  responseDate?: string
  approvedBy?: string
  reason?: string
}

export interface DirectMessage {
  id: string
  name: string
  lastMessage: string
  lastMessageTime: string
  unreadCount: number
  isOnline: boolean
  department?: 'MAINTENANCE' | 'HOUSEKEEPING'
}

export interface CommunicationMessage {
  id: string
  sender: string
  message: string
  timestamp: string
  isOwn: boolean
  channelId?: string
  directMessageId?: string
  status?: 'sent' | 'delivered' | 'read'
}

export interface TaskCardProps {
  task: HousekeepingTask
  onStatusChange: (taskId: string, newStatus: TaskStatus) => void
  onEdit?: (task: HousekeepingTask) => void
}

export interface ChatMessage {
  id: string
  sender: string
  senderName: string
  text: string
  message: string
  timestamp: string
  isOwn: boolean
  isRead: boolean
  department?: 'MAINTENANCE' | 'HOUSEKEEPING'
  threadId?: string
  channelId?: string
  directMessageId?: string
  status?: 'sent' | 'delivered' | 'read'
  attachments?: string[]
} 