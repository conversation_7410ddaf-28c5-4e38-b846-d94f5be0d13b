<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50" @click="closeModal">
    <!-- Modal Content -->
    <div class="bg-white dark:bg-navy-800 rounded-lg shadow-xl max-w-md w-full m-4 max-h-[90vh] overflow-y-auto" @click.stop>
      <!-- <PERSON><PERSON> Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-navy-700 dark:text-white">
          {{ isEditing ? 'Edit Event' : 'Create New Event' }}
        </h3>
      </div>

      <!-- Modal Body -->
      <form @submit.prevent="handleSubmit" class="px-6 py-4">
        <!-- Title -->
        <div class="mb-4">
          <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Event Title *
          </label>
          <input
            id="title"
            v-model="formData.title"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
            placeholder="e.g., Canlı Jazz Müzik Gecesi"
          >
        </div>

        <!-- Description -->
        <div class="mb-4">
          <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            v-model="formData.description"
            rows="4"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
            placeholder="Brief description of the event"
          ></textarea>
        </div>

        <!-- Date and Time -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date *
            </label>
            <input
              id="date"
              v-model="formData.date"
              type="date"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
            >
          </div>
          <div>
            <label for="time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time *
            </label>
            <input
              id="time"
              v-model="formData.time"
              type="time"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
            >
          </div>
        </div>

        <!-- Location -->
        <div class="mb-4">
          <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Location *
          </label>
          <input
            id="location"
            v-model="formData.location"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
            placeholder="e.g., Otel Lounge Bar"
          >
        </div>

        <!-- Max Attendees -->
        <div class="mb-4">
          <label for="maxAttendees" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Maximum Attendees
          </label>
          <input
            id="maxAttendees"
            v-model.number="formData.maxAttendees"
            type="number"
            min="1"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent bg-white dark:bg-navy-700 text-gray-900 dark:text-white"
            placeholder="Optional"
          >
        </div>

        <!-- Image Upload Placeholder -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Event Image
          </label>
          <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Upload event image
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-500">
              (Will be connected to Supabase Storage)
            </p>
          </div>
        </div>

        <!-- Active Toggle -->
        <div class="mb-6">
          <label class="flex items-center">
            <input
              v-model="formData.isActive"
              type="checkbox"
              class="sr-only"
            >
            <div class="relative">
              <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
              <div 
                class="absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition transform"
                :class="formData.isActive ? 'translate-x-6 bg-brand-500' : ''"
              ></div>
            </div>
            <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
              Event is active
            </span>
          </label>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-600 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-brand-500 hover:bg-brand-600 rounded-lg transition-colors"
          >
            {{ isEditing ? 'Update' : 'Create' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { HotelEvent, CreateEventData } from '@/types/management'

interface Props {
  event?: HotelEvent | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: CreateEventData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isEditing = computed(() => !!props.event)

const formData = ref<CreateEventData>({
  title: '',
  description: '',
  date: '',
  time: '',
  location: '',
  maxAttendees: undefined,
  isActive: true
})

const closeModal = () => {
  emit('close')
}

const handleSubmit = () => {
  emit('save', { ...formData.value })
}

onMounted(() => {
  if (props.event) {
    formData.value = {
      title: props.event.title,
      description: props.event.description,
      date: props.event.date,
      time: props.event.time,
      location: props.event.location,
      maxAttendees: props.event.maxAttendees,
      isActive: props.event.isActive
    }
  } else {
    // Set default date to tomorrow
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    formData.value.date = tomorrow.toISOString().split('T')[0]
    formData.value.time = '19:00'
  }
})
</script>

<style scoped>
/* Custom checkbox styling */
input[type="checkbox"]:checked + div {
  background-color: #6366f1;
}

input[type="checkbox"]:checked + div > div {
  transform: translateX(1.5rem);
  background-color: white;
}
</style> 