// apps/mfe-customer-portal/src/data/activitiesData.ts

export interface TimelineEvent {
  id: string;
  type: 'activity' | 'service'; // To distinguish between general activities and personal bookings
  name: string;
  location: string;
  startTime: string; // Format: 'HH:mm'
  endTime: string;   // Format: 'HH:mm'
  description: string;
  image?: string;
  isPromoted?: boolean; // To feature in the promotional section
}

// General daily activities available to all guests
export const dailyActivities: TimelineEvent[] = [
  { 
    id: 'act1', 
    type: 'activity', 
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> (Açık Büfe)', 
    location: 'Ana Restoran', 
    startTime: '07:00', 
    endTime: '10:30', 
    description: '<PERSON><PERSON>ne zengin açık büfe kahvaltımızla başlayın.' 
  },
  { 
    id: 'act2', 
    type: 'activity', 
    name: 'Vitamin Bar', 
    location: 'SPA Merkezi', 
    startTime: '09:00', 
    endTime: '19:00', 
    description: '<PERSON><PERSON> sıkılm<PERSON> meyve suları ve sağlıklı atıştırmalıklar.', 
    image: 'https://picsum.photos/seed/juicebar/600/400', 
    isPromoted: true 
  },
  { 
    id: 'act3', 
    type: 'activity', 
    name: 'Family Club Havuz Bar', 
    location: 'Family Club Havuzu', 
    startTime: '10:00', 
    endTime: '18:00', 
    description: 'Ailenizle havuz başında serinletici içecekler.' 
  },
  { 
    id: 'act4', 
    type: 'activity', 
    name: 'Coffee House', 
    location: 'Lobi Katı', 
    startTime: '10:00', 
    endTime: '18:00', 
    description: 'Özel kahve çeşitleri ve lezzetli pastalar.' 
  },
  { 
    id: 'act5', 
    type: 'activity', 
    name: 'Arena Pool Bar', 
    location: 'Ana Havuz', 
    startTime: '10:00', 
    endTime: '00:00', 
    description: 'Gün boyu müzik ve kokteyller.' 
  },
  { 
    id: 'act6', 
    type: 'activity', 
    name: 'Aqua Aerobik', 
    location: 'Gösteri Havuzu', 
    startTime: '12:00', 
    endTime: '13:00', 
    description: 'Eğlenceli ve enerjik su jimnastiği.', 
    image: 'https://picsum.photos/seed/aquagym/600/400', 
    isPromoted: true 
  },
  { 
    id: 'act7', 
    type: 'activity', 
    name: 'Akşam Yemeği', 
    location: 'Ana Restoran', 
    startTime: '19:00', 
    endTime: '21:30', 
    description: 'Dünya mutfaklarından seçkin lezzetler.' 
  },
  { 
    id: 'act8', 
    type: 'activity', 
    name: 'Canlı Müzik', 
    location: 'Arena Pool Bar', 
    startTime: '21:30', 
    endTime: '23:30', 
    description: 'Gitar dinletisi ile keyifli bir akşam.', 
    image: 'https://picsum.photos/seed/livemusic/600/400', 
    isPromoted: true 
  },
];

// Simulated services booked by the user from the "Services" module
export const userBookedServices: TimelineEvent[] = [
  { 
    id: 'ser1', 
    type: 'service', 
    name: 'Tenis Dersi', 
    location: 'Tenis Kortu 1', 
    startTime: '11:00', 
    endTime: '12:00', 
    description: 'Özel tenis dersiniz başlıyor.' 
  },
  { 
    id: 'ser2', 
    type: 'service', 
    name: 'İsveç Masajı', 
    location: 'SPA Merkezi - Oda 3', 
    startTime: '16:00', 
    endTime: '16:50', 
    description: 'Rahatlama seansınız için hazır olun.' 
  },
]; 