import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import NotificationBell from '../notifications/NotificationBell.vue'
import { useRealtimeStore } from '@/stores/realtimeStore'
import { useStaffNotificationStore } from '@/stores/staffNotificationStore'

// Mock the stores
vi.mock('@/stores/realtimeStore')
vi.mock('@/stores/staffNotificationStore')

describe('NotificationBell', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Mock the store implementations
    vi.mocked(useRealtimeStore).mockReturnValue({
      recentNotifications: [],
      unreadCount: 2,
      isConnected: true,
      markAllAsRead: vi.fn(),
      clearAllNotifications: vi.fn(),
      testNotificationSystem: vi.fn()
    } as any)
    
    vi.mocked(useStaffNotificationStore).mockReturnValue({
      unreadCount: 3
    } as any)
  })

  it('renders notification bell with correct unread count', () => {
    const wrapper = mount(NotificationBell)
    
    // Should show total unread count (2 + 3 = 5)
    expect(wrapper.find('[data-testid="notification-count"]').text()).toBe('5')
  })

  it('shows connection status indicator', () => {
    const wrapper = mount(NotificationBell)
    
    // Should show connected status
    const statusIndicator = wrapper.find('[data-testid="connection-status"]')
    expect(statusIndicator.exists()).toBe(true)
  })

  it('toggles dropdown when bell is clicked', async () => {
    const wrapper = mount(NotificationBell)
    
    // Initially dropdown should be hidden
    expect(wrapper.find('[data-testid="notification-dropdown"]').exists()).toBe(false)
    
    // Click the bell
    await wrapper.find('[data-testid="notification-bell"]').trigger('click')
    
    // Dropdown should now be visible
    expect(wrapper.find('[data-testid="notification-dropdown"]').exists()).toBe(true)
  })

  it('calls markAllAsRead when button is clicked', async () => {
    const mockMarkAllAsRead = vi.fn()
    vi.mocked(useRealtimeStore).mockReturnValue({
      recentNotifications: [
        { id: '1', title: 'Test', message: 'Test message', type: 'info', timestamp: new Date(), isRead: false }
      ],
      unreadCount: 1,
      isConnected: true,
      markAllAsRead: mockMarkAllAsRead,
      clearAllNotifications: vi.fn(),
      testNotificationSystem: vi.fn()
    } as any)
    
    const wrapper = mount(NotificationBell)
    
    // Open dropdown
    await wrapper.find('[data-testid="notification-bell"]').trigger('click')
    
    // Click mark all as read button
    await wrapper.find('[data-testid="mark-all-read"]').trigger('click')
    
    expect(mockMarkAllAsRead).toHaveBeenCalled()
  })

  it('calls testNotificationSystem when test button is clicked', async () => {
    const mockTestNotificationSystem = vi.fn()
    vi.mocked(useRealtimeStore).mockReturnValue({
      recentNotifications: [],
      unreadCount: 0,
      isConnected: true,
      markAllAsRead: vi.fn(),
      clearAllNotifications: vi.fn(),
      testNotificationSystem: mockTestNotificationSystem
    } as any)
    
    const wrapper = mount(NotificationBell)
    
    // Open dropdown
    await wrapper.find('[data-testid="notification-bell"]').trigger('click')
    
    // Click test button
    await wrapper.find('[data-testid="test-notifications"]').trigger('click')
    
    expect(mockTestNotificationSystem).toHaveBeenCalled()
  })

  it('hides unread count badge when count is 0', () => {
    vi.mocked(useRealtimeStore).mockReturnValue({
      recentNotifications: [],
      unreadCount: 0,
      isConnected: true,
      markAllAsRead: vi.fn(),
      clearAllNotifications: vi.fn(),
      testNotificationSystem: vi.fn()
    } as any)
    
    vi.mocked(useStaffNotificationStore).mockReturnValue({
      unreadCount: 0
    } as any)
    
    const wrapper = mount(NotificationBell)
    
    expect(wrapper.find('[data-testid="notification-count"]').exists()).toBe(false)
  })
})
