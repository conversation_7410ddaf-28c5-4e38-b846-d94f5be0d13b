/**
 * Cross-MFE Realtime Service
 * Manages realtime subscriptions and cross-MFE communication for data consistency
 */

import { supabase } from './index'
import type { RealtimeChannel } from '@supabase/supabase-js'

// Event types for cross-MFE communication
export enum CrossMFEEventType {
  // Hotel-related events
  HOTEL_UPDATED = 'hotel:updated',
  HOTEL_STATUS_CHANGED = 'hotel:status_changed',
  
  // User-related events
  USER_PROFILE_UPDATED = 'user:profile_updated',
  USER_ROLE_CHANGED = 'user:role_changed',
  
  // Task-related events
  TASK_STATUS_UPDATED = 'task:status_updated',
  MAINTENANCE_TASK_UPDATED = 'maintenance_task:updated',
  
  // Service-related events
  SERVICE_REQUEST_UPDATED = 'service_request:updated',
  SERVICE_REQUEST_STATUS_CHANGED = 'service_request:status_changed',
  
  // Notification events
  NOTIFICATION_RECEIVED = 'notification:received',
  NOTIFICATION_READ = 'notification:read',
  
  // Room-related events
  ROOM_STATUS_CHANGED = 'room:status_changed',
  ROOM_UPDATED = 'room:updated',
  
  // Order-related events
  ORDER_STATUS_UPDATED = 'order:status_updated',
  ORDER_CREATED = 'order:created',
  
  // Activity-related events
  ACTIVITY_UPDATED = 'activity:updated',
  ACTIVITY_REGISTRATION_CHANGED = 'activity:registration_changed',

  // Data consistency events
  DATA_CONSISTENCY_UPDATE = 'data:consistency_update',
  DATA_STALE = 'data:stale',
  SYSTEM_ERROR = 'system:error'
}

// Event payload structure
export interface CrossMFEEvent {
  type: CrossMFEEventType
  payload: {
    id?: string
    hotelId?: string
    userId?: string
    data?: any
    timestamp: number
    source: string // which MFE triggered the event
    metadata?: Record<string, any>
    // Additional fields for specific event types
    key?: string // for data consistency events
    version?: number // for data versioning
    age?: number // for stale data events
    error?: string // for error events
    eventType?: string // for error context
    [key: string]: any // Allow additional properties
  }
}

// Event handler type
export type CrossMFEEventHandler = (event: CrossMFEEvent) => void

// Subscription cleanup function
export type UnsubscribeFunction = () => void

/**
 * CrossMFERealtimeService - Manages realtime subscriptions and cross-MFE communication
 */
export class CrossMFERealtimeService {
  private static instance: CrossMFERealtimeService | null = null
  private eventHandlers: Map<CrossMFEEventType, Set<CrossMFEEventHandler>> = new Map()
  private realtimeChannels: Map<string, RealtimeChannel> = new Map()
  private isInitialized = false
  private currentHotelId: string | null = null
  private currentUserId: string | null = null
  private dataConsistencyCache: Map<string, { data: any; timestamp: number; version: number }> = new Map()
  private consistencyCheckInterval: NodeJS.Timeout | null = null

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): CrossMFERealtimeService {
    if (!CrossMFERealtimeService.instance) {
      CrossMFERealtimeService.instance = new CrossMFERealtimeService()
    }
    return CrossMFERealtimeService.instance
  }

  /**
   * Initialize the service with user and hotel context
   */
  public async initialize(hotelId: string, userId: string): Promise<void> {
    if (this.isInitialized && this.currentHotelId === hotelId && this.currentUserId === userId) {
      console.log('CrossMFERealtimeService already initialized for this context')
      return
    }

    // Cleanup existing subscriptions if reinitializing
    if (this.isInitialized) {
      await this.cleanup()
    }

    this.currentHotelId = hotelId
    this.currentUserId = userId

    console.log('Initializing CrossMFERealtimeService', { hotelId, userId })

    // Setup shared realtime subscriptions
    await this.setupSharedSubscriptions()

    // Start data consistency monitoring
    this.startConsistencyMonitoring()

    this.isInitialized = true
    console.log('CrossMFERealtimeService initialized successfully')
  }

  /**
   * Subscribe to cross-MFE events
   */
  public subscribe(eventType: CrossMFEEventType, handler: CrossMFEEventHandler): UnsubscribeFunction {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set())
    }

    const handlers = this.eventHandlers.get(eventType)!
    handlers.add(handler)

    console.log(`Subscribed to ${eventType}, total handlers: ${handlers.size}`)

    // Return unsubscribe function
    return () => {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType)
      }
      console.log(`Unsubscribed from ${eventType}`)
    }
  }

  /**
   * Broadcast event to all subscribers
   */
  public broadcast(event: CrossMFEEvent): void {
    const handlers = this.eventHandlers.get(event.type)
    if (!handlers || handlers.size === 0) {
      console.log(`No handlers for event type: ${event.type}`)
      return
    }

    console.log(`Broadcasting ${event.type} to ${handlers.size} handlers`, event.payload)

    // Dispatch to all handlers with enhanced error handling
    handlers.forEach(handler => {
      try {
        handler(event)
      } catch (error) {
        console.error(`Error in event handler for ${event.type}:`, error)

        // Emit error event for monitoring
        const errorEvent: CrossMFEEvent = {
          type: CrossMFEEventType.SYSTEM_ERROR,
          payload: {
            error: error instanceof Error ? error.message : 'Unknown error',
            eventType: event.type,
            timestamp: Date.now(),
            source: 'event-handler'
          }
        }

        // Prevent infinite loops by not broadcasting error events for error events
        if (event.type !== CrossMFEEventType.SYSTEM_ERROR) {
          setTimeout(() => this.broadcast(errorEvent), 0)
        }
      }
    })

    // Also dispatch as custom DOM event for additional flexibility
    const customEvent = new CustomEvent('cross-mfe-event', {
      detail: event
    })
    window.dispatchEvent(customEvent)
  }

  /**
   * Setup shared realtime subscriptions for critical data
   */
  private async setupSharedSubscriptions(): Promise<void> {
    if (!this.currentHotelId || !this.currentUserId) {
      console.error('Cannot setup subscriptions: missing hotel or user ID')
      return
    }

    try {
      // Hotel data changes subscription
      await this.setupHotelSubscription()

      // User profile changes subscription
      await this.setupUserProfileSubscription()

      // Global notifications subscription
      await this.setupNotificationSubscription()

      // Orders subscription for cross-MFE order tracking
      await this.setupOrdersSubscription()

      // Service requests subscription for cross-MFE service tracking
      await this.setupServiceRequestsSubscription()

      // Maintenance tasks subscription for cross-MFE task tracking
      await this.setupMaintenanceTasksSubscription()

      // Rooms subscription for cross-MFE room status tracking
      await this.setupRoomsSubscription()

      // Activities subscription for cross-MFE activity tracking
      await this.setupActivitiesSubscription()

      console.log('Shared realtime subscriptions setup complete')
    } catch (error) {
      console.error('Error setting up shared subscriptions:', error)
    }
  }

  /**
   * Setup orders subscription for cross-MFE order tracking
   */
  private async setupOrdersSubscription(): Promise<void> {
    const channelName = 'shared-orders-changes'

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'room_service_orders',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          this.broadcast({
            type: CrossMFEEventType.ORDER_STATUS_UPDATED,
            payload: {
              id: (payload.new as any)?.id || (payload.old as any)?.id || 'unknown',
              hotelId: this.currentHotelId!,
              data: payload.new || payload.old,
              timestamp: Date.now(),
              source: 'shared-service'
            }
          })
        }
      )
      .subscribe()

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup service requests subscription for cross-MFE service tracking
   */
  private async setupServiceRequestsSubscription(): Promise<void> {
    const channelName = 'shared-service-requests-changes'

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'service_requests',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          this.broadcast({
            type: CrossMFEEventType.SERVICE_REQUEST_UPDATED,
            payload: {
              id: (payload.new as any)?.id || (payload.old as any)?.id || 'unknown',
              hotelId: this.currentHotelId!,
              data: payload.new || payload.old,
              timestamp: Date.now(),
              source: 'shared-service'
            }
          })
        }
      )
      .subscribe()

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup maintenance tasks subscription for cross-MFE task tracking
   */
  private async setupMaintenanceTasksSubscription(): Promise<void> {
    const channelName = 'shared-maintenance-tasks-changes'

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'maintenance_tasks',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          this.broadcast({
            type: CrossMFEEventType.MAINTENANCE_TASK_UPDATED,
            payload: {
              id: (payload.new as any)?.id || (payload.old as any)?.id || 'unknown',
              hotelId: this.currentHotelId!,
              data: payload.new || payload.old,
              timestamp: Date.now(),
              source: 'shared-service'
            }
          })
        }
      )
      .subscribe()

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup rooms subscription for cross-MFE room status tracking
   */
  private async setupRoomsSubscription(): Promise<void> {
    const channelName = 'shared-rooms-changes'

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'rooms',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          this.broadcast({
            type: CrossMFEEventType.ROOM_UPDATED,
            payload: {
              id: (payload.new as any)?.id || (payload.old as any)?.id || 'unknown',
              hotelId: this.currentHotelId!,
              data: payload.new || payload.old,
              timestamp: Date.now(),
              source: 'shared-service'
            }
          })
        }
      )
      .subscribe()

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup activities subscription for cross-MFE activity tracking
   */
  private async setupActivitiesSubscription(): Promise<void> {
    const channelName = 'shared-activities-changes'

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'hotel_activities',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          this.broadcast({
            type: CrossMFEEventType.ACTIVITY_UPDATED,
            payload: {
              id: (payload.new as any)?.id || (payload.old as any)?.id || 'unknown',
              hotelId: this.currentHotelId!,
              data: payload.new || payload.old,
              timestamp: Date.now(),
              source: 'shared-service'
            }
          })
        }
      )
      .subscribe()

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup hotel data changes subscription
   */
  private async setupHotelSubscription(): Promise<void> {
    const channelName = 'shared-hotels-changes'
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'hotels'
        },
        (payload) => {
          console.log('Hotel change detected:', payload)
          this.handleHotelChange(payload)
        }
      )
      .subscribe((status) => {
        console.log(`Hotel subscription status: ${status}`)
      })

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup user profile changes subscription
   */
  private async setupUserProfileSubscription(): Promise<void> {
    const channelName = 'shared-user-profiles-changes'
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_profiles'
        },
        (payload) => {
          console.log('User profile change detected:', payload)
          this.handleUserProfileChange(payload)
        }
      )
      .subscribe((status) => {
        console.log(`User profile subscription status: ${status}`)
      })

    this.realtimeChannels.set(channelName, channel)
  }

  /**
   * Setup notifications subscription
   */
  private async setupNotificationSubscription(): Promise<void> {
    const channelName = 'shared-notifications-changes'
    
    // Subscribe to both guest and staff notifications
    const guestChannel = supabase
      .channel(`${channelName}-guest`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'guest_notifications',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          console.log('Guest notification change detected:', payload)
          this.handleNotificationChange(payload, 'guest')
        }
      )
      .subscribe((status) => {
        console.log(`Guest notification subscription status: ${status}`)
      })

    const staffChannel = supabase
      .channel(`${channelName}-staff`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'staff_notifications',
          filter: `hotel_id=eq.${this.currentHotelId}`
        },
        (payload) => {
          console.log('Staff notification change detected:', payload)
          this.handleNotificationChange(payload, 'staff')
        }
      )
      .subscribe((status) => {
        console.log(`Staff notification subscription status: ${status}`)
      })

    this.realtimeChannels.set(`${channelName}-guest`, guestChannel)
    this.realtimeChannels.set(`${channelName}-staff`, staffChannel)
  }

  /**
   * Handle hotel data changes
   */
  private handleHotelChange(payload: any): void {
    const { eventType, new: newRecord, old: oldRecord } = payload
    const hotelData = newRecord || oldRecord

    if (!hotelData) return

    let eventTypeToEmit: CrossMFEEventType

    switch (eventType) {
      case 'UPDATE':
        // Check if status changed specifically
        if (newRecord?.status !== oldRecord?.status) {
          eventTypeToEmit = CrossMFEEventType.HOTEL_STATUS_CHANGED
        } else {
          eventTypeToEmit = CrossMFEEventType.HOTEL_UPDATED
        }
        break
      case 'INSERT':
      case 'DELETE':
        eventTypeToEmit = CrossMFEEventType.HOTEL_UPDATED
        break
      default:
        return
    }

    this.broadcast({
      type: eventTypeToEmit,
      payload: {
        id: hotelData.id,
        hotelId: hotelData.id,
        data: hotelData,
        timestamp: Date.now(),
        source: 'shared-realtime-service',
        metadata: { eventType, oldData: oldRecord }
      }
    })
  }

  /**
   * Handle user profile changes
   */
  private handleUserProfileChange(payload: any): void {
    const { eventType, new: newRecord, old: oldRecord } = payload
    const userData = newRecord || oldRecord

    if (!userData) return

    let eventTypeToEmit: CrossMFEEventType

    switch (eventType) {
      case 'UPDATE':
        // Check if role changed specifically
        if (newRecord?.role !== oldRecord?.role || newRecord?.hotel_id !== oldRecord?.hotel_id) {
          eventTypeToEmit = CrossMFEEventType.USER_ROLE_CHANGED
        } else {
          eventTypeToEmit = CrossMFEEventType.USER_PROFILE_UPDATED
        }
        break
      case 'INSERT':
      case 'DELETE':
        eventTypeToEmit = CrossMFEEventType.USER_PROFILE_UPDATED
        break
      default:
        return
    }

    this.broadcast({
      type: eventTypeToEmit,
      payload: {
        id: userData.id,
        userId: userData.id,
        hotelId: userData.hotel_id,
        data: userData,
        timestamp: Date.now(),
        source: 'shared-realtime-service',
        metadata: { eventType, oldData: oldRecord }
      }
    })
  }

  /**
   * Handle notification changes
   */
  private handleNotificationChange(payload: any, notificationType: 'guest' | 'staff'): void {
    const { eventType, new: newRecord, old: oldRecord } = payload
    const notificationData = newRecord || oldRecord

    if (!notificationData) return

    let eventTypeToEmit: CrossMFEEventType

    switch (eventType) {
      case 'INSERT':
        eventTypeToEmit = CrossMFEEventType.NOTIFICATION_RECEIVED
        break
      case 'UPDATE':
        // Check if read status changed
        if (newRecord?.is_read !== oldRecord?.is_read && newRecord?.is_read) {
          eventTypeToEmit = CrossMFEEventType.NOTIFICATION_READ
        } else {
          eventTypeToEmit = CrossMFEEventType.NOTIFICATION_RECEIVED
        }
        break
      default:
        return
    }

    this.broadcast({
      type: eventTypeToEmit,
      payload: {
        id: notificationData.id,
        userId: notificationData.user_id || notificationData.guest_id,
        hotelId: notificationData.hotel_id,
        data: notificationData,
        timestamp: Date.now(),
        source: 'shared-realtime-service',
        metadata: { eventType, notificationType, oldData: oldRecord }
      }
    })
  }

  /**
   * Create and broadcast a cross-MFE event
   */
  public createEvent(
    type: CrossMFEEventType,
    id: string,
    data: any,
    source: string,
    metadata?: Record<string, any>
  ): void {
    const event: CrossMFEEvent = {
      type,
      payload: {
        id,
        hotelId: this.currentHotelId || undefined,
        userId: this.currentUserId || undefined,
        data,
        timestamp: Date.now(),
        source,
        metadata
      }
    }

    this.broadcast(event)
  }

  /**
   * Get current context
   */
  public getContext(): { hotelId: string | null; userId: string | null; isInitialized: boolean } {
    return {
      hotelId: this.currentHotelId,
      userId: this.currentUserId,
      isInitialized: this.isInitialized
    }
  }

  /**
   * Cleanup all subscriptions and reset state
   */
  public async cleanup(): Promise<void> {
    console.log('Cleaning up CrossMFERealtimeService')

    // Unsubscribe from all realtime channels
    for (const [channelName, channel] of this.realtimeChannels) {
      try {
        await channel.unsubscribe()
        console.log(`Unsubscribed from channel: ${channelName}`)
      } catch (error) {
        console.error(`Error unsubscribing from channel ${channelName}:`, error)
      }
    }

    // Clear all channels
    this.realtimeChannels.clear()

    // Clear all event handlers
    this.eventHandlers.clear()

    // Stop consistency monitoring
    this.stopConsistencyMonitoring()

    // Clear consistency cache
    this.dataConsistencyCache.clear()

    // Reset state
    this.isInitialized = false
    this.currentHotelId = null
    this.currentUserId = null

    console.log('CrossMFERealtimeService cleanup complete')
  }

  /**
   * Get subscription status
   */
  public getSubscriptionStatus(): Record<string, string> {
    const status: Record<string, string> = {}

    for (const [channelName, channel] of this.realtimeChannels) {
      status[channelName] = channel.state
    }

    return status
  }

  /**
   * Get active event handlers count
   */
  public getEventHandlersCount(): Record<string, number> {
    const counts: Record<string, number> = {}

    for (const [eventType, handlers] of this.eventHandlers) {
      counts[eventType] = handlers.size
    }

    return counts
  }

  /**
   * Update data consistency cache
   */
  public updateDataConsistency(key: string, data: any, version?: number): void {
    const timestamp = Date.now()
    const currentVersion = version || (this.dataConsistencyCache.get(key)?.version || 0) + 1

    this.dataConsistencyCache.set(key, {
      data,
      timestamp,
      version: currentVersion
    })

    // Broadcast data consistency update
    this.broadcast({
      type: CrossMFEEventType.DATA_CONSISTENCY_UPDATE,
      payload: {
        key,
        data,
        version: currentVersion,
        timestamp,
        source: 'consistency-service'
      }
    })
  }

  /**
   * Get data from consistency cache
   */
  public getConsistentData(key: string): { data: any; timestamp: number; version: number } | null {
    return this.dataConsistencyCache.get(key) || null
  }

  /**
   * Validate data consistency across MFEs
   */
  public validateDataConsistency(key: string, data: any, version: number): boolean {
    const cached = this.dataConsistencyCache.get(key)

    if (!cached) {
      // No cached data, accept new data
      this.updateDataConsistency(key, data, version)
      return true
    }

    if (version < cached.version) {
      // Stale data, reject
      console.warn(`Stale data detected for ${key}. Current version: ${cached.version}, received: ${version}`)
      return false
    }

    if (version > cached.version) {
      // Newer data, update cache
      this.updateDataConsistency(key, data, version)
      return true
    }

    // Same version, check timestamp
    const timeDiff = Date.now() - cached.timestamp
    if (timeDiff > 30000) { // 30 seconds threshold
      console.warn(`Data consistency timeout for ${key}. Age: ${timeDiff}ms`)
      this.updateDataConsistency(key, data, version)
      return true
    }

    return true
  }

  /**
   * Start data consistency monitoring
   */
  private startConsistencyMonitoring(): void {
    if (this.consistencyCheckInterval) {
      clearInterval(this.consistencyCheckInterval)
    }

    this.consistencyCheckInterval = setInterval(() => {
      const now = Date.now()
      const staleThreshold = 5 * 60 * 1000 // 5 minutes

      for (const [key, cached] of this.dataConsistencyCache.entries()) {
        if (now - cached.timestamp > staleThreshold) {
          console.warn(`Stale data detected in consistency cache: ${key}`)

          // Emit stale data event
          this.broadcast({
            type: CrossMFEEventType.DATA_STALE,
            payload: {
              key,
              age: now - cached.timestamp,
              timestamp: now,
              source: 'consistency-monitor'
            }
          })
        }
      }
    }, 60000) // Check every minute
  }

  /**
   * Stop data consistency monitoring
   */
  private stopConsistencyMonitoring(): void {
    if (this.consistencyCheckInterval) {
      clearInterval(this.consistencyCheckInterval)
      this.consistencyCheckInterval = null
    }
  }

  /**
   * Get consistency status
   */
  public getConsistencyStatus(): {
    cacheSize: number
    oldestEntry: number | null
    newestEntry: number | null
    staleEntries: number
  } {
    const now = Date.now()
    const staleThreshold = 5 * 60 * 1000 // 5 minutes
    let oldestEntry: number | null = null
    let newestEntry: number | null = null
    let staleEntries = 0

    for (const cached of this.dataConsistencyCache.values()) {
      if (oldestEntry === null || cached.timestamp < oldestEntry) {
        oldestEntry = cached.timestamp
      }
      if (newestEntry === null || cached.timestamp > newestEntry) {
        newestEntry = cached.timestamp
      }
      if (now - cached.timestamp > staleThreshold) {
        staleEntries++
      }
    }

    return {
      cacheSize: this.dataConsistencyCache.size,
      oldestEntry,
      newestEntry,
      staleEntries
    }
  }
}

// Export singleton instance
export const crossMFERealtimeService = CrossMFERealtimeService.getInstance()
