import { test, expect } from '@playwright/test'
import { TestHelpers } from './utils/test-helpers'

test.describe('Core Business Flows', () => {
  let helpers: TestHelpers

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page)
  })

  test.describe('User Authentication Flow', () => {
    test('should complete full login/logout cycle', async ({ page }) => {
      // Test Management Portal login
      await page.goto('http://localhost:5001/management/auth/login')
      await page.waitForTimeout(3000)

      const hasLoginForm = await helpers.hasAuthForm()
      if (hasLoginForm) {
        // Test form validation
        const emailInput = page.locator('input[type="email"], input[name="email"]')
        const passwordInput = page.locator('input[type="password"], input[name="password"]')
        const submitButton = page.locator('button[type="submit"], button:has-text("Giriş")')

        await expect(emailInput).toBeVisible({ timeout: 10000 })
        await expect(passwordInput).toBeVisible()
        await expect(submitButton).toBeVisible()

        // Test empty form submission
        await submitButton.click()
        await page.waitForTimeout(1000)

        // Should show validation errors
        const hasValidationError = await page.locator('.error, .invalid, [role="alert"]').count() > 0
        console.log(`Form validation working: ${hasValidationError}`)

        // Test invalid credentials
        await emailInput.fill('<EMAIL>')
        await passwordInput.fill('wrongpassword')
        await submitButton.click()
        await page.waitForTimeout(2000)

        // Should show authentication error
        const hasAuthError = await page.locator('text=/invalid/i, text=/incorrect/i, text=/hatalı/i').count() > 0
        console.log(`Authentication error handling: ${hasAuthError}`)
      }

      // Test logout functionality if authenticated
      const logoutButton = page.locator('button:has-text("Çıkış"), button:has-text("Logout"), [data-testid="logout"]')
      const logoutButtonExists = await logoutButton.count() > 0

      if (logoutButtonExists) {
        await logoutButton.click()
        await page.waitForTimeout(2000)

        // Should redirect to login or show login form
        const backToLogin = await helpers.hasAuthForm()
        expect(backToLogin).toBe(true)
      }
    })

    test('should handle role-based redirects after login', async ({ page }) => {
      // Test different entry points and expected redirects
      const entryPoints = [
        { url: 'http://localhost:5001/management/dashboard', expectedRole: 'SUPER_ADMIN' },
        { url: 'http://localhost:5002/dashboard', expectedRole: 'HOTEL_ADMIN' },
        { url: 'http://localhost:3001', expectedRole: 'GUEST' }
      ]

      for (const entry of entryPoints) {
        await page.goto(entry.url)
        await page.waitForTimeout(3000)

        const hasAuth = await helpers.hasAuthForm()
        const hasContent = await helpers.hasMainContent()
        const isRedirected = page.url() !== entry.url

        // Should either show auth form, content, or redirect appropriately
        expect(hasAuth || hasContent || isRedirected).toBe(true)
        console.log(`${entry.expectedRole} entry: auth=${hasAuth}, content=${hasContent}, redirected=${isRedirected}`)
      }
    })
  })

  test.describe('Hotel Management Flow (SUPER_ADMIN)', () => {
    test('should display hotel list and management features', async ({ page }) => {
      await page.goto('http://localhost:5001/management/hotels')
      await page.waitForTimeout(3000)

      const hasAuth = await helpers.hasAuthForm()
      if (hasAuth) {
        console.log('Authentication required for hotel management')
        return
      }

      // Check for hotel management features
      const hotelTable = page.locator('table, .hotel-list, [data-testid="hotels"]')
      const addButton = page.locator('button:has-text("Ekle"), button:has-text("Add"), a[href*="create"]')
      const searchInput = page.locator('input[placeholder*="ara"], input[type="search"]')

      const hasHotelTable = await hotelTable.count() > 0
      const hasAddButton = await addButton.count() > 0
      const hasSearch = await searchInput.count() > 0

      console.log(`Hotel management features: table=${hasHotelTable}, add=${hasAddButton}, search=${hasSearch}`)

      // Test search functionality if available
      if (hasSearch) {
        await searchInput.fill('test')
        await page.waitForTimeout(1000)
        // Search should filter results or show no results
      }

      // Test add hotel functionality if available
      if (hasAddButton) {
        await addButton.click()
        await page.waitForTimeout(2000)
        
        // Should navigate to create form or open modal
        const hasForm = await page.locator('form, .modal, [data-testid="hotel-form"]').count() > 0
        console.log(`Add hotel form available: ${hasForm}`)
      }
    })

    test('should handle hotel CRUD operations', async ({ page }) => {
      await page.goto('http://localhost:5001/management/hotels')
      await page.waitForTimeout(3000)

      const hasAuth = await helpers.hasAuthForm()
      if (hasAuth) return

      // Test hotel details view
      const hotelLinks = page.locator('a[href*="hotel"], button:has-text("Detay"), .hotel-item')
      const hasHotelLinks = await hotelLinks.count() > 0

      if (hasHotelLinks) {
        await hotelLinks.first().click()
        await page.waitForTimeout(2000)

        // Should show hotel details
        const hasDetails = await page.locator('.hotel-details, [data-testid="hotel-details"]').count() > 0
        const hasEditButton = await page.locator('button:has-text("Düzenle"), button:has-text("Edit")').count() > 0

        console.log(`Hotel details: details=${hasDetails}, edit=${hasEditButton}`)

        // Test edit functionality
        if (hasEditButton) {
          const editButton = page.locator('button:has-text("Düzenle"), button:has-text("Edit")').first()
          await editButton.click()
          await page.waitForTimeout(2000)

          const hasEditForm = await page.locator('form, .edit-form, [data-testid="edit-form"]').count() > 0
          console.log(`Edit form available: ${hasEditForm}`)
        }
      }
    })
  })

  test.describe('Hotel Dashboard Flow (HOTEL_ADMIN/STAFF)', () => {
    test('should display dashboard with key metrics', async ({ page }) => {
      await page.goto('http://localhost:5002/dashboard')
      await page.waitForTimeout(3000)

      const hasAuth = await helpers.hasAuthForm()
      if (hasAuth) {
        console.log('Authentication required for hotel dashboard')
        return
      }

      // Check for dashboard components
      const dashboardStats = page.locator('.stats, .metrics, [data-testid="stats"]')
      const chartElements = page.locator('.chart, canvas, svg')
      const navigationMenu = page.locator('nav, .sidebar, .menu')

      const hasStats = await dashboardStats.count() > 0
      const hasCharts = await chartElements.count() > 0
      const hasNavigation = await navigationMenu.count() > 0

      console.log(`Dashboard features: stats=${hasStats}, charts=${hasCharts}, navigation=${hasNavigation}`)

      // Test navigation to different modules
      const moduleLinks = [
        { selector: 'a[href*="housekeeping"], button:has-text("Housekeeping")', module: 'housekeeping' },
        { selector: 'a[href*="maintenance"], button:has-text("Maintenance")', module: 'maintenance' },
        { selector: 'a[href*="ordering"], button:has-text("Ordering")', module: 'ordering' }
      ]

      for (const link of moduleLinks) {
        const linkElement = page.locator(link.selector)
        const hasLink = await linkElement.count() > 0

        if (hasLink) {
          await linkElement.first().click()
          await page.waitForTimeout(2000)

          const hasModuleContent = await helpers.hasMainContent()
          console.log(`${link.module} module accessible: ${hasModuleContent}`)

          // Navigate back to dashboard
          await page.goto('http://localhost:5002/dashboard')
          await page.waitForTimeout(1000)
        }
      }
    })

    test('should handle task management workflow', async ({ page }) => {
      await page.goto('http://localhost:5002/maintenance/tasks')
      await page.waitForTimeout(3000)

      const hasAuth = await helpers.hasAuthForm()
      if (hasAuth) return

      // Check for task management features
      const taskList = page.locator('.task-list, table, [data-testid="tasks"]')
      const addTaskButton = page.locator('button:has-text("Ekle"), button:has-text("Add Task")')
      const filterOptions = page.locator('select, .filter, [data-testid="filter"]')

      const hasTasks = await taskList.count() > 0
      const hasAddTask = await addTaskButton.count() > 0
      const hasFilters = await filterOptions.count() > 0

      console.log(`Task management: tasks=${hasTasks}, add=${hasAddTask}, filters=${hasFilters}`)

      // Test task creation if available
      if (hasAddTask) {
        await addTaskButton.first().click()
        await page.waitForTimeout(2000)

        const hasTaskForm = await page.locator('form, .modal, [data-testid="task-form"]').count() > 0
        console.log(`Task creation form: ${hasTaskForm}`)

        if (hasTaskForm) {
          // Test form fields
          const titleInput = page.locator('input[name="title"], input[placeholder*="title"]')
          const descriptionInput = page.locator('textarea[name="description"], textarea[placeholder*="description"]')

          const hasTitleInput = await titleInput.count() > 0
          const hasDescriptionInput = await descriptionInput.count() > 0

          console.log(`Task form fields: title=${hasTitleInput}, description=${hasDescriptionInput}`)
        }
      }
    })
  })

  test.describe('Customer Portal Flow (GUEST)', () => {
    test('should display hotel selection and booking interface', async ({ page }) => {
      await page.goto('http://localhost:3001')
      await page.waitForTimeout(3000)

      // Check for customer portal features
      const hotelList = page.locator('.hotel-list, .hotels, [data-testid="hotels"]')
      const searchInput = page.locator('input[type="search"], input[placeholder*="ara"]')
      const filterOptions = page.locator('.filters, select')

      const hasHotels = await hotelList.count() > 0
      const hasSearch = await searchInput.count() > 0
      const hasFilters = await filterOptions.count() > 0

      console.log(`Customer portal: hotels=${hasHotels}, search=${hasSearch}, filters=${hasFilters}`)

      // Test hotel selection
      const hotelCards = page.locator('.hotel-card, .hotel-item, [data-testid="hotel"]')
      const hasHotelCards = await hotelCards.count() > 0

      if (hasHotelCards) {
        await hotelCards.first().click()
        await page.waitForTimeout(2000)

        // Should navigate to hotel details or booking
        const hasBookingInterface = await page.locator('.booking, .reservation, [data-testid="booking"]').count() > 0
        const hasHotelDetails = await page.locator('.hotel-details, .hotel-info').count() > 0

        console.log(`Hotel selection: booking=${hasBookingInterface}, details=${hasHotelDetails}`)
      }
    })

    test('should handle guest authentication and services', async ({ page }) => {
      await page.goto('http://localhost:3001')
      await page.waitForTimeout(3000)

      // Look for guest authentication
      const guestLoginButton = page.locator('button:has-text("Giriş"), button:has-text("Login"), .guest-login')
      const hasGuestLogin = await guestLoginButton.count() > 0

      if (hasGuestLogin) {
        await guestLoginButton.first().click()
        await page.waitForTimeout(2000)

        const hasGuestForm = await helpers.hasAuthForm()
        console.log(`Guest authentication form: ${hasGuestForm}`)
      }

      // Check for guest services
      const serviceLinks = [
        'a[href*="services"], button:has-text("Services")',
        'a[href*="activities"], button:has-text("Activities")',
        'a[href*="reservations"], button:has-text("Reservations")'
      ]

      for (const linkSelector of serviceLinks) {
        const link = page.locator(linkSelector)
        const hasLink = await link.count() > 0

        if (hasLink) {
          await link.first().click()
          await page.waitForTimeout(2000)

          const hasServiceContent = await helpers.hasMainContent()
          console.log(`Service accessible: ${hasServiceContent}`)

          // Navigate back
          await page.goto('http://localhost:3001')
          await page.waitForTimeout(1000)
        }
      }
    })
  })

  test.describe('Cross-MFE Integration Flow', () => {
    test('should maintain data consistency across MFEs', async ({ context }) => {
      const managementPage = await context.newPage()
      const hotelPage = await context.newPage()

      // Navigate to different MFEs
      await managementPage.goto('http://localhost:5001/management/hotels')
      await hotelPage.goto('http://localhost:5002/dashboard')

      await managementPage.waitForTimeout(3000)
      await hotelPage.waitForTimeout(3000)

      // Check for data consistency
      const managementHasData = await managementPage.locator('table, .hotel-list, [data-testid="data"]').count() > 0
      const hotelHasData = await hotelPage.locator('.stats, .dashboard, [data-testid="data"]').count() > 0

      console.log(`Cross-MFE data: management=${managementHasData}, hotel=${hotelHasData}`)

      // Test real-time updates if possible
      if (managementHasData && hotelHasData) {
        // Simulate data change in management portal
        const refreshButton = managementPage.locator('button:has-text("Refresh"), button:has-text("Yenile")')
        const hasRefresh = await refreshButton.count() > 0

        if (hasRefresh) {
          await refreshButton.click()
          await managementPage.waitForTimeout(2000)

          // Check if hotel dashboard reflects changes
          await hotelPage.reload()
          await hotelPage.waitForTimeout(2000)

          const stillHasData = await hotelPage.locator('.stats, .dashboard, [data-testid="data"]').count() > 0
          console.log(`Data consistency after refresh: ${stillHasData}`)
        }
      }

      await managementPage.close()
      await hotelPage.close()
    })
  })
})
