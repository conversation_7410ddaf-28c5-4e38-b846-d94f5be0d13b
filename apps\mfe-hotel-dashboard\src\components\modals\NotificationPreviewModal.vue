<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block w-full max-w-4xl px-6 py-4 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-navy-800 shadow-xl rounded-2xl">
        <!-- Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Bildirim Önizlemesi</h3>
          <button 
            @click="$emit('close')"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>

        <!-- Content -->
        <div class="py-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Mock Phone UI -->
            <div class="flex justify-center">
              <div class="relative w-80 h-[600px] bg-black rounded-[3rem] p-2 shadow-2xl">
                <!-- Phone Screen -->
                <div class="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                  <!-- Status Bar -->
                  <div class="bg-gray-900 text-white text-xs px-6 py-2 flex justify-between items-center">
                    <span>9:41</span>
                    <div class="flex items-center space-x-1">
                      <div class="w-4 h-2 bg-white rounded-sm"></div>
                      <div class="w-1 h-1 bg-white rounded-full"></div>
                    </div>
                  </div>

                  <!-- Channel Tabs -->
                  <div class="bg-gray-100 border-b">
                    <div class="flex">
                      <button
                        v-for="channel in selectedChannels"
                        :key="channel.id"
                        @click="activePreviewChannel = channel.id"
                        class="flex-1 px-4 py-3 text-sm font-medium transition-colors"
                        :class="[
                          activePreviewChannel === channel.id
                            ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                            : 'text-gray-600 hover:text-gray-800'
                        ]"
                      >
                        <component :is="channel.icon" class="w-4 h-4 inline-block mr-1" />
                        {{ channel.name }}
                      </button>
                    </div>
                  </div>

                  <!-- Preview Content -->
                  <div class="flex-1 p-4 overflow-y-auto h-[480px]">
                    <!-- In-App Preview -->
                    <div v-if="activePreviewChannel === 'in-app'" class="space-y-4">
                      <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                        <div class="flex items-start">
                          <BellIcon class="w-6 h-6 text-blue-400 mr-3 mt-1" />
                          <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">{{ notification.title }}</h4>
                            <p class="text-gray-700 text-sm">{{ notification.message }}</p>
                            <p class="text-xs text-gray-500 mt-2">Şimdi</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- SMS Preview -->
                    <div v-if="activePreviewChannel === 'sms'" class="space-y-4">
                      <div class="bg-green-100 rounded-lg p-3 ml-8">
                        <p class="text-sm text-gray-800 font-medium mb-1">{{ notification.title }}</p>
                        <p class="text-sm text-gray-700">{{ notification.message }}</p>
                        <p class="text-xs text-gray-500 mt-2 text-right">Teslim edildi</p>
                      </div>
                    </div>

                    <!-- Email Preview -->
                    <div v-if="activePreviewChannel === 'email'" class="space-y-4">
                      <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="bg-gray-50 px-4 py-3 border-b">
                          <div class="text-sm">
                            <div class="font-semibold text-gray-900">{{ notification.title }}</div>
                            <div class="text-gray-600 mt-1">Hotelexia &lt;<EMAIL>&gt;</div>
                          </div>
                        </div>
                        <div class="p-4">
                          <p class="text-gray-800 leading-relaxed">{{ notification.message }}</p>
                          <div class="mt-4 pt-4 border-t border-gray-200 text-xs text-gray-500">
                            Bu e-posta Hotelexia sistemi tarafından gönderilmiştir.
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- WhatsApp Preview -->
                    <div v-if="activePreviewChannel === 'whatsapp'" class="space-y-4">
                      <div class="bg-green-500 text-white rounded-lg rounded-bl-none p-3 ml-8 relative">
                        <p class="font-medium mb-1">{{ notification.title }}</p>
                        <p class="text-sm">{{ notification.message }}</p>
                        <div class="flex items-center justify-end mt-2 space-x-1">
                          <span class="text-xs opacity-70">21:30</span>
                          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                          </svg>
                        </div>
                        <div class="absolute bottom-0 right-0 w-0 h-0 border-l-[10px] border-l-green-500 border-b-[10px] border-b-transparent"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Preview Details -->
            <div class="space-y-6">
              <!-- Notification Info -->
              <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-6">
                <h4 class="font-semibold text-navy-700 dark:text-white mb-4">Bildirim Detayları</h4>
                <div class="space-y-3">
                  <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Başlık:</span>
                    <p class="text-gray-900 dark:text-white">{{ notification.title }}</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Mesaj:</span>
                    <p class="text-gray-900 dark:text-white">{{ notification.message }}</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Hedef Kitle:</span>
                    <p class="text-gray-900 dark:text-white">{{ getTargetText() }}</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Kanallar:</span>
                    <div class="flex flex-wrap gap-2 mt-1">
                      <span 
                        v-for="channel in selectedChannels" 
                        :key="channel.id"
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                      >
                        <component :is="channel.icon" class="w-4 h-4 mr-1" />
                        {{ channel.name }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Estimated Reach -->
              <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-6">
                <h4 class="font-semibold text-navy-700 dark:text-white mb-4">Tahmini Erişim</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-brand-600 dark:text-brand-400">{{ estimatedRecipients }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Alıcı</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-brand-600 dark:text-brand-400">{{ selectedChannels.length }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Kanal</div>
                  </div>
                </div>
              </div>

              <!-- Cost Estimate -->
              <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex items-start">
                  <ExclamationTriangleIcon class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5" />
                  <div>
                    <h4 class="font-medium text-yellow-800 dark:text-yellow-200">Maliyet Tahmini</h4>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                      SMS: ~₺{{ (estimatedRecipients * 0.15).toFixed(2) }} | 
                      WhatsApp: ~₺{{ (estimatedRecipients * 0.08).toFixed(2) }} | 
                      Email: Ücretsiz
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="$emit('close')"
            class="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
          >
            İptal Et
          </button>
          <button
            @click="$emit('confirm')"
            class="px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 focus:ring-2 focus:ring-brand-500 transition-colors"
          >
            Onayla ve Gönderime Hazırla
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  XMarkIcon, 
  BellIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

interface Notification {
  targetType: string
  targetValue: string
  channels: string[]
  title: string
  message: string
}

interface Props {
  notification: Notification
}

const props = defineProps<Props>()
defineEmits<{
  close: []
  confirm: []
}>()

const activePreviewChannel = ref('in-app')

const channelMap = {
  'in-app': {
    id: 'in-app',
    name: 'Uygulama',
    icon: BellIcon
  },
  'sms': {
    id: 'sms',
    name: 'SMS',
    icon: DevicePhoneMobileIcon
  },
  'email': {
    id: 'email',
    name: 'E-posta',
    icon: EnvelopeIcon
  },
  'whatsapp': {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: ChatBubbleLeftRightIcon
  }
}

const selectedChannels = computed(() => {
  return props.notification.channels.map(channelId => channelMap[channelId]).filter(Boolean)
})

const getTargetText = () => {
  const { targetType, targetValue } = props.notification
  
  const targetTexts = {
    all: 'Tüm Misafirler',
    specific: `Seçilen Misafir: ${targetValue}`,
    service: `Hizmet: ${targetValue}`,
    activity: `Aktivite: ${targetValue}`,
    room: `Oda Tipi: ${targetValue}`
  }
  
  return targetTexts[targetType] || 'Bilinmiyor'
}

const estimatedRecipients = computed(() => {
  const { targetType } = props.notification
  
  if (targetType === 'all') return 45
  if (targetType === 'specific') return 1
  return Math.floor(Math.random() * 15) + 3
})

// Set initial preview channel to first selected channel
if (props.notification.channels.length > 0) {
  activePreviewChannel.value = props.notification.channels[0]
}
</script> 