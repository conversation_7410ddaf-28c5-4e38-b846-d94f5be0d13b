<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Bakım - Performans <PERSON>lizi</h1>
        <p class="text-gray-600 dark:text-gray-400">Bakım departmanı performans raporları ve analitikleri</p>
      </div>
      <div class="flex items-center space-x-4">
        <select 
          v-model="selectedPeriod"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="week">Bu Hafta</option>
          <option value="month">Bu Ay</option>
          <option value="quarter">Bu Çeyrek</option>
          <option value="year">Bu Yıl</option>
        </select>
        <select 
          v-model="selectedDepartment"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="all">Tüm Departmanlar</option>
          <option value="maintenance">Bakım</option>
          <option value="electrical">Elektrik</option>
          <option value="plumbing">Tesisat</option>
          <option value="hvac">Klima</option>
        </select>
        <button
          @click="exportReport"
          class="px-4 py-2 bg-brand-500 hover:bg-brand-600 text-white rounded-lg font-medium transition-colors"
        >
          Rapor İndir
        </button>
      </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="kpi in kpiCards"
        :key="kpi.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ kpi.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ kpi.value }}</p>
            <p v-if="kpi.change" :class="[
              'text-sm mt-1 flex items-center',
              kpi.change.type === 'positive' ? 'text-green-500' : 'text-red-500'
            ]">
              <component 
                :is="kpi.change.type === 'positive' ? 'ArrowUpIcon' : 'ArrowDownIcon'" 
                class="w-4 h-4 mr-1"
              />
              {{ kpi.change.value }}
            </p>
          </div>
          <div :class="[
            'w-12 h-12 rounded-xl flex items-center justify-center',
            kpi.iconBg
          ]">
            <component :is="kpi.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Daily Performance Trend -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Günlük Performans Trendi</h3>
          <div class="flex items-center space-x-2">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Tamamlanan</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Bekleyen</span>
            </div>
          </div>
        </div>
        <div class="h-64 flex items-end justify-between space-x-2">
          <div
            v-for="(day, index) in performanceData"
            :key="index"
            class="flex flex-col items-center space-y-2 flex-1"
          >
            <div class="flex flex-col space-y-1 w-full">
              <div 
                class="bg-blue-500 rounded-t"
                :style="{ height: `${(day.completed / day.total) * 200}px` }"
              ></div>
              <div 
                class="bg-orange-500 rounded-b"
                :style="{ height: `${(day.pending / day.total) * 200}px` }"
              ></div>
            </div>
            <span class="text-xs text-gray-600 dark:text-gray-400">{{ day.day }}</span>
          </div>
        </div>
      </div>

      <!-- Task Distribution -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Görev Dağılımı</h3>
          <span class="text-sm text-gray-600 dark:text-gray-400">Bu Ay</span>
        </div>
        <div class="space-y-4">
          <div
            v-for="task in taskDistribution"
            :key="task.category"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-4 h-4 rounded-full',
                task.color
              ]"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ task.category }}</span>
            </div>
            <div class="flex items-center space-x-4">
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ task.count }}</span>
              <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div 
                  :class="task.color"
                  class="h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${task.percentage}%` }"
                ></div>
              </div>
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ task.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Tables Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Top Performers -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">En İyi Performans</h3>
          <button class="text-brand-500 hover:text-brand-600 text-sm font-medium">
            Tümünü Gör
          </button>
        </div>
        <div class="space-y-4">
          <div
            v-for="(performer, index) in topPerformers"
            :key="performer.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-navy-800 rounded-lg"
          >
            <div class="flex items-center space-x-4">
              <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold text-sm">
                {{ index + 1 }}
              </div>
              <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                {{ performer.name.charAt(0) }}
              </div>
              <div>
                <p class="text-sm font-medium text-navy-700 dark:text-white">{{ performer.name }}</p>
                <p class="text-xs text-gray-600 dark:text-gray-400">{{ performer.specialization }}</p>
              </div>
            </div>
            <div class="text-right">
              <div class="flex items-center space-x-2">
                <div class="flex items-center">
                  <StarIcon class="w-4 h-4 text-yellow-400" />
                  <span class="text-sm font-medium text-navy-700 dark:text-white ml-1">{{ performer.rating }}</span>
                </div>
              </div>
              <p class="text-xs text-gray-600 dark:text-gray-400">{{ performer.completedTasks }} görev</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quality Scores -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Kalite Skorları</h3>
          <span class="text-sm text-gray-600 dark:text-gray-400">Ortalama: 4.2/5.0</span>
        </div>
        <div class="space-y-4">
          <div
            v-for="score in qualityScores"
            :key="score.category"
            class="space-y-2"
          >
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ score.category }}</span>
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ score.score }}/5.0</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
              <div 
                :class="[
                  'h-2 rounded-full transition-all duration-300',
                  score.score >= 4 ? 'bg-green-500' : score.score >= 3 ? 'bg-yellow-500' : 'bg-red-500'
                ]"
                :style="{ width: `${(score.score / 5) * 100}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Performance Table -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Detaylı Performans Tablosu</h3>
          <div class="flex items-center space-x-4">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Personel ara..."
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
            />
            <select 
              v-model="sortBy"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm"
            >
              <option value="name">İsme Göre</option>
              <option value="performance">Performansa Göre</option>
              <option value="tasks">Görev Sayısına Göre</option>
              <option value="rating">Değerlendirmeye Göre</option>
            </select>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Personel</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Uzmanlık</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tamamlanan</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ortalama Süre</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Değerlendirme</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Durum</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">İşlemler</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
            <tr
              v-for="staff in filteredStaff"
              :key="staff.id"
              class="hover:bg-gray-50 dark:hover:bg-navy-800"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                    {{ staff.name.charAt(0) }}
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-navy-700 dark:text-white">{{ staff.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ staff.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                  {{ staff.specialization }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                {{ staff.completedTasks }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-navy-700 dark:text-white">
                2.5h
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <StarIcon class="w-4 h-4 text-yellow-400" />
                  <span class="ml-1 text-sm text-navy-700 dark:text-white">{{ staff.rating }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'px-2 py-1 text-xs font-medium rounded-full',
                  staff.status === 'AVAILABLE' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
                  staff.status === 'BUSY' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
                ]">
                  {{ staff.status === 'AVAILABLE' ? 'Müsait' : staff.status === 'BUSY' ? 'Meşgul' : 'İzinli' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300">
                  Detay
                </button>
                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                  Görev Ver
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import { 
  ClockIcon,
  CheckCircleIcon,
  UserGroupIcon,
  ChartBarIcon,
  StarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/vue/24/outline'

const managementStore = useManagementStore()

// Reactive data
const selectedPeriod = ref('month')
const selectedDepartment = ref('all')
const searchQuery = ref('')
const sortBy = ref('name')

// Computed properties
const kpiCards = computed(() => [
  {
    title: 'Ortalama Görev Süresi',
    value: '2.5h',
    change: { type: 'positive', value: '15%' },
    icon: ClockIcon,
    iconBg: 'bg-blue-500'
  },
  {
    title: 'Tamamlanan Görevler',
    value: '156',
    change: { type: 'positive', value: '23%' },
    icon: CheckCircleIcon,
    iconBg: 'bg-green-500'
  },
  {
    title: 'Personel Verimliliği',
    value: '87%',
    change: { type: 'positive', value: '8%' },
    icon: UserGroupIcon,
    iconBg: 'bg-purple-500'
  },
  {
    title: 'Kalite Skoru',
    value: '4.2',
    change: { type: 'negative', value: '2%' },
    icon: ChartBarIcon,
    iconBg: 'bg-orange-500'
  }
])

const performanceData = computed(() => [
  { day: 'Pzt', completed: 12, pending: 3, total: 15 },
  { day: 'Sal', completed: 15, pending: 2, total: 17 },
  { day: 'Çar', completed: 18, pending: 4, total: 22 },
  { day: 'Per', completed: 14, pending: 1, total: 15 },
  { day: 'Cum', completed: 20, pending: 3, total: 23 },
  { day: 'Cmt', completed: 8, pending: 2, total: 10 },
  { day: 'Paz', completed: 6, pending: 1, total: 7 }
])

const taskDistribution = computed(() => [
  { category: 'Elektrik Bakımı', count: 45, percentage: 35, color: 'bg-yellow-500' },
  { category: 'Tesisat', count: 32, percentage: 25, color: 'bg-blue-500' },
  { category: 'Klima Sistemleri', count: 28, percentage: 22, color: 'bg-green-500' },
  { category: 'Genel Bakım', count: 23, percentage: 18, color: 'bg-purple-500' }
])

const topPerformers = computed(() => 
  managementStore.maintenanceStaff
    .filter(staff => staff.status === 'AVAILABLE' || staff.status === 'BUSY')
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5)
)

const qualityScores = computed(() => [
  { category: 'İş Kalitesi', score: 4.3 },
  { category: 'Hız', score: 4.1 },
  { category: 'Müşteri Memnuniyeti', score: 4.5 },
  { category: 'Teknik Yeterlilik', score: 3.9 },
  { category: 'İletişim', score: 4.2 }
])

const filteredStaff = computed(() => {
  let staff = managementStore.maintenanceStaff

  // Search filter
  if (searchQuery.value) {
    staff = staff.filter(s => 
      s.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      s.specialization.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // Sort
  switch (sortBy.value) {
    case 'name':
      staff = staff.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'performance':
      staff = staff.sort((a, b) => b.rating - a.rating)
      break
    case 'tasks':
      staff = staff.sort((a, b) => b.completedTasks - a.completedTasks)
      break
    case 'rating':
      staff = staff.sort((a, b) => b.rating - a.rating)
      break
  }

  return staff
})

// Methods
const exportReport = () => {
  // Simulate report export
  alert('Rapor indiriliyor...')
}
</script> 