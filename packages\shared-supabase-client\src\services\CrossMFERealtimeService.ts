import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import { supabase } from '../index'

// Event types for cross-MFE communication
export interface CrossMFEEvent {
  type: string
  payload: any
  source: string
  timestamp: number
  userId?: string
  hotelId?: string
}

export interface HotelDataChangeEvent extends CrossMFEEvent {
  type: 'hotel_data_change'
  payload: {
    hotelId: string
    changeType: 'update' | 'delete' | 'create'
    data: any
  }
}

export interface UserProfileChangeEvent extends CrossMFEEvent {
  type: 'user_profile_change'
  payload: {
    userId: string
    changeType: 'update' | 'role_change' | 'delete'
    data: any
  }
}

export interface TaskStatusChangeEvent extends CrossMFEEvent {
  type: 'task_status_change'
  payload: {
    taskId: string
    hotelId: string
    oldStatus: string
    newStatus: string
    data: any
  }
}

export interface ServiceRequestChangeEvent extends CrossMFEEvent {
  type: 'service_request_change'
  payload: {
    requestId: string
    hotelId: string
    changeType: 'create' | 'update' | 'complete' | 'cancel'
    data: any
  }
}

export interface NotificationChangeEvent extends CrossMFEEvent {
  type: 'notification_change'
  payload: {
    notificationId: string
    userId?: string
    hotelId?: string
    changeType: 'create' | 'read' | 'delete'
    data: any
  }
}

export interface RoomStatusChangeEvent extends CrossMFEEvent {
  type: 'room_status_change'
  payload: {
    roomId: string
    hotelId: string
    oldStatus: string
    newStatus: string
    data: any
  }
}

export type CrossMFEEventType = 
  | HotelDataChangeEvent
  | UserProfileChangeEvent
  | TaskStatusChangeEvent
  | ServiceRequestChangeEvent
  | NotificationChangeEvent
  | RoomStatusChangeEvent

// Event listener type
export type CrossMFEEventListener = (event: CrossMFEEventType) => void

// Subscription configuration
export interface SubscriptionConfig {
  table: string
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
  filter?: string
  schema?: string
}

class CrossMFERealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map()
  private eventListeners: Map<string, Set<CrossMFEEventListener>> = new Map()
  private isInitialized = false
  private currentUserId: string | null = null
  private currentHotelId: string | null = null

  /**
   * Initialize the service with user context
   */
  initialize(userId: string, hotelId?: string) {
    this.currentUserId = userId
    this.currentHotelId = hotelId || null
    this.isInitialized = true
    
    console.log('CrossMFERealtimeService initialized', { userId, hotelId })
  }

  /**
   * Subscribe to a specific event type
   */
  subscribe(eventType: string, listener: CrossMFEEventListener): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set())
    }

    this.eventListeners.get(eventType)!.add(listener)

    console.log(`Subscribed to ${eventType}`)

    // Return unsubscribe function
    return () => {
      this.unsubscribe(eventType, listener)
    }
  }

  /**
   * Unsubscribe from a specific event type
   */
  unsubscribe(eventType: string, listener: CrossMFEEventListener) {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      listeners.delete(listener)
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType)
      }
    }
  }

  /**
   * Emit an event to all listeners
   */
  private emit(event: CrossMFEEventType) {
    const listeners = this.eventListeners.get(event.type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error(`Error in event listener for ${event.type}:`, error)
        }
      })
    }
  }

  /**
   * Start realtime subscriptions for critical data
   */
  startRealtimeSubscriptions() {
    if (!this.isInitialized) {
      console.warn('CrossMFERealtimeService not initialized')
      return
    }

    // Subscribe to hotel data changes
    this.subscribeToTable('hotels', '*', (payload) => {
      this.emit({
        type: 'hotel_data_change',
        payload: {
          hotelId: (payload.new as any)?.id || (payload.old as any)?.id,
          changeType: payload.eventType === 'INSERT' ? 'create' :
                     payload.eventType === 'UPDATE' ? 'update' : 'delete',
          data: payload.new || payload.old
        },
        source: 'supabase_realtime',
        timestamp: Date.now(),
        userId: this.currentUserId || '',
        hotelId: this.currentHotelId || ''
      })
    })

    // Subscribe to user profile changes
    this.subscribeToTable('user_profiles', '*', (payload) => {
      this.emit({
        type: 'user_profile_change',
        payload: {
          userId: (payload.new as any)?.id || (payload.old as any)?.id,
          changeType: payload.eventType === 'UPDATE' ? 'update' : 'delete',
          data: payload.new || payload.old
        },
        source: 'supabase_realtime',
        timestamp: Date.now(),
        userId: this.currentUserId || '',
        hotelId: this.currentHotelId || ''
      })
    })

    // Subscribe to task status changes
    this.subscribeToTable('tasks', 'UPDATE', (payload) => {
      if ((payload.old as any)?.status !== (payload.new as any)?.status) {
        this.emit({
          type: 'task_status_change',
          payload: {
            taskId: (payload.new as any).id,
            hotelId: (payload.new as any).hotel_id,
            oldStatus: (payload.old as any).status,
            newStatus: (payload.new as any).status,
            data: payload.new
          },
          source: 'supabase_realtime',
          timestamp: Date.now(),
          userId: this.currentUserId || '',
          hotelId: this.currentHotelId || ''
        })
      }
    })

    // Subscribe to room service orders (service requests)
    this.subscribeToTable('room_service_orders', '*', (payload) => {
      this.emit({
        type: 'service_request_change',
        payload: {
          requestId: (payload.new as any)?.id || (payload.old as any)?.id,
          hotelId: (payload.new as any)?.hotel_id || (payload.old as any)?.hotel_id,
          changeType: payload.eventType === 'INSERT' ? 'create' :
                     payload.eventType === 'UPDATE' ? 'update' : 'cancel',
          data: payload.new || payload.old
        },
        source: 'supabase_realtime',
        timestamp: Date.now(),
        userId: this.currentUserId || '',
        hotelId: this.currentHotelId || ''
      })
    })

    // Subscribe to room status changes
    this.subscribeToTable('rooms', 'UPDATE', (payload) => {
      if ((payload.old as any)?.status !== (payload.new as any)?.status) {
        this.emit({
          type: 'room_status_change',
          payload: {
            roomId: (payload.new as any).id,
            hotelId: (payload.new as any).hotel_id,
            oldStatus: (payload.old as any).status,
            newStatus: (payload.new as any).status,
            data: payload.new
          },
          source: 'supabase_realtime',
          timestamp: Date.now(),
          userId: this.currentUserId || '',
          hotelId: this.currentHotelId || ''
        })
      }
    })

    console.log('Realtime subscriptions started')
  }

  /**
   * Subscribe to a specific table
   */
  private subscribeToTable(
    table: string, 
    event: 'INSERT' | 'UPDATE' | 'DELETE' | '*',
    callback: (payload: RealtimePostgresChangesPayload<any>) => void
  ) {
    const channelName = `${table}_${event}`
    
    if (this.channels.has(channelName)) {
      console.warn(`Already subscribed to ${channelName}`)
      return
    }

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes' as any,
        {
          event,
          schema: 'public',
          table
        } as any,
        callback
      )
      .subscribe((status: string) => {
        console.log(`Subscription ${channelName} status:`, status)
      })

    this.channels.set(channelName, channel)
  }

  /**
   * Stop all realtime subscriptions
   */
  stopRealtimeSubscriptions() {
    this.channels.forEach((channel, name) => {
      supabase.removeChannel(channel)
      console.log(`Unsubscribed from ${name}`)
    })
    
    this.channels.clear()
    this.eventListeners.clear()
    
    console.log('All realtime subscriptions stopped')
  }

  /**
   * Get subscription status
   */
  getSubscriptionStatus() {
    return {
      isInitialized: this.isInitialized,
      activeChannels: Array.from(this.channels.keys()),
      eventListeners: Array.from(this.eventListeners.keys()),
      currentUserId: this.currentUserId,
      currentHotelId: this.currentHotelId
    }
  }

  /**
   * Manually trigger an event (for testing or manual synchronization)
   */
  triggerEvent(event: CrossMFEEventType) {
    this.emit(event)
  }
}

// Export singleton instance
export const crossMFERealtimeService = new CrossMFERealtimeService()
export default crossMFERealtimeService
