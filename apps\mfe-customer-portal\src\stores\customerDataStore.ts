import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { CustomerPortalService } from '@hotelexia/shared-supabase-client'
import {
  useLoadingState,
  useErrorHandling,
  useCacheManagement
} from '@hotelexia/shared-components'
import { useGuestAuthStore } from './guestAuthStore'

export const useCustomerDataStore = defineStore('customerData', () => {
  const guestAuthStore = useGuestAuthStore()

  // Initialize enhanced composables
  const loadingState = useLoadingState({
    minLoadingTime: 300,
    maxLoadingTime: 20000,
    enableTimeout: true,
    timeoutMessage: 'Customer data is taking longer than expected to load...'
  })

  const errorHandling = useErrorHandling({
    component: 'CustomerPortal',
    userId: guestAuthStore.currentGuest?.id,
    hotelId: guestAuthStore.currentGuest?.hotel_id,
    maxRetries: 3,
    retryDelay: 1000,
    enableRecovery: true
  })

  const cacheManagement = useCacheManagement({
    keyPrefix: 'customer-portal',
    defaultTTL: 3 * 60 * 1000, // 3 minutes
    enableStats: true,
    maxCacheSize: 150
  })

  // State
  const reservations = ref<any[]>([])
  const orders = ref<any[]>([])
  const activities = ref<any[]>([])
  const notifications = ref<any[]>([])
  const serviceRequests = ref<any[]>([])
  const serviceCategories = ref<any[]>([])

  // Loading states
  const reservationsLoading = ref(false)
  const ordersLoading = ref(false)
  const activitiesLoading = ref(false)
  const notificationsLoading = ref(false)
  const serviceRequestsLoading = ref(false)
  const serviceCategoriesLoading = ref(false)

  // Error states
  const reservationsError = ref<string | null>(null)
  const ordersError = ref<string | null>(null)
  const activitiesError = ref<string | null>(null)
  const notificationsError = ref<string | null>(null)
  const serviceRequestsError = ref<string | null>(null)
  const serviceCategoriesError = ref<string | null>(null)

  // Computed
  const currentReservations = computed(() => 
    reservations.value.filter(r => ['confirmed', 'checked_in'].includes(r.status))
  )

  const pastReservations = computed(() => 
    reservations.value.filter(r => ['checked_out', 'completed'].includes(r.status))
  )

  const activeOrders = computed(() => 
    orders.value.filter(o => ['pending', 'confirmed', 'preparing', 'on_the_way'].includes(o.status))
  )

  const pastOrders = computed(() => 
    orders.value.filter(o => ['delivered', 'canceled'].includes(o.status))
  )

  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.isRead)
  )

  const todayActivities = computed(() => {
    const today = new Date().toDateString()
    return activities.value.filter(activity => {
      // For now, return all activities as we don't have date filtering in the current schema
      return activity.isActive
    })
  })

  // Actions
  const fetchReservations = async () => {
    if (!guestAuthStore.isAuthenticated || !guestAuthStore.currentGuest) return

    const operationId = loadingState.startLoading('Fetching reservations...')
    reservationsLoading.value = true
    reservationsError.value = null

    try {
      const cacheKey = `reservations-${guestAuthStore.currentGuest.id}`
      const response = await cacheManagement.get(
        cacheKey,
        () => CustomerPortalService.getGuestReservations(guestAuthStore.currentGuest!.id),
        { ttl: 2 * 60 * 1000 } // 2 minutes cache
      )

      if (response.error) {
        throw new Error(response.error)
      }

      reservations.value = response.data || []
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      reservationsError.value = error.message

      await errorHandling.handleError(error, {
        action: 'fetch_reservations',
        userId: guestAuthStore.currentGuest?.id
      }, {
        retryable: true,
        showToast: true
      })
    } finally {
      loadingState.stopLoading(operationId)
      reservationsLoading.value = false
    }
  }

  const fetchOrders = async () => {
    if (!guestAuthStore.isAuthenticated || !guestAuthStore.currentGuest) return

    ordersLoading.value = true
    ordersError.value = null

    try {
      const hotelId = guestAuthStore.currentGuest.hotel_id
      const response = await CustomerPortalService.getGuestOrders(guestAuthStore.currentGuest.id, hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      orders.value = response.data || []
    } catch (err) {
      ordersError.value = (err as Error).message
      console.error('Error fetching orders:', err)
    } finally {
      ordersLoading.value = false
    }
  }

  const fetchActivities = async () => {
    if (!guestAuthStore.isAuthenticated || !guestAuthStore.currentGuest) return

    activitiesLoading.value = true
    activitiesError.value = null

    try {
      const hotelId = guestAuthStore.currentGuest.hotel_id
      const response = await CustomerPortalService.getHotelActivities(hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      activities.value = response.data || []
    } catch (err) {
      activitiesError.value = (err as Error).message
      console.error('Error fetching activities:', err)
    } finally {
      activitiesLoading.value = false
    }
  }

  const fetchNotifications = async () => {
    if (!guestAuthStore.isAuthenticated || !guestAuthStore.currentGuest) return

    notificationsLoading.value = true
    notificationsError.value = null

    try {
      const hotelId = guestAuthStore.currentGuest.hotel_id
      const response = await CustomerPortalService.getGuestNotifications(guestAuthStore.currentGuest.id, hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      notifications.value = response.data || []
    } catch (err) {
      notificationsError.value = (err as Error).message
      console.error('Error fetching notifications:', err)
    } finally {
      notificationsLoading.value = false
    }
  }

  const fetchServiceRequests = async () => {
    if (!guestAuthStore.isAuthenticated || !guestAuthStore.currentGuest) return

    serviceRequestsLoading.value = true
    serviceRequestsError.value = null

    try {
      const hotelId = guestAuthStore.currentGuest.hotel_id
      const response = await CustomerPortalService.getServiceRequests(hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      serviceRequests.value = response.data || []
    } catch (err) {
      serviceRequestsError.value = (err as Error).message
      console.error('Error fetching service requests:', err)
    } finally {
      serviceRequestsLoading.value = false
    }
  }

  const fetchServiceCategories = async () => {
    if (!guestAuthStore.isAuthenticated || !guestAuthStore.currentGuest) return

    serviceCategoriesLoading.value = true
    serviceCategoriesError.value = null

    try {
      const hotelId = guestAuthStore.currentGuest.hotel_id
      const response = await CustomerPortalService.getServiceCategories(hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      serviceCategories.value = response.data || []
    } catch (err) {
      serviceCategoriesError.value = (err as Error).message
      console.error('Error fetching service categories:', err)
    } finally {
      serviceCategoriesLoading.value = false
    }
  }

  // Initialize all data
  const initializeData = async () => {
    if (!guestAuthStore.isAuthenticated) return

    await Promise.all([
      fetchReservations(),
      fetchOrders(),
      fetchActivities(),
      fetchNotifications(),
      fetchServiceRequests(),
      fetchServiceCategories()
    ])
  }

  return {
    // State
    reservations,
    orders,
    activities,
    notifications,
    serviceRequests,
    serviceCategories,

    // Loading states
    reservationsLoading,
    ordersLoading,
    activitiesLoading,
    notificationsLoading,
    serviceRequestsLoading,
    serviceCategoriesLoading,

    // Error states
    reservationsError,
    ordersError,
    activitiesError,
    notificationsError,
    serviceRequestsError,
    serviceCategoriesError,

    // Computed
    currentReservations,
    pastReservations,
    activeOrders,
    pastOrders,
    unreadNotifications,
    todayActivities,

    // Actions
    fetchReservations,
    fetchOrders,
    fetchActivities,
    fetchNotifications,
    fetchServiceRequests,
    fetchServiceCategories,
    initializeData
  }
})
