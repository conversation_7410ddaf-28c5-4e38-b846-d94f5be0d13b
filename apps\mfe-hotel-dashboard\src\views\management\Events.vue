<template>
  <div>
    <!-- Header with Add Button -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white">
          Hotel Events Management
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Manage hotel events and activities for the customer portal
        </p>
      </div>
      <button
        @click="showModal = true"
        class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
      >
        <PlusIcon class="w-5 h-5 mr-2" />
        Create New Event
      </button>
    </div>

    <!-- Events Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="event in store.events"
        :key="event.id"
        class="bg-white dark:bg-navy-700 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
      >
        <!-- Event Image -->
        <div class="relative">
          <img
            :src="event.image"
            :alt="event.title"
            class="w-full h-48 object-cover"
          >
          <div class="absolute top-4 right-4">
            <span
              v-if="event.isActive"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Active
            </span>
            <span
              v-else
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
            >
              Inactive
            </span>
          </div>
        </div>

        <!-- Event Content -->
        <div class="p-6">
          <div class="flex items-start justify-between mb-3">
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white line-clamp-2">
              {{ event.title }}
            </h3>
          </div>

          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
            {{ event.description }}
          </p>

          <!-- Event Details -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ formatDate(event.date) }} at {{ event.time }}
            </div>
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {{ event.location }}
            </div>
            <div v-if="event.maxAttendees" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {{ event.currentAttendees || 0 }} / {{ event.maxAttendees }} attendees
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              @click="toggleEventStatus(event.id)"
              class="text-sm font-medium transition-colors"
              :class="event.isActive 
                ? 'text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300' 
                : 'text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300'"
            >
              {{ event.isActive ? 'Deactivate' : 'Activate' }}
            </button>
            <div class="flex space-x-2">
              <button
                @click="editEvent(event)"
                class="text-sm font-medium text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300"
              >
                Edit
              </button>
              <button
                @click="deleteEvent(event.id)"
                class="text-sm font-medium text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="store.events.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No events</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new event.</p>
    </div>

    <!-- Event Modal -->
    <EventModal
      v-if="showModal"
      :event="editingEvent"
      @close="closeModal"
      @save="saveEvent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PlusIcon } from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import EventModal from '@/components/modals/EventModal.vue'
import type { HotelEvent, CreateEventData } from '@/types/management'

const store = useManagementStore()
const showModal = ref(false)
const editingEvent = ref<HotelEvent | null>(null)

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const editEvent = (event: HotelEvent) => {
  editingEvent.value = event
  showModal.value = true
}

const deleteEvent = (id: string) => {
  if (confirm('Bu etkinliği silmek istediğinizden emin misiniz?')) {
    store.deleteEvent(id)
  }
}

const toggleEventStatus = (id: string) => {
  store.toggleEventStatus(id)
}

const closeModal = () => {
  showModal.value = false
  editingEvent.value = null
}

const saveEvent = (eventData: CreateEventData) => {
  if (editingEvent.value) {
    // Update existing event
    store.updateEvent(editingEvent.value.id, eventData)
  } else {
    // Create new event
    store.addEvent(eventData)
  }
  closeModal()
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 