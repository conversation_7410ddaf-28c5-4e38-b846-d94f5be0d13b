<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 pt-6 pb-8">
      <div class="max-w-md mx-auto">
        <div class="flex items-center mb-4">
          <button
            @click="$router.back()"
            class="mr-3 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
          >
            <span class="text-xl">←</span>
          </button>
          <h1 class="text-2xl font-bold">Servis <PERSON>im</h1>
        </div>
        
        <!-- Request Summary Cards -->
        <div class="grid grid-cols-4 gap-2">
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
            <p class="text-xl font-bold">{{ serviceRequestStore.statusCounts.pending }}</p>
            <p class="text-white/80 text-xs"><PERSON><PERSON><PERSON></p>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
            <p class="text-xl font-bold">{{ serviceRequestStore.statusCounts.in_progress }}</p>
            <p class="text-white/80 text-xs">İşlemde</p>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
            <p class="text-xl font-bold">{{ serviceRequestStore.statusCounts.completed }}</p>
            <p class="text-white/80 text-xs">Tamamlanan</p>
          </div>
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
            <p class="text-xl font-bold">{{ serviceRequestStore.statusCounts.total }}</p>
            <p class="text-white/80 text-xs">Toplam</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filter Tabs -->
    <div class="max-w-md mx-auto px-4 -mt-6 mb-6">
      <div class="bg-white rounded-xl shadow-lg p-2">
        <div class="flex space-x-1">
          <button
            v-for="filter in filters"
            :key="filter.key"
            @click="activeFilter = filter.key"
            :class="[
              'flex-1 py-2 px-2 rounded-lg text-xs font-medium transition-colors',
              activeFilter === filter.key
                ? 'bg-blue-500 text-white'
                : 'text-gray-600 hover:bg-gray-100'
            ]"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="max-w-md mx-auto px-4 mb-6">
      <div class="bg-white rounded-xl shadow-lg p-4">
        <h2 class="text-lg font-semibold text-navy-700 mb-3">Hızlı Talep</h2>
        <div class="grid grid-cols-2 gap-3">
          <button
            @click="createQuickRequest('Housekeeping')"
            class="bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-lg transition-colors text-sm"
          >
            <span class="text-xl block mb-1">🧹</span>
            Temizlik
          </button>
          <button
            @click="createQuickRequest('Maintenance')"
            class="bg-red-500 hover:bg-red-600 text-white p-3 rounded-lg transition-colors text-sm"
          >
            <span class="text-xl block mb-1">🔧</span>
            Bakım
          </button>
          <button
            @click="createQuickRequest('Amenities')"
            class="bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg transition-colors text-sm"
          >
            <span class="text-xl block mb-1">🏊‍♂️</span>
            Olanaklar
          </button>
          <button
            @click="navigateToServiceRequest"
            class="bg-purple-500 hover:bg-purple-600 text-white p-3 rounded-lg transition-colors text-sm"
          >
            <span class="text-xl block mb-1">📋</span>
            Diğer
          </button>
        </div>
      </div>
    </div>

    <!-- Requests List -->
    <div class="max-w-md mx-auto px-4 pb-20">
      <!-- Loading State -->
      <div v-if="serviceRequestStore.isLoading" class="text-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p class="text-gray-600 mt-4">Talepler yükleniyor...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="serviceRequestStore.error" class="bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
        <div class="flex items-center">
          <span class="text-2xl mr-3">❌</span>
          <div>
            <h3 class="font-semibold text-red-800">Hata Oluştu</h3>
            <p class="text-red-700 text-sm">{{ serviceRequestStore.error }}</p>
          </div>
        </div>
        <button
          @click="refreshRequests"
          class="mt-3 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg text-sm"
        >
          Tekrar Dene
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredRequests.length === 0" class="text-center py-8">
        <span class="text-6xl mb-4 block">🛎️</span>
        <h3 class="text-lg font-semibold text-gray-700 mb-2">
          {{ getEmptyStateTitle() }}
        </h3>
        <p class="text-gray-600 mb-4">
          {{ getEmptyStateMessage() }}
        </p>
        <button
          @click="navigateToServiceRequest"
          class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded-lg font-medium"
        >
          Yeni Talep Oluştur
        </button>
      </div>

      <!-- Requests List -->
      <div v-else class="space-y-4">
        <RequestItem
          v-for="request in filteredRequests"
          :key="request.id"
          :request="request"
          :is-loading="cancellingRequestId === request.id"
          @cancel="handleCancelRequest"
          @create-similar="handleCreateSimilar"
          @track="handleTrackRequest"
        />
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div
      v-if="successMessage"
      class="fixed top-4 left-4 right-4 bg-green-500 text-white p-3 rounded-lg shadow-lg z-50"
    >
      {{ successMessage }}
    </div>

    <div
      v-if="errorMessage"
      class="fixed top-4 left-4 right-4 bg-red-500 text-white p-3 rounded-lg shadow-lg z-50"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useServiceRequestStore } from '../stores/serviceRequestStore'
import { useGuestAuthStore } from '../stores/guestAuthStore'
import RequestItem from '../components/RequestItem.vue'
import type { ServiceRequest, ServiceRequestType } from '@hotelexia/shared-supabase-client'

const router = useRouter()
const serviceRequestStore = useServiceRequestStore()
const guestAuthStore = useGuestAuthStore()

// State
const activeFilter = ref('all')
const cancellingRequestId = ref<string | null>(null)
const successMessage = ref('')
const errorMessage = ref('')

// Filters
const filters = [
  { key: 'all', label: 'Tümü' },
  { key: 'pending', label: 'Bekleyen' },
  { key: 'in_progress', label: 'İşlemde' },
  { key: 'completed', label: 'Tamamlanan' },
  { key: 'canceled', label: 'İptal' }
]

// Computed
const filteredRequests = computed(() => {
  if (activeFilter.value === 'all') {
    return serviceRequestStore.requests
  }
  return serviceRequestStore.requests.filter(request => request.status === activeFilter.value)
})

// Methods
const refreshRequests = async () => {
  try {
    if (guestAuthStore.currentGuest?.id) {
      await serviceRequestStore.fetchRequests(guestAuthStore.currentGuest.id)
    }
  } catch (error) {
    console.error('Error refreshing requests:', error)
  }
}

const createQuickRequest = async (type: ServiceRequestType) => {
  try {
    if (!guestAuthStore.currentGuest) return
    
    const description = `${type === 'Housekeeping' ? 'Oda temizliği' : 
                        type === 'Maintenance' ? 'Bakım talebi' : 
                        type === 'Amenities' ? 'Olanaklar talebi' : 'Genel talep'} - Hızlı talep`
    
    await serviceRequestStore.createRequest({
      hotel_id: guestAuthStore.currentGuest.hotel_id,
      reservation_id: guestAuthStore.currentGuest.id,
      room_number: guestAuthStore.currentGuest.room_number,
      request_type: type,
      description
    })
    
    showSuccessMessage('Talebiniz başarıyla oluşturuldu!')
    await refreshRequests()
  } catch (error) {
    console.error('Error creating quick request:', error)
    showErrorMessage('Talep oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}

const navigateToServiceRequest = () => {
  router.push('/service-request')
}

const handleCancelRequest = async (requestId: string) => {
  const confirmed = confirm('Bu talebi iptal etmek istediğinizden emin misiniz?')
  if (!confirmed) return

  cancellingRequestId.value = requestId
  try {
    await serviceRequestStore.cancelRequest(requestId)
    showSuccessMessage('Talep başarıyla iptal edildi!')
  } catch (error) {
    console.error('Error cancelling request:', error)
    showErrorMessage('Talep iptal edilirken bir hata oluştu. Lütfen tekrar deneyin.')
  } finally {
    cancellingRequestId.value = null
  }
}

const handleCreateSimilar = (request: ServiceRequest) => {
  router.push({
    path: '/service-request',
    query: {
      type: request.request_type,
      description: request.description
    }
  })
}

const handleTrackRequest = (requestId: string) => {
  const request = serviceRequestStore.requests.find(r => r.id === requestId)
  if (request) {
    showSuccessMessage(`Talep #${request.id.slice(-6)} durumu: ${getStatusText(request.status)}`)
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': 'Bekliyor',
    'in_progress': 'İşleniyor',
    'completed': 'Tamamlandı',
    'canceled': 'İptal Edildi'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getEmptyStateTitle = () => {
  if (activeFilter.value === 'pending') return 'Bekleyen talep yok'
  if (activeFilter.value === 'in_progress') return 'İşlemdeki talep yok'
  if (activeFilter.value === 'completed') return 'Tamamlanan talep yok'
  if (activeFilter.value === 'canceled') return 'İptal edilen talep yok'
  return 'Henüz hiç talebiniz yok'
}

const getEmptyStateMessage = () => {
  if (activeFilter.value === 'pending') return 'Şu anda bekleyen bir talebiniz bulunmuyor.'
  if (activeFilter.value === 'in_progress') return 'Şu anda işlemde olan bir talebiniz bulunmuyor.'
  if (activeFilter.value === 'completed') return 'Henüz tamamlanmış bir talebiniz bulunmuyor.'
  if (activeFilter.value === 'canceled') return 'İptal edilen bir talebiniz bulunmuyor.'
  return 'İlk servis talebinizi oluşturmak için butona tıklayın.'
}

const showSuccessMessage = (message: string) => {
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const showErrorMessage = (message: string) => {
  errorMessage.value = message
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

// Lifecycle
onMounted(async () => {
  if (guestAuthStore.isAuthenticated) {
    await refreshRequests()
  }
})

// Auto-dismiss messages
watch([successMessage, errorMessage], () => {
  if (successMessage.value) {
    setTimeout(() => successMessage.value = '', 3000)
  }
  if (errorMessage.value) {
    setTimeout(() => errorMessage.value = '', 5000)
  }
})
</script> 