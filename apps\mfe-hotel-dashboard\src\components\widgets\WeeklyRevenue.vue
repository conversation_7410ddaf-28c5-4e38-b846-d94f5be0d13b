<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h4 class="text-lg font-bold text-navy-700 dark:text-white"><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">Son 7 Gün</p>
      </div>
      <div class="text-right">
        <h3 class="text-2xl font-bold text-navy-700 dark:text-white">₺18,425</h3>
        <p class="text-sm text-green-500 font-medium">+5.2%</p>
      </div>
    </div>

    <!-- Chart Placeholder -->
    <div class="h-64 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
      <div class="text-white text-center">
        <svg class="w-12 h-12 mx-auto mb-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21h18" />
        </svg>
        <p class="text-sm opacity-80">Line Chart</p>
        <p class="text-xs opacity-60">Chart.js entegrasyonu</p>
      </div>
    </div>

    <!-- Chart Legend -->
    <div class="flex items-center justify-center mt-4 space-x-6">
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
        <span class="text-sm text-gray-600 dark:text-gray-400">Gelir</span>
      </div>
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
        <span class="text-sm text-gray-600 dark:text-gray-400">Hedef</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// TODO: Chart.js integration will be added here
// import { Line } from 'vue-chartjs'
// import { Chart as ChartJS, Title, Tooltip, Legend, LineElement, CategoryScale, LinearScale, PointElement } from 'chart.js'
</script>

<style scoped>
/* Chart specific styles */
</style> 