<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900 p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        Performans Analizi
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Temizlik departmanı performans metrikleri ve analitikleri
      </p>
    </div>

    <!-- Performance KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <KpiCard
        v-for="kpi in performanceKpis"
        :key="kpi.title"
        :title="kpi.title"
        :value="kpi.value"
        :subtitle="kpi.subtitle"
        :icon="kpi.icon"
        :trend="kpi.trend.value"
        :color="kpi.color as 'blue' | 'green' | 'yellow' | 'purple' | 'red' | 'indigo' | 'gray'"
        :format="kpi.format as 'number' | 'percentage' | 'currency' | 'time' | 'text'"
      />
    </div>

    <!-- Charts and Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Daily Performance Chart -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
            Günlük Performans Trendi
          </h3>
          <div class="flex items-center space-x-2">
            <button
              v-for="period in chartPeriods"
              :key="period.value"
              @click="selectedPeriod = period.value"
              :class="[
                'text-sm px-3 py-1 rounded-lg transition-colors',
                selectedPeriod === period.value
                  ? 'bg-brand-500 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-navy-700'
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-navy-700 rounded-lg">
          <div class="text-center">
            <div class="text-4xl mb-2">📊</div>
            <p class="text-gray-600 dark:text-gray-400">
              Performans grafiği burada görüntülenecek
            </p>
          </div>
        </div>
      </div>

      <!-- Task Completion Rate -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">
          Görev Tamamlama Oranları
        </h3>
        <div class="space-y-4">
          <div
            v-for="category in taskCategories"
            :key="category.name"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div class="text-2xl">{{ category.icon }}</div>
              <div>
                <div class="font-medium text-navy-700 dark:text-white">
                  {{ category.name }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ category.completed }}/{{ category.total }} görev
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-semibold text-navy-700 dark:text-white">
                {{ Math.round((category.completed / category.total) * 100) }}%
              </div>
              <div class="w-20 bg-gray-200 dark:bg-navy-700 rounded-full h-2">
                <div
                  class="bg-brand-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${(category.completed / category.total) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Top Performers -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
            En İyi Performans
          </h3>
          <div class="text-2xl">🏆</div>
        </div>
        <div class="space-y-4">
          <div
            v-for="(performer, index) in topPerformers"
            :key="performer.id"
            class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-navy-700"
          >
            <div class="flex items-center space-x-3">
              <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-brand-400 to-brand-600 text-white font-bold text-sm">
                {{ index + 1 }}
              </div>
              <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-medium">
                {{ performer.name.charAt(0) }}
              </div>
              <div>
                <div class="font-medium text-navy-700 dark:text-white">
                  {{ performer.name }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  Kat Personeli
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-semibold text-green-600">
                {{ performer.score }}%
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ performer.tasksCompleted }} görev
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quality Metrics -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
            Kalite Metrikleri
          </h3>
          <div class="text-2xl">⭐</div>
        </div>
        <div class="space-y-4">
          <div
            v-for="metric in qualityMetrics"
            :key="metric.name"
            class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-navy-700"
          >
            <div class="flex items-center space-x-3">
              <div class="text-2xl">{{ metric.icon }}</div>
              <div>
                <div class="font-medium text-navy-700 dark:text-white">
                  {{ metric.name }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ metric.description }}
                </div>
              </div>
            </div>
            <div class="text-right">
              <div
                :class="[
                  'text-lg font-semibold',
                  metric.score >= 90 ? 'text-green-600' :
                  metric.score >= 80 ? 'text-yellow-600' : 'text-red-600'
                ]"
              >
                {{ metric.score }}%
              </div>
              <div class="flex items-center space-x-1">
                <div
                  v-for="star in 5"
                  :key="star"
                  :class="[
                    'w-3 h-3',
                    star <= Math.round(metric.score / 20) ? 'text-yellow-500' : 'text-gray-300'
                  ]"
                >
                  ⭐
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Response Time Analysis -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">
          Yanıt Süresi Analizi
        </h3>
        <div class="space-y-4">
          <div
            v-for="responseTime in responseTimeAnalysis"
            :key="responseTime.category"
            class="flex items-center justify-between"
          >
            <div>
              <div class="font-medium text-navy-700 dark:text-white">
                {{ responseTime.category }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                Ortalama süre
              </div>
            </div>
            <div class="text-right">
              <div class="text-lg font-semibold text-navy-700 dark:text-white">
                {{ responseTime.averageTime }}
              </div>
              <div
                :class="[
                  'text-sm',
                  responseTime.trend === 'up' ? 'text-green-600' :
                  responseTime.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                ]"
              >
                {{ responseTime.trendText }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Efficiency Metrics -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">
          Verimlilik Metrikleri
        </h3>
        <div class="space-y-4">
          <div
            v-for="efficiency in efficiencyMetrics"
            :key="efficiency.name"
            class="text-center p-4 rounded-lg bg-gray-50 dark:bg-navy-700"
          >
            <div class="text-3xl mb-2">{{ efficiency.icon }}</div>
            <div class="text-2xl font-bold text-navy-700 dark:text-white mb-1">
              {{ efficiency.value }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ efficiency.name }}
            </div>
            <div
              :class="[
                'text-xs mt-1',
                efficiency.change > 0 ? 'text-green-600' :
                efficiency.change < 0 ? 'text-red-600' : 'text-gray-600'
              ]"
            >
              {{ efficiency.change > 0 ? '+' : '' }}{{ efficiency.change }}% geçen haftaya göre
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Alerts -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">
          Son Uyarılar
        </h3>
        <div class="space-y-3">
          <div
            v-for="alert in recentAlerts"
            :key="alert.id"
            class="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-navy-700"
          >
            <div
              :class="[
                'w-3 h-3 rounded-full mt-1',
                alert.severity === 'high' ? 'bg-red-500' :
                alert.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
              ]"
            ></div>
            <div class="flex-1">
              <div class="font-medium text-navy-700 dark:text-white text-sm">
                {{ alert.title }}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {{ alert.description }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                {{ alert.timestamp }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import KpiCard from '@/components/housekeeping/KpiCard.vue'

const housekeepingStore = useHousekeepingStore()

// Reactive variables
const selectedPeriod = ref('7d')

// Chart periods
const chartPeriods = [
  { label: '7 Gün', value: '7d' },
  { label: '30 Gün', value: '30d' },
  { label: '3 Ay', value: '3m' }
]

// Computed properties
const performanceKpis = computed(() => [
  {
    title: 'Ortalama Tamamlama Süresi',
    value: '2.4',
    subtitle: 'saat',
    icon: '⏱️',
    trend: { value: -12, isPositive: true },
    color: 'blue',
    format: 'time'
  },
  {
    title: 'Günlük Verimlilik',
    value: '94.5',
    subtitle: 'skor',
    icon: '📈',
    trend: { value: 8, isPositive: true },
    color: 'green',
    format: 'percentage'
  },
  {
    title: 'Kalite Puanı',
    value: '4.8',
    subtitle: '/ 5.0',
    icon: '⭐',
    trend: { value: 0.3, isPositive: true },
    color: 'yellow',
    format: 'number'
  },
  {
    title: 'Müşteri Memnuniyeti',
    value: '96.2',
    subtitle: 'puan',
    icon: '😊',
    trend: { value: 2.1, isPositive: true },
    color: 'purple',
    format: 'percentage'
  }
])

const taskCategories = computed(() => [
  { name: 'Oda Temizliği', icon: '🛏️', completed: 45, total: 50 },
  { name: 'Banyo Temizliği', icon: '🚿', completed: 38, total: 42 },
  { name: 'Genel Alanlar', icon: '🏨', completed: 22, total: 25 },
  { name: 'Özel Talep', icon: '⭐', completed: 15, total: 18 }
])

const topPerformers = computed(() => {
  // Mock data for top performers
  return [
    {
      id: 'staff1',
      name: 'Ayşe Yılmaz',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b1e2?w=150&h=150&fit=crop&crop=face',
      score: 95,
      tasksCompleted: 28,
      efficiency: '94%',
      rank: 1
    },
    {
      id: 'staff2',
      name: 'Mehmet Kaya',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      score: 92,
      tasksCompleted: 25,
      efficiency: '91%',
      rank: 2
    },
    {
      id: 'staff3',
      name: 'Fatma Demir',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      score: 89,
      tasksCompleted: 23,
      efficiency: '88%',
      rank: 3
    },
    {
      id: 'staff4',
      name: 'Ali Özkan',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      score: 87,
      tasksCompleted: 22,
      efficiency: '85%',
      rank: 4
    },
    {
      id: 'staff5',
      name: 'Zeynep Şahin',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
      score: 85,
      tasksCompleted: 20,
      efficiency: '83%',
      rank: 5
    }
  ]
})

const qualityMetrics = computed(() => [
  {
    name: 'Temizlik Standartları',
    description: 'Genel temizlik kalitesi',
    score: 95,
    icon: '🧹'
  },
  {
    name: 'Zaman Uyumu',
    description: 'Planlanan sürelere uyum',
    score: 88,
    icon: '⏰'
  },
  {
    name: 'Müşteri Şikayetleri',
    description: 'Şikayet oranı (düşük daha iyi)',
    score: 92,
    icon: '📝'
  },
  {
    name: 'Malzeme Kullanımı',
    description: 'Verimli malzeme kullanımı',
    score: 87,
    icon: '🧴'
  }
])

const responseTimeAnalysis = computed(() => [
  { category: 'Acil Durumlar', averageTime: '12 dk', trend: 'up', trendText: '↑ 15% gelişme' },
  { category: 'Rutin Görevler', averageTime: '2.4 saat', trend: 'stable', trendText: 'Sabit kalıyor' },
  { category: 'Özel Talepler', averageTime: '45 dk', trend: 'down', trendText: '↓ 8% kötüleşme' }
])

const efficiencyMetrics = computed(() => [
  { name: 'Oda/Saat', value: '3.2', icon: '🏃', change: 12 },
  { name: 'Görev/Gün', value: '18', icon: '✅', change: -5 },
  { name: 'Kalite Skoru', value: '94%', icon: '🎯', change: 3 }
])

const recentAlerts = computed(() => [
  {
    id: 1,
    title: 'Yüksek İş Yükü Uyarısı',
    description: '3. katta personel sayısı yetersiz',
    timestamp: '15 dakika önce',
    severity: 'high'
  },
  {
    id: 2,
    title: 'Malzeme Azalması',
    description: 'Temizlik malzemeleri azalıyor',
    timestamp: '1 saat önce',
    severity: 'medium'
  },
  {
    id: 3,
    title: 'Performans Düşüşü',
    description: 'Oda 305 temizlik süresi uzadı',
    timestamp: '2 saat önce',
    severity: 'low'
  }
])
</script> 