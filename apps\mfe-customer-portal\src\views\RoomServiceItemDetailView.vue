<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="sticky top-0 z-10 bg-white shadow-sm">
      <div class="flex items-center justify-between px-4 py-3">
        <!-- Back Button -->
        <button 
          @click="goBack"
          class="flex items-center justify-center w-10 h-10 bg-backgroundLight rounded-lg"
        >
          <svg class="w-5 h-5 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- Page Title -->
        <h1 class="text-lg font-semibold text-textDark"><PERSON><PERSON><PERSON><PERSON></h1>

        <!-- Cart Button -->
        <button 
          @click="showCart"
          class="flex items-center justify-center w-10 h-10 relative"
        >
          <svg 
            class="w-6 h-6 text-textDark" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          <div class="absolute -top-1 -right-1 bg-primary text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
            2
          </div>
        </button>
      </div>
    </div>

    <div v-if="item" class="max-w-md mx-auto">
      <!-- Product Image -->
      <div class="relative px-4 py-6">
        <div class="aspect-square rounded-3xl overflow-hidden shadow-lg relative">
          <img 
            :src="item.image" 
            :alt="item.name"
            class="w-full h-full object-cover"
          />
          
          <!-- Badges -->
          <div class="absolute top-4 left-4 flex space-x-2">
            <div 
              v-if="item.isNew"
              class="bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full"
            >
              YENİ
            </div>
            <div 
              v-if="item.isSpicy"
              class="text-2xl"
            >
              🌶️
            </div>
          </div>

          <!-- Rating Badge -->
          <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1">
            <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <span class="text-sm font-semibold text-textDark">{{ item.rating }}</span>
          </div>
        </div>
        
        <!-- Image Description -->
        <div class="px-4 py-3">
          <p class="text-center text-textMedium text-sm leading-relaxed">
            {{ item.shortDescription || 'Taze ve lezzetli malzemelerle özenle hazırlanmış' }}
          </p>
        </div>
      </div>

      <!-- Product Info -->
      <div class="px-4 mb-6">
        <!-- Name and Price -->
        <div class="mb-4">
          <h2 class="text-2xl font-bold text-textDark mb-2">{{ item.name }}</h2>
          <div class="flex items-center justify-between mb-3">
            <span class="text-3xl font-bold text-primary">₺{{ item.price.toFixed(2) }}</span>
            <div class="text-textMedium text-sm">
              {{ item.calories }} kcal • {{ item.weight }}g
            </div>
          </div>
        </div>

        <!-- Description -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-textDark mb-2">Açıklama</h3>
          <p class="text-textMedium leading-relaxed">{{ item.fullDescription }}</p>
        </div>

        <!-- Ingredients -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-textDark mb-3">İçerikler</h3>
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="ingredient in item.ingredients" 
              :key="ingredient"
              class="bg-backgroundLight text-textDark px-3 py-1 rounded-full text-sm"
            >
              {{ ingredient }}
            </span>
          </div>
        </div>

        <!-- Nutritional Info -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-textDark mb-3">Besin Değerleri</h3>
          <div class="bg-white rounded-2xl p-4 shadow-sm">
            <div class="grid grid-cols-2 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ item.calories }}</div>
                <div class="text-textMedium text-sm">Kalori</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ item.protein }}g</div>
                <div class="text-textMedium text-sm">Protein</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ item.carbs }}g</div>
                <div class="text-textMedium text-sm">Karbonhidrat</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ item.fat }}g</div>
                <div class="text-textMedium text-sm">Yağ</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Customization Options -->
        <div v-if="item.customizations && item.customizations.length > 0" class="mb-6">
          <h3 class="text-lg font-semibold text-textDark mb-3">Özelleştir</h3>
          <div class="space-y-3">
            <div 
              v-for="customization in item.customizations" 
              :key="customization.id"
              class="bg-white rounded-xl p-4 shadow-sm"
            >
              <div class="flex justify-between items-center mb-2">
                <span class="font-medium text-textDark">{{ customization.name }}</span>
                <span class="text-textMedium text-sm">+₺{{ customization.price.toFixed(2) }}</span>
              </div>
              <label class="flex items-center space-x-3 cursor-pointer">
                <input 
                  type="checkbox" 
                  v-model="selectedCustomizations"
                  :value="customization.id"
                  class="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span class="text-textMedium text-sm">{{ customization.description }}</span>
              </label>
            </div>
          </div>
        </div>


      </div>
    </div>

    <!-- Bottom Action Bar -->
    <div class="fixed bottom-16 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4 z-50">
      <div class="max-w-md mx-auto">
        <div class="flex items-center space-x-4">
          <!-- Quantity Selector -->
          <div class="flex items-center space-x-3 bg-backgroundLight rounded-xl px-4 py-3">
            <button 
              @click="decrementQuantity"
              :disabled="quantity <= 1"
              class="w-8 h-8 bg-white rounded-lg flex items-center justify-center disabled:opacity-50"
            >
              <svg class="w-4 h-4 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
              </svg>
            </button>
            
            <span class="font-semibold text-textDark min-w-[2rem] text-center">{{ quantity }}</span>
            
            <button 
              @click="incrementQuantity"
              class="w-8 h-8 bg-white rounded-lg flex items-center justify-center"
            >
              <svg class="w-4 h-4 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>
          </div>

          <!-- Add to Cart Button -->
          <button 
            @click="addToCart"
            class="flex-1 bg-gradient-to-r from-primary to-coral-600 hover:from-primary/90 hover:to-coral-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 shadow-lg"
          >
            <div class="flex items-center justify-center space-x-2">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H3m4 10v6a1 1 0 001 1h9a1 1 0 001-1v-6M7 13v6a1 1 0 001 1h2M17 13v2"/>
              </svg>
              <span>Sepete Ekle - ₺{{ totalPrice.toFixed(2) }}</span>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl p-6 flex flex-col items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-3"></div>
        <p class="text-textDark">Yükleniyor...</p>
      </div>
    </div>

    <!-- Success Toast -->
    <div 
      v-if="showSuccessToast"
      class="fixed top-20 left-4 right-4 bg-green-500 text-white px-4 py-3 rounded-xl shadow-lg z-50 transform transition-transform duration-300"
      :class="showSuccessToast ? 'translate-y-0' : '-translate-y-full'"
    >
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <span>Ürün sepete eklendi!</span>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCartStore } from '../stores/cartStore'

// Router
const route = useRoute()
const router = useRouter()

// Cart store
const cartStore = useCartStore()

// Reactive data
const isLoading = ref(true)
const quantity = ref(1)
const selectedCustomizations = ref<string[]>([])
const showSuccessToast = ref(false)

// Get item ID from route
const itemId = computed(() => route.params.id as string)

// Dummy item data (in real app, this would come from API/store)
const item = ref<any>(null)

// Methods
const goBack = () => {
  router.go(-1)
}

const showCart = () => {
  // Navigate to cart page
  router.push('/cart')
}



const incrementQuantity = () => {
  quantity.value++
}

const decrementQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const customizationTotal = computed(() => {
  if (!item.value?.customizations) return 0
  return selectedCustomizations.value.reduce((total, customizationId) => {
    const customization = item.value.customizations.find((c: any) => c.id === customizationId)
    return total + (customization?.price || 0)
  }, 0)
})

const totalPrice = computed(() => {
  if (!item.value) return 0
  return (item.value.price + customizationTotal.value) * quantity.value
})

const addToCart = () => {
  if (!item.value) return
  
  // Get customization names for the cart
  const customizationNames = selectedCustomizations.value.map(customizationId => {
    const customization = item.value.customizations?.find((c: any) => c.id === customizationId)
    return customization?.name || ''
  }).filter(name => name)
  
  // Add to cart store
  cartStore.addToCart({
    id: item.value.id,
    name: item.value.name,
    price: item.value.price + customizationTotal.value,
    calories: item.value.calories,
    weight: item.value.weight,
    image: item.value.image,
    isNew: item.value.isNew
  }, quantity.value, customizationNames)
  
  // Show success message
  showSuccessToast.value = true
  setTimeout(() => {
    showSuccessToast.value = false
  }, 3000)
}

// Load item data (dummy implementation)
const loadItemData = () => {
  isLoading.value = true
  
  // Dummy data for Caesar Salata
  const itemsData: {[key: string]: any} = {
    '5': {
      id: '5',
      name: 'Caesar Salata',
      price: 45.99,
      calories: 185,
      weight: 200,
      protein: 8,
      carbs: 12,
      fat: 15,
      rating: 4.7,
      reviewCount: 23,
      image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=800&fit=crop&crop=center',
      isNew: false,
      isSpicy: false,
      isFavorite: false,
      shortDescription: 'Taze marul, kruton ve özel Caesar sosu ile hazırlanan klasik salata',
      fullDescription: 'Taze marul yaprakları, özenle seçilmiş parmesan peyniri, ev yapımı kruton ve özel caesar sosumuz ile hazırlanan eşsiz bir salata deneyimi. Geleneksel tarifimizle hazırlanan caesar sosumuz, sarımsaklı krutonlarımız ve taze çıtır marul ile mükemmel bir uyum yakalıyor.',
      ingredients: ['Marul', 'Parmesan Peyniri', 'Kruton', 'Caesar Sos', 'Sarımsak', 'Zeytinyağı', 'Limon'],
      customizations: [
        {
          id: 'chicken',
          name: 'Izgara Tavuk',
          description: 'Lezzetli ızgara tavuk eti ekleyin',
          price: 15.00
        },
        {
          id: 'shrimp',
          name: 'Karides',
          description: 'Taze karides ekleyin',
          price: 25.00
        },
        {
          id: 'extra-cheese',
          name: 'Ekstra Parmesan',
          description: 'Daha fazla parmesan peyniri',
          price: 8.00
        }
      ]
    }
  }
  
  // Simulate API delay
  setTimeout(() => {
    item.value = itemsData[itemId.value] || null
    isLoading.value = false
  }, 500)
}

// Lifecycle
onMounted(() => {
  loadItemData()
})
</script>

<style scoped>
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.coral-600 {
  background-color: #ff5656;
}

.coral-700 {
  background-color: #e74c3c;
}
</style> 