<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900 p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        <PERSON>el İletişim
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Temizlik departmanı gerçek zamanlı iletişim merkezi
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-140px)]">
      <!-- Sidebar - Channels & Direct Messages -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm h-full p-4">
          <!-- Online Status -->
          <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
                Çevrimiçi Durum
              </h3>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-green-600 dark:text-green-400">Aktif</span>
              </div>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              12 kişi çevrimiçi
            </div>
          </div>

          <!-- Channel List -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Kanallar
            </h4>
            <div class="space-y-2">
              <div
                v-for="channel in channels"
                :key="channel.id"
                @click="selectChannel(channel)"
                :class="[
                  'flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors',
                  selectedChannel?.id === channel.id
                    ? 'bg-brand-500 text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-navy-700'
                ]"
              >
                <div class="flex items-center space-x-3">
                  <div class="text-lg">{{ channel.icon }}</div>
                  <div>
                    <div class="font-medium text-sm">{{ channel.name }}</div>
                    <div class="text-xs opacity-75">{{ channel.memberCount }} üye</div>
                  </div>
                </div>
                <div
                  v-if="channel.unreadCount > 0"
                  class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                >
                  {{ channel.unreadCount }}
                </div>
              </div>
            </div>
          </div>

          <!-- Direct Messages -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Direkt Mesajlar
            </h4>
            <div class="space-y-2">
              <div
                v-for="dm in directMessages"
                :key="dm.id"
                @click="selectDirectMessage(dm)"
                :class="[
                  'flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors',
                  selectedDirectMessage?.id === dm.id
                    ? 'bg-brand-500 text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-navy-700'
                ]"
              >
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <div class="w-8 h-8 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      {{ dm.name.charAt(0) }}
                    </div>
                    <div
                      v-if="dm.isOnline"
                      class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                    ></div>
                  </div>
                  <div>
                    <div class="font-medium text-sm">{{ dm.name }}</div>
                    <div class="text-xs opacity-75">{{ dm.lastMessage }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-xs opacity-75">{{ dm.lastMessageTime }}</div>
                  <div
                    v-if="dm.unreadCount > 0"
                    class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mt-1"
                  >
                    {{ dm.unreadCount }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Area -->
      <div class="lg:col-span-3">
        <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm h-full flex flex-col">
          <!-- Chat Header -->
          <div class="p-4 border-b border-gray-200 dark:border-navy-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="text-2xl">
                  {{ selectedChannel?.icon || '💬' }}
                </div>
                <div>
                  <h3 class="font-semibold text-navy-700 dark:text-white">
                    {{ selectedChannel?.name || selectedDirectMessage?.name || 'Sohbet Seçin' }}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ selectedChannel ? `${selectedChannel.memberCount} üye` : 
                        selectedDirectMessage ? (selectedDirectMessage.isOnline ? 'Çevrimiçi' : 'Çevrimdışı') : 
                        'Bir kanal veya kişi seçin' }}
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
                  title="Arama"
                >
                  <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
                <button
                  class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
                  title="Ayarlar"
                >
                  <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Messages Area -->
          <div class="flex-1 overflow-y-auto p-4 space-y-4" ref="messagesContainer">
            <div
              v-for="message in currentMessages"
              :key="message.id"
              :class="[
                'flex items-start space-x-3',
                message.isOwn ? 'justify-end' : 'justify-start'
              ]"
            >
              <div v-if="!message.isOwn" class="flex-shrink-0">
                <div class="w-8 h-8 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  {{ message.sender?.charAt(0) || 'U' }}
                </div>
              </div>
              <div
                :class="[
                  'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                  message.isOwn
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 dark:bg-navy-700 text-gray-900 dark:text-white'
                ]"
              >
                <div v-if="!message.isOwn" class="text-sm font-medium mb-1">
                  {{ message.sender }}
                </div>
                <div class="text-sm">{{ message.text }}</div>
                <div
                  :class="[
                    'text-xs mt-1',
                    message.isOwn ? 'text-white/70' : 'text-gray-500 dark:text-gray-400'
                  ]"
                >
                  {{ message.timestamp }}
                </div>
              </div>
              <div v-if="message.isOwn" class="flex-shrink-0">
                <div class="w-8 h-8 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  S
                </div>
              </div>
            </div>
          </div>

          <!-- Message Input -->
          <div class="p-4 border-t border-gray-200 dark:border-navy-700">
            <div class="flex items-center space-x-3">
              <div class="flex-1 relative">
                <input
                  v-model="newMessage"
                  @keydown.enter="sendMessage"
                  type="text"
                  placeholder="Mesajınızı yazın..."
                  class="w-full px-4 py-2 pr-12 border border-gray-300 dark:border-navy-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-700 dark:text-white"
                />
                <button
                  @click="sendMessage"
                  :disabled="!newMessage.trim()"
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-brand-500 hover:text-brand-600 disabled:text-gray-400 disabled:cursor-not-allowed"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                  </svg>
                </button>
              </div>
              <button
                class="p-2 text-gray-600 dark:text-gray-400 hover:text-brand-500 transition-colors"
                title="Dosya Ekle"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
              </button>
              <button
                class="p-2 text-gray-600 dark:text-gray-400 hover:text-brand-500 transition-colors"
                title="Emoji"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'

const housekeepingStore = useHousekeepingStore()

// Reactive variables
const selectedChannel = ref<any>(null)
const selectedDirectMessage = ref<any>(null)
const newMessage = ref('')
const messagesContainer = ref<HTMLElement>()

// Computed properties
const channels = computed(() => housekeepingStore.chatChannels)
const directMessages = computed(() => housekeepingStore.directMessages)

const currentMessages = computed(() => {
  if (selectedChannel.value) {
    return housekeepingStore.channelMessages[selectedChannel.value.id] || []
  } else if (selectedDirectMessage.value) {
    return housekeepingStore.directMessageHistory[selectedDirectMessage.value.id] || []
  }
  return []
})

// Methods
const selectChannel = (channel: any) => {
  selectedChannel.value = channel
  selectedDirectMessage.value = null
  // Mark channel as read
  housekeepingStore.markChannelAsRead(channel.id)
  scrollToBottom()
}

const selectDirectMessage = (dm: any) => {
  selectedDirectMessage.value = dm
  selectedChannel.value = null
  // Mark direct message as read
  housekeepingStore.markDirectMessageAsRead(dm.id)
  scrollToBottom()
}

const sendMessage = () => {
  if (!newMessage.value.trim()) return

  const messageData = {
    text: newMessage.value,
    sender: 'Siz',
    timestamp: new Date().toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' }),
    isOwn: true
  }

  if (selectedChannel.value) {
    housekeepingStore.sendChannelMessage(selectedChannel.value.id, messageData)
  } else if (selectedDirectMessage.value) {
    housekeepingStore.sendDirectMessage(selectedDirectMessage.value.id, messageData)
  }

  newMessage.value = ''
  scrollToBottom()
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// Lifecycle
onMounted(() => {
  // Select first channel by default
  if (channels.value.length > 0) {
    selectChannel(channels.value[0])
  }
})
</script> 