<template>
  <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card p-6 border border-gray-200 dark:border-navy-700 hover:shadow-lg transition-shadow duration-300">
    <div class="flex items-center justify-between">
      <div class="flex flex-col">
        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
          {{ title }}
        </p>
        <h3 class="text-3xl font-bold text-navy-700 dark:text-white mt-2">
          {{ value }}
        </h3>
        <div v-if="subtitle" class="flex items-center mt-2">
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ subtitle }}
          </span>
        </div>
      </div>
      
      <div class="flex items-center justify-center w-12 h-12 rounded-lg" :class="iconBgClass">
        <component :is="icon" class="w-6 h-6" :class="iconColorClass" />
      </div>
    </div>
    
    <!-- Optional trend indicator -->
    <div v-if="trend" class="flex items-center mt-4 pt-4 border-t border-gray-100 dark:border-navy-700">
      <component 
        :is="trend.isPositive ? 'ArrowTrendingUpIcon' : 'ArrowTrendingDownIcon'" 
        class="w-4 h-4 mr-1"
        :class="trend.isPositive ? 'text-green-500' : 'text-red-500'"
      />
      <span 
        class="text-sm font-medium"
        :class="trend.isPositive ? 'text-green-500' : 'text-red-500'"
      >
        {{ trend.percentage }}%
      </span>
      <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">
        {{ trend.period }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/vue/24/outline'

interface TrendData {
  isPositive: boolean
  percentage: number
  period: string
}

interface Props {
  title: string
  value: string | number
  subtitle?: string
  icon: any
  iconBgClass?: string
  iconColorClass?: string
  trend?: TrendData
}

const props = withDefaults(defineProps<Props>(), {
  iconBgClass: 'bg-brand-500/10',
  iconColorClass: 'text-brand-500'
})
</script> 