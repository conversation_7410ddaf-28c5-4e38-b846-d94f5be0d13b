<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="mb-6">
      <h4 class="text-lg font-bold text-navy-700 dark:text-white">Oda Durumu</h4>
      <p class="text-sm text-gray-600 dark:text-gray-400">Toplam 150 Oda</p>
    </div>

    <!-- Chart Placeholder -->
    <div class="flex items-center justify-center mb-6">
      <div class="relative w-32 h-32">
        <!-- Pie chart placeholder -->
        <div class="w-32 h-32 rounded-full bg-gradient-conic from-green-400 via-blue-500 to-red-400 flex items-center justify-center">
          <div class="w-20 h-20 bg-white dark:bg-navy-800 rounded-full flex items-center justify-center">
            <div class="text-center">
              <div class="text-xl font-bold text-navy-700 dark:text-white">78%</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Doluluk</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Legend -->
    <div class="space-y-3">
      <div v-for="item in roomStats" :key="item.label" class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">{{ item.label }}</span>
        </div>
        <div class="text-right">
          <div class="text-sm font-medium text-navy-700 dark:text-white">{{ item.count }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">{{ item.percentage }}%</div>
        </div>
      </div>
    </div>

    <!-- Total -->
    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam</span>
        <span class="text-sm font-bold text-navy-700 dark:text-white">150 Oda</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface RoomStat {
  label: string
  count: number
  percentage: number
  color: string
}

const roomStats = ref<RoomStat[]>([
  {
    label: 'Dolu',
    count: 87,
    percentage: 58,
    color: '#10B981'
  },
  {
    label: 'Temiz',
    count: 45,
    percentage: 30,
    color: '#3B82F6'
  },
  {
    label: 'Bakımda',
    count: 12,
    percentage: 8,
    color: '#F59E0B'
  },
  {
    label: 'Kirli',
    count: 6,
    percentage: 4,
    color: '#EF4444'
  }
])

// TODO: Chart.js integration will be added here
// import { Doughnut } from 'vue-chartjs'
// import { Chart as ChartJS, Title, Tooltip, Legend, ArcElement } from 'chart.js'
</script>

<style scoped>
.bg-gradient-conic {
  background: conic-gradient(from 0deg, #10B981 0deg 208.8deg, #3B82F6 208.8deg 316.8deg, #F59E0B 316.8deg 345.6deg, #EF4444 345.6deg 360deg);
}
</style> 