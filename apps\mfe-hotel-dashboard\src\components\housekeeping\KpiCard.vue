<template>
  <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-xl transition-shadow duration-200">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-3">
        <!-- Icon -->
        <div :class="getIconBackgroundClass()" class="p-3 rounded-xl">
          <component :is="getIcon()" class="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ title }}</h3>
          <p v-if="subtitle" class="text-xs text-gray-500 dark:text-gray-500">{{ subtitle }}</p>
        </div>
      </div>
      
      <!-- Trend Indicator -->
      <div v-if="trend !== undefined" class="flex items-center space-x-1">
        <div :class="getTrendClass()">
          <svg v-if="trend > 0" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          <svg v-else-if="trend < 0" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 112 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <span :class="getTrendTextClass()" class="text-sm font-medium">
          {{ Math.abs(trend) }}%
        </span>
      </div>
    </div>

    <!-- Value -->
    <div class="mb-2">
      <div class="flex items-baseline space-x-2">
        <span class="text-3xl font-bold text-gray-800 dark:text-white">
          {{ formattedValue }}
        </span>
        <span v-if="unit" class="text-lg text-gray-500 dark:text-gray-400">
          {{ unit }}
        </span>
      </div>
    </div>

    <!-- Progress Bar (for percentage type) -->
    <div v-if="format === 'percentage' && showProgress" class="mb-3">
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          :class="getProgressBarClass()"
          class="h-2 rounded-full transition-all duration-500 ease-out"
          :style="{ width: `${Math.min(value as number, 100)}%` }"
        ></div>
      </div>
    </div>

    <!-- Description -->
    <div v-if="description" class="text-sm text-gray-600 dark:text-gray-400">
      {{ description }}
    </div>

    <!-- Additional Info -->
    <div v-if="additionalInfo" class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <span class="text-xs text-gray-500 dark:text-gray-500">{{ additionalInfo.label }}</span>
      <span class="text-xs font-medium text-gray-700 dark:text-gray-300">{{ additionalInfo.value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Icons
import {
  ClipboardDocumentListIcon,
  ClockIcon,
  CogIcon,
  CheckCircleIcon,
  CheckIcon,
  UserGroupIcon,
  HomeIcon,
  ExclamationTriangleIcon,
  TrophyIcon,
  StarIcon,
  ChartBarIcon,
  FireIcon,
  BoltIcon,
  HeartIcon
} from '@heroicons/vue/24/outline'

interface Props {
  title: string
  value: number | string
  format?: 'number' | 'percentage' | 'currency' | 'time' | 'text'
  color?: 'blue' | 'green' | 'yellow' | 'purple' | 'red' | 'indigo' | 'gray'
  icon?: string
  subtitle?: string
  description?: string
  unit?: string
  trend?: number // positive for increase, negative for decrease
  showProgress?: boolean
  additionalInfo?: {
    label: string
    value: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  format: 'number',
  color: 'blue',
  icon: 'chart-bar',
  showProgress: false
})

// Computed
const formattedValue = computed(() => {
  if (typeof props.value === 'string') return props.value
  
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(props.value as number)
    case 'percentage':
      return `${props.value}%`
    case 'time':
      // Assume value is in minutes
      const hours = Math.floor((props.value as number) / 60)
      const minutes = (props.value as number) % 60
      if (hours > 0) {
        return `${hours}s ${minutes}dk`
      }
      return `${minutes}dk`
    case 'number':
    default:
      return new Intl.NumberFormat('tr-TR').format(props.value as number)
  }
})

const getIconBackgroundClass = () => {
  const classes = {
    blue: 'bg-gradient-to-br from-blue-500 to-blue-600',
    green: 'bg-gradient-to-br from-green-500 to-green-600',
    yellow: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
    purple: 'bg-gradient-to-br from-purple-500 to-purple-600',
    red: 'bg-gradient-to-br from-red-500 to-red-600',
    indigo: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
    gray: 'bg-gradient-to-br from-gray-500 to-gray-600'
  }
  return classes[props.color]
}

const getProgressBarClass = () => {
  const classes = {
    blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
    green: 'bg-gradient-to-r from-green-500 to-green-600',
    yellow: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
    purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
    red: 'bg-gradient-to-r from-red-500 to-red-600',
    indigo: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
    gray: 'bg-gradient-to-r from-gray-500 to-gray-600'
  }
  return classes[props.color]
}

const getTrendClass = () => {
  if (props.trend === undefined) return ''
  if (props.trend > 0) return 'text-green-500'
  if (props.trend < 0) return 'text-red-500'
  return 'text-gray-500'
}

const getTrendTextClass = () => {
  if (props.trend === undefined) return ''
  if (props.trend > 0) return 'text-green-600 dark:text-green-400'
  if (props.trend < 0) return 'text-red-600 dark:text-red-400'
  return 'text-gray-600 dark:text-gray-400'
}

const getIcon = () => {
  const iconMap = {
    'clipboard-list': ClipboardDocumentListIcon,
    'clock': ClockIcon,
    'cog': CogIcon,
    'check-circle': CheckCircleIcon,
    'check': CheckIcon,
    'user-group': UserGroupIcon,
    'home': HomeIcon,
    'exclamation-triangle': ExclamationTriangleIcon,
    'trophy': TrophyIcon,
    'star': StarIcon,
    'chart-bar': ChartBarIcon,
    'fire': FireIcon,
    'bolt': BoltIcon,
    'heart': HeartIcon
  }
  return iconMap[props.icon as keyof typeof iconMap] || ChartBarIcon
}
</script> 