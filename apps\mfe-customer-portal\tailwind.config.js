/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // New Mobile Theme Colors (based on 04_spes.png)
        primary: '#FF5656', // Coral red
        textDark: '#3E3E3E', // Dark text
        textMedium: '#808080', // Medium gray text
        textLight: '#A3A3A3', // Light gray text
        backgroundLight: '#F1F1F3', // Light background
        backgroundWhite: '#FFFFFF', // White background
        borderColor: '#EAEAEA', // Border color
        
        // Original Horizon UI Colors (maintained for compatibility)
        brand: {
          50: '#EFF4FF',
          100: '#DEE7FE',
          200: '#C5D2FD',
          300: '#9DB1FB',
          400: '#6E85F7',
          500: '#422AFB',
          600: '#3311DB',
          700: '#2111A5',
          800: '#0F0A6B',
          900: '#11047A',
        },
        navy: {
          50: '#d0dcfb',
          100: '#aac0fe',
          200: '#a3b9f8',
          300: '#728fea',
          400: '#3652ba',
          500: '#1b3bbb',
          600: '#24388a',
          700: '#1B254B',
          800: '#111c44',
          900: '#0b1426',
        },
        secondaryGray: {
          100: '#E0E5F2',
          200: '#E1E9F8',
          300: '#F4F7FE',
          400: '#E9EDF7',
          500: '#8F9BBA',
          600: '#A3AED0',
          700: '#707EAE',
          800: '#2D3748',
          900: '#1A202C',
        },
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'], // SF Pro Text replacement
      },
    },
  },
  plugins: [],
} 