import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { ManagementPortalService, ManagementPortal } from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'

export const useHotelStore = defineStore('hotel', () => {
  // Get auth store for user context
  const authStore = useAuthStore()
  
  // State
  const currentHotel = ref<ManagementPortal.Hotel | null>(null)
  const hotels = ref<ManagementPortal.Hotel[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const hasMultipleHotels = computed(() => hotels.value.length > 1)
  const canSwitchHotels = computed(() => 
    authStore.isSuperAdmin && hasMultipleHotels.value
  )

  const currentHotelName = computed(() => 
    currentHotel.value?.name || 'Hotel'
  )

  const activeHotels = computed(() =>
    hotels.value.filter(h => h.status === 'Aktif')
  )

  // Actions
  const fetchHotels = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await ManagementPortalService.getHotels()
      
      if (response.error) {
        throw new Error(response.error)
      }

      hotels.value = response.data || []

      // Set current hotel based on user context
      if (authStore.userHotelId && hotels.value.length > 0) {
        const userHotel = hotels.value.find(h => h.id === authStore.userHotelId)
        if (userHotel) {
          currentHotel.value = userHotel
        }
      } else if (authStore.isSuperAdmin && hotels.value.length > 0) {
        // For super admin, default to first active hotel
        const firstActiveHotel = activeHotels.value[0] || hotels.value[0]
        currentHotel.value = firstActiveHotel
      }

    } catch (err) {
      error.value = (err as Error).message
      console.error('Error fetching hotels:', err)
    } finally {
      isLoading.value = false
    }
  }

  const setCurrentHotel = (hotel: ManagementPortal.Hotel) => {
    if (authStore.isSuperAdmin || hotel.id === authStore.userHotelId) {
      currentHotel.value = hotel
    } else {
      console.warn('User does not have access to this hotel')
    }
  }

  const switchHotel = (hotelId: string) => {
    const hotel = hotels.value.find(h => h.id === hotelId)
    if (hotel) {
      setCurrentHotel(hotel)
    }
  }

  const refreshCurrentHotel = async () => {
    if (currentHotel.value) {
      await fetchHotels()
      // Update current hotel with fresh data
      const updatedHotel = hotels.value.find(h => h.id === currentHotel.value?.id)
      if (updatedHotel) {
        currentHotel.value = updatedHotel
      }
    }
  }

  const getHotelById = (hotelId: string) => {
    return hotels.value.find(h => h.id === hotelId) || null
  }

  // Initialize hotels when auth is ready
  const initializeHotels = async () => {
    if (authStore.isAuthenticated) {
      await fetchHotels()
    }
  }

  return {
    // State
    currentHotel: readonly(currentHotel),
    hotels: readonly(hotels),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Computed
    hasMultipleHotels,
    canSwitchHotels,
    currentHotelName,
    activeHotels,

    // Actions
    fetchHotels,
    setCurrentHotel,
    switchHotel,
    refreshCurrentHotel,
    getHotelById,
    initializeHotels
  }
})
