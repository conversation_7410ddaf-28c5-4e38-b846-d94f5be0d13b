<template>
  <div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
          Aktivite Yönetimi
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          Otel misafirleri için etkinlik ve aktiviteleri yönetin
        </p>
      </div>
      <button 
        @click="openCreateModal"
        class="bg-brand-500 hover:bg-brand-600 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center"
      >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Yeni Aktivite Oluştur
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white dark:bg-navy-800 rounded-20 p-6 shadow-card">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-brand-50 dark:bg-brand-500/20 rounded-xl flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-navy-700 dark:text-white">{{ activities.length }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Toplam Aktivite</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-20 p-6 shadow-card">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-green-50 dark:bg-green-500/20 rounded-xl flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-navy-700 dark:text-white">{{ activeActivities.length }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Aktif Aktivite</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-20 p-6 shadow-card">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-yellow-50 dark:bg-yellow-500/20 rounded-xl flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-navy-700 dark:text-white">{{ upcomingActivities.length }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Yaklaşan</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-20 p-6 shadow-card">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-purple-50 dark:bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-navy-700 dark:text-white">{{ totalCapacity }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">Toplam Kapasite</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filter Tabs -->
    <div class="mb-6">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="filter in filters"
            :key="filter.value"
            @click="activeFilter = filter.value"
            class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
            :class="activeFilter === filter.value 
              ? 'border-brand-500 text-brand-600 dark:text-brand-400' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
          >
            {{ filter.label }}
          </button>
        </nav>
      </div>
    </div>

    <!-- Activities Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <ActivityCard
        v-for="activity in filteredActivities"
        :key="activity.id"
        :activity="activity"
        @edit="editActivity"
        @delete="deleteActivity"
        @toggle-status="toggleActivityStatus"
      />
    </div>

    <!-- Empty State -->
    <div v-if="filteredActivities.length === 0" class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Aktivite bulunamadı</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-6">Bu filtre ile eşleşen aktivite yok. Yeni bir aktivite oluşturun.</p>
      <button 
        @click="openCreateModal"
        class="bg-brand-500 hover:bg-brand-600 text-white px-6 py-3 rounded-xl font-medium transition-colors"
      >
        İlk Aktiviteyi Oluştur
      </button>
    </div>

    <!-- Activity Modal -->
    <ActivityModal
      v-if="isModalOpen"
      :activity="selectedActivity"
      @close="closeModal"
      @save="saveActivity"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import ActivityCard from '@/components/cards/ActivityCard.vue'
import ActivityModal from '@/components/modals/ActivityModal.vue'
import type { HotelActivity } from '@/types/management'

const managementStore = useManagementStore()

// State
const isModalOpen = ref(false)
const selectedActivity = ref<HotelActivity | null>(null)
const activeFilter = ref('all')

// Computed
const activities = computed(() => managementStore.activities)

const activeActivities = computed(() => 
  activities.value.filter(activity => activity.isActive)
)

const upcomingActivities = computed(() => {
  const now = new Date()
  return activities.value.filter(activity => {
    const activityDate = new Date(activity.startTimeUtc)
    return activityDate > now && activity.isActive
  })
})

const totalCapacity = computed(() => 
  activities.value.reduce((total, activity) => total + (activity.capacity || 0), 0)
)

const filters = [
  { value: 'all', label: 'Tümü' },
  { value: 'active', label: 'Aktif' },
  { value: 'inactive', label: 'Pasif' },
  { value: 'upcoming', label: 'Yaklaşan' },
  { value: 'past', label: 'Geçmiş' }
]

const filteredActivities = computed(() => {
  const now = new Date()
  
  switch (activeFilter.value) {
    case 'active':
      return activities.value.filter(activity => activity.isActive)
    case 'inactive':
      return activities.value.filter(activity => !activity.isActive)
    case 'upcoming':
      return activities.value.filter(activity => {
        const activityDate = new Date(activity.startTimeUtc)
        return activityDate > now
      })
    case 'past':
      return activities.value.filter(activity => {
        const activityDate = new Date(activity.startTimeUtc)
        return activityDate <= now
      })
    default:
      return activities.value
  }
})

// Methods
const openCreateModal = () => {
  selectedActivity.value = null
  isModalOpen.value = true
}

const editActivity = (activity: HotelActivity) => {
  selectedActivity.value = activity
  isModalOpen.value = true
}

const closeModal = () => {
  isModalOpen.value = false
  selectedActivity.value = null
}

const saveActivity = (activityData: Omit<HotelActivity, 'id'>) => {
  if (selectedActivity.value) {
    // Update existing activity
    const updatedActivity: HotelActivity = {
      ...selectedActivity.value,
      ...activityData
    }
    managementStore.updateActivity(updatedActivity)
  } else {
    // Create new activity
    managementStore.addActivity(activityData)
  }
  closeModal()
}

const deleteActivity = (activityId: string) => {
  if (confirm('Bu aktiviteyi silmek istediğinizden emin misiniz?')) {
    managementStore.deleteActivity(activityId)
  }
}

const toggleActivityStatus = (activityId: string) => {
  managementStore.toggleActivityStatus(activityId)
}
</script> 