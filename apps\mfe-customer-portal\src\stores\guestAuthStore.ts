import { ref, computed, readonly } from 'vue'
import { defineStore } from 'pinia'
import { supabase } from '@hotelexia/shared-supabase-client'
import type { GuestSession } from '@hotelexia/shared-supabase-client'

// Removed unused interface - using any type for flexibility

interface Hotel {
  id: string
  name: string
  description?: string
  address: string
  city: string
  country: string
  phone?: string
  email?: string
  website?: string
  rating?: number
  image_urls?: string[]
  amenities?: string[]
  check_in_time?: string
  check_out_time?: string
  status?: string
}

export const useGuestAuthStore = defineStore('guestAuth', () => {
  // State
  const guestSession = ref<GuestSession | null>(null)
  const currentHotel = ref<Hotel | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => guestSession.value !== null)
  const currentGuest = computed(() => guestSession.value)
  const currentHotelId = computed(() => guestSession.value?.hotel_id || null)

  // Enhanced guest authentication with better error handling and validation
  async function loginWithReservation(reservationNumber: string, lastName: string, hotelId: string) {
    isLoading.value = true
    error.value = null

    try {
      // Input validation
      if (!reservationNumber.trim() || !lastName.trim() || !hotelId.trim()) {
        throw new Error('Tüm alanları doldurunuz.')
      }

      // Normalize inputs
      const normalizedReservationNumber = reservationNumber.trim().toUpperCase()
      const normalizedLastName = lastName.trim().toLowerCase()

      // Query reservations table with guest authentication data
      const { data: reservations, error: dbError } = await supabase
        .from('reservations')
        .select('*')
        .eq('hotel_id', hotelId)
        .eq('reservation_number', normalizedReservationNumber)
        .ilike('guest_last_name', normalizedLastName)

      if (dbError) {
        console.error('Database error:', dbError)
        throw new Error('Veritabanı hatası oluştu. Lütfen tekrar deneyin.')
      }

      if (!reservations || reservations.length === 0) {
        throw new Error('Rezervasyon bulunamadı. Lütfen rezervasyon numaranızı ve soyadınızı kontrol edin.')
      }

      const data = reservations[0]

      // Check if reservation is active (not expired)
      const checkoutDate = new Date(data.check_out_date)
      const today = new Date()
      if (today > checkoutDate) {
        throw new Error('Bu rezervasyon süresi dolmuş. Lütfen yeni bir rezervasyon yapın.')
      }

      // Check if check-in date has arrived
      const checkinDate = new Date(data.check_in_date)
      const dayBeforeCheckin = new Date(checkinDate)
      dayBeforeCheckin.setDate(dayBeforeCheckin.getDate() - 1)

      if (today < dayBeforeCheckin) {
        throw new Error('Check-in tarihiniz henüz gelmedi. Lütfen daha sonra tekrar deneyin.')
      }

      const reservation = data as any

      // Get room details separately
      const { data: roomData } = await supabase
        .from('rooms')
        .select('room_number, room_type, floor')
        .eq('id', reservation.room_id)
        .single()

      // Create guest session
      guestSession.value = {
        id: reservation.id,
        hotel_id: reservation.hotel_id,
        room_id: reservation.room_id,
        guest_name: reservation.guest_first_name,
        guest_last_name: reservation.guest_last_name,
        reservation_number: reservation.reservation_number,
        check_in_date: reservation.check_in_date,
        check_out_date: reservation.check_out_date,
        room_number: roomData?.room_number || 'N/A'
      }

      // Store session in localStorage for persistence
      localStorage.setItem('hotelexia_guest_session', JSON.stringify(guestSession.value))

      return { success: true }
    } catch (err: any) {
      console.error('Login error:', err)
      error.value = err.message || 'Giriş sırasında bir hata oluştu.'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  async function fetchHotelDetails(hotelId: string) {
    try {
      isLoading.value = true
      error.value = null

      console.log('Fetching hotel details for ID:', hotelId)

      // Enhanced query with explicit .single() modifier for exactly one row
      const { data: hotelData, error: hotelError } = await supabase
        .from('hotels')
        .select('*')
        .eq('id', hotelId)
        .single() // Explicitly expect exactly one row

      if (hotelError) {
        console.error('Hotel fetch error:', hotelError)
        
        // Handle specific PostgREST errors
        if (hotelError.code === 'PGRST116') {
          throw new Error(`Otel bulunamadı (ID: ${hotelId}). Rezervasyon bilgilerinizi kontrol edin.`)
        }
        
        throw new Error(`Otel bilgileri alınamadı: ${hotelError.message}`)
      }

      if (!hotelData) {
        throw new Error('Otel verisi boş döndü.')
      }

      console.log('Hotel data fetched successfully:', hotelData.name)
      currentHotel.value = hotelData as Hotel
      return { success: true, hotel: hotelData }
    } catch (err: any) {
      console.error('Hotel details error:', err)
      error.value = err.message || 'Otel bilgileri alınırken bir hata oluştu.'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }



  // Enhanced session validation with automatic cleanup
  async function checkExistingSession() {
    try {
      const storedSession = localStorage.getItem('hotelexia_guest_session')
      if (!storedSession) {
        return false
      }

      const session = JSON.parse(storedSession) as GuestSession

      // Basic session structure validation
      if (!session.id || !session.hotel_id || !session.check_out_date) {
        console.warn('Invalid session structure, clearing...')
        localStorage.removeItem('hotelexia_guest_session')
        return false
      }

      // Check if checkout date has passed (client-side check first)
      const checkoutDate = new Date(session.check_out_date)
      const today = new Date()
      if (today > checkoutDate) {
        console.log('Session expired (checkout date passed), clearing...')
        localStorage.removeItem('hotelexia_guest_session')
        return false
      }

      // Verify session is still valid by checking reservation in database
      const { data, error: dbError } = await supabase
        .from('reservations')
        .select('id, check_out_date, guest_first_name, guest_last_name')
        .eq('id', session.id)
        .single()

      if (dbError || !data) {
        console.warn('Session validation failed, reservation not found:', dbError?.message)
        localStorage.removeItem('hotelexia_guest_session')
        return false
      }

      // Double-check with database checkout date
      const dbCheckoutDate = new Date((data as any).check_out_date)
      if (today > dbCheckoutDate) {
        console.log('Session expired (database checkout date passed), clearing...')
        localStorage.removeItem('hotelexia_guest_session')
        return false
      }

      // Session is valid, restore it
      guestSession.value = session
      console.log('Valid session restored for guest:', (data as any).guest_first_name)
      return true

    } catch (err) {
      console.error('Session check error:', err)
      localStorage.removeItem('hotelexia_guest_session')
      return false
    }
  }

  // Enhanced logout with proper cleanup
  async function logout() {
    try {
      // Clear all guest-related data
      guestSession.value = null
      currentHotel.value = null
      error.value = null

      // Clear localStorage
      localStorage.removeItem('hotelexia_guest_session')
      localStorage.removeItem('hotelexia_remember_guest')

      // Clear any cached data
      sessionStorage.removeItem('hotelexia_guest_cache')

      console.log('Guest session cleared successfully')
    } catch (err) {
      console.error('Error during logout:', err)
    }
  }

  function clearError() {
    error.value = null
  }

  // Guest data privacy and cleanup procedures
  async function scheduleDataCleanup() {
    if (!guestSession.value) return

    const checkoutDate = new Date(guestSession.value.check_out_date)
    const now = new Date()
    const timeUntilCheckout = checkoutDate.getTime() - now.getTime()

    // Schedule cleanup 24 hours after checkout
    const cleanupDelay = timeUntilCheckout + (24 * 60 * 60 * 1000)

    if (cleanupDelay > 0) {
      setTimeout(() => {
        console.log('Scheduled guest data cleanup triggered')
        logout()
      }, cleanupDelay)
    }
  }

  // Enhanced session security
  const validateSessionSecurity = () => {
    if (!guestSession.value) return false

    // Check if session was created recently (prevent old session reuse)
    const sessionAge = Date.now() - ((guestSession.value as any).created_at ? new Date((guestSession.value as any).created_at).getTime() : 0)
    const maxSessionAge = 7 * 24 * 60 * 60 * 1000 // 7 days

    if (sessionAge > maxSessionAge) {
      console.warn('Session too old, clearing for security')
      logout()
      return false
    }

    return true
  }

  // Guest-specific route access validation
  const validateGuestAccess = (routeName: string): boolean => {
    if (!isAuthenticated.value) return false

    // Define guest-accessible routes
    const guestRoutes = [
      'GuestDashboard',
      'RoomServiceMenu',
      'RoomServiceCategory',
      'RoomServiceView',
      'RoomServiceItemDetail',
      'MyOrders',
      'MyCart',
      'MyRequests',
      'Activities',
      'ActivityDetail',
      'Services',
      'MyReservations',
      'OrderSuccess',
      'OrderFailed'
    ]

    return guestRoutes.includes(routeName)
  }

  // Development-only: Set up mock guest session for testing
  async function setGuestSessionForDevelopment(hotelId: string, reservationId: string) {
    isLoading.value = true
    error.value = null
    
    try {
      console.log('Setting up development session:', { hotelId, reservationId })
      
      // Fetch hotel details
      const { data: hotelData, error: hotelError } = await supabase
        .from('hotels')
        .select('*')
        .eq('id', hotelId)
        .single()
      
      if (hotelError) {
        throw new Error(`Hotel could not be fetched: ${hotelError.message}`)
      }
      
      // Fetch reservation details
      const { data: reservationData, error: reservationError } = await supabase
        .from('reservations')
        .select('*')
        .eq('id', reservationId)
        .single()
      
      if (reservationError) {
        throw new Error(`Reservation could not be fetched: ${reservationError.message}`)
      }

      // Get room details separately
      const { data: roomData } = await supabase
        .from('rooms')
        .select('room_number, room_type, floor')
        .eq('id', reservationData.room_id)
        .single()

      // Set hotel data
      currentHotel.value = hotelData as Hotel

      // Create guest session
      guestSession.value = {
        id: reservationData.id,
        hotel_id: reservationData.hotel_id,
        room_id: reservationData.room_id,
        guest_name: reservationData.guest_name || 'Dev Guest',
        guest_last_name: reservationData.guest_last_name,
        reservation_number: reservationData.reservation_number,
        check_in_date: reservationData.check_in_date,
        check_out_date: reservationData.check_out_date,
        room_number: roomData?.room_number || 'N/A'
      }
      
      // Save session to localStorage to persist it
      localStorage.setItem('hotelexia_guest_session', JSON.stringify(guestSession.value))
      
      console.log('Development session created successfully:', guestSession.value)
      return { success: true }
    } catch (err: any) {
      console.error('Error during dev session setup:', err)
      error.value = err.message
      guestSession.value = null
      currentHotel.value = null
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  // Calculate days remaining in stay
  const daysRemaining = computed(() => {
    if (!guestSession.value) return 0
    
    const checkoutDate = new Date(guestSession.value.check_out_date)
    const today = new Date()
    const diffTime = checkoutDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  })

  return {
    // State
    guestSession: readonly(guestSession),
    currentHotel: readonly(currentHotel),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Getters
    isAuthenticated,
    currentGuest,
    currentHotelId,
    daysRemaining,

    // Actions
    loginWithReservation,
    fetchHotelDetails,
    logout,
    checkExistingSession,
    clearError,
    setGuestSessionForDevelopment,
    scheduleDataCleanup,
    validateSessionSecurity,
    validateGuestAccess
  }
}) 