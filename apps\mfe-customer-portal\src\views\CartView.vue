<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-4 py-4 sticky top-0 z-40">
      <div class="max-w-md mx-auto">
        <div class="flex items-center justify-between">
          <!-- User Avatar -->
          <div class="flex items-center space-x-3">
            <img 
              src="https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face" 
              alt="User Avatar" 
              class="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <h3 class="font-semibold text-textDark text-sm">Ay<PERSON>e <PERSON>a</h3>
              <p class="text-textMedium text-xs">Oda 205</p>
            </div>
          </div>

          <!-- Page Title -->
          <h1 class="text-xl font-bold text-textDark">Sepet</h1>

          <!-- Cart Total Badge -->
          <div class="bg-teal-500 text-white px-3 py-1 rounded-full">
            <span class="font-semibold text-sm">₺{{ cartStore.cartTotal.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty Cart State -->
    <div v-if="cartStore.isEmpty" class="px-4 py-12">
      <div class="max-w-md mx-auto text-center">
        <!-- Empty Cart Image -->
        <div class="mb-8">
          <img 
            src="https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400&h=400&fit=crop&crop=center" 
            alt="Empty Cart" 
            class="w-64 h-64 mx-auto object-cover rounded-2xl opacity-80"
          />
        </div>
        
        <!-- Empty Cart Message -->
        <h2 class="text-2xl font-bold text-textDark mb-4">Sepetiniz Boş</h2>
        <p class="text-textMedium mb-8">Lezzetli yemekleri keşfetmek için menüye göz atın</p>
        
        <!-- Browse Menu Button -->
        <button 
          @click="goToMenu"
          class="bg-gradient-to-r from-primary to-coral-600 text-white py-4 px-8 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200"
        >
          Menüye Göz At
        </button>
      </div>
    </div>

    <!-- Cart Items (when not empty) -->
    <div v-else class="px-4 py-6 pb-20">
      <div class="max-w-md mx-auto space-y-4">
        <div 
          v-for="item in cartStore.cartItems" 
          :key="`${item.id}-${JSON.stringify(item.customizations || [])}`"
          class="bg-white rounded-xl p-4 shadow-sm"
        >
          <div class="flex space-x-4">
            <!-- Item Image -->
            <div class="w-16 h-16 rounded-xl overflow-hidden flex-shrink-0 relative">
              <img 
                :src="item.image" 
                :alt="item.name"
                class="w-full h-full object-cover"
              />
              <!-- NEW Badge -->
              <div 
                v-if="item.isNew" 
                class="absolute top-1 left-1 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded"
              >
                YENİ
              </div>
            </div>

            <!-- Item Details -->
            <div class="flex-1">
              <h3 class="font-semibold text-textDark mb-1">{{ item.name }}</h3>
              <p class="text-textMedium text-sm mb-2">{{ item.calories }} kcal • {{ item.weight }}g</p>
              
              <!-- Customizations -->
              <div v-if="item.customizations && item.customizations.length > 0" class="mb-2">
                <p class="text-xs text-textMedium">Ekstralar: {{ item.customizations.join(', ') }}</p>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="font-bold text-primary text-lg">₺{{ item.price.toFixed(2) }}</span>
                
                <!-- Quantity Controls -->
                <div class="flex items-center space-x-2">
                  <button 
                    @click="cartStore.decrementQuantity(item.id, item.customizations || [])"
                    class="w-8 h-8 bg-gray-200 text-textDark rounded-lg flex items-center justify-center"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                    </svg>
                  </button>
                  
                  <div class="w-8 h-8 bg-teal-500 text-white rounded-lg flex items-center justify-center font-semibold text-sm">
                    {{ item.quantity }}
                  </div>
                  
                  <button 
                    @click="cartStore.incrementQuantity(item.id, item.customizations || [])"
                    class="w-8 h-8 bg-teal-500 text-white rounded-lg flex items-center justify-center"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Apply Promocode Section -->
        <div class="mt-6">
          <button 
            @click="showPromoCodeLocal = !showPromoCodeLocal"
            class="text-teal-500 font-medium text-sm flex items-center space-x-1"
          >
            <span>Apply promocode</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
          
          <div v-if="showPromoCodeLocal" class="mt-3 flex space-x-2">
            <input 
              v-model="promoCodeInput"
              type="text" 
              placeholder="Promosyon kodu girin"
              class="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
            <button 
              @click="applyPromoCode"
              class="bg-teal-500 text-white px-6 py-3 rounded-xl font-medium hover:bg-teal-600 transition-colors"
            >
              Uygula
            </button>
          </div>
          
          <!-- Applied Promo Code Display -->
          <div v-if="cartStore.promoCode" class="mt-3 bg-green-50 border border-green-200 rounded-xl p-3">
            <div class="flex items-center justify-between">
              <span class="text-green-700 font-medium">{{ cartStore.promoCode }} uygulandı</span>
              <span class="text-green-700 font-bold">-₺{{ cartStore.promoDiscount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="border-2 border-teal-500 rounded-xl p-4 mt-6">
          <div class="space-y-3">
            <div class="flex justify-between text-textDark">
              <span class="font-medium">Ara Toplam</span>
              <span class="font-bold">₺{{ cartStore.subtotal.toFixed(2) }}</span>
            </div>
            <div class="flex justify-between text-textMedium">
              <span>Teslimat</span>
              <span>₺{{ cartStore.deliveryFee.toFixed(2) }}</span>
            </div>
            <div v-if="cartStore.promoDiscount > 0" class="flex justify-between text-green-600">
              <span>İndirim</span>
              <span>-₺{{ cartStore.promoDiscount.toFixed(2) }}</span>
            </div>
            <hr class="border-gray-200">
            <div class="flex justify-between font-bold text-textDark text-xl">
              <span>Toplam</span>
              <span>₺{{ cartStore.cartTotal.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- Checkout Button -->
        <button 
          @click="proceedToCheckout"
          :disabled="isProcessingOrder"
          :class="[
            'w-full py-4 px-6 rounded-xl font-bold text-lg mt-6 shadow-lg transition-all duration-200',
            isProcessingOrder 
              ? 'bg-gray-400 text-white cursor-not-allowed' 
              : 'bg-teal-500 hover:bg-teal-600 text-white'
          ]"
        >
          <div class="flex items-center justify-center space-x-3">
            <!-- Loading Spinner -->
            <div v-if="isProcessingOrder" class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-3a2 2 0 00-2-2H9a2 2 0 00-2 2v3a2 2 0 002 2z"/>
            </svg>
            <span>{{ isProcessingOrder ? 'İşleniyor...' : 'Siparişi Tamamla' }}</span>
          </div>
        </button>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cartStore'

const router = useRouter()

// Cart store
const cartStore = useCartStore()

// Loading state
const isProcessingOrder = ref(false)
const showPromoCodeLocal = ref(false)
const promoCodeInput = ref('')

// Methods
const goToMenu = () => {
  router.push('/room-service-menu')
}

const applyPromoCode = () => {
  if (cartStore.applyPromoCode(promoCodeInput.value)) {
    showPromoCodeLocal.value = false
    promoCodeInput.value = ''
  } else {
    alert('Geçersiz promosyon kodu! Deneyin: HOTELEXIA10, WELCOME20, FIRST15')
  }
}

const proceedToCheckout = async () => {
  try {
    // Set loading state
    isProcessingOrder.value = true
    
    // Simulate order processing with random success/failure
    const orderTotal = cartStore.cartTotal
    
    // Simulate API call delay
    const processingDelay = new Promise(resolve => setTimeout(resolve, 2000))
    await processingDelay
    
    // Simulate random failure (20% chance for demo purposes)
    const isSuccess = Math.random() > 0.2
    
    if (isSuccess) {
      // Clear cart on successful order
      cartStore.clearCart()
      
      // Navigate to order success page
      router.push({
        name: 'OrderSuccess',
        query: {
          total: orderTotal.toString()
        }
      })
    } else {
      // Navigate to order failed page with error details
      router.push({
        name: 'OrderFailed',
        query: {
          message: 'Ödeme işlemi başarısız. Kartınızda yeterli bakiye bulunmuyor.',
          code: 'INSUFFICIENT_FUNDS'
        }
      })
    }
  } catch (error) {
    // Handle unexpected errors
    router.push({
      name: 'OrderFailed',
      query: {
        message: 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.',
        code: 'UNKNOWN_ERROR'
      }
    })
  } finally {
    // Reset loading state
    isProcessingOrder.value = false
  }
}
</script>

<style scoped>
.coral-600 {
  background-color: #ff5656;
}

.coral-700 {
  background-color: #e74c3c;
}
</style> 