import { describe, it, expect } from 'vitest'

// Simple validation utilities to test
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPhone(phone: string): boolean {
  // Turkish phone number validation
  const phoneRegex = /^(\+90|0)?[5][0-9]{9}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

export function isValidPassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  return passwordRegex.test(password)
}

export function isValidHotelName(name: string): boolean {
  return name.trim().length >= 2 && name.trim().length <= 100
}

describe('Validation Utilities', () => {
  describe('isValidEmail', () => {
    it('validates correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
    })

    it('rejects invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
      expect(isValidEmail('@domain.com')).toBe(false)
      expect(isValidEmail('test.domain.com')).toBe(false)
      expect(isValidEmail('')).toBe(false)
    })
  })

  describe('isValidPhone', () => {
    it('validates Turkish phone numbers', () => {
      expect(isValidPhone('05551234567')).toBe(true)
      expect(isValidPhone('+905551234567')).toBe(true)
      expect(isValidPhone('5551234567')).toBe(true)
      expect(isValidPhone('0555 123 45 67')).toBe(true)
    })

    it('rejects invalid phone numbers', () => {
      expect(isValidPhone('1234567890')).toBe(false)
      expect(isValidPhone('05551234')).toBe(false)
      expect(isValidPhone('06551234567')).toBe(false)
      expect(isValidPhone('')).toBe(false)
    })
  })

  describe('isValidPassword', () => {
    it('validates strong passwords', () => {
      expect(isValidPassword('Password123')).toBe(true)
      expect(isValidPassword('MySecure1Pass')).toBe(true)
      expect(isValidPassword('Admin2024!')).toBe(true)
    })

    it('rejects weak passwords', () => {
      expect(isValidPassword('password')).toBe(false) // no uppercase, no number
      expect(isValidPassword('PASSWORD')).toBe(false) // no lowercase, no number
      expect(isValidPassword('Password')).toBe(false) // no number
      expect(isValidPassword('Pass1')).toBe(false) // too short
      expect(isValidPassword('')).toBe(false)
    })
  })

  describe('isValidHotelName', () => {
    it('validates proper hotel names', () => {
      expect(isValidHotelName('Grand Hotel')).toBe(true)
      expect(isValidHotelName('Hilton Istanbul')).toBe(true)
      expect(isValidHotelName('AB')).toBe(true) // minimum length
    })

    it('rejects invalid hotel names', () => {
      expect(isValidHotelName('A')).toBe(false) // too short
      expect(isValidHotelName('')).toBe(false)
      expect(isValidHotelName('   ')).toBe(false) // only spaces
      expect(isValidHotelName('A'.repeat(101))).toBe(false) // too long
    })
  })
})
