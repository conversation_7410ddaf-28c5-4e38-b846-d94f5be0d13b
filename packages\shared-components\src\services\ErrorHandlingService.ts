/**
 * Enhanced Error Handling Service
 * Provides consistent error handling, user feedback, and error recovery across all MFEs
 */

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  hotelId?: string
  timestamp: number
  url: string
  userAgent: string
}

export interface ErrorHandlingOptions {
  showToast?: boolean
  showModal?: boolean
  logToConsole?: boolean
  reportToService?: boolean
  retryable?: boolean
  fallbackData?: any
  customMessage?: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
}

export interface ErrorReport {
  id: string
  error: Error
  context: ErrorContext
  severity: 'low' | 'medium' | 'high' | 'critical'
  handled: boolean
  timestamp: number
}

export type ErrorHandler = (error: Error, context: ErrorContext) => void | Promise<void>

class ErrorHandlingService {
  private errorHandlers = new Map<string, ErrorHandler[]>()
  private errorReports: ErrorReport[] = []
  private maxReports = 100

  // Default error messages
  private defaultMessages = {
    network: 'Ağ bağlantısı hatası. Lütfen internet bağlantınızı kontrol edin.',
    auth: 'Kimlik doğrulama hatası. Lütfen tekrar giriş yapın.',
    permission: 'Bu işlem için yetkiniz bulunmuyor.',
    validation: 'Girilen bilgiler geçersiz. Lütfen kontrol edin.',
    server: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.',
    timeout: 'İşlem zaman aşımına uğradı. Lütfen tekrar deneyin.',
    unknown: 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.'
  }

  /**
   * Handle an error with comprehensive error processing
   */
  async handleError(
    error: Error,
    context: Partial<ErrorContext> = {},
    options: ErrorHandlingOptions = {}
  ): Promise<void> {
    const {
      showToast = true,
      showModal = false,
      logToConsole = true,
      reportToService = true,
      retryable = false,
      fallbackData = null,
      customMessage
    } = options

    // Create full context
    const fullContext: ErrorContext = {
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      ...context
    }

    // Determine error severity
    const severity = this.determineSeverity(error)

    // Create error report
    const report: ErrorReport = {
      id: this.generateErrorId(),
      error,
      context: fullContext,
      severity,
      handled: true,
      timestamp: Date.now()
    }

    // Store error report
    this.storeErrorReport(report)

    // Log to console if requested
    if (logToConsole) {
      this.logError(error, fullContext, severity)
    }

    // Get user-friendly message
    const userMessage = customMessage || this.getUserFriendlyMessage(error)

    // Show user feedback
    if (showToast) {
      this.showToast(userMessage, severity)
    }

    if (showModal) {
      this.showModal(userMessage, error, retryable)
    }

    // Call registered error handlers
    await this.callErrorHandlers(error, fullContext)

    // Report to monitoring service
    if (reportToService && severity !== 'low') {
      await this.reportToMonitoringService(report)
    }
  }

  /**
   * Register an error handler for specific error types
   */
  registerHandler(errorType: string, handler: ErrorHandler): void {
    if (!this.errorHandlers.has(errorType)) {
      this.errorHandlers.set(errorType, [])
    }
    this.errorHandlers.get(errorType)!.push(handler)
  }

  /**
   * Unregister an error handler
   */
  unregisterHandler(errorType: string, handler: ErrorHandler): void {
    const handlers = this.errorHandlers.get(errorType)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * Handle Supabase-specific errors
   */
  handleSupabaseError(error: any, context: Partial<ErrorContext> = {}): Promise<void> {
    let processedError: Error
    let options: ErrorHandlingOptions = {}

    // Process Supabase error codes
    if (error?.code) {
      switch (error.code) {
        case 'PGRST116': // No rows returned
          processedError = new Error('Veri bulunamadı')
          options = { severity: 'low', showToast: false }
          break
        case 'PGRST301': // Permission denied
          processedError = new Error('Bu işlem için yetkiniz bulunmuyor')
          options = { severity: 'high', customMessage: this.defaultMessages.permission }
          break
        case '42501': // Insufficient privilege
          processedError = new Error('Yetersiz yetki')
          options = { severity: 'high', customMessage: this.defaultMessages.permission }
          break
        case '23505': // Unique violation
          processedError = new Error('Bu kayıt zaten mevcut')
          options = { severity: 'medium' }
          break
        case '23503': // Foreign key violation
          processedError = new Error('İlişkili kayıtlar nedeniyle işlem gerçekleştirilemedi')
          options = { severity: 'medium' }
          break
        default:
          processedError = new Error(error.message || 'Veritabanı hatası')
          options = { severity: 'medium', customMessage: this.defaultMessages.server }
      }
    } else {
      processedError = error instanceof Error ? error : new Error(String(error))
      options = { severity: 'medium' }
    }

    return this.handleError(processedError, { ...context, action: 'supabase_query' }, options)
  }

  /**
   * Handle network errors
   */
  handleNetworkError(error: Error, context: Partial<ErrorContext> = {}): Promise<void> {
    return this.handleError(error, { ...context, action: 'network_request' }, {
      customMessage: this.defaultMessages.network,
      retryable: true,
      severity: 'high'
    })
  }

  /**
   * Handle authentication errors
   */
  handleAuthError(error: Error, context: Partial<ErrorContext> = {}): Promise<void> {
    return this.handleError(error, { ...context, action: 'authentication' }, {
      customMessage: this.defaultMessages.auth,
      severity: 'critical',
      showModal: true
    })
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number
    bySeverity: Record<string, number>
    recent: ErrorReport[]
  } {
    const bySeverity = this.errorReports.reduce((acc, report) => {
      acc[report.severity] = (acc[report.severity] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recent = this.errorReports
      .slice(-10)
      .sort((a, b) => b.timestamp - a.timestamp)

    return {
      total: this.errorReports.length,
      bySeverity,
      recent
    }
  }

  /**
   * Clear error reports
   */
  clearErrorReports(): void {
    this.errorReports = []
  }

  /**
   * Determine error severity based on error type and message
   */
  private determineSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const message = error.message.toLowerCase()

    if (message.includes('network') || message.includes('fetch')) {
      return 'high'
    }
    if (message.includes('auth') || message.includes('permission')) {
      return 'critical'
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'medium'
    }
    if (message.includes('not found') || message.includes('no data')) {
      return 'low'
    }

    return 'medium'
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Store error report
   */
  private storeErrorReport(report: ErrorReport): void {
    this.errorReports.push(report)

    // Keep only the most recent reports
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(-this.maxReports)
    }
  }

  /**
   * Log error to console with formatting
   */
  private logError(error: Error, context: ErrorContext, severity: string): void {
    const style = severity === 'critical' ? 'color: red; font-weight: bold;' :
                  severity === 'high' ? 'color: orange; font-weight: bold;' :
                  severity === 'medium' ? 'color: yellow;' : 'color: gray;'

    console.group(`%c[${severity.toUpperCase()}] Error in ${context.component || 'Unknown'}`, style)
    console.error('Error:', error)
    console.log('Context:', context)
    console.log('Stack:', error.stack)
    console.groupEnd()
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: Error): string {
    const message = error.message.toLowerCase()

    if (message.includes('network') || message.includes('fetch')) {
      return this.defaultMessages.network
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return this.defaultMessages.auth
    }
    if (message.includes('permission') || message.includes('forbidden')) {
      return this.defaultMessages.permission
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return this.defaultMessages.validation
    }
    if (message.includes('timeout')) {
      return this.defaultMessages.timeout
    }
    if (message.includes('server') || message.includes('internal')) {
      return this.defaultMessages.server
    }

    return this.defaultMessages.unknown
  }

  /**
   * Show toast notification
   */
  private showToast(message: string, severity: string): void {
    // This would integrate with your toast notification system
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('show-toast', {
        detail: { message, type: severity }
      }))
    }
  }

  /**
   * Show error modal
   */
  private showModal(message: string, error: Error, retryable: boolean): void {
    // This would integrate with your modal system
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('show-error-modal', {
        detail: { message, error: error.message, retryable }
      }))
    }
  }

  /**
   * Call registered error handlers
   */
  private async callErrorHandlers(error: Error, context: ErrorContext): Promise<void> {
    const errorType = error.constructor.name
    const handlers = this.errorHandlers.get(errorType) || []

    for (const handler of handlers) {
      try {
        await handler(error, context)
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError)
      }
    }
  }

  /**
   * Report to monitoring service
   */
  private async reportToMonitoringService(report: ErrorReport): Promise<void> {
    try {
      // This would integrate with your monitoring service (Sentry, LogRocket, etc.)
      console.log('Would report to monitoring service:', report)
    } catch (error) {
      console.error('Failed to report error to monitoring service:', error)
    }
  }
}

// Export singleton instance
export const errorHandlingService = new ErrorHandlingService()
export default errorHandlingService
