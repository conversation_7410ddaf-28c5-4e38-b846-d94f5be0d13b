<template>
  <div>
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-bold text-navy-700 dark:text-white mb-2">
          Oda Servisi Kategorileri
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          Otel oda servisi için kategori yönetimi
        </p>
      </div>
      <button 
        @click="openCreateModal"
        class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center"
      >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Yeni <PERSON>gori <PERSON>
      </button>
    </div>

    <!-- Categories Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-200 dark:border-gray-700">
            <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">
              Görsel
            </th>
            <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">
              Kategori Adı
            </th>
            <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">
              Sıra
            </th>
            <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">
              Eylemler
            </th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="category in roomServiceCategories" 
            :key="category.id"
            class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-navy-700"
          >
            <td class="py-4 px-4">
              <div class="w-8 h-8 rounded-lg overflow-hidden">
                <img 
                  :src="category.image_url || 'https://via.placeholder.com/32x32.png?text=' + category.name.charAt(0)" 
                  :alt="category.name"
                  class="w-full h-full object-cover"
                />
              </div>
            </td>
            <td class="py-4 px-4 font-medium text-navy-700 dark:text-white">
              {{ category.name }}
            </td>
            <td class="py-4 px-4 text-gray-600 dark:text-gray-400">
              {{ category.display_order || 0 }}
            </td>
            <td class="py-4 px-4">
              <div class="flex space-x-2">
                <button
                  @click="openEditModal(category)"
                  class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  Düzenle
                </button>
                <button
                  @click="confirmDelete(category)"
                  class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  Sil
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Empty State -->
      <div v-if="roomServiceCategories.length === 0" class="text-center py-12">
        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        <h3 class="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
          Henüz kategori bulunmuyor
        </h3>
        <p class="text-gray-500 dark:text-gray-500 mb-4">
          İlk oda servisi kategorinizi oluşturun
        </p>
        <button 
          @click="openCreateModal"
          class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          Yeni Kategori Oluştur
        </button>
      </div>
    </div>

    <!-- Category Modal -->
    <CategoryModal
      v-if="showModal"
      :category="editingCategory"
      category-type="ROOM_SERVICE"
      @close="closeModal"
      @save="saveCategory"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import type { ServiceCategory } from '@/types/management'
import CategoryModal from '@/components/modals/CategoryModal.vue'

const managementStore = useManagementStore()

// Computed
const roomServiceCategories = computed(() => 
  managementStore.categories.filter(category => category.type === 'ROOM_SERVICE')
)

// State
const showModal = ref(false)
const editingCategory = ref<ServiceCategory | null>(null)

// Methods
const openCreateModal = () => {
  editingCategory.value = null
  showModal.value = true
}

const openEditModal = (category: ServiceCategory) => {
  editingCategory.value = category
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  editingCategory.value = null
}

const saveCategory = async (categoryData: Omit<ServiceCategory, 'id'> | ServiceCategory) => {
  const currentHotelId = '4dfec6ed-6ad4-4591-b60c-4f68c74ec150' // TODO: Get from auth store
  
  try {
    if (editingCategory.value) {
      // Update existing category
      await managementStore.updateCategory(categoryData as ServiceCategory, currentHotelId)
    } else {
      // Create new category
      await managementStore.addCategory(categoryData as Omit<ServiceCategory, 'id'>, currentHotelId)
    }
    closeModal()
  } catch (error) {
    console.error('Error saving category:', error)
    alert('Kategori kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.')
  }
}

const confirmDelete = async (category: ServiceCategory) => {
  const currentHotelId = '4dfec6ed-6ad4-4591-b60c-4f68c74ec150' // TODO: Get from auth store
  
  if (confirm(`"${category.name}" kategorisini silmek istediğinizden emin misiniz?`)) {
    try {
      await managementStore.deleteCategory(category.id, currentHotelId)
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('Kategori silinirken bir hata oluştu. Lütfen tekrar deneyin.')
    }
  }
}
</script> 