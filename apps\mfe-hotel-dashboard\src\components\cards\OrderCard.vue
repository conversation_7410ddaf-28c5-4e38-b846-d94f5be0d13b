<template>
  <div class="bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow">
    <!-- Order Header -->
    <div class="flex justify-between items-start mb-3">
      <div>
        <h4 class="font-semibold text-navy-700 dark:text-white text-sm">
          #{{ order.id.slice(-6) }} - Oda: {{ order.roomNumber }}
        </h4>
        <p class="text-gray-600 dark:text-gray-400 text-xs">{{ order.guestName }}</p>
      </div>
      <div class="text-right">
        <p class="font-bold text-navy-700 dark:text-white text-sm">₺{{ order.totalPrice.toFixed(2) }}</p>
        <p class="text-xs text-gray-500 dark:text-gray-400">{{ timeAgo(order.createdAt) }}</p>
      </div>
    </div>

    <!-- Order Items Summary -->
    <div class="mb-3">
      <p class="text-xs text-gray-600 dark:text-gray-400 mb-1"><PERSON><PERSON><PERSON><PERSON> İçeriği:</p>
      <div class="space-y-1">
        <p 
          v-for="item in order.items.slice(0, 2)" 
          :key="item.id"
          class="text-xs text-gray-700 dark:text-gray-300"
        >
          {{ item.quantity }}x {{ item.menuItemName }}
        </p>
        <p v-if="order.items.length > 2" class="text-xs text-gray-500 dark:text-gray-400 italic">
          +{{ order.items.length - 2 }} item daha...
        </p>
      </div>
    </div>

    <!-- Notes -->
    <div v-if="order.notes" class="mb-3">
      <p class="text-xs text-gray-600 dark:text-gray-400 mb-1">Not:</p>
      <p class="text-xs text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-2 rounded">
        {{ order.notes }}
      </p>
    </div>

    <!-- Actions -->
    <div v-if="showActions && actions && actions.length > 0" class="space-y-2">
      <button
        v-for="action in actions"
        :key="action.label"
        @click="action.action"
        :class="getActionButtonClass(action.color)"
        class="w-full px-3 py-2 rounded-lg text-xs font-medium transition-colors"
      >
        {{ action.label }}
      </button>
    </div>

    <!-- Status Badge (for completed/cancelled orders) -->
    <div v-if="!showActions" class="flex justify-center">
      <span :class="getStatusBadgeClass(order.status)" class="px-3 py-1 rounded-full text-xs font-medium">
        {{ getStatusText(order.status) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RoomServiceOrder } from '@/types/management'

interface ActionButton {
  label: string
  action: () => void
  color: 'blue' | 'yellow' | 'green' | 'red'
}

interface Props {
  order: RoomServiceOrder
  showActions?: boolean
  actions?: ActionButton[]
}

defineProps<Props>()

defineEmits<{
  updateStatus: [orderId: string, newStatus: RoomServiceOrder['status']]
}>()

// Helper function to format time ago
const timeAgo = (date: Date): string => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Az önce'
  if (diffInMinutes < 60) return `${diffInMinutes} dakika önce`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours} saat önce`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays} gün önce`
}

// Helper function to get action button class
const getActionButtonClass = (color: string): string => {
  const baseClasses = 'w-full px-3 py-2 rounded-lg text-xs font-medium transition-colors'
  
  switch (color) {
    case 'blue':
      return `${baseClasses} bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/40`
    case 'yellow':
      return `${baseClasses} bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:hover:bg-yellow-900/40`
    case 'green':
      return `${baseClasses} bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/40`
    case 'red':
      return `${baseClasses} bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/40`
    default:
      return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600`
  }
}

// Helper function to get status badge class
const getStatusBadgeClass = (status: RoomServiceOrder['status']): string => {
  switch (status) {
    case 'DELIVERED':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300'
    case 'CANCELLED':
      return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
  }
}

// Helper function to get status text
const getStatusText = (status: RoomServiceOrder['status']): string => {
  switch (status) {
    case 'NEW':
      return 'Yeni'
    case 'PREPARING':
      return 'Hazırlanıyor'
    case 'ON_THE_WAY':
      return 'Yolda'
    case 'DELIVERED':
      return 'Teslim Edildi'
    case 'CANCELLED':
      return 'İptal Edildi'
    default:
      return status
  }
}
</script> 