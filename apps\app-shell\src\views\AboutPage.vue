<template>
  <div class="min-h-screen bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-16">
        <h1 class="text-4xl font-bold text-gray-900 mb-6">Hakkımızda</h1>
        <p class="text-xl text-gray-600">Hotelexia - Modern Otel Yönetim Platformu</p>
      </div>

      <div class="prose prose-lg mx-auto">
        <h2>Proje <PERSON></h2>
        <p>
          Hotelexia, otel endüstrisi için kapsamlı bir web tabanlı SaaS (Software as a Service) platformu geliştirme projesidir. 
          Platform, operasyonel verimliliği artırmayı, misafir deneyimini iyileştirmeyi ve otellere modern yönetim araçları 
          sağlamayı amaçlamaktadır.
        </p>

        <h2>Teknik Mimari</h2>
        <p>
          <PERSON><PERSON>, mikro frontend (MFE) mimarisi kullanılarak geliştirilmektedir. Bu yaklaşım sayesinde:
        </p>
        <ul>
          <li>Modüler ve ölçeklenebilir yapı</li>
          <li>Bağımsız geliştirme ve deployment</li>
          <li>Teknoloji çeşitliliği desteği</li>
          <li>Kolay bakım ve güncelleme</li>
        </ul>

        <h2>Teknoloji Stack</h2>
        <div class="grid md:grid-cols-2 gap-6 not-prose">
          <div class="bg-blue-50 p-6 rounded-lg">
            <h3 class="font-bold text-lg mb-3">Frontend</h3>
            <ul class="space-y-2 text-sm">
              <li>• Vue 3 + Composition API</li>
              <li>• TypeScript</li>
              <li>• Tailwind CSS</li>
              <li>• Vite Build Tool</li>
              <li>• Pinia State Management</li>
            </ul>
          </div>
          <div class="bg-green-50 p-6 rounded-lg">
            <h3 class="font-bold text-lg mb-3">Backend</h3>
            <ul class="space-y-2 text-sm">
              <li>• Supabase</li>
              <li>• PostgreSQL</li>
              <li>• Row Level Security</li>
              <li>• Edge Functions</li>
              <li>• Real-time Subscriptions</li>
            </ul>
          </div>
        </div>

        <h2>Geliştirme Yaklaşımı</h2>
        <p>
          Proje, iteratif geliştirme metodolojisi ile MVP (Minimum Viable Product) odaklı olarak geliştirilmektedir. 
          Her modül önce temel fonksiyonlarla hayata geçirilmekte, sonrasında geri bildirimler doğrultusunda 
          artırımsal iyileştirmeler yapılmaktadır.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// About page component
</script> 