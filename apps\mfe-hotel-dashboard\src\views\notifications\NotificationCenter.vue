<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Bil<PERSON><PERSON></h1>
      <p class="text-gray-600 dark:text-gray-300">Misa<PERSON><PERSON><PERSON> bildirim gönderme ve takip sistemi</p>
    </div>

    <!-- Tabs Navigation -->
    <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
      <nav class="flex border-b border-gray-200 dark:border-gray-700">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          class="px-6 py-4 text-sm font-medium transition-colors"
          :class="[
            activeTab === tab.id
              ? 'border-b-2 border-brand-500 text-brand-600 dark:text-brand-400'
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          ]"
        >
          <component :is="tab.icon" class="w-5 h-5 inline-block mr-2" />
          {{ tab.name }}
        </button>
      </nav>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- Personel Bildirimleri -->
        <NotificationsView v-if="activeTab === 'notifications'" />

        <!-- Yeni Bildirim Oluştur -->
        <ComposeNotification v-if="activeTab === 'compose'" />

        <!-- Gönderim Geçmişi -->
        <SentHistory v-if="activeTab === 'sent-history'" />

        <!-- Misafir Bildirimleri -->
        <GuestHistory v-if="activeTab === 'guest-history'" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  PencilSquareIcon,
  ClockIcon,
  UserIcon,
  BellIcon
} from '@heroicons/vue/24/outline'
import ComposeNotification from './ComposeNotification.vue'
import SentHistory from './SentHistory.vue'
import GuestHistory from './GuestHistory.vue'
import NotificationsView from './NotificationsView.vue'

const activeTab = ref('notifications')

const tabs = [
  {
    id: 'notifications',
    name: 'Personel Bildirimleri',
    icon: BellIcon
  },
  {
    id: 'compose',
    name: 'Yeni Bildirim Oluştur',
    icon: PencilSquareIcon
  },
  {
    id: 'sent-history',
    name: 'Gönderim Geçmişi',
    icon: ClockIcon
  },
  {
    id: 'guest-history',
    name: 'Misafir Bildirimleri',
    icon: UserIcon
  }
]
</script> 