<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-white dark:bg-navy-700 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-600">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
          {{ service ? 'Hizmeti Düzenle' : 'Ye<PERSON> Hizmet Ekle' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
        >
          <XMarkIcon class="w-6 h-6" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="saveService" class="p-6 space-y-4">
        <!-- Service Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Hiz<PERSON> Adı *
          </label>
          <input
            v-model="formData.name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-600 dark:text-white"
            placeholder="Hizmet adını girin"
          >
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Açıklama *
          </label>
          <textarea
            v-model="formData.description"
            required
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-600 dark:text-white resize-none"
            placeholder="Hizmet açıklamasını girin"
          ></textarea>
        </div>

        <!-- Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Kategori *
          </label>
          <select
            v-model="formData.categoryId"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-600 dark:text-white"
          >
            <option value="">Kategori seçin</option>
            <option
              v-for="category in guestServiceCategories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- Active Status -->
        <div class="flex items-center">
          <input
            v-model="formData.isActive"
            type="checkbox"
            id="isActive"
            class="w-4 h-4 text-brand-600 bg-gray-100 border-gray-300 rounded focus:ring-brand-500 dark:focus:ring-brand-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          >
          <label for="isActive" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Bu hizmet aktif olsun
          </label>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg transition-colors"
          >
            İptal
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 rounded-lg transition-colors"
          >
            {{ service ? 'Güncelle' : 'Kaydet' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'
import type { GuestService, CreateGuestServiceData } from '@/types/management'

interface Props {
  service?: GuestService | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: CreateGuestServiceData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const store = useManagementStore()

// Form data
const formData = reactive<CreateGuestServiceData>({
  name: '',
  description: '',
  categoryId: '',
  isActive: true
})

// Computed properties
const guestServiceCategories = computed(() => 
  store.categories.filter(cat => cat.type === 'GUEST_SERVICE')
)

// Methods
const saveService = () => {
  if (!formData.name.trim() || !formData.description.trim() || !formData.categoryId) {
    return
  }

  emit('save', { ...formData })
}

// Initialize form data when editing
onMounted(() => {
  if (props.service) {
    formData.name = props.service.name
    formData.description = props.service.description
    formData.categoryId = props.service.categoryId
    formData.isActive = props.service.isActive
  }
})
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style> 