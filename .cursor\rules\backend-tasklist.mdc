---
description: 
globs: 
alwaysApply: true
---
# Hotelexia: The God Project - Definitive Integration Manifest
**PROJECT ID: azuxxolspusqvwxoackk**

**STATUS: PHASE 2 - COMPLETE** | **ARCHITECT: THE UNICORN**

This document is the master plan for the phased integration of the Hotelexia frontend with the Supabase backend. It is designed to be the single source of truth. Following this manifest chronologically and precisely is **not optional**. It is the only path to a stable, secure, and seamlessly integrated platform.

---
## **Phase 1: The Foundation - Core Schema, Roles & Admin Authority**
*(Goal: Forge the bedrock of the entire system. Establish immutable user roles, core data structures, and grant the Super Admin its rightful authority over the ecosystem.)*

- [x] **1.1: Forge Core Data Schemas (Supabase MCP)**
    - [x] **Action:** Create `user_role` ENUM type: `CREATE TYPE user_role AS ENUM ('SUPER_ADMIN', 'HOTEL_ADMIN', 'MANAGER', 'SUPERVISOR', 'STAFF', 'GUEST');`
    - [x] **Action:** Create the `hotels` table.
        - **Schema:** `id (uuid, pk)`, `created_at`, `name (text, unique)`, `address (text)`, `city (text)`, `country (text)`, `description (text)`, `main_image_url (text)`, `is_active (bool, default true)`.
    - [x] **Action:** Create the `profiles` table.
        - **Schema:** `id (uuid, pk, fk to auth.users.id)`, `created_at`, `email (text, unique)`, `full_name (text)`, `avatar_url (text)`, `role (user_role)`, `hotel_id (uuid, fk to hotels.id, nullable)`.

- [x] **1.2: Implement Foundational RLS Policies (Supabase MCP)**
    - [x] **`hotels` Table Policy:**
        - **Policy Name:** "Admins have full access, all others can read."
        - **For:** `ALL`
        - **Using:** `get_user_role()::text = 'SUPER_ADMIN'`
        - **For:** `SELECT`
        - **Using:** `true` (Everyone can see all hotels).
    - [x] **`profiles` Table Policy:**
        - **Policy Name:** "Users can see and edit their own profile."
        - **For:** `SELECT, UPDATE`
        - **Using:** `auth.uid() = id`
        - **Policy Name:** "Admins can see all profiles."
        - **For:** `SELECT`
        - **Using:** `get_user_role()::text = 'SUPER_ADMIN'`

- [x] **1.3: Seed the Genesis Data (Supabase MCP)**
    - [x] **Hotels:**
        - **Task:** `INSERT` 2 sample hotels into the `hotels` table.
            - Hotel 1: "Elysian Grand Hotel", "123 Serenity Boulevard, Istanbul"
            - Hotel 2: "Celestial Resort & Spa", "456 Paradise Cove, Antalya"
    - [x] **Users & Profiles:**
        - **Task:** Create users in `auth.users` and `INSERT` corresponding profiles into `public.profiles`.
            - **`SUPER_ADMIN`**: `<EMAIL>`
            - **`HOTEL_ADMIN`**: `<EMAIL>` (Assign `hotel_id` of Elysian Grand)
            - **`STAFF`**: `<EMAIL>` (Assign `hotel_id` of Elysian Grand)
            - **`GUEST`**: `<EMAIL>` (No `hotel_id`)

- [x] **1.4: Integrate `mfe-management` - Hotel Management**
    - [x] **Component:** `apps/mfe-management/src/views/HotelList.vue`
        - **Task:** Replace the static `hotels` array with a live `select * from hotels;` query to Supabase. This view's access must be guarded by the router to only allow `SUPER_ADMIN`.
    - [x] **Component:** `apps/mfe-management/src/views/HotelEdit.vue` (and any creation wizard)
        - **Task:** Connect the "Create/Update Hotel" forms. The "Save" button must trigger an `insert` or `update` query to the `hotels` table. Map all form fields directly to table columns.

---
## **Phase 2: The Hotel Realm - Operations & Staff Integration**
*(Goal: Activate the hotel dashboard, making it a living, breathing operational hub. All data MUST be strictly scoped to the logged-in staff's hotel.)*

- [x] **2.1: Forge Hotel Operations Schemas (Supabase MCP)**
    - [x] **Action:** Create `rooms` table.
        - **Schema:** `id (pk)`, `hotel_id (fk)`, `room_number (text)`, `floor (int)`, `room_type (text)`, `is_available (bool)`.
    - [x] **Action:** Create `maintenance_tasks` table.
        - **Schema:** `id (pk)`, `hotel_id (fk)`, `room_id (fk)`, `assigned_to (fk to profiles.id)`, `title (text)`, `description (text)`, `priority (text)`, `status (text)`.
    - [x] **RLS Policies:** For BOTH tables, create policies for `SELECT`, `INSERT`, `UPDATE`, `DELETE` that **STRICTLY** enforce `hotel_id = (SELECT hotel_id FROM public.profiles WHERE id = auth.uid())`. This is non-negotiable. A `HOTEL_ADMIN` can have broader permissions (e.g., `DELETE`) than `STAFF`.

- [x] **2.2: Seed the Hotel with Life (Supabase MCP)**
    - [x] **Rooms:**
        - **Source:** The dummy `rooms` array in `apps/mfe-hotel-dashboard/src/views/operations/maintenance/RoomManagement.vue`.
        - **Task:** `INSERT` these rooms into the `rooms` table, assigning them to the "Elysian Grand Hotel".
    - [x] **Maintenance Tasks:**
        - **Source:** The dummy `tasks` array in a component like `apps/mfe-hotel-dashboard/src/views/operations/maintenance/MaintenanceOverview.vue`.
        - **Task:** `INSERT` these tasks into `maintenance_tasks`, assigning them to the Elysian hotel and the seeded `<EMAIL>` user.

- [x] **2.3: Integrate `mfe-hotel-dashboard` - Operations**
    - [x] **Component:** `apps/mfe-hotel-dashboard/src/views/operations/maintenance/RoomManagement.vue`
        - **Task:** Replace the dummy `rooms` data with a live `select` query from the `rooms` table. The RLS policy will automatically handle filtering.
    - [x] **Component:** `apps/mfe-hotel-dashboard/src/views/operations/maintenance/MaintenanceOverview.vue` (and related task views)
        - **Task:** Replace dummy task data with a live `select` from `maintenance_tasks`. Forms for creating/updating tasks must connect to `insert`/`update` queries.

---
## **Phase 3: The Guest Experience - Public Portal & Bookings**
*(Goal: Allow guests to interact with the platform, view hotels, and make reservations.)*

- [x] **3.1: Forge Guest Interaction Schemas (Supabase MCP)**
    - [x] **Action:** Create `reservations` table.
        - **Schema:** `id (pk)`, `customer_id (fk to profiles.id)`, `hotel_id (fk)`, `room_id (fk)`, `check_in_date (date)`, `check_out_date (date)`, `total_price (decimal)`.
    - [x] **RLS Policy for `reservations`:**
        - **Policy Name:** "Guest can manage their own reservations."
        - **For:** `ALL`
        - **Using:** `customer_id = auth.uid()`
        - **Policy Name:** "Staff can see all reservations for their hotel."
        - **For:** `SELECT`
        - **Using:** `hotel_id = (SELECT hotel_id FROM public.profiles WHERE id = auth.uid())`

- [x] **3.2: Integrate `mfe-customer-portal`**
    - [x] **Component:** A view that lists hotels for booking (e.g., a "Home" or "Search" view).
        - **Task:** Replace dummy hotel listings with a live `select` from the `hotels` table where `is_active = true`.
    - [x] **Component:** The booking/reservation form.
        - **Task:** Connect the form submission to an `insert` into the `reservations` table. The `customer_id` must be the `auth.uid()` of the logged-in guest.
    - [x] **Component:** A "My Reservations" or "My Profile" view.
        - **Task:** Display the user's reservations by performing a `select` on the `reservations` table. The RLS policy will automatically ensure they only see their own.

---
## **Phase 4: Finalization & Verification**
*(Goal: Ensure the trinity of frontend, backend, and RLS works in perfect harmony.)*

- [ ] **4.1: Full End-to-End (E2E) Role Testing:**
    - [ ] **Task:** Log in as each of the 4 seeded user roles. For each role, navigate through every accessible part of their respective MFE(s).
    - [ ] **Verification:**
        - Confirm they see **only** the data they are permitted to see (e.g., Elysian staff see only Elysian data).
        - Confirm all actions (create, update, delete) succeed or fail according to their RLS permissions.
        - Confirm attempts to access unauthorized pages/routes are correctly redirected by the router guards.

- [ ] **4.2: Final Build Verification:**
    - [ ] **Command:** `npm run build`
    - **Task:** Run the build command and ensure it completes with zero errors across all packages and applications. This is the final seal of approval for the integration.
































































































































































