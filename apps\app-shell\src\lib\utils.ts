import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Tailwind CSS sınıflarını birleştirmek ve çakışmaları önlemek için utility fonksiyonu
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Portal tipi belirleme utility'si
 */
export type PortalType = 'customer' | 'hotel' | 'manager'

/**
 * Portal tipine göre tema sınıflarını döndürür
 */
export function getPortalTheme(portal: PortalType) {
  const themes = {
    customer: {
      primary: 'brand-500',
      bg: 'secondaryGray-300',
      accent: 'blue-500',
      text: 'navy-700'
    },
    hotel: {
      primary: 'brand-500', 
      bg: 'navy-900',
      accent: 'orange-500',
      text: 'white'
    },
    manager: {
      primary: 'red-500',
      bg: 'gray-900',
      accent: 'green-500', 
      text: 'white'
    }
  }
  
  return themes[portal]
}

/**
 * Responsive utility'ler
 */
export const breakpoints = {
  xs: '475px',
  sm: '320px',
  '2sm': '380px',
  md: '768px',
  lg: '960px',
  xl: '1200px',
  '2xl': '1600px',
  '3xl': '1920px'
}

/**
 * Color variant helper
 */
export function getColorClasses(variant: 'brand' | 'secondary' | 'success' | 'warning' | 'error' = 'brand') {
  const variants = {
    brand: {
      bg: 'bg-brand-500',
      text: 'text-white',
      border: 'border-brand-500',
      hover: 'hover:bg-brand-600'
    },
    secondary: {
      bg: 'bg-secondaryGray-500',
      text: 'text-white', 
      border: 'border-secondaryGray-500',
      hover: 'hover:bg-secondaryGray-600'
    },
    success: {
      bg: 'bg-green-500',
      text: 'text-white',
      border: 'border-green-500', 
      hover: 'hover:bg-green-600'
    },
    warning: {
      bg: 'bg-orange-500',
      text: 'text-white',
      border: 'border-orange-500',
      hover: 'hover:bg-orange-600'
    },
    error: {
      bg: 'bg-red-500',
      text: 'text-white',
      border: 'border-red-500',
      hover: 'hover:bg-red-600'
    }
  }
  
  return variants[variant]
} 