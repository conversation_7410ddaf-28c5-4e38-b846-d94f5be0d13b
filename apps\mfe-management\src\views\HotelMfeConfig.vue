<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-navy-700 dark:text-white">MFE Yönetimi</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          {{ hotel?.name || 'Otel' }} için erişilebilir özellikleri yönetin
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <button 
          @click="goBack"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors"
        >
          Geri <PERSON>
        </button>
        <button 
          @click="saveConfiguration"
          class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
        >
          Değişiklikleri Kaydet
        </button>
      </div>
    </div>

    <div v-if="!hotel" class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
      <div class="text-center">
        <ExclamationTriangleIcon class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Otel Bulunamadı</h3>
        <p class="text-gray-500 dark:text-gray-400">Belirtilen ID'ye sahip otel bulunamadı.</p>
        <button 
          @click="goBack"
          class="mt-4 px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
        >
          Geri Dön
        </button>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- MFE Configuration -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Available MFEs -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-bold text-navy-700 dark:text-white">Erişilebilir Özellikler</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">
              {{ activeCount }}/{{ totalCount }} aktif
            </span>
          </div>
          
          <div class="space-y-6">
            <div 
              v-for="mfe in mfeList" 
              :key="mfe.key"
              class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors"
            >
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 rounded-lg flex items-center justify-center" :class="mfe.bgColor">
                  <component :is="mfe.icon" class="h-6 w-6" :class="mfe.iconColor" />
                </div>
                <div>
                  <h4 class="font-medium text-navy-700 dark:text-white">{{ mfe.name }}</h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">{{ mfe.description }}</p>
                  <div class="flex items-center space-x-2 mt-1">
                    <span 
                      class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="mfe.planRequired === 'Basic' ? 'bg-blue-100 text-blue-800' : 
                              mfe.planRequired === 'Premium' ? 'bg-purple-100 text-purple-800' : 
                              'bg-orange-100 text-orange-800'"
                    >
                      {{ mfe.planRequired }}+ Plan
                    </span>
                    <span v-if="mfe.isNew" class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                      YENİ
                    </span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ mfeConfig[mfe.key] ? 'Aktif' : 'Pasif' }}
                </span>
                <button
                  @click="toggleMfe(mfe.key)"
                  :disabled="!canEnableMfe(mfe)"
                  class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="mfeConfig[mfe.key] ? 'bg-green-500' : 'bg-gray-300'"
                >
                  <span
                    class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                    :class="mfeConfig[mfe.key] ? 'translate-x-6' : 'translate-x-1'"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Plan Upgrade Notice -->
        <div v-if="hasRestrictedMfes" class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-20 p-6">
          <div class="flex items-start space-x-3">
            <ExclamationTriangleIcon class="h-6 w-6 text-orange-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 class="font-medium text-orange-800 dark:text-orange-200">Plan Yükseltme Gerekli</h4>
              <p class="text-sm text-orange-700 dark:text-orange-300 mt-1">
                Bazı özellikler mevcut {{ hotel.plan }} planında kullanılamaz. 
                Bu özellikleri etkinleştirmek için planı yükseltmeniz gerekir.
              </p>
              <button class="mt-3 px-4 py-2 bg-orange-500 text-white text-sm rounded-lg hover:bg-orange-600 transition-colors">
                Planı Yükselt
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar Info -->
      <div class="space-y-6">
        <!-- Hotel Info -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Otel Bilgileri</h3>
          <div class="space-y-3">
            <div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Otel Adı</span>
              <p class="font-medium text-navy-700 dark:text-white">{{ hotel.name }}</p>
            </div>
            <div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Mevcut Plan</span>
              <div class="mt-1">
                <span 
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="{
                    'bg-blue-100 text-blue-800': hotel.plan === 'Basic',
                    'bg-purple-100 text-purple-800': hotel.plan === 'Premium',
                    'bg-orange-100 text-orange-800': hotel.plan === 'Enterprise'
                  }"
                >
                  {{ hotel.plan }}
                </span>
              </div>
            </div>
            <div>
              <span class="text-sm text-gray-600 dark:text-gray-400">Durum</span>
              <div class="mt-1">
                <span 
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="hotel.status === 'Aktif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ hotel.status }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Configuration Summary -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Konfigürasyon Özeti</h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">Aktif Özellikler</span>
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ activeCount }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">Toplam Özellik</span>
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ totalCount }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600 dark:text-gray-400">Kullanım Oranı</span>
              <span class="text-sm font-medium text-navy-700 dark:text-white">
                {{ Math.round((activeCount / totalCount) * 100) }}%
              </span>
            </div>
          </div>
          <div class="mt-4 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              class="bg-brand-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(activeCount / totalCount) * 100}%` }"
            ></div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Hızlı İşlemler</h3>
          <div class="space-y-3">
            <button 
              @click="enableAll"
              class="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              Tümünü Etkinleştir
            </button>
            <button 
              @click="disableAll"
              class="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Tümünü Devre Dışı Bırak
            </button>
            <button 
              @click="resetToDefault"
              class="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Varsayılana Sıfırla
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ExclamationTriangleIcon,
  SparklesIcon,
  WrenchScrewdriverIcon,
  ShoppingBagIcon,
  CalendarDaysIcon,
  HeartIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'

const store = useManagementStore()
const router = useRouter()
const route = useRoute()

const hotelId = route.params.id as string
const hotel = computed(() => store.getHotelById(hotelId))

// MFE Configuration
const mfeConfig = ref({
  housekeeping: false,
  maintenance: false,
  ordering: false,
  events: false,
  spaWellness: false
})

// MFE List with Turkish names and descriptions
const mfeList = [
  {
    key: 'housekeeping',
    name: 'Akıllı Kat Hizmetleri Yönetimi',
    description: 'Oda temizliği, personel takibi ve görev yönetimi',
    icon: SparklesIcon,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600',
    planRequired: 'Basic',
    isNew: false
  },
  {
    key: 'maintenance',
    name: 'Otel Bakım ve Arıza Takibi',
    description: 'Preventif bakım, arıza bildirimi ve teknisyen yönetimi',
    icon: WrenchScrewdriverIcon,
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600',
    planRequired: 'Premium',
    isNew: false
  },
  {
    key: 'ordering',
    name: 'Oda İçi Dijital Sipariş Sistemi',
    description: 'Misafir sipariş sistemi, menü yönetimi ve ödeme entegrasyonu',
    icon: ShoppingBagIcon,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600',
    planRequired: 'Basic',
    isNew: false
  },
  {
    key: 'events',
    name: 'Etkinlik ve Aktivite Yönetimi',
    description: 'Otel etkinlikleri, rezervasyon sistemi ve katılımcı takibi',
    icon: CalendarDaysIcon,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
    planRequired: 'Premium',
    isNew: true
  },
  {
    key: 'spaWellness',
    name: 'SPA & Wellness Rezervasyonları',
    description: 'SPA randevu sistemi, terapeut yönetimi ve paket satışları',
    icon: HeartIcon,
    bgColor: 'bg-pink-100',
    iconColor: 'text-pink-600',
    planRequired: 'Enterprise',
    isNew: true
  }
]

// Computed
const activeCount = computed(() => {
  return Object.values(mfeConfig.value).filter(Boolean).length
})

const totalCount = computed(() => {
  return mfeList.length
})

const hasRestrictedMfes = computed(() => {
  if (!hotel.value) return false
  return mfeList.some(mfe => !canEnableMfe(mfe))
})

// Methods
const goBack = () => {
  router.push('/hotels')
}

const saveConfiguration = () => {
  if (hotel.value) {
    store.updateHotelMfeConfig(hotelId, mfeConfig.value)
    // Show success message (in real app)
    goBack()
  }
}

const toggleMfe = (mfeKey: string) => {
  mfeConfig.value[mfeKey as keyof typeof mfeConfig.value] = !mfeConfig.value[mfeKey as keyof typeof mfeConfig.value]
}

const canEnableMfe = (mfe: any) => {
  if (!hotel.value) return false
  
  const planHierarchy = { 'Basic': 1, 'Premium': 2, 'Enterprise': 3 }
  const currentPlanLevel = planHierarchy[hotel.value.plan as keyof typeof planHierarchy]
  const requiredPlanLevel = planHierarchy[mfe.planRequired as keyof typeof planHierarchy]
  
  return currentPlanLevel >= requiredPlanLevel
}

const enableAll = () => {
  mfeList.forEach(mfe => {
    if (canEnableMfe(mfe)) {
      mfeConfig.value[mfe.key as keyof typeof mfeConfig.value] = true
    }
  })
}

const disableAll = () => {
  Object.keys(mfeConfig.value).forEach(key => {
    mfeConfig.value[key as keyof typeof mfeConfig.value] = false
  })
}

const resetToDefault = () => {
  // Default configuration based on plan
  if (hotel.value?.plan === 'Basic') {
    mfeConfig.value = {
      housekeeping: true,
      maintenance: false,
      ordering: true,
      events: false,
      spaWellness: false
    }
  } else if (hotel.value?.plan === 'Premium') {
    mfeConfig.value = {
      housekeeping: true,
      maintenance: true,
      ordering: true,
      events: true,
      spaWellness: false
    }
  } else if (hotel.value?.plan === 'Enterprise') {
    mfeConfig.value = {
      housekeeping: true,
      maintenance: true,
      ordering: true,
      events: true,
      spaWellness: true
    }
  }
}

// Initialize configuration
onMounted(() => {
  if (hotel.value) {
    mfeConfig.value = { ...hotel.value.mfeConfig }
  }
})
</script> 