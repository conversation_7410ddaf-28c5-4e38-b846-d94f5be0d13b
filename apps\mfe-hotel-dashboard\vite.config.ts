import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import federation from '@originjs/vite-plugin-federation'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    federation({
      name: 'hotelDashboard',
      filename: 'remoteEntry.js',
      exposes: {
        './HotelDashboard': './src/App.vue',
        './LoginView': './src/views/LoginView.vue',
        './Dashboard': './src/views/Dashboard.vue',
        './OperationsOverview': './src/views/operations/OperationsOverview.vue',
        './HousekeepingOverview': './src/views/operations/housekeeping/HousekeepingOverview.vue',
        './TaskManagement': './src/views/operations/housekeeping/TaskManagement.vue',
        './MaintenanceOverview': './src/views/operations/maintenance/MaintenanceOverview.vue',
        './ContentOverview': './src/views/content/ContentOverview.vue',
        './RoomServiceManagement': './src/views/content/room-service/RoomServiceManagement.vue',
        './ActivityManagement': './src/views/management/ActivityManagement.vue',
        './NotificationCenter': './src/views/notifications/NotificationCenter.vue',
        './CustomerReports': './src/views/reports/CustomerReports.vue',
        './StaffReports': './src/views/reports/StaffReports.vue',
        './OperationalReports': './src/views/reports/OperationalReports.vue',
        './Settings': './src/views/Settings.vue',
        './Profile': './src/views/Profile.vue'
      },
      shared: {
        vue: { singleton: true, requiredVersion: '^3.4.15' },
        'vue-router': { singleton: true, requiredVersion: '^4.2.5' },
        pinia: { singleton: true, requiredVersion: '^2.1.7' },
        '@hotelexia/shared-supabase-client': { singleton: true },
        '@hotelexia/shared-components': { singleton: true }
      }
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@hotelexia/shared-components': resolve(__dirname, '../../packages/shared-components/src/index.ts'),
      '@hotelexia/shared-supabase-client': resolve(__dirname, '../../packages/shared-supabase-client/src/index.ts')
    }
  },
  server: {
    port: 5002,
    host: true,
    origin: 'http://localhost:5002',
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    },
    fs: {
      allow: ['..']
    }
  },
  build: {
    modulePreload: false,
    target: 'esnext',
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      external: []
    }
  },
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
}) 