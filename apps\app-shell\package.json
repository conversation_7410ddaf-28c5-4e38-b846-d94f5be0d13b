{"name": "@hotelexia/app-shell", "version": "1.0.0", "description": "Hotelexia Promotional Website - Simple Vue 3 promotional site", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "clean": "rm -rf dist"}, "keywords": [], "author": "", "dependencies": {"@heroicons/vue": "^2.1.1", "@hotelexia/shared-components": "workspace:*", "@hotelexia/shared-supabase-client": "workspace:*", "@supabase/supabase-js": "^2.50.2", "clsx": "^2.1.1", "pinia": "^2.1.7", "tailwind-merge": "^2.4.0", "vue": "^3.5.16", "vue-i18n": "12.0.0-alpha.2", "vue-router": "^4.2.5"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.4.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/vue": "^8.0.2", "@types/node": "^20.11.17", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.2.2", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.21.1", "jsdom": "^24.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.3.3", "vite": "^5.4.19", "vitest": "^1.2.2", "vue-tsc": "^1.8.27"}}