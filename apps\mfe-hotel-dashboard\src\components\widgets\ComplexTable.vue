<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <h4 class="text-lg font-bold text-navy-700 dark:text-white"><PERSON><PERSON><PERSON><PERSON></h4>
      <button class="text-brand-500 text-sm font-medium hover:text-brand-600">
        <PERSON>ü<PERSON>ü<PERSON><PERSON>
      </button>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-200 dark:border-gray-700">
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">MİSAFİR ADI</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">ODA</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">GİRİŞ</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">DURUM</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">İŞLEMLER</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="guest in guests" :key="guest.id" class="hover:bg-gray-50 dark:hover:bg-navy-700">
            <td class="py-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                  <span class="text-white text-sm font-medium">{{ getInitials(guest.name) }}</span>
                </div>
                <div>
                  <div class="text-sm font-medium text-navy-700 dark:text-white">{{ guest.name }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">{{ guest.email }}</div>
                </div>
              </div>
            </td>
            <td class="py-4">
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ guest.room }}</span>
            </td>
            <td class="py-4">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ guest.checkIn }}</span>
            </td>
            <td class="py-4">
              <span 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusClass(guest.status)"
              >
                {{ guest.status }}
              </span>
            </td>
            <td class="py-4">
              <div class="flex items-center space-x-2">
                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <EyeIcon class="w-4 h-4" />
                </button>
                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <PencilIcon class="w-4 h-4" />
                </button>
                <button class="text-gray-400 hover:text-red-600">
                  <TrashIcon class="w-4 h-4" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { EyeIcon, PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'

interface Guest {
  id: string
  name: string
  email: string
  room: string
  checkIn: string
  status: 'Onaylandı' | 'Bekliyor' | 'İptal Edildi'
}

const guests = ref<Guest[]>([
  {
    id: '1',
    name: 'Mehmet Yılmaz',
    email: '<EMAIL>',
    room: '201',
    checkIn: '15 Oca 2024',
    status: 'Onaylandı'
  },
  {
    id: '2',
    name: 'Ayşe Demir',
    email: '<EMAIL>',
    room: '305',
    checkIn: '16 Oca 2024',
    status: 'Bekliyor'
  },
  {
    id: '3',
    name: 'Ali Kaya',
    email: '<EMAIL>',
    room: '128',
    checkIn: '14 Oca 2024',
    status: 'Onaylandı'
  },
  {
    id: '4',
    name: 'Fatma Şahin',
    email: '<EMAIL>',
    room: '412',
    checkIn: '17 Oca 2024',
    status: 'İptal Edildi'
  },
  {
    id: '5',
    name: 'Can Özkan',
    email: '<EMAIL>',
    room: '209',
    checkIn: '15 Oca 2024',
    status: 'Onaylandı'
  }
])

const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Onaylandı':
      return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
    case 'Bekliyor':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
    case 'İptal Edildi':
      return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
  }
}
</script>

<style scoped>
/* Table specific styles */
</style> 