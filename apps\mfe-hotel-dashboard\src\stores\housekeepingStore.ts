import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { HotelDashboardService } from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'
import type { 
  Room, 
  HousekeepingTask, 
  StaffMember, 
  RoomStatus, 
  TaskStatus, 
  TaskPriority, 
  StaffStatus,
  Floor,
  StaffRequest,
  ChatChannel,
  ChatMessage,
  PerformanceMetric,
  DirectMessage
} from '@/types/housekeeping'

export const useHousekeepingStore = defineStore('housekeeping', () => {
  // Get auth store for user context
  const authStore = useAuthStore()
  
  // State
  const tasks = ref<HousekeepingTask[]>([])
  const rooms = ref<Room[]>([])
  const staff = ref<StaffMember[]>([])
  
  // Loading and error states
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Data fetching functions
  const fetchTasks = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const response = await HotelDashboardService.getTasks(hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      // Transform Supabase data to local interface format
      tasks.value = (response.data || []).map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        roomNumber: task.roomNumber,
        status: task.status,
        priority: task.priority,
        assignedTo: task.assignedTo,
        estimatedTime: task.estimatedTime,
        deadline: task.deadline,
        notes: task.notes,
        createdAt: task.createdAt,
        completedAt: task.completedAt,
        roomId: task.roomId
      }))
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error fetching tasks:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchRooms = async () => {
    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const response = await HotelDashboardService.getRooms(hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      // Transform Supabase data to local interface format
      rooms.value = (response.data || []).map(room => ({
        id: room.id,
        number: room.number,
        type: room.type,
        floor: room.floor,
        status: room.status as RoomStatus,
        lastCleaned: room.lastCleaned,
        nextInspection: room.nextInspection,
        currentGuest: room.currentGuest,
        guestName: room.guestName,
        checkOutTime: room.checkOutTime,
        checkInTime: room.checkInTime,
        notes: room.notes,
        assignedStaff: room.assignedStaff
      }))
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error fetching rooms:', err)
    }
  }

  const fetchStaff = async () => {
    try {
      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const response = await HotelDashboardService.getStaffMembers(hotelId)
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      // Transform Supabase data to local interface format
      staff.value = (response.data || []).map(member => ({
        id: member.id,
        name: member.name,
        surname: member.surname,
        role: member.role,
        status: member.status as StaffStatus,
        avatar: member.avatar,
        shift: member.shift,
        performanceScore: member.performanceScore,
        tasksCompleted: member.tasksCompleted,
        averageTime: member.averageTime,
        phone: member.phone,
        email: member.email,
        startDate: member.startDate,
        currentTasks: member.currentTasks
      }))
    } catch (err) {
      error.value = (err as Error).message
      console.error('Error fetching staff:', err)
    }
  }

  // Initialize data on store creation
  const initializeData = async () => {
    await Promise.all([
      fetchTasks(),
      fetchRooms(),
      fetchStaff()
    ])
  }

  // Call initialization
  initializeData()

  // Legacy dummy data arrays (will be removed gradually)
  const floors = ref<Floor[]>([
    {
      id: 'floor1',
      number: 1,
      totalRooms: 20,
      cleanRooms: 15,
      dirtyRooms: 3,
      outOfOrderRooms: 2,
      rooms: []
    }
  ])

  const staffRequests = ref<StaffRequest[]>([])
  const chatChannels = ref<ChatChannel[]>([])
  const chatMessages = ref<ChatMessage[]>([])
  const performanceMetrics = ref<PerformanceMetric[]>([])
  const directMessages = ref<DirectMessage[]>([])
  const directMessageHistory = ref<Record<string, ChatMessage[]>>({})
  const channelMessages = ref<Record<string, ChatMessage[]>>({})

  // Computed Properties
  const tasksByStatus = computed(() => {
    return {
      unassigned: tasks.value.filter(t => t.status === 'NEW'),
      inProgress: tasks.value.filter(t => t.status === 'IN_PROGRESS'),
      qualityCheck: tasks.value.filter(t => t.status === 'QUALITY_CHECK'),
      completed: tasks.value.filter(t => t.status === 'COMPLETED'),
      cancelled: tasks.value.filter(t => t.status === 'CANCELLED')
    }
  })

  const availableStaff = computed(() =>
    staff.value.filter(s => s.status === 'AVAILABLE')
  )

  const busyStaff = computed(() =>
    staff.value.filter(s => s.status === 'BUSY')
  )

  // Simplified computed properties for rooms (using real data from rooms ref)
  const totalRooms = computed(() => rooms.value.length)
  const cleanRoomsTotal = computed(() => rooms.value.filter(r => r.status === 'CLEAN').length)
  const dirtyRoomsTotal = computed(() => rooms.value.filter(r => r.status === 'DIRTY').length)

  // Legacy computed properties (will be updated as features are migrated)
  const pendingRequests = computed(() => staffRequests.value.filter(r => r.status === 'PENDING'))
  const unreadMessagesTotal = computed(() => chatChannels.value.reduce((total, channel) => total + channel.unreadCount, 0))
  const averagePerformanceScore = computed(() => {
    if (staff.value.length === 0) return 0
    const total = staff.value.reduce((sum, s) => sum + s.performanceScore, 0)
    return Math.round(total / staff.value.length)
  })

  // Actions
  const updateTaskStatus = (taskId: string, newStatus: TaskStatus) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.status = newStatus
      if (newStatus === 'COMPLETED') {
        task.completedAt = new Date().toISOString()
      }
    }
  }

  const assignTask = (taskId: string, staffId: string) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.assignedTo = staffId
      task.status = 'ASSIGNED'
    }
  }

  const updateRoomStatus = (roomId: string, newStatus: RoomStatus) => {
    const room = rooms.value.find(r => r.id === roomId)
    if (room) {
      room.status = newStatus
      if (newStatus === 'CLEAN') {
        room.lastCleaned = new Date().toISOString()
      }
    }
  }

  const addTask = (task: HousekeepingTask) => {
    tasks.value.push(task)
  }

  const updateTask = (taskId: string, updates: Partial<HousekeepingTask>) => {
    const taskIndex = tasks.value.findIndex(t => t.id === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex] = { ...tasks.value[taskIndex], ...updates }
    }
  }

  // Communication methods for legacy components
  const sendChatMessage = (channelId: string, senderId: string, message: string) => {
    // Legacy method for compatibility
    console.log('Sending message:', { channelId, senderId, message })
  }

  const markChannelAsRead = (channelId: string) => {
    // Legacy method for compatibility
    console.log('Marking channel as read:', channelId)
  }

  const markDirectMessageAsRead = (dmId: string) => {
    // Legacy method for compatibility
    console.log('Marking DM as read:', dmId)
  }

  const sendChannelMessage = (channelId: string, messageData: any) => {
    // Legacy method for compatibility
    console.log('Sending channel message:', { channelId, messageData })
  }

  const sendDirectMessage = (dmId: string, messageData: any) => {
    // Legacy method for compatibility
    console.log('Sending direct message:', { dmId, messageData })
  }

  // Refresh data functions
  const refreshTasks = () => fetchTasks()
  const refreshRooms = () => fetchRooms()
  const refreshStaff = () => fetchStaff()
  const refreshAll = () => initializeData()

  return {
    // State
    tasks,
    staff,
    rooms,
    floors,
    staffRequests,
    chatChannels,
    chatMessages,
    performanceMetrics,
    directMessages,
    directMessageHistory,
    channelMessages,
    isLoading,
    error,

    // Computed
    tasksByStatus,
    availableStaff,
    busyStaff,
    totalRooms,
    cleanRoomsTotal,
    dirtyRoomsTotal,
    pendingRequests,
    unreadMessagesTotal,
    averagePerformanceScore,

    // Actions
    updateTaskStatus,
    assignTask,
    updateRoomStatus,
    addTask,
    updateTask,
    sendChatMessage,
    markChannelAsRead,
    markDirectMessageAsRead,
    sendChannelMessage,
    sendDirectMessage,
    refreshTasks,
    refreshRooms,
    refreshStaff,
    refreshAll,
    fetchTasks,
    fetchRooms,
    fetchStaff,
    initializeData
  }
})

// Export types for convenience
export type {
  Room,
  HousekeepingTask,
  StaffMember,
  RoomStatus,
  TaskStatus,
  TaskPriority,
  StaffStatus,
  Floor,
  StaffRequest,
  ChatChannel,
  ChatMessage,
  PerformanceMetric,
  DirectMessage
} from '@/types/housekeeping'
