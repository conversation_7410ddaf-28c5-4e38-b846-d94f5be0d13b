// MFE Auth Module Declarations
declare module 'mfe_auth/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfe_auth/LoginView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfe_auth/RegisterView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfe_auth/ForgotPasswordView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfe_auth/UpdatePasswordView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// MFE Housekeeping Module Declarations
declare module 'mfe_housekeeping/HousekeepingApp' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// MFE Profile Module Declarations
declare module 'mfe_profile/ProfileView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// MFE Customer Portal Module Declarations
declare module 'mfe_customer_portal/CustomerPortalApp' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfe_customer_portal/MyOrdersView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfe_customer_portal/DevLoginView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// MFE Hotel Dashboard Module Declarations
declare module 'hotelDashboard/HotelDashboard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'hotelDashboard/Dashboard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// MFE Management Module Declarations
declare module 'mfeManagement/Management' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/Dashboard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/HotelList' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/HotelEdit' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/HotelMfeConfig' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/Settings' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/Billing' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/Analytics' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/SystemHealth' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/Announcements' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/AuditLog' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/HotelSetupWizard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeManagement/ConfigurePlansWizard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// MFE Hotel Dashboard Module Declarations
declare module 'hotelDashboard/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'hotelDashboard/Dashboard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Future Portal MFE Declarations
declare module 'customer_portal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'hotel_portal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'manager_portal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'mfeCustomerPortal/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/OnboardingView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/GuestLoginView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/DevLoginView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/DashboardView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/RoomServiceMenuView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/RoomServiceCategoryView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/ProductDetailView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/CartView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/ServicesView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/ServiceDetailView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/ServiceRequestView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/ActivitiesView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/ActivityDetailView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/NotificationsView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/OrderSuccessView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeCustomerPortal/OrderFailedView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/App' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/Dashboard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/OperationsOverview' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/HousekeepingOverview' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/TaskManagement' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/MaintenanceOverview' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/ContentOverview' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/RoomServiceManagement' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/GuestServiceManagement' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/ActivityManagement' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/ActivityCategoryManagement' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/NotificationCenter' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/CustomerReports' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/StaffReports' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/OperationalReports' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/Settings' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'hotelDashboard/Profile' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeHotelDashboard/LoginView' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/Management' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/Dashboard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/HotelList' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/HotelEdit' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/HotelMfeConfig' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/Settings' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/Billing' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/Analytics' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/SystemHealth' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/Announcements' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/AuditLog' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/HotelSetupWizard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
}

declare module 'mfeManagement/ConfigurePlansWizard' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent
  export default component
} 