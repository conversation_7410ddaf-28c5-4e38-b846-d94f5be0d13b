<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Kullanıcı Yönetimi</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Platform kullanıcılarını ve rollerini yönetin</p>
      </div>
      <div class="flex items-center space-x-3">
        <button
          @click="showCreateUserModal = true"
          class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors flex items-center space-x-2"
        >
          <PlusIcon class="h-4 w-4" />
          <span><PERSON><PERSON>llan<PERSON></span>
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Kullanıcı</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ stats.totalUsers }}</p>
          </div>
          <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <UsersIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Aktif Kullanıcı</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ stats.activeUsers }}</p>
          </div>
          <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <CheckCircleIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Otel Yöneticisi</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ stats.hotelManagers }}</p>
          </div>
          <div class="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <BuildingOfficeIcon class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Personel</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ stats.staff }}</p>
          </div>
          <div class="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <UserGroupIcon class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <div class="relative">
            <MagnifyingGlassIcon class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Kullanıcı ara..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
            />
          </div>
        </div>
        <div class="flex gap-3">
          <select
            v-model="selectedRole"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
          >
            <option value="">Tüm Roller</option>
            <option value="SUPER_ADMIN">Süper Admin</option>
            <option value="HOTEL_ADMIN">Otel Yöneticisi</option>
            <option value="MANAGER">Müdür</option>
            <option value="SUPERVISOR">Süpervizör</option>
            <option value="STAFF">Personel</option>
            <option value="GUEST">Misafir</option>
          </select>
          <select
            v-model="selectedHotel"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
          >
            <option value="">Tüm Oteller</option>
            <option v-for="hotel in hotels" :key="hotel.id" :value="hotel.id">
              {{ hotel.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white dark:bg-navy-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Kullanıcı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Rol
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Otel
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Durum
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Son Giriş
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50 dark:hover:bg-navy-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div
                      class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"
                    >
                      <UserIcon class="h-6 w-6 text-gray-500 dark:text-gray-400" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ user.fullName || 'İsimsiz Kullanıcı' }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ user.email }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="getRoleBadgeClass(user.role)"
                >
                  {{ getRoleDisplayName(user.role) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ user.hotelName || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'"
                >
                  {{ user.status === 'active' ? 'Aktif' : 'Pasif' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(user.lastLogin) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="editUser(user)"
                    class="text-brand-600 hover:text-brand-900 dark:text-brand-400 dark:hover:text-brand-300"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </button>
                  <button
                    @click="toggleUserStatus(user)"
                    class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    <PowerIcon class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="store.usersLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
    </div>

    <!-- Empty State -->
    <div v-if="!store.usersLoading && filteredUsers.length === 0" class="text-center py-12">
      <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Kullanıcı bulunamadı</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Arama kriterlerinize uygun kullanıcı bulunamadı.
      </p>
    </div>

    <!-- Create User Modal -->
    <UserCreateModal
      v-if="showCreateUserModal"
      @close="showCreateUserModal = false"
      @created="handleUserCreated"
    />

    <!-- Edit User Modal -->
    <UserEditModal
      v-if="showEditUserModal && selectedUser"
      :user="selectedUser"
      @close="showEditUserModal = false"
      @updated="handleUserUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  PlusIcon,
  UsersIcon,
  CheckCircleIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  MagnifyingGlassIcon,
  UserIcon,
  PencilIcon,
  PowerIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'

// Import components (to be created)
import UserCreateModal from '@/components/modals/UserCreateModal.vue'
import UserEditModal from '@/components/modals/UserEditModal.vue'

// Types
interface User {
  id: string
  email: string
  full_name: string | null
  role: string | null
  hotel_id: string | null
  hotel_name?: string | null
  created_at?: string
}

interface Hotel {
  id: string
  name: string
}

interface Stats {
  totalUsers: number
  activeUsers: number
  hotelManagers: number
  staff: number
}

// Store
const store = useManagementStore()

// Local state
const searchQuery = ref('')
const selectedRole = ref('')
const selectedHotel = ref('')
const showCreateUserModal = ref(false)
const showEditUserModal = ref(false)
const selectedUser = ref<User | null>(null)

// Computed stats from store data
const stats = computed(() => {
  const users = store.users
  return {
    totalUsers: users.length,
    activeUsers: users.filter(u => u.status === 'active').length,
    hotelManagers: users.filter(u => u.role === 'HOTEL_ADMIN').length,
    staff: users.filter(u => u.role === 'HOTEL_STAFF').length
  }
})

// Computed
const filteredUsers = computed(() => {
  let filtered = store.users

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.fullName?.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    )
  }

  if (selectedRole.value) {
    filtered = filtered.filter(user => user.role === selectedRole.value)
  }

  if (selectedHotel.value) {
    filtered = filtered.filter(user => user.hotelId === selectedHotel.value)
  }

  return filtered
})

// Methods
const editUser = (user: User) => {
  selectedUser.value = user
  showEditUserModal.value = true
}

const toggleUserStatus = async (user: User) => {
  try {
    // TODO: Implement user deactivation/activation logic
    console.log('Managing user status:', user.id)
    // This could involve updating a status field in user_profiles table
    // or implementing a soft delete mechanism
  } catch (error) {
    console.error('Error managing user status:', error)
  }
}

const handleUserCreated = (user: User) => {
  // Refresh users data from store
  store.fetchUsers()
  showCreateUserModal.value = false
}

const handleUserUpdated = (updatedUser: User) => {
  // Refresh users data from store
  store.fetchUsers()
  showEditUserModal.value = false
}

const getRoleBadgeClass = (role: string | null) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    case 'HOTEL_ADMIN':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
    case 'MANAGER':
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
    case 'SUPERVISOR':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'STAFF':
      return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400'
    case 'GUEST':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

const getRoleDisplayName = (role: string | null) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'Süper Admin'
    case 'HOTEL_ADMIN':
      return 'Otel Yöneticisi'
    case 'MANAGER':
      return 'Müdür'
    case 'SUPERVISOR':
      return 'Süpervizör'
    case 'STAFF':
      return 'Personel'
    case 'GUEST':
      return 'Misafir'
    default:
      return 'Belirsiz'
  }
}

const getHotelName = (hotelId: string | null) => {
  if (!hotelId) return null
  const hotel = store.hotels.find(h => h.id === hotelId)
  return hotel?.name || 'Bilinmeyen Otel'
}

const formatDate = (dateString: string | null) => {
  if (!dateString) return 'Hiç'
  return new Date(dateString).toLocaleDateString('tr-TR')
}

// Lifecycle - Data is already loaded by store initialization
</script>
