<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white">
        Oda Servisi Sipariş Takibi
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Gerçek zamanlı sipariş durumu yönetimi ve takibi
      </p>
    </div>

    <!-- Statistics Row -->
    <div class="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
      <div class="bg-white dark:bg-navy-800 p-4 rounded-xl shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <ClockIcon class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Yeni</p>
            <p class="text-lg font-bold text-navy-700 dark:text-white">{{ newOrdersCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 p-4 rounded-xl shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <CogIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Hazırlanıyor</p>
            <p class="text-lg font-bold text-navy-700 dark:text-white">{{ preparingOrdersCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 p-4 rounded-xl shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
            <TruckIcon class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Yolda</p>
            <p class="text-lg font-bold text-navy-700 dark:text-white">{{ onTheWayOrdersCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 p-4 rounded-xl shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <CheckIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Teslim Edildi</p>
            <p class="text-lg font-bold text-navy-700 dark:text-white">{{ deliveredOrdersCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 p-4 rounded-xl shadow-card">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
            <XMarkIcon class="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">İptal</p>
            <p class="text-lg font-bold text-navy-700 dark:text-white">{{ cancelledOrdersCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
      <!-- Yeni Siparişler -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <h3 class="font-semibold text-navy-700 dark:text-white flex items-center">
            <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
            Yeni Siparişler
            <span class="ml-2 bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 text-xs px-2 py-1 rounded-full">
              {{ newOrders.length }}
            </span>
          </h3>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <OrderCard
            v-for="order in newOrders"
            :key="order.id"
            :order="order"
            :show-actions="true"
            :actions="[
              { label: 'Hazırlanıyor Olarak İşaretle', action: () => updateStatus(order.id, 'PREPARING'), color: 'blue' }
            ]"
            @update-status="updateStatus"
          />
        </div>
      </div>

      <!-- Hazırlanıyor -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <h3 class="font-semibold text-navy-700 dark:text-white flex items-center">
            <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
            Hazırlanıyor
            <span class="ml-2 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 text-xs px-2 py-1 rounded-full">
              {{ preparingOrders.length }}
            </span>
          </h3>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <OrderCard
            v-for="order in preparingOrders"
            :key="order.id"
            :order="order"
            :show-actions="true"
            :actions="[
              { label: 'Yola Çıkarıldı', action: () => updateStatus(order.id, 'ON_THE_WAY'), color: 'yellow' }
            ]"
            @update-status="updateStatus"
          />
        </div>
      </div>

      <!-- Yolda -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <h3 class="font-semibold text-navy-700 dark:text-white flex items-center">
            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
            Yolda
            <span class="ml-2 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 text-xs px-2 py-1 rounded-full">
              {{ onTheWayOrders.length }}
            </span>
          </h3>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <OrderCard
            v-for="order in onTheWayOrders"
            :key="order.id"
            :order="order"
            :show-actions="true"
            :actions="[
              { label: 'Teslim Edildi', action: () => updateStatus(order.id, 'DELIVERED'), color: 'green' }
            ]"
            @update-status="updateStatus"
          />
        </div>
      </div>

      <!-- Teslim Edildi -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <h3 class="font-semibold text-navy-700 dark:text-white flex items-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            Teslim Edildi
            <span class="ml-2 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 text-xs px-2 py-1 rounded-full">
              {{ deliveredOrders.length }}
            </span>
          </h3>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <OrderCard
            v-for="order in deliveredOrders"
            :key="order.id"
            :order="order"
            :show-actions="false"
            @update-status="updateStatus"
          />
        </div>
      </div>

      <!-- İptal Edilenler -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600">
          <h3 class="font-semibold text-navy-700 dark:text-white flex items-center">
            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
            İptal Edilenler
            <span class="ml-2 bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs px-2 py-1 rounded-full">
              {{ cancelledOrders.length }}
            </span>
          </h3>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <OrderCard
            v-for="order in cancelledOrders"
            :key="order.id"
            :order="order"
            :show-actions="false"
            @update-status="updateStatus"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import OrderCard from '@/components/cards/OrderCard.vue'
import {
  ClockIcon,
  CogIcon,
  TruckIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import type { RoomServiceOrder } from '@/types/management'

const managementStore = useManagementStore()

// Computed properties to organize orders by status
const newOrders = computed(() =>
  managementStore.roomServiceOrders.filter(order => order.status === 'NEW')
)

const preparingOrders = computed(() =>
  managementStore.roomServiceOrders.filter(order => order.status === 'PREPARING')
)

const onTheWayOrders = computed(() =>
  managementStore.roomServiceOrders.filter(order => order.status === 'ON_THE_WAY')
)

const deliveredOrders = computed(() =>
  managementStore.roomServiceOrders.filter(order => order.status === 'DELIVERED')
)

const cancelledOrders = computed(() =>
  managementStore.roomServiceOrders.filter(order => order.status === 'CANCELLED')
)

// Computed properties for statistics
const newOrdersCount = computed(() => newOrders.value.length)
const preparingOrdersCount = computed(() => preparingOrders.value.length)
const onTheWayOrdersCount = computed(() => onTheWayOrders.value.length)
const deliveredOrdersCount = computed(() => deliveredOrders.value.length)
const cancelledOrdersCount = computed(() => cancelledOrders.value.length)

// Action to update order status
const updateStatus = (orderId: string, newStatus: RoomServiceOrder['status']) => {
  managementStore.updateOrderStatus(orderId, newStatus)
}
</script> 