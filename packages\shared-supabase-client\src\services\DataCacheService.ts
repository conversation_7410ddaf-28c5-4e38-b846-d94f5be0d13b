/**
 * Data Cache Service for Supabase queries
 * Provides in-memory caching with TTL, invalidation, and refresh strategies
 */

export interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
  tags?: string[]
}

export interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  tags?: string[] // Tags for cache invalidation
  forceRefresh?: boolean // Skip cache and fetch fresh data
  staleWhileRevalidate?: boolean // Return stale data while fetching fresh
}

export interface CacheStats {
  hits: number
  misses: number
  entries: number
  totalSize: number
  hitRate: number
}

class DataCacheService {
  private cache = new Map<string, CacheEntry>()
  private stats = {
    hits: 0,
    misses: 0
  }
  
  // Default TTL: 5 minutes
  private defaultTTL = 5 * 60 * 1000
  
  // Maximum cache size (number of entries)
  private maxSize = 1000
  
  // Cleanup interval: 1 minute
  private cleanupInterval = 60 * 1000
  private cleanupTimer: NodeJS.Timeout | null = null

  constructor() {
    this.startCleanupTimer()
  }

  /**
   * Get data from cache or execute fetcher function
   */
  async get<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const {
      ttl = this.defaultTTL,
      tags = [],
      forceRefresh = false,
      staleWhileRevalidate = false
    } = options

    // Check if we should skip cache
    if (forceRefresh) {
      const data = await this.fetchAndCache(key, fetcher, ttl, tags)
      return data
    }

    // Check cache first
    const cached = this.cache.get(key)
    const now = Date.now()

    if (cached) {
      // Check if cache is still valid
      if (now - cached.timestamp < cached.ttl) {
        this.stats.hits++
        return cached.data
      }

      // Handle stale-while-revalidate
      if (staleWhileRevalidate) {
        this.stats.hits++
        // Return stale data immediately
        const staleData = cached.data
        
        // Fetch fresh data in background
        this.fetchAndCache(key, fetcher, ttl, tags).catch(error => {
          console.error('Background refresh failed:', error)
        })
        
        return staleData
      }
    }

    // Cache miss or expired - fetch fresh data
    this.stats.misses++
    const data = await this.fetchAndCache(key, fetcher, ttl, tags)
    return data
  }

  /**
   * Fetch data and store in cache
   */
  private async fetchAndCache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number,
    tags: string[]
  ): Promise<T> {
    try {
      const data = await fetcher()
      
      // Store in cache
      this.set(key, data, { ttl, tags })
      
      return data
    } catch (error) {
      // Remove failed entry from cache
      this.delete(key)
      throw error
    }
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, options: CacheOptions = {}): void {
    const { ttl = this.defaultTTL, tags = [] } = options
    
    // Check cache size limit
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      key,
      tags
    }

    this.cache.set(key, entry)
  }

  /**
   * Get data from cache without fetching
   */
  getSync<T>(key: string): T | null {
    const cached = this.cache.get(key)
    
    if (!cached) {
      return null
    }

    const now = Date.now()
    if (now - cached.timestamp >= cached.ttl) {
      this.delete(key)
      return null
    }

    this.stats.hits++
    return cached.data
  }

  /**
   * Check if key exists in cache and is valid
   */
  has(key: string): boolean {
    const cached = this.cache.get(key)
    
    if (!cached) {
      return false
    }

    const now = Date.now()
    if (now - cached.timestamp >= cached.ttl) {
      this.delete(key)
      return false
    }

    return true
  }

  /**
   * Delete specific cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Invalidate cache entries by tags
   */
  invalidateByTags(tags: string[]): number {
    let invalidated = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key)
        invalidated++
      }
    }
    
    console.log(`Invalidated ${invalidated} cache entries with tags:`, tags)
    return invalidated
  }

  /**
   * Invalidate cache entries by key pattern
   */
  invalidateByPattern(pattern: RegExp): number {
    let invalidated = 0
    
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key)
        invalidated++
      }
    }
    
    console.log(`Invalidated ${invalidated} cache entries matching pattern:`, pattern)
    return invalidated
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.stats.hits = 0
    this.stats.misses = 0
    console.log('Cache cleared')
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0
    
    // Calculate total size (approximate)
    let totalSize = 0
    for (const entry of this.cache.values()) {
      totalSize += JSON.stringify(entry.data).length
    }

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      entries: this.cache.size,
      totalSize,
      hitRate: Math.round(hitRate * 100) / 100
    }
  }

  /**
   * Get all cache keys
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache entries by tag
   */
  getByTag(tag: string): CacheEntry[] {
    const entries: CacheEntry[] = []
    
    for (const entry of this.cache.values()) {
      if (entry.tags && entry.tags.includes(tag)) {
        entries.push(entry)
      }
    }
    
    return entries
  }

  /**
   * Evict oldest cache entry
   */
  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestTimestamp = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      console.log('Evicted oldest cache entry:', oldestKey)
    }
  }

  /**
   * Start cleanup timer to remove expired entries
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.cleanupInterval)
  }

  /**
   * Clean up expired cache entries
   */
  private cleanup(): void {
    const now = Date.now()
    let cleaned = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp >= entry.ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }

    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired cache entries`)
    }
  }

  /**
   * Stop cleanup timer
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.clear()
  }
}

// Export singleton instance
export const dataCacheService = new DataCacheService()
export default dataCacheService
