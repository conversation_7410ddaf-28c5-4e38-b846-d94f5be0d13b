import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  // Auth Layout Routes (No Sidebar/Navbar)
  {
    path: '/auth',
    component: () => import('../layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('../views/LoginView.vue'),
        meta: {
          title: 'Süper Admin Girişi - Hotelexia',
          public: true
        }
      }
    ]
  },
  // Legacy login route redirect
  {
    path: '/login',
    redirect: '/auth/login'
  },
  // Dashboard Layout Routes (With Sidebar/Navbar)
  {
    path: '/',
    component: () => import('../layouts/DashboardLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: {
          title: 'Dashboard - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'hotels',
        name: 'hotels',
        component: () => import('../views/HotelList.vue'),
        meta: {
          title: 'Oteller - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'hotels/new',
        name: 'hotel-new',
        component: () => import('../components/wizards/HotelSetupWizard.vue'),
        meta: {
          title: 'Yeni Otel Kurulumu - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'hotels/edit/:id',
        name: 'hotel-edit',
        component: () => import('../views/HotelEdit.vue'),
        props: true,
        meta: {
          title: 'Otel Düzenle - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'hotels/mfe-config/:id',
        name: 'hotel-mfe-config',
        component: () => import('../views/HotelMfeConfig.vue'),
        props: true,
        meta: {
          title: 'MFE Yönetimi - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'users',
        name: 'users',
        component: () => import('../views/UserManagement.vue'),
        meta: {
          title: 'Kullanıcı Yönetimi - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'settings',
        name: 'settings',
        component: () => import('../views/Settings.vue'),
        meta: {
          title: 'Platform Ayarları - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'billing',
        name: 'billing',
        component: () => import('../views/Billing.vue'),
        meta: {
          title: 'Abonelik Yönetimi - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'billing/configure',
        name: 'billing-configure',
        component: () => import('../views/ConfigurePlansWizard.vue'),
        meta: {
          title: 'Abonelik Planlarını Yapılandır - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'analytics',
        name: 'analytics',
        component: () => import('../views/Analytics.vue'),
        meta: {
          title: 'Platform Analitikleri - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'system-health',
        name: 'system-health',
        component: () => import('../views/SystemHealth.vue'),
        meta: {
          title: 'Sistem Sağlığı - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'announcements',
        name: 'announcements',
        component: () => import('../views/Announcements.vue'),
        meta: {
          title: 'Global Duyurular - Süper Admin',
          requiresAuth: true
        }
      },
      {
        path: 'audit-log',
        name: 'audit-log',
        component: () => import('../views/AuditLog.vue'),
        meta: {
          title: 'Denetim Günlüğü - Süper Admin',
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Authentication guard with page title update
router.beforeEach(async (to, from, next) => {
  // Update page title
  document.title = to.meta.title as string || 'Hotelexia - Süper Admin'

  // Get auth store instance
  const authStore = useAuthStore()

  // Check if route requires authentication
  const requiresAuth = to.meta.requiresAuth !== false && to.name !== 'login'
  const isPublicRoute = to.meta.public === true

  if (!isPublicRoute && requiresAuth) {
    // Try to restore session if not already authenticated
    if (!authStore.isAuthenticated) {
      const sessionRestored = await authStore.restoreSession()
      if (!sessionRestored) {
        // No valid session found, redirect to login
        if (to.path !== '/auth/login') {
          sessionStorage.setItem('hotelexia_management_redirect', to.fullPath)
        }
        return next('/auth/login')
      }
    }

    // Check if user has SUPER_ADMIN role
    if (!authStore.isSuperAdmin) {
      // Invalid role, sign out and redirect to login
      await authStore.signOut()
      return next('/auth/login')
    }
  }

  // If user is authenticated and trying to access login page
  if (to.name === 'login') {
    if (authStore.isAuthenticated) {
      // Check for stored redirect path
      const redirectPath = sessionStorage.getItem('hotelexia_management_redirect')
      if (redirectPath) {
        sessionStorage.removeItem('hotelexia_management_redirect')
        return next(redirectPath)
      }
      // Default redirect to dashboard
      return next('/dashboard')
    }
  }

  next()
})

export default router 