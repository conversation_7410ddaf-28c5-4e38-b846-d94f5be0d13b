import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useLoadingState } from './useLoadingState'
import { useErrorHandling } from './useErrorHandling'
import { useCacheManagement } from './useCacheManagement'

// Mock CacheOptions type for build compatibility
interface CacheOptions {
  ttl?: number
  tags?: string[]
}

export interface DataFetchingOptions<T> {
  /**
   * Unique key for this data fetching operation
   */
  key: string
  
  /**
   * Function to fetch data
   */
  fetcher: () => Promise<T>
  
  /**
   * Enable automatic fetching on mount
   */
  immediate?: boolean
  
  /**
   * Refetch interval in milliseconds
   */
  refetchInterval?: number
  
  /**
   * Enable refetch on window focus
   */
  refetchOnFocus?: boolean
  
  /**
   * Enable refetch on network reconnect
   */
  refetchOnReconnect?: boolean
  
  /**
   * Transform function for data
   */
  transform?: (data: any) => T
  
  /**
   * Validation function for data
   */
  validate?: (data: T) => boolean
  
  /**
   * Default value when no data
   */
  defaultValue?: T
  
  /**
   * Cache options
   */
  cache?: boolean | CacheOptions
  
  /**
   * Loading options
   */
  loading?: {
    message?: string
    minTime?: number
    maxTime?: number
  }
  
  /**
   * Error handling options
   */
  error?: {
    maxRetries?: number
    retryDelay?: number
    enableRecovery?: boolean
  }
  
  /**
   * Dependencies to watch for refetching
   */
  dependencies?: any[]
  
  /**
   * Enable optimistic updates
   */
  enableOptimisticUpdates?: boolean
  
  /**
   * Stale time in milliseconds
   */
  staleTime?: number
}

export function useDataFetching<T>(options: DataFetchingOptions<T>) {
  const {
    key,
    fetcher,
    immediate = true,
    refetchInterval,
    refetchOnFocus = true,
    refetchOnReconnect = true,
    transform,
    validate,
    defaultValue,
    cache = true,
    loading = {},
    error: errorOptions = {},
    dependencies = [],
    enableOptimisticUpdates = false,
    staleTime = 5 * 60 * 1000 // 5 minutes
  } = options

  // Initialize composables
  const loadingState = useLoadingState({
    minLoadingTime: loading.minTime || 300,
    maxLoadingTime: loading.maxTime || 30000,
    enableTimeout: true
  })

  const errorHandling = useErrorHandling({
    component: `DataFetching-${key}`,
    maxRetries: errorOptions.maxRetries || 3,
    retryDelay: errorOptions.retryDelay || 1000,
    enableRecovery: errorOptions.enableRecovery !== false
  })

  const cacheManagement = useCacheManagement({
    keyPrefix: 'data-fetching',
    enableStats: true
  })

  // State
  const data = ref<T | null>(defaultValue || null)
  const isStale = ref(false)
  const lastFetchTime = ref<number | null>(null)
  const fetchCount = ref(0)
  const optimisticData = ref<T | null>(null)

  // Timers
  let refetchTimer: NodeJS.Timeout | null = null
  let staleTimer: NodeJS.Timeout | null = null

  // Computed
  const isLoading = computed(() => loadingState.isLoading.value)
  const error = computed(() => errorHandling.lastError.value)
  const hasError = computed(() => !!error.value)
  const hasData = computed(() => data.value !== null)
  const isEmpty = computed(() => !hasData.value && !isLoading.value && !hasError.value)
  
  const currentData = computed(() => {
    if (enableOptimisticUpdates && optimisticData.value !== null) {
      return optimisticData.value
    }
    return data.value
  })

  const status = computed(() => {
    if (isLoading.value) return 'loading'
    if (hasError.value) return 'error'
    if (hasData.value) return 'success'
    return 'idle'
  })

  /**
   * Execute the data fetching operation
   */
  const execute = async (showLoading = true): Promise<T | null> => {
    try {
      if (showLoading) {
        loadingState.startLoading(loading.message || `Loading ${key}...`, key)
      }

      fetchCount.value++
      
      let result: T

      if (cache) {
        const cacheOptions = typeof cache === 'object' ? cache : { ttl: staleTime }
        result = await cacheManagement.get(key, fetcher, cacheOptions)
      } else {
        result = await fetcher()
      }

      // Transform data if needed
      if (transform) {
        result = transform(result)
      }

      // Validate data if needed
      if (validate && !validate(result)) {
        throw new Error(`Data validation failed for ${key}`)
      }

      // Update state
      data.value = result
      lastFetchTime.value = Date.now()
      isStale.value = false
      
      // Clear optimistic data
      optimisticData.value = null

      // Start stale timer
      startStaleTimer()

      // Clear any previous errors
      errorHandling.clearErrors()

      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      await errorHandling.handleError(error, { action: `fetch_attempt_${fetchCount.value}` })
      
      // Clear optimistic data on error
      optimisticData.value = null
      
      throw error
    } finally {
      if (showLoading) {
        loadingState.stopLoading(key)
      }
    }
  }

  /**
   * Refresh data (force refetch)
   */
  const refresh = async (): Promise<T | null> => {
    // Clear cache for this key
    cacheManagement.remove(key)
    return execute()
  }

  /**
   * Mutate data optimistically
   */
  const mutate = async (
    updater: (current: T | null) => T | Promise<T>,
    options: { optimistic?: boolean; revalidate?: boolean } = {}
  ): Promise<T | null> => {
    const { optimistic = enableOptimisticUpdates, revalidate = true } = options

    try {
      const currentValue = data.value
      const newValue = await updater(currentValue)

      if (optimistic) {
        // Apply optimistic update
        optimisticData.value = newValue
      } else {
        // Apply immediate update
        data.value = newValue
        
        // Update cache
        if (cache) {
          cacheManagement.set(key, newValue)
        }
      }

      // Revalidate if requested
      if (revalidate) {
        setTimeout(() => execute(false), 0)
      }

      return newValue
    } catch (error) {
      // Clear optimistic data on error
      optimisticData.value = null
      throw error
    }
  }

  /**
   * Start stale timer
   */
  const startStaleTimer = () => {
    if (staleTimer) {
      clearTimeout(staleTimer)
    }

    if (staleTime > 0) {
      staleTimer = setTimeout(() => {
        isStale.value = true
      }, staleTime)
    }
  }

  /**
   * Start refetch interval
   */
  const startRefetchInterval = () => {
    if (refetchInterval && refetchInterval > 0) {
      refetchTimer = setInterval(() => {
        if (document.visibilityState === 'visible') {
          execute(false)
        }
      }, refetchInterval)
    }
  }

  /**
   * Stop refetch interval
   */
  const stopRefetchInterval = () => {
    if (refetchTimer) {
      clearInterval(refetchTimer)
      refetchTimer = null
    }
  }

  /**
   * Handle window focus
   */
  const handleFocus = () => {
    if (refetchOnFocus && isStale.value) {
      execute(false)
    }
  }

  /**
   * Handle network reconnect
   */
  const handleOnline = () => {
    if (refetchOnReconnect && hasError.value) {
      execute()
    }
  }

  /**
   * Setup event listeners
   */
  const setupEventListeners = () => {
    if (refetchOnFocus) {
      window.addEventListener('focus', handleFocus)
      window.addEventListener('visibilitychange', handleFocus)
    }

    if (refetchOnReconnect) {
      window.addEventListener('online', handleOnline)
    }
  }

  /**
   * Cleanup event listeners
   */
  const cleanupEventListeners = () => {
    window.removeEventListener('focus', handleFocus)
    window.removeEventListener('visibilitychange', handleFocus)
    window.removeEventListener('online', handleOnline)
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    stopRefetchInterval()
    cleanupEventListeners()
    
    if (staleTimer) {
      clearTimeout(staleTimer)
      staleTimer = null
    }
  }

  // Watch dependencies for refetching
  if (dependencies.length > 0) {
    watch(dependencies, () => {
      execute(false)
    }, { deep: true })
  }

  // Lifecycle
  onMounted(() => {
    setupEventListeners()
    startRefetchInterval()
    
    if (immediate) {
      execute()
    }
  })

  onUnmounted(cleanup)

  return {
    // State
    data: currentData,
    isLoading,
    error,
    hasError,
    hasData,
    isEmpty,
    isStale,
    status,
    lastFetchTime,
    fetchCount,

    // Actions
    execute,
    refresh,
    mutate,
    cleanup,

    // Composable states
    loadingState: loadingState.getStatus(),
    errorState: errorHandling.getErrorStats(),
    cacheState: cacheManagement.cacheStats
  }
}

/**
 * Specialized hook for Supabase data fetching
 */
export function useSupabaseData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: Omit<DataFetchingOptions<T>, 'key' | 'fetcher'> = {}
) {
  return useDataFetching({
    key,
    fetcher,
    cache: {
      ttl: 5 * 60 * 1000, // 5 minutes
      tags: ['supabase', key]
    },
    loading: {
      message: 'Loading data...',
      minTime: 300
    },
    error: {
      maxRetries: 3,
      retryDelay: 1000,
      enableRecovery: true
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options
  })
}
