<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Görev Yönetimi</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">Kat hizmetleri görev takibi ve yönetimi</p>
        </div>
        
        <!-- Actions -->
        <div class="flex items-center space-x-4">
          <button
            @click="openTaskModal"
            class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <PERSON><PERSON>
          </button>
          
          <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors font-medium">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Rapor Al
          </button>
        </div>
      </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <KpiCard
        title="Toplam Görevler"
        :value="totalTasks"
        format="number"
        color="blue"
        icon="ClipboardListIcon"
        :trend="5"
        :additional-info="{ label: 'bugün', value: '' }"
      />
      <KpiCard
        title="Devam Eden"
        :value="inProgressTasks"
        format="number"
        color="yellow"
        icon="ClockIcon"
        :trend="-2"
        :additional-info="{ label: 'aktif', value: '' }"
      />
      <KpiCard
        title="Tamamlanan"
        :value="completedTasks"
        format="number"
        color="green"
        icon="CheckCircleIcon"
        :trend="12"
        :additional-info="{ label: 'bugün', value: '' }"
      />
      <KpiCard
        title="Ortalama Süre"
        :value="averageCompletionTime"
        format="time"
        color="purple"
        icon="TrendingUpIcon"
        :trend="8"
        :additional-info="{ label: 'dakika', value: '' }"
      />
      <KpiCard
        title="Verimlilik"
        :value="efficiency"
        format="percentage"
        color="indigo"
        icon="ChartBarIcon"
        :trend="15"
        :additional-info="{ label: 'hedef %85', value: '' }"
      />
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Filtrele</label>
            <select 
              v-model="selectedFilter" 
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
            >
              <option value="all">Tüm Görevler</option>
              <option value="high-priority">Yüksek Öncelik</option>
              <option value="overdue">Gecikmiş</option>
              <option value="today">Bugün</option>
              <option value="my-tasks">Görevlerim</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Kat</label>
            <select 
              v-model="selectedFloor" 
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
            >
              <option value="">Tüm Katlar</option>
              <option v-for="floor in floors" :key="floor.id" :value="floor.number">
                {{ floor.number }}. Kat
              </option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Personel</label>
            <select 
              v-model="selectedStaff" 
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
            >
              <option value="">Tüm Personel</option>
              <option v-for="staff in housekeepingStore.staff" :key="staff.id" :value="staff.id">
                {{ staff.name }} {{ staff.surname }}
              </option>
            </select>
          </div>
        </div>
        
        <div class="text-sm text-gray-500 dark:text-gray-400">
          {{ filteredTasks.length }} görev gösteriliyor
        </div>
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 min-h-96">
      <!-- New Tasks -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Yeni Görevler</h3>
            <span class="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 px-2 py-1 rounded-full text-sm font-medium">
              {{ getTasksByStatus('NEW').length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <TaskCard
            v-for="task in getTasksByStatus('NEW')"
            :key="task.id"
            :task="task"
            @update-status="updateTaskStatus"
            @edit-task="editTask"
          />
          <div v-if="getTasksByStatus('NEW').length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="text-sm">Yeni görev yok</p>
          </div>
        </div>
      </div>

      <!-- Assigned Tasks -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Atanmış</h3>
            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-sm font-medium">
              {{ getTasksByStatus('ASSIGNED').length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <TaskCard
            v-for="task in getTasksByStatus('ASSIGNED')"
            :key="task.id"
            :task="task"
            @update-status="updateTaskStatus"
            @edit-task="editTask"
          />
          <div v-if="getTasksByStatus('ASSIGNED').length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <p class="text-sm">Atanmış görev yok</p>
          </div>
        </div>
      </div>

      <!-- In Progress Tasks -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Devam Ediyor</h3>
            <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300 px-2 py-1 rounded-full text-sm font-medium">
              {{ getTasksByStatus('IN_PROGRESS').length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <TaskCard
            v-for="task in getTasksByStatus('IN_PROGRESS')"
            :key="task.id"
            :task="task"
            @update-status="updateTaskStatus"
            @edit-task="editTask"
          />
          <div v-if="getTasksByStatus('IN_PROGRESS').length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-sm">Devam eden görev yok</p>
          </div>
        </div>
      </div>

      <!-- Quality Check Tasks -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Kalite Kontrolü</h3>
            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300 px-2 py-1 rounded-full text-sm font-medium">
              {{ getTasksByStatus('QUALITY_CHECK').length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <TaskCard
            v-for="task in getTasksByStatus('QUALITY_CHECK')"
            :key="task.id"
            :task="task"
            @update-status="updateTaskStatus"
            @edit-task="editTask"
          />
          <div v-if="getTasksByStatus('QUALITY_CHECK').length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-sm">Kontrol bekleyen görev yok</p>
          </div>
        </div>
      </div>

      <!-- Completed Tasks -->
      <div class="bg-white dark:bg-navy-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Tamamlandı</h3>
            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300 px-2 py-1 rounded-full text-sm font-medium">
              {{ getTasksByStatus('COMPLETED').length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-96 overflow-y-auto">
          <TaskCard
            v-for="task in getTasksByStatus('COMPLETED')"
            :key="task.id"
            :task="task"
            @update-status="updateTaskStatus"
            @edit-task="editTask"
          />
          <div v-if="getTasksByStatus('COMPLETED').length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <p class="text-sm">Tamamlanan görev yok</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Task Creation/Edit Modal -->
    <div v-if="showTaskModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75 dark:bg-gray-900"></div>
        </div>

        <div class="inline-block align-bottom bg-white dark:bg-navy-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white dark:bg-navy-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ editingTask ? 'Görevi Düzenle' : 'Yeni Görev Oluştur' }}
              </h3>
              <button @click="closeTaskModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <form @submit.prevent="saveTask" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Görev Başlığı</label>
                <input
                  v-model="taskForm.title"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  placeholder="Görev başlığını girin..."
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Açıklama</label>
                <textarea
                  v-model="taskForm.description"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  placeholder="Görev açıklamasını girin..."
                ></textarea>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Oda</label>
                  <select
                    v-model="taskForm.roomId"
                    required
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  >
                    <option value="">Oda seçin</option>
                    <option v-for="room in housekeepingStore.rooms" :key="room.id" :value="room.id">
                      {{ room.number }}
                    </option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Personel</label>
                  <select
                    v-model="taskForm.assignedTo"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  >
                    <option value="">Personel seçin</option>
                    <option v-for="staff in housekeepingStore.staff" :key="staff.id" :value="staff.id">
                      {{ staff.name }} {{ staff.surname }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Öncelik</label>
                  <select
                    v-model="taskForm.priority"
                    required
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  >
                    <option value="LOW">Düşük</option>
                    <option value="MEDIUM">Orta</option>
                    <option value="HIGH">Yüksek</option>
                    <option value="URGENT">Acil</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tahmini Süre (dk)</label>
                  <input
                    v-model.number="taskForm.estimatedTime"
                    type="number"
                    min="1"
                    required
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Son Tarih</label>
                <input
                  v-model="taskForm.deadline"
                  type="datetime-local"
                  required
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                />
              </div>

              <div class="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  @click="closeTaskModal"
                  class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium"
                >
                  {{ editingTask ? 'Güncelle' : 'Oluştur' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import TaskCard from '@/components/housekeeping/TaskCard.vue'
import KpiCard from '@/components/housekeeping/KpiCard.vue'
import type { HousekeepingTask, TaskStatus, TaskPriority } from '@/types/housekeeping'

// Store
const housekeepingStore = useHousekeepingStore()

// State
const selectedFilter = ref('all')
const selectedFloor = ref('')
const selectedStaff = ref('')
const showTaskModal = ref(false)
const editingTask = ref<HousekeepingTask | null>(null)

// Task form
const taskForm = ref({
  title: '',
  description: '',
  roomId: '',
  assignedTo: '',
  priority: 'MEDIUM' as TaskPriority,
  estimatedTime: 30,
  deadline: ''
})

// Computed
const floors = computed(() => housekeepingStore.floors)

const filteredTasks = computed(() => {
  let tasks = housekeepingStore.tasks

  // Filter by selected criteria
  if (selectedFilter.value !== 'all') {
    switch (selectedFilter.value) {
      case 'high-priority':
        tasks = tasks.filter(t => t.priority === 'HIGH' || t.priority === 'URGENT')
        break
      case 'overdue':
        tasks = tasks.filter(t => new Date(t.deadline) < new Date() && t.status !== 'COMPLETED')
        break
      case 'today':
        const today = new Date().toDateString()
        tasks = tasks.filter(t => new Date(t.deadline).toDateString() === today)
        break
      case 'my-tasks':
        // Would filter by current user if we had user context
        break
    }
  }

  // Filter by floor
  if (selectedFloor.value) {
    tasks = tasks.filter(t => {
      const room = housekeepingStore.rooms.find(r => r.id === t.roomId)
      return room?.floorNumber === parseInt(selectedFloor.value)
    })
  }

  // Filter by staff
  if (selectedStaff.value) {
    tasks = tasks.filter(t => t.assignedTo === selectedStaff.value)
  }

  return tasks
})

const totalTasks = computed(() => filteredTasks.value.length)
const inProgressTasks = computed(() => filteredTasks.value.filter(t => 
  t.status === 'ASSIGNED' || t.status === 'IN_PROGRESS'
).length)
const completedTasks = computed(() => filteredTasks.value.filter(t => 
  t.status === 'COMPLETED'
).length)
const averageCompletionTime = computed(() => 45) // Mock data
const efficiency = computed(() => 88) // Mock data

// Methods
const getTasksByStatus = (status: TaskStatus) => {
  return filteredTasks.value.filter(task => task.status === status)
}

const updateTaskStatus = (taskId: string, newStatus: TaskStatus) => {
  housekeepingStore.updateTaskStatus(taskId, newStatus)
}

const openTaskModal = () => {
  editingTask.value = null
  resetTaskForm()
  showTaskModal.value = true
}

const editTask = (task: HousekeepingTask) => {
  editingTask.value = task
  taskForm.value = {
    title: task.title,
    description: task.description,
    roomId: task.roomId || '',
    assignedTo: task.assignedTo || '',
    priority: task.priority,
    estimatedTime: task.estimatedTime,
    deadline: new Date(task.deadline).toISOString().slice(0, 16)
  }
  showTaskModal.value = true
}

const closeTaskModal = () => {
  showTaskModal.value = false
  editingTask.value = null
  resetTaskForm()
}

const resetTaskForm = () => {
  taskForm.value = {
    title: '',
    description: '',
    roomId: '',
    assignedTo: '',
    priority: 'MEDIUM',
    estimatedTime: 30,
    deadline: ''
  }
}

const saveTask = () => {
  if (editingTask.value) {
    // Update existing task
    housekeepingStore.updateTask(editingTask.value.id, {
      ...taskForm.value,
      deadline: taskForm.value.deadline
    })
  } else {
    // Create new task
    const newTask: HousekeepingTask = {
      id: Date.now().toString(),
      ...taskForm.value,
      roomNumber: taskForm.value.roomId, // Use roomId as roomNumber for compatibility
      status: 'NEW',
      deadline: taskForm.value.deadline,
      createdAt: new Date().toISOString()
    }
    housekeepingStore.addTask(newTask)
  }
  
  closeTaskModal()
}

// Lifecycle
onMounted(() => {
  // Set default deadline to 2 hours from now
  const defaultDeadline = new Date()
  defaultDeadline.setHours(defaultDeadline.getHours() + 2)
  taskForm.value.deadline = defaultDeadline.toISOString().slice(0, 16)
})
</script> 