export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activity_registrations: {
        Row: {
          activity_feedback: string | null
          activity_id: string | null
          activity_rating: number | null
          adult_participants: number | null
          attendance_notes: string | null
          checked_in_at: string | null
          checked_out_at: string | null
          child_participants: number | null
          created_at: string | null
          dietary_restrictions: Json | null
          emergency_contact: Json | null
          guest_name: string
          guest_phone: string | null
          hotel_id: string | null
          id: string
          medical_conditions: string | null
          participant_details: Json | null
          payment_status: string | null
          registration_number: string
          registration_time: string | null
          reservation_id: string | null
          room_number: string
          special_requirements: string | null
          status: string | null
          total_amount: number | null
          total_participants: number | null
          updated_at: string | null
        }
        Insert: {
          activity_feedback?: string | null
          activity_id?: string | null
          activity_rating?: number | null
          adult_participants?: number | null
          attendance_notes?: string | null
          checked_in_at?: string | null
          checked_out_at?: string | null
          child_participants?: number | null
          created_at?: string | null
          dietary_restrictions?: Json | null
          emergency_contact?: Json | null
          guest_name: string
          guest_phone?: string | null
          hotel_id?: string | null
          id?: string
          medical_conditions?: string | null
          participant_details?: Json | null
          payment_status?: string | null
          registration_number: string
          registration_time?: string | null
          reservation_id?: string | null
          room_number: string
          special_requirements?: string | null
          status?: string | null
          total_amount?: number | null
          total_participants?: number | null
          updated_at?: string | null
        }
        Update: {
          activity_feedback?: string | null
          activity_id?: string | null
          activity_rating?: number | null
          adult_participants?: number | null
          attendance_notes?: string | null
          checked_in_at?: string | null
          checked_out_at?: string | null
          child_participants?: number | null
          created_at?: string | null
          dietary_restrictions?: Json | null
          emergency_contact?: Json | null
          guest_name?: string
          guest_phone?: string | null
          hotel_id?: string | null
          id?: string
          medical_conditions?: string | null
          participant_details?: Json | null
          payment_status?: string | null
          registration_number?: string
          registration_time?: string | null
          reservation_id?: string | null
          room_number?: string
          special_requirements?: string | null
          status?: string | null
          total_amount?: number | null
          total_participants?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activity_registrations_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "hotel_activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_registrations_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_registrations_reservation_id_fkey"
            columns: ["reservation_id"]
            isOneToOne: false
            referencedRelation: "reservations"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          created_at: string | null
          hotel_id: string | null
          id: string
          ip_address: unknown | null
          metadata: Json | null
          new_values: Json | null
          old_values: Json | null
          resource_id: string | null
          resource_type: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          hotel_id?: string | null
          id?: string
          ip_address?: unknown | null
          metadata?: Json | null
          new_values?: Json | null
          old_values?: Json | null
          resource_id?: string | null
          resource_type: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          hotel_id?: string | null
          id?: string
          ip_address?: unknown | null
          metadata?: Json | null
          new_values?: Json | null
          old_values?: Json | null
          resource_id?: string | null
          resource_type?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      communication_channels: {
        Row: {
          channel_type: Database["public"]["Enums"]["channel_type"] | null
          created_at: string | null
          created_by: string | null
          department: Database["public"]["Enums"]["staff_department"] | null
          description: string | null
          file_sharing_enabled: boolean | null
          hotel_id: string | null
          id: string
          is_archived: boolean | null
          is_private: boolean | null
          last_message_at: string | null
          max_members: number | null
          member_count: number | null
          members: Json | null
          message_retention_days: number | null
          moderators: Json | null
          name: string
          notification_settings: Json | null
          total_messages: number | null
          updated_at: string | null
        }
        Insert: {
          channel_type?: Database["public"]["Enums"]["channel_type"] | null
          created_at?: string | null
          created_by?: string | null
          department?: Database["public"]["Enums"]["staff_department"] | null
          description?: string | null
          file_sharing_enabled?: boolean | null
          hotel_id?: string | null
          id?: string
          is_archived?: boolean | null
          is_private?: boolean | null
          last_message_at?: string | null
          max_members?: number | null
          member_count?: number | null
          members?: Json | null
          message_retention_days?: number | null
          moderators?: Json | null
          name: string
          notification_settings?: Json | null
          total_messages?: number | null
          updated_at?: string | null
        }
        Update: {
          channel_type?: Database["public"]["Enums"]["channel_type"] | null
          created_at?: string | null
          created_by?: string | null
          department?: Database["public"]["Enums"]["staff_department"] | null
          description?: string | null
          file_sharing_enabled?: boolean | null
          hotel_id?: string | null
          id?: string
          is_archived?: boolean | null
          is_private?: boolean | null
          last_message_at?: string | null
          max_members?: number | null
          member_count?: number | null
          members?: Json | null
          message_retention_days?: number | null
          moderators?: Json | null
          name?: string
          notification_settings?: Json | null
          total_messages?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "communication_channels_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communication_channels_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      floors: {
        Row: {
          created_at: string | null
          floor_name: string | null
          floor_number: number
          hotel_id: string | null
          id: string
          is_active: boolean | null
          supervisor_id: string | null
          total_rooms: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          floor_name?: string | null
          floor_number: number
          hotel_id?: string | null
          id?: string
          is_active?: boolean | null
          supervisor_id?: string | null
          total_rooms?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          floor_name?: string | null
          floor_number?: number
          hotel_id?: string | null
          id?: string
          is_active?: boolean | null
          supervisor_id?: string | null
          total_rooms?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "floors_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "floors_supervisor_id_fkey"
            columns: ["supervisor_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      guest_notifications: {
        Row: {
          action_taken: string | null
          channels: Database["public"]["Enums"]["notification_channel"][]
          clicked_at: string | null
          created_at: string | null
          delivered_at: string | null
          delivery_attempts: number | null
          email_status: string | null
          expires_at: string | null
          failure_reason: string | null
          guest_email: string | null
          guest_feedback: string | null
          guest_name: string | null
          guest_phone: string | null
          hotel_id: string | null
          id: string
          in_app_status: string | null
          message: string
          metadata: Json | null
          notification_type: string | null
          push_status: string | null
          read_at: string | null
          reservation_id: string | null
          room_number: string | null
          scheduled_at: string | null
          sent_at: string | null
          sent_by: string | null
          sms_status: string | null
          status: string | null
          template_id: string | null
          title: string
          updated_at: string | null
          whatsapp_status: string | null
        }
        Insert: {
          action_taken?: string | null
          channels: Database["public"]["Enums"]["notification_channel"][]
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          delivery_attempts?: number | null
          email_status?: string | null
          expires_at?: string | null
          failure_reason?: string | null
          guest_email?: string | null
          guest_feedback?: string | null
          guest_name?: string | null
          guest_phone?: string | null
          hotel_id?: string | null
          id?: string
          in_app_status?: string | null
          message: string
          metadata?: Json | null
          notification_type?: string | null
          push_status?: string | null
          read_at?: string | null
          reservation_id?: string | null
          room_number?: string | null
          scheduled_at?: string | null
          sent_at?: string | null
          sent_by?: string | null
          sms_status?: string | null
          status?: string | null
          template_id?: string | null
          title: string
          updated_at?: string | null
          whatsapp_status?: string | null
        }
        Update: {
          action_taken?: string | null
          channels?: Database["public"]["Enums"]["notification_channel"][]
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          delivery_attempts?: number | null
          email_status?: string | null
          expires_at?: string | null
          failure_reason?: string | null
          guest_email?: string | null
          guest_feedback?: string | null
          guest_name?: string | null
          guest_phone?: string | null
          hotel_id?: string | null
          id?: string
          in_app_status?: string | null
          message?: string
          metadata?: Json | null
          notification_type?: string | null
          push_status?: string | null
          read_at?: string | null
          reservation_id?: string | null
          room_number?: string | null
          scheduled_at?: string | null
          sent_at?: string | null
          sent_by?: string | null
          sms_status?: string | null
          status?: string | null
          template_id?: string | null
          title?: string
          updated_at?: string | null
          whatsapp_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guest_notifications_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_notifications_reservation_id_fkey"
            columns: ["reservation_id"]
            isOneToOne: false
            referencedRelation: "reservations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_notifications_sent_by_fkey"
            columns: ["sent_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_notifications_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "notification_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      guest_service_requests: {
        Row: {
          actual_cost: number | null
          actual_duration: number | null
          assigned_department:
            | Database["public"]["Enums"]["staff_department"]
            | null
          assigned_staff_id: string | null
          attachments: Json | null
          completed_time: string | null
          completion_notes: string | null
          created_at: string | null
          description: string
          estimated_cost: number | null
          estimated_duration: number | null
          guest_feedback: string | null
          guest_name: string
          guest_phone: string | null
          guest_rating: number | null
          hotel_id: string | null
          id: string
          priority: Database["public"]["Enums"]["task_priority"] | null
          request_number: string
          requested_time: string | null
          reservation_id: string | null
          response_notes: string | null
          room_id: string | null
          room_number: string
          scheduled_time: string | null
          service_id: string | null
          service_location: string | null
          service_type: string
          status: Database["public"]["Enums"]["task_status"] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          actual_cost?: number | null
          actual_duration?: number | null
          assigned_department?:
            | Database["public"]["Enums"]["staff_department"]
            | null
          assigned_staff_id?: string | null
          attachments?: Json | null
          completed_time?: string | null
          completion_notes?: string | null
          created_at?: string | null
          description: string
          estimated_cost?: number | null
          estimated_duration?: number | null
          guest_feedback?: string | null
          guest_name: string
          guest_phone?: string | null
          guest_rating?: number | null
          hotel_id?: string | null
          id?: string
          priority?: Database["public"]["Enums"]["task_priority"] | null
          request_number: string
          requested_time?: string | null
          reservation_id?: string | null
          response_notes?: string | null
          room_id?: string | null
          room_number: string
          scheduled_time?: string | null
          service_id?: string | null
          service_location?: string | null
          service_type: string
          status?: Database["public"]["Enums"]["task_status"] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          actual_cost?: number | null
          actual_duration?: number | null
          assigned_department?:
            | Database["public"]["Enums"]["staff_department"]
            | null
          assigned_staff_id?: string | null
          attachments?: Json | null
          completed_time?: string | null
          completion_notes?: string | null
          created_at?: string | null
          description?: string
          estimated_cost?: number | null
          estimated_duration?: number | null
          guest_feedback?: string | null
          guest_name?: string
          guest_phone?: string | null
          guest_rating?: number | null
          hotel_id?: string | null
          id?: string
          priority?: Database["public"]["Enums"]["task_priority"] | null
          request_number?: string
          requested_time?: string | null
          reservation_id?: string | null
          response_notes?: string | null
          room_id?: string | null
          room_number?: string
          scheduled_time?: string | null
          service_id?: string | null
          service_location?: string | null
          service_type?: string
          status?: Database["public"]["Enums"]["task_status"] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guest_service_requests_assigned_staff_id_fkey"
            columns: ["assigned_staff_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_service_requests_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_service_requests_reservation_id_fkey"
            columns: ["reservation_id"]
            isOneToOne: false
            referencedRelation: "reservations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_service_requests_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_service_requests_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "guest_services"
            referencedColumns: ["id"]
          },
        ]
      }
      guest_services: {
        Row: {
          age_restrictions: string | null
          blackout_dates: Json | null
          cancellation_policy: string | null
          category_id: string | null
          created_at: string | null
          description: string | null
          description_en: string | null
          display_order: number | null
          dress_code: string | null
          duration_minutes: number | null
          equipment_required: Json | null
          gallery_images: Json | null
          health_restrictions: string | null
          hotel_id: string | null
          id: string
          image_url: string | null
          is_available: boolean | null
          is_featured: boolean | null
          location: string | null
          max_advance_booking: number | null
          max_capacity: number | null
          min_advance_booking: number | null
          name: string
          name_en: string | null
          operating_hours: Json | null
          preparation_time_minutes: number | null
          price: number | null
          service_code: string | null
          special_requirements: string | null
          staff_required: number | null
          tags: Json | null
          updated_at: string | null
        }
        Insert: {
          age_restrictions?: string | null
          blackout_dates?: Json | null
          cancellation_policy?: string | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          description_en?: string | null
          display_order?: number | null
          dress_code?: string | null
          duration_minutes?: number | null
          equipment_required?: Json | null
          gallery_images?: Json | null
          health_restrictions?: string | null
          hotel_id?: string | null
          id?: string
          image_url?: string | null
          is_available?: boolean | null
          is_featured?: boolean | null
          location?: string | null
          max_advance_booking?: number | null
          max_capacity?: number | null
          min_advance_booking?: number | null
          name: string
          name_en?: string | null
          operating_hours?: Json | null
          preparation_time_minutes?: number | null
          price?: number | null
          service_code?: string | null
          special_requirements?: string | null
          staff_required?: number | null
          tags?: Json | null
          updated_at?: string | null
        }
        Update: {
          age_restrictions?: string | null
          blackout_dates?: Json | null
          cancellation_policy?: string | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          description_en?: string | null
          display_order?: number | null
          dress_code?: string | null
          duration_minutes?: number | null
          equipment_required?: Json | null
          gallery_images?: Json | null
          health_restrictions?: string | null
          hotel_id?: string | null
          id?: string
          image_url?: string | null
          is_available?: boolean | null
          is_featured?: boolean | null
          location?: string | null
          max_advance_booking?: number | null
          max_capacity?: number | null
          min_advance_booking?: number | null
          name?: string
          name_en?: string | null
          operating_hours?: Json | null
          preparation_time_minutes?: number | null
          price?: number | null
          service_code?: string | null
          special_requirements?: string | null
          staff_required?: number | null
          tags?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guest_services_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "service_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guest_services_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      hotel_activities: {
        Row: {
          activity_code: string | null
          advance_booking_required: boolean | null
          age_max: number | null
          age_min: number | null
          cancellation_policy: string | null
          category_id: string | null
          child_price: number | null
          created_at: string | null
          current_registrations: number | null
          description: string | null
          description_en: string | null
          end_time: string | null
          equipment_needed: Json | null
          equipment_provided: Json | null
          gallery_images: Json | null
          hotel_id: string | null
          id: string
          image_url: string | null
          indoor_location: boolean | null
          instructor_id: string | null
          instructor_name: string | null
          is_active: boolean | null
          is_featured: boolean | null
          is_promoted: boolean | null
          is_recurring: boolean | null
          location: string | null
          max_participants: number | null
          member_price: number | null
          min_participants: number | null
          physical_requirements: string | null
          price: number | null
          recurrence_pattern: Json | null
          registration_required: boolean | null
          skill_level: string | null
          staff_required: number | null
          start_time: string | null
          status: string | null
          tags: Json | null
          title: string
          title_en: string | null
          updated_at: string | null
          waitlist_enabled: boolean | null
          weather_dependent: boolean | null
          what_to_bring: Json | null
        }
        Insert: {
          activity_code?: string | null
          advance_booking_required?: boolean | null
          age_max?: number | null
          age_min?: number | null
          cancellation_policy?: string | null
          category_id?: string | null
          child_price?: number | null
          created_at?: string | null
          current_registrations?: number | null
          description?: string | null
          description_en?: string | null
          end_time?: string | null
          equipment_needed?: Json | null
          equipment_provided?: Json | null
          gallery_images?: Json | null
          hotel_id?: string | null
          id?: string
          image_url?: string | null
          indoor_location?: boolean | null
          instructor_id?: string | null
          instructor_name?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_promoted?: boolean | null
          is_recurring?: boolean | null
          location?: string | null
          max_participants?: number | null
          member_price?: number | null
          min_participants?: number | null
          physical_requirements?: string | null
          price?: number | null
          recurrence_pattern?: Json | null
          registration_required?: boolean | null
          skill_level?: string | null
          staff_required?: number | null
          start_time?: string | null
          status?: string | null
          tags?: Json | null
          title: string
          title_en?: string | null
          updated_at?: string | null
          waitlist_enabled?: boolean | null
          weather_dependent?: boolean | null
          what_to_bring?: Json | null
        }
        Update: {
          activity_code?: string | null
          advance_booking_required?: boolean | null
          age_max?: number | null
          age_min?: number | null
          cancellation_policy?: string | null
          category_id?: string | null
          child_price?: number | null
          created_at?: string | null
          current_registrations?: number | null
          description?: string | null
          description_en?: string | null
          end_time?: string | null
          equipment_needed?: Json | null
          equipment_provided?: Json | null
          gallery_images?: Json | null
          hotel_id?: string | null
          id?: string
          image_url?: string | null
          indoor_location?: boolean | null
          instructor_id?: string | null
          instructor_name?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          is_promoted?: boolean | null
          is_recurring?: boolean | null
          location?: string | null
          max_participants?: number | null
          member_price?: number | null
          min_participants?: number | null
          physical_requirements?: string | null
          price?: number | null
          recurrence_pattern?: Json | null
          registration_required?: boolean | null
          skill_level?: string | null
          staff_required?: number | null
          start_time?: string | null
          status?: string | null
          tags?: Json | null
          title?: string
          title_en?: string | null
          updated_at?: string | null
          waitlist_enabled?: boolean | null
          weather_dependent?: boolean | null
          what_to_bring?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "hotel_activities_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "service_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "hotel_activities_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "hotel_activities_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      hotels: {
        Row: {
          address: string | null
          city: string | null
          country: string | null
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          main_image_url: string | null
          name: string
        }
        Insert: {
          address?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          main_image_url?: string | null
          name: string
        }
        Update: {
          address?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          main_image_url?: string | null
          name?: string
        }
        Relationships: []
      }
      maintenance_tasks: {
        Row: {
          assigned_to: string | null
          created_at: string
          description: string | null
          hotel_id: string
          id: string
          priority: string | null
          room_id: string | null
          status: string
          title: string
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string
          description?: string | null
          hotel_id: string
          id?: string
          priority?: string | null
          room_id?: string | null
          status?: string
          title: string
        }
        Update: {
          assigned_to?: string | null
          created_at?: string
          description?: string | null
          hotel_id?: string
          id?: string
          priority?: string | null
          room_id?: string | null
          status?: string
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_tasks_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_tasks_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_tasks_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      menu_items: {
        Row: {
          allergens: Json | null
          availability_hours: Json | null
          calories: number | null
          category_id: string | null
          complexity_level: number | null
          cost: number | null
          course_type: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          description_en: string | null
          display_order: number | null
          gallery_images: Json | null
          hotel_id: string | null
          id: string
          image_url: string | null
          ingredients: Json | null
          is_available: boolean | null
          is_chef_special: boolean | null
          is_featured: boolean | null
          is_gluten_free: boolean | null
          is_halal: boolean | null
          is_kosher: boolean | null
          is_new: boolean | null
          is_vegan: boolean | null
          is_vegetarian: boolean | null
          item_code: string | null
          kitchen_station: string | null
          low_stock_threshold: number | null
          name: string
          name_en: string | null
          nutritional_info: Json | null
          portion_size: string | null
          preparation_time_minutes: number | null
          price: number
          seasonal_availability: Json | null
          seasonal_prices: Json | null
          serving_temperature: string | null
          stock_quantity: number | null
          tags: Json | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          allergens?: Json | null
          availability_hours?: Json | null
          calories?: number | null
          category_id?: string | null
          complexity_level?: number | null
          cost?: number | null
          course_type?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          description_en?: string | null
          display_order?: number | null
          gallery_images?: Json | null
          hotel_id?: string | null
          id?: string
          image_url?: string | null
          ingredients?: Json | null
          is_available?: boolean | null
          is_chef_special?: boolean | null
          is_featured?: boolean | null
          is_gluten_free?: boolean | null
          is_halal?: boolean | null
          is_kosher?: boolean | null
          is_new?: boolean | null
          is_vegan?: boolean | null
          is_vegetarian?: boolean | null
          item_code?: string | null
          kitchen_station?: string | null
          low_stock_threshold?: number | null
          name: string
          name_en?: string | null
          nutritional_info?: Json | null
          portion_size?: string | null
          preparation_time_minutes?: number | null
          price: number
          seasonal_availability?: Json | null
          seasonal_prices?: Json | null
          serving_temperature?: string | null
          stock_quantity?: number | null
          tags?: Json | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          allergens?: Json | null
          availability_hours?: Json | null
          calories?: number | null
          category_id?: string | null
          complexity_level?: number | null
          cost?: number | null
          course_type?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          description_en?: string | null
          display_order?: number | null
          gallery_images?: Json | null
          hotel_id?: string | null
          id?: string
          image_url?: string | null
          ingredients?: Json | null
          is_available?: boolean | null
          is_chef_special?: boolean | null
          is_featured?: boolean | null
          is_gluten_free?: boolean | null
          is_halal?: boolean | null
          is_kosher?: boolean | null
          is_new?: boolean | null
          is_vegan?: boolean | null
          is_vegetarian?: boolean | null
          item_code?: string | null
          kitchen_station?: string | null
          low_stock_threshold?: number | null
          name?: string
          name_en?: string | null
          nutritional_info?: Json | null
          portion_size?: string | null
          preparation_time_minutes?: number | null
          price?: number
          seasonal_availability?: Json | null
          seasonal_prices?: Json | null
          serving_temperature?: string | null
          stock_quantity?: number | null
          tags?: Json | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "menu_items_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "service_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "menu_items_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "menu_items_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "menu_items_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          attachments: Json | null
          channel_id: string | null
          content: string
          created_at: string | null
          edit_history: Json | null
          hotel_id: string | null
          id: string
          is_announcement: boolean | null
          is_deleted: boolean | null
          is_edited: boolean | null
          is_pinned: boolean | null
          is_thread_reply: boolean | null
          is_urgent: boolean | null
          mentions: Json | null
          message_type: Database["public"]["Enums"]["message_type"] | null
          reactions: Json | null
          read_by: Json | null
          read_count: number | null
          recipient_id: string | null
          reply_count: number | null
          sender_id: string | null
          thread_id: string | null
          updated_at: string | null
        }
        Insert: {
          attachments?: Json | null
          channel_id?: string | null
          content: string
          created_at?: string | null
          edit_history?: Json | null
          hotel_id?: string | null
          id?: string
          is_announcement?: boolean | null
          is_deleted?: boolean | null
          is_edited?: boolean | null
          is_pinned?: boolean | null
          is_thread_reply?: boolean | null
          is_urgent?: boolean | null
          mentions?: Json | null
          message_type?: Database["public"]["Enums"]["message_type"] | null
          reactions?: Json | null
          read_by?: Json | null
          read_count?: number | null
          recipient_id?: string | null
          reply_count?: number | null
          sender_id?: string | null
          thread_id?: string | null
          updated_at?: string | null
        }
        Update: {
          attachments?: Json | null
          channel_id?: string | null
          content?: string
          created_at?: string | null
          edit_history?: Json | null
          hotel_id?: string | null
          id?: string
          is_announcement?: boolean | null
          is_deleted?: boolean | null
          is_edited?: boolean | null
          is_pinned?: boolean | null
          is_thread_reply?: boolean | null
          is_urgent?: boolean | null
          mentions?: Json | null
          message_type?: Database["public"]["Enums"]["message_type"] | null
          reactions?: Json | null
          read_by?: Json | null
          read_count?: number | null
          recipient_id?: string | null
          reply_count?: number | null
          sender_id?: string | null
          thread_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "communication_channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_thread_id_fkey"
            columns: ["thread_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_templates: {
        Row: {
          channels: Database["public"]["Enums"]["notification_channel"][] | null
          created_at: string | null
          created_by: string | null
          description: string | null
          email_body_template: string | null
          email_subject_template: string | null
          hotel_id: string | null
          id: string
          is_active: boolean | null
          is_system_template: boolean | null
          last_used_at: string | null
          message_template: string
          name: string
          sms_template: string | null
          target_audience: Json | null
          template_type: string
          title_template: string | null
          updated_at: string | null
          usage_count: number | null
          variables: Json | null
        }
        Insert: {
          channels?:
            | Database["public"]["Enums"]["notification_channel"][]
            | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          email_body_template?: string | null
          email_subject_template?: string | null
          hotel_id?: string | null
          id?: string
          is_active?: boolean | null
          is_system_template?: boolean | null
          last_used_at?: string | null
          message_template: string
          name: string
          sms_template?: string | null
          target_audience?: Json | null
          template_type: string
          title_template?: string | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: Json | null
        }
        Update: {
          channels?:
            | Database["public"]["Enums"]["notification_channel"][]
            | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          email_body_template?: string | null
          email_subject_template?: string | null
          hotel_id?: string | null
          id?: string
          is_active?: boolean | null
          is_system_template?: boolean | null
          last_used_at?: string | null
          message_template?: string
          name?: string
          sms_template?: string | null
          target_audience?: Json | null
          template_type?: string
          title_template?: string | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notification_templates_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          add_ons: Json | null
          created_at: string | null
          id: string
          item_description: string | null
          item_name: string
          item_status: string | null
          menu_item_id: string | null
          modifications: Json | null
          order_id: string | null
          preparation_notes: string | null
          preparation_time_minutes: number | null
          quantity: number
          special_instructions: string | null
          total_price: number
          unit_price: number
          updated_at: string | null
        }
        Insert: {
          add_ons?: Json | null
          created_at?: string | null
          id?: string
          item_description?: string | null
          item_name: string
          item_status?: string | null
          menu_item_id?: string | null
          modifications?: Json | null
          order_id?: string | null
          preparation_notes?: string | null
          preparation_time_minutes?: number | null
          quantity: number
          special_instructions?: string | null
          total_price: number
          unit_price: number
          updated_at?: string | null
        }
        Update: {
          add_ons?: Json | null
          created_at?: string | null
          id?: string
          item_description?: string | null
          item_name?: string
          item_status?: string | null
          menu_item_id?: string | null
          modifications?: Json | null
          order_id?: string | null
          preparation_notes?: string | null
          preparation_time_minutes?: number | null
          quantity?: number
          special_instructions?: string | null
          total_price?: number
          unit_price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_items_menu_item_id_fkey"
            columns: ["menu_item_id"]
            isOneToOne: false
            referencedRelation: "menu_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "room_service_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          address: Json | null
          contact_email: string
          contact_phone: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          settings: Json | null
          slug: string
          updated_at: string | null
        }
        Insert: {
          address?: Json | null
          contact_email: string
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          settings?: Json | null
          slug: string
          updated_at?: string | null
        }
        Update: {
          address?: Json | null
          contact_email?: string
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          settings?: Json | null
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          email: string | null
          full_name: string | null
          hotel_id: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"]
        }
        Insert: {
          email?: string | null
          full_name?: string | null
          hotel_id?: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"]
        }
        Update: {
          email?: string | null
          full_name?: string | null
          hotel_id?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"]
        }
        Relationships: [
          {
            foreignKeyName: "profiles_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      reservations: {
        Row: {
          accessibility_needs: Json | null
          actual_checkin_time: string | null
          actual_checkout_time: string | null
          adult_guests: number | null
          booking_agent: string | null
          booking_source: string | null
          check_in_date: string
          check_out_date: string
          checkin_staff_id: string | null
          checkout_staff_id: string | null
          child_guests: number | null
          commission_rate: number | null
          confirmation_code: string
          created_at: string | null
          deposit_amount: number | null
          dietary_restrictions: Json | null
          early_checkin_requested: boolean | null
          guest_details: Json | null
          guest_email: string | null
          guest_first_name: string
          guest_id_number: string | null
          guest_last_name: string
          guest_nationality: string | null
          guest_phone: string | null
          hotel_id: string | null
          id: string
          late_checkout_requested: boolean | null
          loyalty_member_id: string | null
          payment_status: string | null
          previous_stays: number | null
          profile_id: string | null
          reservation_number: string
          room_id: string | null
          room_preferences: Json | null
          room_rate: number
          special_requests: string | null
          status: string | null
          taxes: number | null
          total_amount: number
          total_guests: number | null
          total_nights: number
          updated_at: string | null
          vip_status: boolean | null
        }
        Insert: {
          accessibility_needs?: Json | null
          actual_checkin_time?: string | null
          actual_checkout_time?: string | null
          adult_guests?: number | null
          booking_agent?: string | null
          booking_source?: string | null
          check_in_date: string
          check_out_date: string
          checkin_staff_id?: string | null
          checkout_staff_id?: string | null
          child_guests?: number | null
          commission_rate?: number | null
          confirmation_code: string
          created_at?: string | null
          deposit_amount?: number | null
          dietary_restrictions?: Json | null
          early_checkin_requested?: boolean | null
          guest_details?: Json | null
          guest_email?: string | null
          guest_first_name: string
          guest_id_number?: string | null
          guest_last_name: string
          guest_nationality?: string | null
          guest_phone?: string | null
          hotel_id?: string | null
          id?: string
          late_checkout_requested?: boolean | null
          loyalty_member_id?: string | null
          payment_status?: string | null
          previous_stays?: number | null
          profile_id?: string | null
          reservation_number: string
          room_id?: string | null
          room_preferences?: Json | null
          room_rate: number
          special_requests?: string | null
          status?: string | null
          taxes?: number | null
          total_amount: number
          total_guests?: number | null
          total_nights: number
          updated_at?: string | null
          vip_status?: boolean | null
        }
        Update: {
          accessibility_needs?: Json | null
          actual_checkin_time?: string | null
          actual_checkout_time?: string | null
          adult_guests?: number | null
          booking_agent?: string | null
          booking_source?: string | null
          check_in_date?: string
          check_out_date?: string
          checkin_staff_id?: string | null
          checkout_staff_id?: string | null
          child_guests?: number | null
          commission_rate?: number | null
          confirmation_code?: string
          created_at?: string | null
          deposit_amount?: number | null
          dietary_restrictions?: Json | null
          early_checkin_requested?: boolean | null
          guest_details?: Json | null
          guest_email?: string | null
          guest_first_name?: string
          guest_id_number?: string | null
          guest_last_name?: string
          guest_nationality?: string | null
          guest_phone?: string | null
          hotel_id?: string | null
          id?: string
          late_checkout_requested?: boolean | null
          loyalty_member_id?: string | null
          payment_status?: string | null
          previous_stays?: number | null
          profile_id?: string | null
          reservation_number?: string
          room_id?: string | null
          room_preferences?: Json | null
          room_rate?: number
          special_requests?: string | null
          status?: string | null
          taxes?: number | null
          total_amount?: number
          total_guests?: number | null
          total_nights?: number
          updated_at?: string | null
          vip_status?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "reservations_checkin_staff_id_fkey"
            columns: ["checkin_staff_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reservations_checkout_staff_id_fkey"
            columns: ["checkout_staff_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reservations_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reservations_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reservations_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      room_service_orders: {
        Row: {
          actual_delivery_time: string | null
          assigned_delivery_staff_id: string | null
          assigned_kitchen_staff_id: string | null
          created_at: string | null
          delivery_fee: number | null
          delivery_rating: number | null
          dietary_notes: string | null
          discount_amount: number | null
          estimated_delivery_time: string | null
          guest_feedback: string | null
          guest_name: string
          guest_phone: string | null
          guest_rating: number | null
          hotel_id: string | null
          id: string
          kitchen_notes: string | null
          order_number: string
          order_time: string | null
          payment_method: string | null
          payment_status: string | null
          preparation_completed_at: string | null
          preparation_started_at: string | null
          requested_delivery_time: string | null
          reservation_id: string | null
          room_id: string | null
          room_number: string
          service_charge: number | null
          special_instructions: string | null
          status: Database["public"]["Enums"]["order_status"] | null
          subtotal: number
          tax_amount: number | null
          tip_amount: number | null
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          actual_delivery_time?: string | null
          assigned_delivery_staff_id?: string | null
          assigned_kitchen_staff_id?: string | null
          created_at?: string | null
          delivery_fee?: number | null
          delivery_rating?: number | null
          dietary_notes?: string | null
          discount_amount?: number | null
          estimated_delivery_time?: string | null
          guest_feedback?: string | null
          guest_name: string
          guest_phone?: string | null
          guest_rating?: number | null
          hotel_id?: string | null
          id?: string
          kitchen_notes?: string | null
          order_number: string
          order_time?: string | null
          payment_method?: string | null
          payment_status?: string | null
          preparation_completed_at?: string | null
          preparation_started_at?: string | null
          requested_delivery_time?: string | null
          reservation_id?: string | null
          room_id?: string | null
          room_number: string
          service_charge?: number | null
          special_instructions?: string | null
          status?: Database["public"]["Enums"]["order_status"] | null
          subtotal: number
          tax_amount?: number | null
          tip_amount?: number | null
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          actual_delivery_time?: string | null
          assigned_delivery_staff_id?: string | null
          assigned_kitchen_staff_id?: string | null
          created_at?: string | null
          delivery_fee?: number | null
          delivery_rating?: number | null
          dietary_notes?: string | null
          discount_amount?: number | null
          estimated_delivery_time?: string | null
          guest_feedback?: string | null
          guest_name?: string
          guest_phone?: string | null
          guest_rating?: number | null
          hotel_id?: string | null
          id?: string
          kitchen_notes?: string | null
          order_number?: string
          order_time?: string | null
          payment_method?: string | null
          payment_status?: string | null
          preparation_completed_at?: string | null
          preparation_started_at?: string | null
          requested_delivery_time?: string | null
          reservation_id?: string | null
          room_id?: string | null
          room_number?: string
          service_charge?: number | null
          special_instructions?: string | null
          status?: Database["public"]["Enums"]["order_status"] | null
          subtotal?: number
          tax_amount?: number | null
          tip_amount?: number | null
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "room_service_orders_assigned_delivery_staff_id_fkey"
            columns: ["assigned_delivery_staff_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "room_service_orders_assigned_kitchen_staff_id_fkey"
            columns: ["assigned_kitchen_staff_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "room_service_orders_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "room_service_orders_reservation_id_fkey"
            columns: ["reservation_id"]
            isOneToOne: false
            referencedRelation: "reservations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "room_service_orders_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      rooms: {
        Row: {
          amenities: Json | null
          assigned_housekeeper_id: string | null
          assigned_maintenance_id: string | null
          base_price: number | null
          bed_type: string | null
          check_in_time: string | null
          check_out_time: string | null
          created_at: string | null
          current_guest_id: string | null
          estimated_checkout: string | null
          floor: number | null
          floor_id: string | null
          hotel_id: string | null
          housekeeping_notes: string | null
          id: string
          is_active: boolean | null
          is_available: boolean | null
          last_cleaned: string | null
          last_inspection: string | null
          maintenance_notes: string | null
          max_occupancy: number | null
          next_maintenance: string | null
          out_of_order_reason: string | null
          room_number: string
          room_type: string | null
          seasonal_rates: Json | null
          size_sqm: number | null
          special_requirements: Json | null
          status: Database["public"]["Enums"]["room_status"] | null
          updated_at: string | null
        }
        Insert: {
          amenities?: Json | null
          assigned_housekeeper_id?: string | null
          assigned_maintenance_id?: string | null
          base_price?: number | null
          bed_type?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          created_at?: string | null
          current_guest_id?: string | null
          estimated_checkout?: string | null
          floor?: number | null
          floor_id?: string | null
          hotel_id?: string | null
          housekeeping_notes?: string | null
          id?: string
          is_active?: boolean | null
          is_available?: boolean | null
          last_cleaned?: string | null
          last_inspection?: string | null
          maintenance_notes?: string | null
          max_occupancy?: number | null
          next_maintenance?: string | null
          out_of_order_reason?: string | null
          room_number: string
          room_type?: string | null
          seasonal_rates?: Json | null
          size_sqm?: number | null
          special_requirements?: Json | null
          status?: Database["public"]["Enums"]["room_status"] | null
          updated_at?: string | null
        }
        Update: {
          amenities?: Json | null
          assigned_housekeeper_id?: string | null
          assigned_maintenance_id?: string | null
          base_price?: number | null
          bed_type?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          created_at?: string | null
          current_guest_id?: string | null
          estimated_checkout?: string | null
          floor?: number | null
          floor_id?: string | null
          hotel_id?: string | null
          housekeeping_notes?: string | null
          id?: string
          is_active?: boolean | null
          is_available?: boolean | null
          last_cleaned?: string | null
          last_inspection?: string | null
          maintenance_notes?: string | null
          max_occupancy?: number | null
          next_maintenance?: string | null
          out_of_order_reason?: string | null
          room_number?: string | null
          room_type?: string | null
          seasonal_rates?: Json | null
          size_sqm?: number | null
          special_requirements?: Json | null
          status?: Database["public"]["Enums"]["room_status"] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rooms_assigned_housekeeper_id_fkey"
            columns: ["assigned_housekeeper_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rooms_assigned_maintenance_id_fkey"
            columns: ["assigned_maintenance_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rooms_floor_id_fkey"
            columns: ["floor_id"]
            isOneToOne: false
            referencedRelation: "floors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rooms_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      service_categories: {
        Row: {
          availability_rules: Json | null
          base_markup_percentage: number | null
          category_type: Database["public"]["Enums"]["service_category_type"]
          color: string | null
          created_at: string | null
          description: string | null
          description_en: string | null
          display_order: number | null
          hotel_id: string | null
          icon: string | null
          id: string
          image_url: string | null
          is_active: boolean | null
          is_featured: boolean | null
          max_advance_booking: number | null
          min_advance_booking: number | null
          name: string
          name_en: string | null
          operating_hours: Json | null
          updated_at: string | null
        }
        Insert: {
          availability_rules?: Json | null
          base_markup_percentage?: number | null
          category_type: Database["public"]["Enums"]["service_category_type"]
          color?: string | null
          created_at?: string | null
          description?: string | null
          description_en?: string | null
          display_order?: number | null
          hotel_id?: string | null
          icon?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          max_advance_booking?: number | null
          min_advance_booking?: number | null
          name: string
          name_en?: string | null
          operating_hours?: Json | null
          updated_at?: string | null
        }
        Update: {
          availability_rules?: Json | null
          base_markup_percentage?: number | null
          category_type?: Database["public"]["Enums"]["service_category_type"]
          color?: string | null
          created_at?: string | null
          description?: string | null
          description_en?: string | null
          display_order?: number | null
          hotel_id?: string | null
          icon?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean | null
          is_featured?: boolean | null
          max_advance_booking?: number | null
          min_advance_booking?: number | null
          name?: string
          name_en?: string | null
          operating_hours?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "service_categories_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      service_requests: {
        Row: {
          created_at: string
          details: string | null
          guest_name: string | null
          hotel_id: string
          id: string
          request_type: string
          room_number: string | null
          status: string
        }
        Insert: {
          created_at?: string
          details?: string | null
          guest_name?: string | null
          hotel_id: string
          id?: string
          request_type: string
          room_number?: string | null
          status?: string
        }
        Update: {
          created_at?: string
          details?: string | null
          guest_name?: string | null
          hotel_id?: string
          id?: string
          request_type?: string
          room_number?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_requests_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
        ]
      }
      staff_notifications: {
        Row: {
          action_taken: boolean | null
          action_taken_at: string | null
          category: string | null
          created_at: string | null
          hotel_id: string | null
          id: string
          is_read: boolean | null
          is_urgent: boolean | null
          message: string
          metadata: Json | null
          notification_type: string | null
          priority: Database["public"]["Enums"]["task_priority"] | null
          read_at: string | null
          recipient_id: string | null
          related_order_id: string | null
          related_request_id: string | null
          related_task_id: string | null
          requires_action: boolean | null
          sent_by: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          action_taken?: boolean | null
          action_taken_at?: string | null
          category?: string | null
          created_at?: string | null
          hotel_id?: string | null
          id?: string
          is_read?: boolean | null
          is_urgent?: boolean | null
          message: string
          metadata?: Json | null
          notification_type?: string | null
          priority?: Database["public"]["Enums"]["task_priority"] | null
          read_at?: string | null
          recipient_id?: string | null
          related_order_id?: string | null
          related_request_id?: string | null
          related_task_id?: string | null
          requires_action?: boolean | null
          sent_by?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          action_taken?: boolean | null
          action_taken_at?: string | null
          category?: string | null
          created_at?: string | null
          hotel_id?: string | null
          id?: string
          is_read?: boolean | null
          is_urgent?: boolean | null
          message?: string
          metadata?: Json | null
          notification_type?: string | null
          priority?: Database["public"]["Enums"]["task_priority"] | null
          read_at?: string | null
          recipient_id?: string | null
          related_order_id?: string | null
          related_request_id?: string | null
          related_task_id?: string | null
          requires_action?: boolean | null
          sent_by?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "staff_notifications_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_notifications_related_order_id_fkey"
            columns: ["related_order_id"]
            isOneToOne: false
            referencedRelation: "room_service_orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_notifications_related_request_id_fkey"
            columns: ["related_request_id"]
            isOneToOne: false
            referencedRelation: "guest_service_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_notifications_related_task_id_fkey"
            columns: ["related_task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_notifications_sent_by_fkey"
            columns: ["sent_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      staff_requests: {
        Row: {
          approval_notes: string | null
          approved_at: string | null
          approved_end_date: string | null
          approved_start_date: string | null
          approver_id: string | null
          attachments: Json | null
          created_at: string | null
          description: string
          hotel_id: string | null
          id: string
          metadata: Json | null
          priority: Database["public"]["Enums"]["task_priority"] | null
          rejected_at: string | null
          rejection_reason: string | null
          request_type: string
          requested_end_date: string | null
          requested_start_date: string | null
          requester_id: string | null
          status: Database["public"]["Enums"]["task_status"] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          approval_notes?: string | null
          approved_at?: string | null
          approved_end_date?: string | null
          approved_start_date?: string | null
          approver_id?: string | null
          attachments?: Json | null
          created_at?: string | null
          description: string
          hotel_id?: string | null
          id?: string
          metadata?: Json | null
          priority?: Database["public"]["Enums"]["task_priority"] | null
          rejected_at?: string | null
          rejection_reason?: string | null
          request_type: string
          requested_end_date?: string | null
          requested_start_date?: string | null
          requester_id?: string | null
          status?: Database["public"]["Enums"]["task_status"] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          approval_notes?: string | null
          approved_at?: string | null
          approved_end_date?: string | null
          approved_start_date?: string | null
          approver_id?: string | null
          attachments?: Json | null
          created_at?: string | null
          description?: string
          hotel_id?: string | null
          id?: string
          metadata?: Json | null
          priority?: Database["public"]["Enums"]["task_priority"] | null
          rejected_at?: string | null
          rejection_reason?: string | null
          request_type?: string
          requested_end_date?: string | null
          requested_start_date?: string | null
          requester_id?: string | null
          status?: Database["public"]["Enums"]["task_status"] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "staff_requests_approver_id_fkey"
            columns: ["approver_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_requests_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_requests_requester_id_fkey"
            columns: ["requester_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      task_history: {
        Row: {
          change_reason: string | null
          changed_at: string | null
          changed_by: string | null
          field_name: string
          id: string
          new_value: string | null
          old_value: string | null
          task_id: string | null
        }
        Insert: {
          change_reason?: string | null
          changed_at?: string | null
          changed_by?: string | null
          field_name: string
          id?: string
          new_value?: string | null
          old_value?: string | null
          task_id?: string | null
        }
        Update: {
          change_reason?: string | null
          changed_at?: string | null
          changed_by?: string | null
          field_name?: string
          id?: string
          new_value?: string | null
          old_value?: string | null
          task_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "task_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_history_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          actual_cost: number | null
          actual_duration: number | null
          after_photos: Json | null
          assigned_to: string | null
          before_photos: Json | null
          completed_at: string | null
          created_at: string | null
          created_by: string | null
          deadline: string | null
          description: string | null
          documents: Json | null
          equipment_needed: Json | null
          estimated_cost: number | null
          estimated_duration: number | null
          hotel_id: string | null
          id: string
          inspected_at: string | null
          inspected_by: string | null
          instructions: string | null
          is_recurring: boolean | null
          notes: string | null
          parent_task_id: string | null
          priority: Database["public"]["Enums"]["task_priority"] | null
          quality_notes: string | null
          quality_score: number | null
          recurrence_pattern: Json | null
          requires_inspection: boolean | null
          room_id: string | null
          safety_requirements: string | null
          scheduled_start: string | null
          started_at: string | null
          status: Database["public"]["Enums"]["task_status"] | null
          supplies_needed: Json | null
          task_type: Database["public"]["Enums"]["request_type"] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          actual_cost?: number | null
          actual_duration?: number | null
          after_photos?: Json | null
          assigned_to?: string | null
          before_photos?: Json | null
          completed_at?: string | null
          created_at?: string | null
          created_by?: string | null
          deadline?: string | null
          description?: string | null
          documents?: Json | null
          equipment_needed?: Json | null
          estimated_cost?: number | null
          estimated_duration?: number | null
          hotel_id?: string | null
          id?: string
          inspected_at?: string | null
          inspected_by?: string | null
          instructions?: string | null
          is_recurring?: boolean | null
          notes?: string | null
          parent_task_id?: string | null
          priority?: Database["public"]["Enums"]["task_priority"] | null
          quality_notes?: string | null
          quality_score?: number | null
          recurrence_pattern?: Json | null
          requires_inspection?: boolean | null
          room_id?: string | null
          safety_requirements?: string | null
          scheduled_start?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["task_status"] | null
          supplies_needed?: Json | null
          task_type?: Database["public"]["Enums"]["request_type"] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          actual_cost?: number | null
          actual_duration?: number | null
          after_photos?: Json | null
          assigned_to?: string | null
          before_photos?: Json | null
          completed_at?: string | null
          created_at?: string | null
          created_by?: string | null
          deadline?: string | null
          description?: string | null
          documents?: Json | null
          equipment_needed?: Json | null
          estimated_cost?: number | null
          estimated_duration?: number | null
          hotel_id?: string | null
          id?: string
          inspected_at?: string | null
          inspected_by?: string | null
          instructions?: string | null
          is_recurring?: boolean | null
          notes?: string | null
          parent_task_id?: string | null
          priority?: Database["public"]["Enums"]["task_priority"] | null
          quality_notes?: string | null
          quality_score?: number | null
          recurrence_pattern?: Json | null
          requires_inspection?: boolean | null
          room_id?: string | null
          safety_requirements?: string | null
          scheduled_start?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["task_status"] | null
          supplies_needed?: Json | null
          task_type?: Database["public"]["Enums"]["request_type"] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_inspected_by_fkey"
            columns: ["inspected_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_parent_task_id_fkey"
            columns: ["parent_task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_room_id_fkey"
            columns: ["room_id"]
            isOneToOne: false
            referencedRelation: "rooms"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          avatar_url: string | null
          average_task_time: number | null
          birth_date: string | null
          created_at: string | null
          customer_rating: number | null
          department: Database["public"]["Enums"]["staff_department"] | null
          email: string
          emergency_contact: Json | null
          employee_id: string | null
          first_name: string
          hire_date: string | null
          hotel_id: string | null
          hourly_rate: number | null
          id: string
          last_activity: string | null
          last_login: string | null
          last_name: string
          organization_id: string | null
          performance_score: number | null
          permissions: Json | null
          phone: string | null
          position: string | null
          role: Database["public"]["Enums"]["user_role"] | null
          shift_schedule: Json | null
          status: Database["public"]["Enums"]["staff_status"] | null
          total_tasks_completed: number | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          average_task_time?: number | null
          birth_date?: string | null
          created_at?: string | null
          customer_rating?: number | null
          department?: Database["public"]["Enums"]["staff_department"] | null
          email: string
          emergency_contact?: Json | null
          employee_id?: string | null
          first_name: string
          hire_date?: string | null
          hotel_id?: string | null
          hourly_rate?: number | null
          id: string
          last_activity?: string | null
          last_login?: string | null
          last_name: string
          organization_id?: string | null
          performance_score?: number | null
          permissions?: Json | null
          phone?: string | null
          position?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          shift_schedule?: Json | null
          status?: Database["public"]["Enums"]["staff_status"] | null
          total_tasks_completed?: number | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          average_task_time?: number | null
          birth_date?: string | null
          created_at?: string | null
          customer_rating?: number | null
          department?: Database["public"]["Enums"]["staff_department"] | null
          email?: string
          emergency_contact?: Json | null
          employee_id?: string | null
          first_name?: string
          hire_date?: string | null
          hotel_id?: string | null
          hourly_rate?: number | null
          id?: string
          last_activity?: string | null
          last_login?: string | null
          last_name?: string
          organization_id?: string | null
          performance_score?: number | null
          permissions?: Json | null
          phone?: string | null
          position?: string | null
          role?: Database["public"]["Enums"]["user_role"] | null
          shift_schedule?: Json | null
          status?: Database["public"]["Enums"]["staff_status"] | null
          total_tasks_completed?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_hotel_id_fkey"
            columns: ["hotel_id"]
            isOneToOne: false
            referencedRelation: "hotels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_profiles_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_hotel_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
      user_has_hotel_access: {
        Args: { target_hotel_id: string }
        Returns: boolean
      }
    }
    Enums: {
      channel_type: "GENERAL" | "DEPARTMENT" | "DIRECT" | "ANNOUNCEMENT"
      message_type: "TEXT" | "IMAGE" | "FILE" | "AUDIO" | "VIDEO"
      notification_channel: "IN_APP" | "EMAIL" | "SMS" | "WHATSAPP" | "PUSH"
      order_status:
        | "NEW"
        | "CONFIRMED"
        | "PREPARING"
        | "ON_THE_WAY"
        | "DELIVERED"
        | "CANCELLED"
      request_type:
        | "HOUSEKEEPING"
        | "MAINTENANCE"
        | "ROOM_SERVICE"
        | "GUEST_SERVICE"
        | "CONCIERGE"
        | "SPA"
        | "OTHER"
      room_status:
        | "CLEAN"
        | "DIRTY"
        | "OCCUPIED"
        | "OUT_OF_ORDER"
        | "MAINTENANCE"
        | "INSPECTION"
      room_type: "STANDARD" | "DELUXE" | "SUITE" | "FAMILY" | "PENTHOUSE"
      service_category_type:
        | "ROOM_SERVICE"
        | "GUEST_SERVICE"
        | "ACTIVITY"
        | "SPA_WELLNESS"
      staff_department:
        | "MANAGEMENT"
        | "HOUSEKEEPING"
        | "MAINTENANCE"
        | "FRONT_DESK"
        | "FOOD_SERVICE"
        | "SPA_WELLNESS"
        | "SECURITY"
      staff_status: "ACTIVE" | "INACTIVE" | "ON_BREAK" | "ON_LEAVE" | "OFF_DUTY"
      subscription_plan: "BASIC" | "PREMIUM" | "ENTERPRISE"
      task_priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT"
      task_status:
        | "NEW"
        | "ASSIGNED"
        | "IN_PROGRESS"
        | "QUALITY_CHECK"
        | "COMPLETED"
        | "CANCELLED"
      user_role:
        | "SUPER_ADMIN"
        | "HOTEL_ADMIN"
        | "MANAGER"
        | "SUPERVISOR"
        | "STAFF"
        | "GUEST"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      channel_type: ["GENERAL", "DEPARTMENT", "DIRECT", "ANNOUNCEMENT"],
      message_type: ["TEXT", "IMAGE", "FILE", "AUDIO", "VIDEO"],
      notification_channel: ["IN_APP", "EMAIL", "SMS", "WHATSAPP", "PUSH"],
      order_status: [
        "NEW",
        "CONFIRMED",
        "PREPARING",
        "ON_THE_WAY",
        "DELIVERED",
        "CANCELLED",
      ],
      request_type: [
        "HOUSEKEEPING",
        "MAINTENANCE",
        "ROOM_SERVICE",
        "GUEST_SERVICE",
        "CONCIERGE",
        "SPA",
        "OTHER",
      ],
      room_status: [
        "CLEAN",
        "DIRTY",
        "OCCUPIED",
        "OUT_OF_ORDER",
        "MAINTENANCE",
        "INSPECTION",
      ],
      room_type: ["STANDARD", "DELUXE", "SUITE", "FAMILY", "PENTHOUSE"],
      service_category_type: [
        "ROOM_SERVICE",
        "GUEST_SERVICE",
        "ACTIVITY",
        "SPA_WELLNESS",
      ],
      staff_department: [
        "MANAGEMENT",
        "HOUSEKEEPING",
        "MAINTENANCE",
        "FRONT_DESK",
        "FOOD_SERVICE",
        "SPA_WELLNESS",
        "SECURITY",
      ],
      staff_status: ["ACTIVE", "INACTIVE", "ON_BREAK", "ON_LEAVE", "OFF_DUTY"],
      subscription_plan: ["BASIC", "PREMIUM", "ENTERPRISE"],
      task_priority: ["LOW", "MEDIUM", "HIGH", "URGENT"],
      task_status: [
        "NEW",
        "ASSIGNED",
        "IN_PROGRESS",
        "QUALITY_CHECK",
        "COMPLETED",
        "CANCELLED",
      ],
      user_role: [
        "SUPER_ADMIN",
        "HOTEL_ADMIN",
        "MANAGER",
        "SUPERVISOR",
        "STAFF",
        "GUEST",
      ],
    },
  },
} as const