<template>
  <aside data-testid="sidebar" class="bg-white dark:bg-navy-800 w-[290px] h-screen border-r border-gray-200 dark:border-gray-700 flex flex-col fixed left-0 top-0 z-50 transition-all duration-300"
         :class="{ 'w-[80px]': store.isSidebarCollapsed }">
    <!-- Logo Section -->
    <div class="flex items-center p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
        <span class="text-white font-bold text-sm">H</span>
      </div>
      <div v-if="!store.isSidebarCollapsed" class="ml-3">
        <div class="flex items-center space-x-2">
          <span class="text-lg font-bold text-navy-700 dark:text-white">Hotelexia</span>
          <span class="bg-orange-500 text-white text-xs px-2 py-0.5 rounded-full font-medium">ADMIN</span>
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">Platform Yönetimi</div>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 overflow-y-auto">
      <div class="space-y-2">
        <!-- Dashboard -->
        <router-link
          to="/dashboard"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/dashboard' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <HomeIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Dashboard</span>
        </router-link>

        <!-- Hotel Management -->
        <router-link
          to="/hotels"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path.includes('/hotels') ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <BuildingOfficeIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Otel Yönetimi</span>
        </router-link>

        <!-- User Management -->
        <router-link
          to="/users"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/users' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <UsersIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Kullanıcı Yönetimi</span>
        </router-link>

        <!-- Analytics -->
        <router-link
          to="/analytics"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/analytics' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <ChartBarIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Platform Analitikleri</span>
        </router-link>

        <!-- Billing -->
        <router-link
          to="/billing"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/billing' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <CurrencyDollarIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Abonelik Yönetimi</span>
        </router-link>

        <!-- System Health -->
        <router-link
          to="/system-health"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/system-health' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <ServerIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Sistem Sağlığı</span>
        </router-link>

        <!-- Announcements -->
        <router-link
          to="/announcements"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/announcements' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <SpeakerWaveIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Global Duyurular</span>
        </router-link>

        <!-- Audit Log -->
        <router-link
          to="/audit-log"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/audit-log' ? 'bg-brand-50 dark:bg-brand-900/20 text-brand-600 dark:text-brand-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700'"
        >
          <ClipboardDocumentListIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Denetim Günlüğü</span>
        </router-link>

        <!-- Divider -->
        <div v-if="!store.isSidebarCollapsed" class="py-2">
          <div class="border-t border-gray-200 dark:border-gray-700"></div>
        </div>

        <!-- Settings -->
        <router-link
          to="/settings"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
          :class="$route.path === '/settings' ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'"
        >
          <Cog6ToothIcon class="h-5 w-5" />
          <span v-if="!store.isSidebarCollapsed" class="ml-3">Ayarlar</span>
        </router-link>
      </div>
    </nav>

    <!-- Help Card (only show when expanded) -->
    <div v-if="!store.isSidebarCollapsed" class="p-4 border-t border-gray-200 dark:border-gray-700">
      <div class="bg-gradient-to-r from-brand-500 to-brand-600 rounded-lg p-4 text-white">
        <div class="flex items-center space-x-2 mb-2">
          <QuestionMarkCircleIcon class="h-5 w-5" />
          <span class="font-medium text-sm">Yardıma mı ihtiyacınız var?</span>
        </div>
        <p class="text-xs text-brand-100 mb-3">
          Platform yönetimi için dokümantasyon ve destek.
        </p>
        <button class="w-full bg-white/20 hover:bg-white/30 text-white text-xs font-medium py-2 px-3 rounded-md transition-colors">
          Yardım Merkezi
        </button>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import {
  HomeIcon,
  BuildingOfficeIcon,
  UsersIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ServerIcon,
  SpeakerWaveIcon,
  ClipboardDocumentListIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'

const store = useManagementStore()
</script> 