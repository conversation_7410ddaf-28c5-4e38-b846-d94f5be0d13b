<template>
  <div class="p-6 space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white">İçerik Yönetimi Özet</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-1">
          Tüm içerik türlerinizin genel durumu ve hızlı işlem seçenekleri
        </p>
      </div>
      <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
        <ClockIcon class="w-4 h-4" />
        <span>Son güncelleme: {{ lastUpdated }}</span>
      </div>
    </div>

    <!-- KPI Cards Grid - Top Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <InfoCard
        title="Akt<PERSON>"
        :value="kpiStats.activeMenuItems"
        subtitle="Oda servisi menüsü"
        :icon="CubeIcon"
        icon-bg-class="bg-blue-100 dark:bg-blue-900/20"
        icon-color-class="text-blue-600 dark:text-blue-400"
        :trend="{ isPositive: true, percentage: 12, period: 'Bu ay' }"
      />
      
      <InfoCard
        title="Yayındaki Aktiviteler"
        :value="kpiStats.publishedActivities"
        subtitle="Misafir aktiviteleri"
        :icon="CalendarIcon"
        icon-bg-class="bg-green-100 dark:bg-green-900/20"
        icon-color-class="text-green-600 dark:text-green-400"
        :trend="{ isPositive: true, percentage: 8, period: 'Bu hafta' }"
      />
      
      <InfoCard
        title="Tanımlı Hizmetler"
        :value="kpiStats.definedServices"
        subtitle="Misafir hizmetleri"
        :icon="HeartIcon"
        icon-bg-class="bg-purple-100 dark:bg-purple-900/20"
        icon-color-class="text-purple-600 dark:text-purple-400"
      />
      
      <InfoCard
        title="Geçerli Promosyonlar"
        :value="kpiStats.activePromotions"
        subtitle="Aktif kampanyalar"
        :icon="TagIcon"
        icon-bg-class="bg-orange-100 dark:bg-orange-900/20"
        icon-color-class="text-orange-600 dark:text-orange-400"
        :trend="{ isPositive: false, percentage: 3, period: 'Bu ay' }"
      />
    </div>

    <!-- Second Row - Quick Actions and Content Distribution -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <QuickActions />
      <ContentPieChart :content-distribution="contentDistribution" />
    </div>

    <!-- Third Row - Recent Content Table -->
    <div class="grid grid-cols-1 gap-6">
      <RecentContentTable :recent-content="recentContent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import { 
  ClockIcon,
  CubeIcon,
  CalendarIcon,
  HeartIcon,
  TagIcon
} from '@heroicons/vue/24/outline'

// Widget Components
import InfoCard from '@/components/dashboards/content/InfoCard.vue'
import QuickActions from '@/components/dashboards/content/QuickActions.vue'
import RecentContentTable from '@/components/dashboards/content/RecentContentTable.vue'
import ContentPieChart from '@/components/dashboards/content/ContentPieChart.vue'

const managementStore = useManagementStore()

// KPI Statistics - Computed from store data
const kpiStats = computed(() => ({
  activeMenuItems: managementStore.menuItems.filter(item => item.isAvailable).length,
  publishedActivities: managementStore.activities.filter(activity => activity.isActive).length,
  definedServices: managementStore.guestServices.filter(service => service.isActive).length,
  activePromotions: 3 // Mock data - will be from actual promotions when implemented
}))

// Content Distribution for Pie Chart
const contentDistribution = computed(() => [
  { name: 'Oda Servisi Öğeleri', value: kpiStats.value.activeMenuItems },
  { name: 'Aktiviteler', value: kpiStats.value.publishedActivities },
  { name: 'Hizmetler', value: kpiStats.value.definedServices },
  { name: 'Promosyonlar', value: kpiStats.value.activePromotions }
])

// Recent Content - Merged and sorted by modification date
const recentContent = computed(() => {
  const allContent: Array<{
    id: string
    name: string
    type: string
    modifiedAt: Date
    status: string
  }> = []

  // Add menu items
  managementStore.menuItems.forEach(item => {
    allContent.push({
      id: item.id,
      name: item.name,
      type: 'Oda Servisi',
      modifiedAt: (item as any).updatedAt || new Date(),
      status: (item as any).isAvailable ? 'Aktif' : 'Pasif'
    })
  })

  // Add activities
  managementStore.activities.forEach(activity => {
    allContent.push({
      id: activity.id,
      name: activity.title,
      type: 'Aktivite', 
      modifiedAt: new Date(activity.startTimeUtc), // Using start time as proxy for modification
      status: activity.isActive ? 'Yayında' : 'Pasif'
    })
  })

  // Add guest services
  managementStore.guestServices.forEach(service => {
    allContent.push({
      id: service.id,
      name: service.name,
      type: 'Hizmet',
      modifiedAt: service.updatedAt,
      status: service.isActive ? 'Aktif' : 'Pasif'
    })
  })

  // Add events (as activities)
  managementStore.events.forEach(event => {
    allContent.push({
      id: event.id,
      name: event.title,
      type: 'Aktivite',
      modifiedAt: event.updatedAt,
      status: event.isActive ? 'Yayında' : 'Pasif'
    })
  })

  // Sort by modification date (newest first) and take top 5
  return allContent
    .sort((a, b) => b.modifiedAt.getTime() - a.modifiedAt.getTime())
    .slice(0, 5)
})

// Last updated timestamp
const lastUpdated = ref(new Date().toLocaleString('tr-TR', {
  day: '2-digit',
  month: '2-digit', 
  year: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
}))
</script> 