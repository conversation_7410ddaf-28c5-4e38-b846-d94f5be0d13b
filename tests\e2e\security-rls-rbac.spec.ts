import { test, expect } from '@playwright/test'
import { TestHelpers } from './utils/test-helpers'

test.describe('Security - RLS/RBAC Tests', () => {
  let helpers: TestHelpers

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page)
  })

  test.describe('Row Level Security (RLS) Tests', () => {
    test('should prevent unauthorized hotel data access', async ({ page }) => {
      // Test that users can only access their own hotel's data
      await page.goto('http://localhost:5002') // Hotel Dashboard
      await page.waitForTimeout(3000)

      // Check if authentication is required
      const hasAuth = await helpers.hasAuthForm()
      if (hasAuth) {
        console.log('Authentication required - RLS protection active')
        return
      }

      // If authenticated, test data isolation
      const hotelData = await page.locator('[data-testid="hotel-data"], .hotel-info, .dashboard-stats').count()
      if (hotelData > 0) {
        // Try to access another hotel's data via URL manipulation
        const currentUrl = page.url()
        const hotelIdMatch = currentUrl.match(/hotel[_-]?id[=:](\d+)/i)
        
        if (hotelIdMatch) {
          const currentHotelId = hotelIdMatch[1]
          const otherHotelId = String(Number(currentHotelId) + 1)
          
          // Try to access another hotel's data
          const manipulatedUrl = currentUrl.replace(currentHotelId, otherHotelId)
          await page.goto(manipulatedUrl)
          await page.waitForTimeout(2000)
          
          // Should be redirected or show access denied
          const hasAccessDenied = await page.locator('text=/access denied/i, text=/yetkisiz/i, .unauthorized').count() > 0
          const isRedirected = !page.url().includes(otherHotelId)
          
          expect(hasAccessDenied || isRedirected).toBe(true)
        }
      }
    })

    test('should enforce role-based data access in Management Portal', async ({ page }) => {
      await page.goto('http://localhost:5001/management/hotels')
      await page.waitForTimeout(3000)

      const hasAuth = await helpers.hasAuthForm()
      if (hasAuth) {
        console.log('Authentication required for Management Portal')
        return
      }

      // Test access to sensitive management functions
      const sensitiveRoutes = [
        '/management/users',
        '/management/system-settings',
        '/management/billing',
        '/management/analytics'
      ]

      for (const route of sensitiveRoutes) {
        await page.goto(`http://localhost:3000${route}`)
        await page.waitForTimeout(2000)

        // Should either require authentication or show appropriate content
        const hasContent = await helpers.hasMainContent()
        const hasAuth = await helpers.hasAuthForm()
        const hasAccessDenied = await page.locator('text=/access denied/i, text=/yetkisiz/i').count() > 0

        expect(hasContent || hasAuth || hasAccessDenied).toBe(true)
        console.log(`Route ${route}: content=${hasContent}, auth=${hasAuth}, denied=${hasAccessDenied}`)
      }
    })

    test('should prevent cross-tenant data leakage', async ({ page, context }) => {
      // Test that data from one hotel doesn't leak to another
      const page1 = await context.newPage()
      const page2 = await context.newPage()

      // Navigate to different hotel contexts
      await page1.goto('http://localhost:5002/housekeeping/rooms')
      await page2.goto('http://localhost:5002/maintenance/tasks')
      
      await page1.waitForTimeout(3000)
      await page2.waitForTimeout(3000)

      // Check for data isolation
      const page1Data = await page1.locator('[data-hotel-id], [data-testid*="hotel"]').count()
      const page2Data = await page2.locator('[data-hotel-id], [data-testid*="hotel"]').count()

      if (page1Data > 0 && page2Data > 0) {
        // Extract hotel IDs if present
        const page1HotelId = await page1.getAttribute('[data-hotel-id]', 'data-hotel-id')
        const page2HotelId = await page2.getAttribute('[data-hotel-id]', 'data-hotel-id')

        if (page1HotelId && page2HotelId) {
          // Should be the same hotel or properly isolated
          expect(page1HotelId === page2HotelId || page1HotelId !== page2HotelId).toBe(true)
        }
      }
    })
  })

  test.describe('Role-Based Access Control (RBAC) Tests', () => {
    test('should enforce SUPER_ADMIN access controls', async ({ page }) => {
      // Test SUPER_ADMIN specific routes
      const superAdminRoutes = [
        'http://localhost:5001/management/dashboard',
        'http://localhost:5001/management/hotels',
        'http://localhost:5001/management/users'
      ]

      for (const route of superAdminRoutes) {
        await page.goto(route)
        await page.waitForTimeout(3000)

        // Should either show content or require authentication
        const hasAuth = await helpers.hasAuthForm()
        const hasContent = await helpers.hasMainContent()
        const hasError = await helpers.hasErrorState()

        expect(hasAuth || hasContent || hasError).toBe(true)
        console.log(`SUPER_ADMIN route ${route}: auth=${hasAuth}, content=${hasContent}, error=${hasError}`)
      }
    })

    test('should enforce HOTEL_ADMIN/STAFF access controls', async ({ page }) => {
      // Test hotel staff specific routes
      const hotelStaffRoutes = [
        'http://localhost:5002/dashboard',
        'http://localhost:5002/housekeeping/rooms',
        'http://localhost:5002/maintenance/tasks',
        'http://localhost:5002/ordering/menu'
      ]

      for (const route of hotelStaffRoutes) {
        await page.goto(route)
        await page.waitForTimeout(3000)

        const hasAuth = await helpers.hasAuthForm()
        const hasContent = await helpers.hasMainContent()
        const hasError = await helpers.hasErrorState()

        expect(hasAuth || hasContent || hasError).toBe(true)
        console.log(`Hotel staff route ${route}: auth=${hasAuth}, content=${hasContent}, error=${hasError}`)
      }
    })

    test('should enforce GUEST access controls', async ({ page }) => {
      // Test guest-specific routes in Customer Portal
      const guestRoutes = [
        'http://localhost:3001',
        'http://localhost:3001/reservations',
        'http://localhost:3001/services',
        'http://localhost:3001/activities'
      ]

      for (const route of guestRoutes) {
        await page.goto(route)
        await page.waitForTimeout(3000)

        const hasAuth = await helpers.hasAuthForm()
        const hasContent = await helpers.hasMainContent()
        const hasError = await helpers.hasErrorState()

        expect(hasAuth || hasContent || hasError).toBe(true)
        console.log(`Guest route ${route}: auth=${hasAuth}, content=${hasContent}, error=${hasError}`)
      }
    })

    test('should prevent privilege escalation', async ({ page }) => {
      // Test that users cannot access higher privilege functions
      await page.goto('http://localhost:5002') // Hotel Dashboard
      await page.waitForTimeout(3000)

      // Try to access management functions from hotel dashboard
      const managementUrls = [
        'http://localhost:5001/management/users',
        'http://localhost:5001/management/system-settings'
      ]

      for (const url of managementUrls) {
        await page.goto(url)
        await page.waitForTimeout(2000)

        // Should be redirected or show access denied
        const hasAccessDenied = await page.locator('text=/access denied/i, text=/yetkisiz/i').count() > 0
        const isRedirected = !page.url().includes('/management/')
        const hasAuth = await helpers.hasAuthForm()

        expect(hasAccessDenied || isRedirected || hasAuth).toBe(true)
      }
    })
  })

  test.describe('Authentication Security Tests', () => {
    test('should handle session timeout and renewal', async ({ page }) => {
      await page.goto('http://localhost:5002')
      await page.waitForTimeout(3000)

      // Check if session management is working
      const hasAuth = await helpers.hasAuthForm()
      if (!hasAuth) {
        // If authenticated, test session persistence
        await page.reload()
        await page.waitForTimeout(2000)

        const stillAuthenticated = !(await helpers.hasAuthForm())
        console.log(`Session persistence: ${stillAuthenticated}`)
      }
    })

    test('should prevent unauthorized API access', async ({ page }) => {
      // Monitor network requests for unauthorized API calls
      const unauthorizedRequests: string[] = []

      page.on('response', response => {
        if (response.status() === 401 || response.status() === 403) {
          unauthorizedRequests.push(`${response.status()}: ${response.url()}`)
        }
      })

      await page.goto('http://localhost:5002')
      await page.waitForTimeout(5000)

      // Navigate through different sections to trigger API calls
      const sections = ['housekeeping', 'maintenance', 'ordering']
      for (const section of sections) {
        try {
          await page.goto(`http://localhost:5002/${section}`)
          await page.waitForTimeout(2000)
        } catch (error) {
          console.log(`Navigation to ${section} failed: ${error}`)
        }
      }

      // Log any unauthorized requests (these might be expected)
      if (unauthorizedRequests.length > 0) {
        console.log('Unauthorized requests detected:', unauthorizedRequests)
      }
    })
  })

  test.describe('Data Validation Security Tests', () => {
    test('should validate input sanitization', async ({ page }) => {
      await page.goto('http://localhost:5002')
      await page.waitForTimeout(3000)

      // Look for input fields to test
      const inputFields = await page.locator('input[type="text"], textarea').count()
      
      if (inputFields > 0) {
        const testInput = page.locator('input[type="text"], textarea').first()
        
        // Test XSS prevention
        const xssPayload = '<script>alert("xss")</script>'
        await testInput.fill(xssPayload)
        
        // Check that script is not executed
        const alertDialogs: string[] = []
        page.on('dialog', dialog => {
          alertDialogs.push(dialog.message())
          dialog.dismiss()
        })
        
        await page.waitForTimeout(1000)
        expect(alertDialogs.length).toBe(0)
      }
    })
  })
})
