import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { 
  supabase,
  type ServiceRequest,
  type ServiceRequestType,
  type ServiceRequestStatus
} from '@hotelexia/shared-supabase-client'
import { useGuestAuthStore } from './guestAuthStore'

interface CreateRequestData {
  reservation_id: string
  hotel_id: string
  room_number: string
  request_type: ServiceRequestType
  description: string
}

export const useServiceRequestStore = defineStore('serviceRequest', () => {
  // State
  const requests = ref<ServiceRequest[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const pendingRequests = computed(() => 
    requests.value.filter(req => req.status === 'pending')
  )
  
  const inProgressRequests = computed(() => 
    requests.value.filter(req => req.status === 'in_progress')
  )
  
  const completedRequests = computed(() => 
    requests.value.filter(req => req.status === 'completed')
  )

  const requestsByType = computed(() => {
    const grouped: Record<string, ServiceRequest[]> = {}
    requests.value.forEach(req => {
      if (!grouped[req.request_type]) {
        grouped[req.request_type] = []
      }
      grouped[req.request_type].push(req)
    })
    return grouped
  })

  // Actions
  async function createRequest(requestData: CreateRequestData) {
    isLoading.value = true
    error.value = null

    try {
      const { data, error: dbError } = await supabase
        .from('service_requests')
        .insert([{
          hotel_id: requestData.hotel_id,
          reservation_id: requestData.reservation_id,
          room_number: requestData.room_number,
          request_type: requestData.request_type,
          description: requestData.description,
          status: 'pending' as const
        }])
        .select()
        .single()

      if (dbError) {
        console.error('Database error creating request:', dbError)
        throw new Error('Servis talebi oluşturulurken bir hata oluştu.')
      }

      if (!data) {
        throw new Error('Servis talebi verisi alınamadı.')
      }

      // Add the new request to local state
      requests.value.unshift(data as ServiceRequest)

      return { success: true, request: data }
    } catch (err: any) {
      console.error('Create request error:', err)
      error.value = err.message || 'Servis talebi oluşturulurken bir hata oluştu.'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  async function fetchRequests(reservationId: string) {
    isLoading.value = true
    error.value = null

    try {
      const { data, error: dbError } = await supabase
        .from('service_requests')
        .select('*')
        .eq('reservation_id', reservationId)
        .order('created_at', { ascending: false })

      if (dbError) {
        console.error('Database error fetching requests:', dbError)
        throw new Error('Servis talepleri yüklenirken bir hata oluştu.')
      }

      requests.value = (data as ServiceRequest[]) || []

      return { success: true, requests: data }
    } catch (err: any) {
      console.error('Fetch requests error:', err)
      error.value = err.message || 'Servis talepleri yüklenirken bir hata oluştu.'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  async function updateRequestStatus(requestId: string, newStatus: ServiceRequestStatus) {
    isLoading.value = true
    error.value = null

    try {
      const { data, error: dbError } = await supabase
        .from('service_requests')
        .update({ status: newStatus })
        .eq('id', requestId)
        .select()
        .single()

      if (dbError) {
        console.error('Database error updating request:', dbError)
        throw new Error('Servis talebi güncellenirken bir hata oluştu.')
      }

      if (!data) {
        throw new Error('Güncellenmiş servis talebi verisi alınamadı.')
      }

      // Update local state
      const index = requests.value.findIndex(req => req.id === requestId)
      if (index !== -1) {
        requests.value[index] = data as ServiceRequest
      }

      return { success: true, request: data }
    } catch (err: any) {
      console.error('Update request error:', err)
      error.value = err.message || 'Servis talebi güncellenirken bir hata oluştu.'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  async function cancelRequest(requestId: string) {
    const guestAuthStore = useGuestAuthStore()
    
    if (!guestAuthStore.isAuthenticated) {
      throw new Error('İptal işlemi için giriş yapmalısınız.')
    }

    isLoading.value = true
    error.value = null

    // Store original state for potential rollback
    const requestIndex = requests.value.findIndex(req => req.id === requestId)
    const originalRequest = requestIndex !== -1 ? { ...requests.value[requestIndex] } : null

    try {
      // Optimistically update local state first
      if (requestIndex !== -1) {
        requests.value[requestIndex] = { ...requests.value[requestIndex], status: 'canceled' }
      }

      const guestSession = guestAuthStore.currentGuest!

      // Call guest-friendly RPC function to cancel service request
      const response = await (supabase as any).rpc('cancel_service_request_by_reservation', { 
        request_id_to_cancel: requestId,
        user_reservation_id: guestSession.id
      })
      const { data, error: rpcError } = response

      if (rpcError) {
        console.error('Service request cancellation RPC error:', rpcError)
        throw new Error('Servis talebi iptal edilirken hata oluştu.')
      }

      // Check RPC response
      if (data && !data.success) {
        throw new Error(data.error || 'Servis talebi iptal edilirken hata oluştu.')
      }

      return { success: true, data }
    } catch (err: any) {
      console.error('Cancel request error:', err)
      error.value = err.message || 'Servis talebi iptal edilirken bir hata oluştu.'
      
      // Revert optimistic update on any error
      if (requestIndex !== -1 && originalRequest) {
        requests.value[requestIndex] = originalRequest
      }
      
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  function clearError() {
    error.value = null
  }

  function clearRequests() {
    requests.value = []
    error.value = null
  }

  // Computed status counts for dashboard display
  const statusCounts = computed(() => ({
    pending: pendingRequests.value.length,
    in_progress: inProgressRequests.value.length,
    completed: completedRequests.value.length,
    total: requests.value.length
  }))

  return {
    // State
    requests,
    isLoading,
    error,
    
    // Getters
    pendingRequests,
    inProgressRequests,
    completedRequests,
    requestsByType,
    statusCounts,
    
    // Actions
    createRequest,
    fetchRequests,
    updateRequestStatus,
    cancelRequest,
    clearError,
    clearRequests
  }
}) 