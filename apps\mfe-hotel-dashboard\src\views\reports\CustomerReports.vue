<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Müş<PERSON>i Raporları</h1>
        <p class="text-gray-600 dark:text-gray-400">Misafir verilerinizi analiz edin ve öngörüler elde edin</p>
      </div>
      <div class="flex items-center space-x-4">
        <!-- Period Filter -->
        <select 
          v-model="selectedPeriod"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="week">Bu Hafta</option>
          <option value="month">Bu Ay</option>
          <option value="quarter">Bu Çeyrek</option>
          <option value="year">Bu Yıl</option>
        </select>
        <!-- Export Button -->
        <button
          @click="exportReports"
          class="px-6 py-2 bg-brand-500 hover:bg-brand-600 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
        >
          <ArrowDownTrayIcon class="w-5 h-5" />
          <span>Raporu İndir</span>
        </button>
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="metric in keyMetrics"
        :key="metric.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ metric.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ metric.value }}</p>
            <p v-if="metric.change" :class="[
              'text-sm mt-1 flex items-center',
              metric.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
            ]">
              <component :is="metric.change.startsWith('+') ? ArrowTrendingUpIcon : ArrowTrendingDownIcon" class="w-4 h-4 mr-1" />
              {{ metric.change }}
            </p>
          </div>
          <div :class="[
            'p-3 rounded-xl',
            metric.iconBg
          ]">
            <component :is="metric.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Guest Satisfaction Chart -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Misafir Memnuniyeti Trendi</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">Son 12 Ay</span>
        </div>
        <div class="h-64 flex items-center justify-center">
          <!-- Chart Placeholder -->
          <div class="text-center">
            <ChartBarIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">Memnuniyet trendi chart burada gösterilecek</p>
          </div>
        </div>
      </div>

      <!-- Booking Sources Chart -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Rezervasyon Kaynakları</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">{{ selectedPeriod === 'week' ? 'Bu Hafta' : selectedPeriod === 'month' ? 'Bu Ay' : 'Bu Yıl' }}</span>
        </div>
        <div class="space-y-4">
          <div v-for="source in bookingSources" :key="source.name" class="flex items-center justify-between">
            <div class="flex items-center">
              <div :class="['w-3 h-3 rounded-full mr-3', source.color]"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ source.name }}</span>
            </div>
            <div class="text-right">
              <span class="text-sm font-semibold text-navy-700 dark:text-white">{{ source.percentage }}%</span>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ source.count }} rezervasyon</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Segments Table -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Müşteri Segmentleri</h3>
          <div class="flex items-center space-x-4">
            <!-- Search Input -->
            <div class="relative">
              <MagnifyingGlassIcon class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Segment ara..."
                class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Segment
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Müşteri Sayısı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Ortalama Harcama
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Konaklama Süresi
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Memnuniyet Oranı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tekrar Gelme Oranı
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr v-for="segment in filteredSegments" :key="segment.name" class="hover:bg-gray-50 dark:hover:bg-navy-600">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div :class="['w-3 h-3 rounded-full mr-3', segment.color]"></div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ segment.name }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ segment.customerCount.toLocaleString('tr-TR') }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ₺{{ segment.averageSpend.toLocaleString('tr-TR') }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ segment.averageStay }} gece
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="text-sm text-gray-900 dark:text-white mr-2">{{ segment.satisfaction }}%</div>
                  <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-green-500 h-2 rounded-full"
                      :style="{ width: segment.satisfaction + '%' }"
                    ></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="text-sm text-gray-900 dark:text-white mr-2">{{ segment.returnRate }}%</div>
                  <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-blue-500 h-2 rounded-full"
                      :style="{ width: segment.returnRate + '%' }"
                    ></div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Popular Services & Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Popular Services -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">En Popüler Hizmetler</h3>
        <div class="space-y-4">
          <div v-for="service in popularServices" :key="service.name" class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-brand-400 to-brand-600 flex items-center justify-center mr-3">
                <component :is="service.icon" class="w-5 h-5 text-white" />
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ service.category }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-semibold text-navy-700 dark:text-white">{{ service.orders }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">sipariş</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Popular Activities -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">En Popüler Aktiviteler</h3>
        <div class="space-y-4">
          <div v-for="activity in popularActivities" :key="activity.name" class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-400 to-purple-600 flex items-center justify-center mr-3">
                <component :is="activity.icon" class="w-5 h-5 text-white" />
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ activity.name }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ activity.category }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-semibold text-navy-700 dark:text-white">{{ activity.participants }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">katılımcı</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ArrowDownTrayIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  UsersIcon,
  CurrencyDollarIcon,
  StarIcon,
  CalendarDaysIcon,
  FaceSmileIcon,
  GiftIcon,
  SparklesIcon,
  MusicalNoteIcon,
  CameraIcon,
  HeartIcon
} from '@heroicons/vue/24/outline'

// Reactive refs
const selectedPeriod = ref('month')
const searchQuery = ref('')

// Key Metrics Data
const keyMetrics = computed(() => [
  {
    title: 'Toplam Misafir',
    value: '2,847',
    change: '+12.3%',
    icon: UsersIcon,
    iconBg: 'bg-blue-500'
  },
  {
    title: 'Ortalama Harcama',
    value: '₺3,420',
    change: '+8.5%',
    icon: CurrencyDollarIcon,
    iconBg: 'bg-green-500'
  },
  {
    title: 'Memnuniyet Oranı',
    value: '94.2%',
    change: '+2.1%',
    icon: FaceSmileIcon,
    iconBg: 'bg-yellow-500'
  },
  {
    title: 'Tekrar Gelme Oranı',
    value: '67.8%',
    change: '+5.4%',
    icon: StarIcon,
    iconBg: 'bg-purple-500'
  }
])

// Booking Sources Data
const bookingSources = ref([
  { name: 'Booking.com', percentage: 35, count: 892, color: 'bg-blue-500' },
  { name: 'Doğrudan Rezervasyon', percentage: 28, count: 714, color: 'bg-green-500' },
  { name: 'Expedia', percentage: 18, count: 459, color: 'bg-yellow-500' },
  { name: 'Trivago', percentage: 12, count: 306, color: 'bg-purple-500' },
  { name: 'Diğer', percentage: 7, count: 178, color: 'bg-gray-500' }
])

// Customer Segments Data
const customerSegments = ref([
  {
    name: 'VIP Müşteriler',
    customerCount: 284,
    averageSpend: 8750,
    averageStay: 4.2,
    satisfaction: 98,
    returnRate: 89,
    color: 'bg-yellow-500'
  },
  {
    name: 'İş Seyahati',
    customerCount: 756,
    averageSpend: 2840,
    averageStay: 2.1,
    satisfaction: 92,
    returnRate: 74,
    color: 'bg-blue-500'
  },
  {
    name: 'Aile Tatili',
    customerCount: 1203,
    averageSpend: 4150,
    averageStay: 5.8,
    satisfaction: 96,
    returnRate: 58,
    color: 'bg-green-500'
  },
  {
    name: 'Çift Tatili',
    customerCount: 489,
    averageSpend: 3650,
    averageStay: 3.4,
    satisfaction: 95,
    returnRate: 72,
    color: 'bg-pink-500'
  },
  {
    name: 'Grup Etkinlikleri',
    customerCount: 115,
    averageSpend: 1890,
    averageStay: 2.8,
    satisfaction: 88,
    returnRate: 45,
    color: 'bg-purple-500'
  }
])

// Popular Services Data
const popularServices = ref([
  {
    name: 'Kahvaltı Servisi',
    category: 'Oda Servisi',
    orders: 1247,
    icon: GiftIcon
  },
  {
    name: 'Spa Masajı',
    category: 'Wellness',
    orders: 892,
    icon: SparklesIcon
  },
  {
    name: 'Havalimanı Transferi',
    category: 'Ulaşım',
    orders: 756,
    icon: CalendarDaysIcon
  },
  {
    name: 'Akşam Yemeği',
    category: 'Oda Servisi',
    orders: 634,
    icon: GiftIcon
  },
  {
    name: 'Çamaşır Servisi',
    category: 'Temizlik',
    orders: 523,
    icon: SparklesIcon
  }
])

// Popular Activities Data
const popularActivities = ref([
  {
    name: 'Havuz Aktiviteleri',
    category: 'Su Sporları',
    participants: 1456,
    icon: MusicalNoteIcon
  },
  {
    name: 'Canlı Müzik Gecesi',
    category: 'Eğlence',
    participants: 892,
    icon: MusicalNoteIcon
  },
  {
    name: 'Yoga Seansı',
    category: 'Wellness',
    participants: 634,
    icon: HeartIcon
  },
  {
    name: 'Fotoğraf Turu',
    category: 'Kültür',
    participants: 521,
    icon: CameraIcon
  },
  {
    name: 'Çocuk Aktiviteleri',
    category: 'Aile',
    participants: 445,
    icon: SparklesIcon
  }
])

// Computed Properties
const filteredSegments = computed(() => {
  if (!searchQuery.value) return customerSegments.value
  const query = searchQuery.value.toLowerCase()
  return customerSegments.value.filter(segment =>
    segment.name.toLowerCase().includes(query)
  )
})

// Methods
const exportReports = () => {
  alert('Rapor indirme özelliği backend entegrasyonu ile aktif hale gelecek.')
}
</script> 