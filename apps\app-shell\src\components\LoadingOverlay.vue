<template>
  <Teleport to="body">
    <Transition name="loading-overlay">
      <div 
        v-if="show" 
        class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]"
        @click.self="$emit('close')"
      >
        <div class="bg-white rounded-2xl p-8 shadow-2xl max-w-sm w-full mx-4 text-center">
          <!-- Animated Logo/Spinner -->
          <div class="relative mb-6">
            <div class="w-16 h-16 mx-auto mb-4">
              <div class="w-full h-full border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
            </div>
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-xs">H</span>
              </div>
            </div>
          </div>

          <!-- Loading Message -->
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            {{ title || 'Yükleniyor...' }}
          </h3>
          
          <p class="text-sm text-gray-600 mb-4">
            {{ message || 'Lütfen bekleyiniz, işlem devam ediyor.' }}
          </p>

          <!-- Progress Animation -->
          <div class="w-full bg-gray-200 rounded-full h-1 mb-4">
            <div class="bg-indigo-600 h-1 rounded-full animate-pulse" :style="{ width: progress + '%' }"></div>
          </div>

          <!-- Cancel Button (Optional) -->
          <button 
            v-if="cancellable"
            @click="$emit('cancel')"
            class="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            İptal Et
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  show: boolean
  title?: string
  message?: string
  cancellable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  title: '',
  message: '',
  cancellable: false
})

const emit = defineEmits<{
  close: []
  cancel: []
}>()

const progress = ref(0)
let progressInterval: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  if (props.show) {
    startProgress()
  }
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
})

const startProgress = () => {
  progress.value = 0
  progressInterval = setInterval(() => {
    progress.value += Math.random() * 30
    if (progress.value >= 90) {
      progress.value = 90 // Don't complete until actually done
      if (progressInterval) {
        clearInterval(progressInterval)
      }
    }
  }, 800)
}

// Reset progress when showing
watch(() => props.show, (newVal) => {
  if (newVal) {
    startProgress()
  } else if (progressInterval) {
    clearInterval(progressInterval)
    progress.value = 100
  }
})
</script>

<style scoped>
.loading-overlay-enter-active,
.loading-overlay-leave-active {
  transition: all 0.3s ease;
}

.loading-overlay-enter-from,
.loading-overlay-leave-to {
  opacity: 0;
  backdrop-filter: blur(0);
}

.loading-overlay-enter-to,
.loading-overlay-leave-from {
  opacity: 1;
  backdrop-filter: blur(4px);
}

.loading-overlay-enter-active .bg-white,
.loading-overlay-leave-active .bg-white {
  transition: all 0.3s ease;
  transform-origin: center;
}

.loading-overlay-enter-from .bg-white,
.loading-overlay-leave-to .bg-white {
  transform: scale(0.9);
  opacity: 0;
}
</style> 