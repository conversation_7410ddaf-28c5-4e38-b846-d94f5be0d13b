import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, ManagementPortalService, ManagementPortal, type Database, dataCacheService } from '@hotelexia/shared-supabase-client'
import {
  errorHandlingService,
  useLoadingState,
  useErrorHandling,
  useCacheManagement
} from '@hotelexia/shared-components'

export interface Hotel {
  id: string
  name: string
  status: 'Aktif' | 'Pasif'
  plan: string
  registrationDate: string
  contactPerson: string
  email: string
  phone: string
  address: string
  mfeConfig: {
    housekeeping: boolean
    maintenance: boolean
    ordering: boolean
    events: boolean
    spaWellness: boolean
  }
}

export interface PlatformStats {
  totalHotels: number
  activeSubscriptions: number
  pendingRequests: number
  systemHealth: string
}

export const useManagementStore = defineStore('management', () => {
  // Initialize enhanced composables
  const loadingState = useLoadingState({
    minLoadingTime: 500,
    maxLoadingTime: 30000,
    enableTimeout: true,
    timeoutMessage: 'Management Portal is taking longer than expected to load...'
  })

  const errorHandling = useErrorHandling({
    component: 'ManagementPortal',
    maxRetries: 3,
    retryDelay: 1000,
    enableRecovery: true
  })

  const cacheManagement = useCacheManagement({
    keyPrefix: 'management-portal',
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    enableStats: true,
    maxCacheSize: 200
  })

  // State
  const darkMode = ref(false)
  const isSidebarCollapsed = ref(false)
  // Platform Stats - Supabase reactive data
  const platformStats = ref<PlatformStats>({
    totalHotels: 0,
    activeSubscriptions: 0,
    pendingRequests: 0,
    systemHealth: 'Loading...'
  })
  const platformStatsLoading = ref(false)
  const platformStatsError = ref<string | null>(null)

  const fetchPlatformStats = async () => {
    const operationId = loadingState.startLoading('Fetching platform statistics...')
    platformStatsLoading.value = true
    platformStatsError.value = null

    try {
      const response = await cacheManagement.get(
        'platform-stats',
        () => ManagementPortalService.getPlatformStats(),
        { ttl: 2 * 60 * 1000 } // 2 minutes cache
      )

      if (response.error) {
        throw new Error(response.error)
      }

      if (response.data) {
        platformStats.value = response.data
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      platformStatsError.value = error.message

      await errorHandling.handleError(error, {
        action: 'fetch_platform_stats'
      }, {
        retryable: true,
        showToast: true
      })
    } finally {
      loadingState.stopLoading(operationId)
      platformStatsLoading.value = false
    }
  }

  // Hotels - Supabase reactive data
  const hotels = ref<Hotel[]>([])
  const hotelsLoading = ref(false)
  const hotelsError = ref<string | null>(null)

  const fetchHotels = async () => {
    const operationId = loadingState.startLoading('Fetching hotels...')
    hotelsLoading.value = true
    hotelsError.value = null

    try {
      const response = await cacheManagement.get(
        'hotels-list',
        () => ManagementPortalService.getHotels(),
        { ttl: 3 * 60 * 1000 } // 3 minutes cache
      )

      if (response.error) {
        throw new Error(response.error)
      }

      // Transform ManagementPortal.Hotel to local Hotel interface
      hotels.value = (response.data || []).map(hotel => ({
        id: hotel.id,
        name: hotel.name,
        status: hotel.isActive ? 'Aktif' : 'Pasif',
        plan: hotel.subscriptionPlan || 'Basic',
        registrationDate: hotel.createdAt?.split('T')[0] || '',
        contactPerson: hotel.contactPerson || '',
        email: hotel.email || '',
        phone: hotel.phone || '',
        address: hotel.address || '',
        mfeConfig: {
          housekeeping: true,
          maintenance: true,
          ordering: true,
          events: true,
          spaWellness: false
        }
      }))
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      hotelsError.value = error.message

      await errorHandling.handleError(error, {
        action: 'fetch_hotels'
      }, {
        retryable: true,
        showToast: true
      })
    } finally {
      loadingState.stopLoading(operationId)
      hotelsLoading.value = false
    }
  }

  // Users - Supabase reactive data
  const users = ref<ManagementPortal.User[]>([])
  const usersLoading = ref(false)
  const usersError = ref<string | null>(null)

  const fetchUsers = async () => {
    usersLoading.value = true
    usersError.value = null

    try {
      const response = await ManagementPortalService.getUsers()

      if (response.error) {
        throw new Error(response.error)
      }

      users.value = response.data || []
    } catch (err) {
      usersError.value = (err as Error).message
      console.error('Error fetching users:', err)
    } finally {
      usersLoading.value = false
    }
  }

  // Hotel Comparison - Supabase reactive data
  const hotelComparison = ref<ManagementPortal.HotelComparison[]>([])
  const hotelComparisonLoading = ref(false)
  const hotelComparisonError = ref<string | null>(null)

  const fetchHotelComparison = async () => {
    hotelComparisonLoading.value = true
    hotelComparisonError.value = null

    try {
      const response = await ManagementPortalService.getHotelComparison()

      if (response.error) {
        throw new Error(response.error)
      }

      hotelComparison.value = response.data || []
    } catch (err) {
      hotelComparisonError.value = (err as Error).message
      console.error('Error fetching hotel comparison:', err)
    } finally {
      hotelComparisonLoading.value = false
    }
  }

  // Initialize all data
  const initializeData = async () => {
    try {
      // Use Promise.allSettled to prevent one failure from stopping all data loading
      const results = await Promise.allSettled([
        fetchPlatformStats(),
        fetchHotels(),
        fetchUsers(),
        fetchHotelComparison()
      ])

      // Log any failures for debugging
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const operations = ['fetchPlatformStats', 'fetchHotels', 'fetchUsers', 'fetchHotelComparison']
          console.error(`Management Store: ${operations[index]} failed:`, result.reason)
        }
      })
    } catch (error) {
      console.error('Management Store: Critical error during data initialization:', error)
    }
  }

  // Computed
  const activeHotels = computed(() => 
    hotels.value.filter(hotel => hotel.status === 'Aktif')
  )

  const inactiveHotels = computed(() => 
    hotels.value.filter(hotel => hotel.status === 'Pasif')
  )

  // Actions
  const toggleDarkMode = () => {
    darkMode.value = !darkMode.value
    if (darkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  const toggleSidebar = () => {
    isSidebarCollapsed.value = !isSidebarCollapsed.value
  }

  const getHotelById = (id: string) => {
    return hotels.value.find(hotel => hotel.id === id)
  }

  const updateHotelStatus = (id: string, status: 'Aktif' | 'Pasif') => {
    const hotel = hotels.value.find(h => h.id === id)
    if (hotel) {
      hotel.status = status
      // Update platform stats
      if (status === 'Aktif') {
        platformStats.value.activeSubscriptions++
      } else {
        platformStats.value.activeSubscriptions--
      }
    }
  }

  const updateHotelMfeConfig = (id: string, config: Partial<Hotel['mfeConfig']>) => {
    const hotel = hotels.value.find(h => h.id === id)
    if (hotel) {
      hotel.mfeConfig = { ...hotel.mfeConfig, ...config }
    }
  }

  const updateHotelInfo = (id: string, info: Partial<Hotel>) => {
    const index = hotels.value.findIndex(h => h.id === id)
    if (index !== -1) {
      hotels.value[index] = { ...hotels.value[index], ...info }
    }
  }

  // Supabase integration functions
  const fetchHotelsFromSupabase = async () => {
    try {
      const { data, error } = await supabase
        .from('hotels')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching hotels:', error)
        return false
      }

      // Transform Supabase data to local format
      if (data) {
        hotels.value = data.map(hotel => ({
          id: hotel.id,
          name: hotel.name,
          status: (hotel as any).is_active ? 'Aktif' : 'Pasif',
          plan: 'Basic', // Default plan since subscription_plan doesn't exist
          registrationDate: hotel.created_at?.split('T')[0] || '',
          contactPerson: '', // contact_person doesn't exist in current schema
          email: '', // email doesn't exist in current schema
          phone: hotel.phone || '',
          address: hotel.address || '',
          mfeConfig: {
            housekeeping: true,
            maintenance: true,
            ordering: true,
            events: true,
            spaWellness: false
          }
        }))
      }

      return true
    } catch (err) {
      console.error('Unexpected error fetching hotels:', err)
      return false
    }
  }

  const createHotelInSupabase = async (hotelData: Partial<Hotel>) => {
    try {
      const { data, error } = await supabase
        .from('hotels')
        .insert([{
          name: hotelData.name || '',
          address: hotelData.address || '',
          city: hotelData.address || '', // Use address as city for now
          country: 'Türkiye', // Default country
          phone: hotelData.phone,
          is_active: hotelData.status === 'Aktif'
        }])
        .select()
        .single()

      if (error) {
        console.error('Error creating hotel:', error)
        return null
      }

      return data
    } catch (err) {
      console.error('Unexpected error creating hotel:', err)
      return null
    }
  }

  const updateHotelInSupabase = async (id: string, hotelData: Partial<Hotel>) => {
    try {
      const { data, error } = await supabase
        .from('hotels')
        .update({
          name: hotelData.name,
          email: hotelData.email,
          phone: hotelData.phone,
          address: hotelData.address,
          contact_person: hotelData.contactPerson,
          subscription_plan: hotelData.plan,
          status: hotelData.status === 'Aktif' ? 'active' : 'inactive'
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating hotel:', error)
        return null
      }

      return data
    } catch (err) {
      console.error('Unexpected error updating hotel:', err)
      return null
    }
  }

  const deleteHotelFromSupabase = async (id: string) => {
    try {
      const { error } = await supabase
        .from('hotels')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting hotel:', error)
        return false
      }

      // Remove from local state
      const index = hotels.value.findIndex(h => h.id === id)
      if (index !== -1) {
        hotels.value.splice(index, 1)
      }

      return true
    } catch (err) {
      console.error('Unexpected error deleting hotel:', err)
      return false
    }
  }

  // Enhanced action handlers for Management Portal
  const handleAddHotelClick = () => {
    console.log('Add Hotel button clicked - navigating to create form')
    // This will be handled by router navigation in the component
  }

  const handleEditHotelClick = (hotelId: string) => {
    console.log('Edit Hotel button clicked for hotel:', hotelId)
    // This will be handled by router navigation in the component
  }

  const handleDeleteHotelClick = async (hotelId: string, hotelName?: string) => {
    console.log('Delete Hotel button clicked for hotel:', hotelId)

    // Show confirmation dialog
    const confirmMessage = hotelName
      ? `Are you sure you want to delete "${hotelName}"? This action cannot be undone.`
      : `Are you sure you want to delete this hotel? This action cannot be undone.`

    const confirmed = confirm(confirmMessage)

    if (!confirmed) {
      return { success: false, cancelled: true }
    }

    const operationId = loadingState.startLoading('Deleting hotel...')

    try {
      // Delete from Supabase
      const response = await supabase
        .from('hotels')
        .delete()
        .eq('id', hotelId)
        .select()

      if (response.error) {
        throw response.error
      }

      // Clear cache to ensure fresh data
      await cacheManagement.invalidateByTag('hotels')

      // Refresh hotels list
      await fetchHotels()

      console.log('Hotel deleted successfully')

      return {
        success: true,
        message: 'Hotel deleted successfully!'
      }

    } catch (error: any) {
      console.error('Error deleting hotel:', error)

      let errorMessage = 'Failed to delete hotel'

      // Handle specific error types
      if (error.code === '23503') {
        errorMessage = 'Cannot delete hotel because it has related data (reservations, staff, etc.). Please remove related data first.'
      } else if (error.code === '42501') {
        errorMessage = 'You do not have permission to delete this hotel.'
      } else if (error.message) {
        errorMessage = error.message
      }

      await errorHandling.handleError(error, {
        action: 'delete_hotel',
        hotelId,
        hotelName
      }, {
        retryable: false, // Deletion is usually not retryable
        showToast: true
      })

      return {
        success: false,
        error: errorMessage
      }
    } finally {
      loadingState.stopLoading(operationId)
    }
  }

  const handleMfeManagementClick = (hotelId: string) => {
    console.log('MFE Management button clicked for hotel:', hotelId)
    // This will be handled by router navigation in the component
  }

  const handleAddHotelSubmit = async (hotelData: any) => {
    const operationId = loadingState.startLoading('Creating new hotel...')

    try {
      // Validate required fields
      if (!hotelData.name || !hotelData.name.trim()) {
        throw new Error('Hotel name is required')
      }

      // Prepare data for Supabase
      const dataToSave = {
        name: hotelData.name.trim(),
        address: hotelData.address || '',
        city: hotelData.city || '',
        country: hotelData.country || 'Türkiye',
        description: hotelData.description || '',
        main_image_url: hotelData.main_image_url || '',
        is_active: hotelData.is_active !== undefined ? hotelData.is_active : true,
        contact_person: hotelData.contactPerson || '',
        phone: hotelData.phone || '',
        email: hotelData.email || '',
        subscription_plan: hotelData.plan || 'Basic',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // Insert into Supabase
      const response = await supabase
        .from('hotels')
        .insert(dataToSave)
        .select()
        .single()

      if (response.error) {
        throw response.error
      }

      // Clear cache to ensure fresh data
      await cacheManagement.invalidateByTag('hotels')

      // Refresh hotels list
      await fetchHotels()

      console.log('Hotel created successfully:', response.data)

      return {
        success: true,
        data: response.data,
        message: 'Hotel created successfully!'
      }

    } catch (error: any) {
      console.error('Error creating hotel:', error)

      let errorMessage = 'Failed to create hotel'

      // Handle specific error types
      if (error.code === '23505') {
        errorMessage = 'A hotel with this name already exists. Please choose a different name.'
      } else if (error.code === '42501') {
        errorMessage = 'You do not have permission to create hotels.'
      } else if (error.message) {
        errorMessage = error.message
      }

      await errorHandling.handleError(error, {
        action: 'create_hotel',
        hotelData: { name: hotelData.name }
      }, {
        retryable: true,
        showToast: true
      })

      return {
        success: false,
        error: errorMessage
      }
    } finally {
      loadingState.stopLoading(operationId)
    }
  }

  const handleEditHotelSubmit = async (hotelId: string, hotelData: any) => {
    const operationId = loadingState.startLoading('Updating hotel...')

    try {
      // Validate required fields
      if (!hotelData.name || !hotelData.name.trim()) {
        throw new Error('Hotel name is required')
      }

      if (!hotelId) {
        throw new Error('Hotel ID is required for update')
      }

      // Prepare data for Supabase update
      const dataToUpdate = {
        name: hotelData.name.trim(),
        address: hotelData.address || '',
        city: hotelData.city || '',
        country: hotelData.country || 'Türkiye',
        description: hotelData.description || '',
        main_image_url: hotelData.main_image_url || '',
        is_active: hotelData.is_active !== undefined ? hotelData.is_active : true,
        contact_person: hotelData.contactPerson || '',
        phone: hotelData.phone || '',
        email: hotelData.email || '',
        subscription_plan: hotelData.plan || 'Basic',
        updated_at: new Date().toISOString()
      }

      // Update in Supabase
      const response = await supabase
        .from('hotels')
        .update(dataToUpdate)
        .eq('id', hotelId)
        .select()
        .single()

      if (response.error) {
        throw response.error
      }

      // Clear cache to ensure fresh data
      await cacheManagement.invalidateByTag('hotels')

      // Refresh hotels list
      await fetchHotels()

      console.log('Hotel updated successfully:', response.data)

      return {
        success: true,
        data: response.data,
        message: 'Hotel updated successfully!'
      }

    } catch (error: any) {
      console.error('Error updating hotel:', error)

      let errorMessage = 'Failed to update hotel'

      // Handle specific error types
      if (error.code === '23505') {
        errorMessage = 'A hotel with this name already exists. Please choose a different name.'
      } else if (error.code === '42501') {
        errorMessage = 'You do not have permission to update this hotel.'
      } else if (error.code === '23503') {
        errorMessage = 'Cannot update hotel due to related data constraints.'
      } else if (error.message) {
        errorMessage = error.message
      }

      await errorHandling.handleError(error, {
        action: 'update_hotel',
        hotelId,
        hotelData: { name: hotelData.name }
      }, {
        retryable: true,
        showToast: true
      })

      return {
        success: false,
        error: errorMessage
      }
    } finally {
      loadingState.stopLoading(operationId)
    }
  }

  // Note: initializeData is not called automatically to prevent blank pages
  // Components should call initializeData() when they're ready to load data

  return {
    // State
    darkMode,
    isSidebarCollapsed,
    platformStats,
    hotels,
    users,
    hotelComparison,

    // Loading states
    hotelsLoading,
    hotelsError,
    platformStatsLoading,
    platformStatsError,
    usersLoading,
    usersError,
    hotelComparisonLoading,
    hotelComparisonError,

    // Computed
    activeHotels,
    inactiveHotels,

    // Actions
    toggleDarkMode,
    toggleSidebar,
    getHotelById,
    updateHotelStatus,
    updateHotelMfeConfig,
    updateHotelInfo,

    // Data fetching actions
    fetchHotels,
    fetchPlatformStats,
    fetchUsers,
    fetchHotelComparison,
    initializeData,

    // Supabase actions
    fetchHotelsFromSupabase,
    createHotelInSupabase,
    updateHotelInSupabase,
    deleteHotelFromSupabase,

    // Action handlers
    handleAddHotelClick,
    handleEditHotelClick,
    handleDeleteHotelClick,
    handleMfeManagementClick,
    handleAddHotelSubmit,
    handleEditHotelSubmit
  }
}) 