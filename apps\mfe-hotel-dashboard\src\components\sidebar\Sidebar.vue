<template>
  <div class="fixed left-0 top-0 h-screen bg-white dark:bg-navy-800 shadow-xl overflow-y-auto transition-all duration-300"
       :class="store.isSidebarCollapsed ? 'w-[80px]' : 'w-[290px]'">
    <!-- Brand -->
    <div class="flex items-center px-6 py-6 border-b border-gray-200 dark:border-gray-700"
         :class="store.isSidebarCollapsed ? 'justify-center' : ''">
      <div class="flex items-center">
        <!-- Logo -->
        <div class="w-8 h-8 bg-gradient-to-r from-brand-500 to-brand-600 rounded-lg flex items-center justify-center"
             :class="store.isSidebarCollapsed ? '' : 'mr-3'">
          <span class="text-white font-bold text-sm">H</span>
        </div>
        <h2 v-if="!store.isSidebarCollapsed" class="text-xl font-bold text-navy-700 dark:text-white">HOTELEXIA</h2>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="px-4 pt-6" :class="store.isSidebarCollapsed ? 'px-2' : ''">
      <!-- Primary Navigation -->
      <div class="mb-8">
        <ul class="space-y-1">
          <SidebarMenuItem
            v-for="item in primaryNavigationItems"
            :key="item.name"
            :item="item"
            :is-collapsed="store.isSidebarCollapsed"
            :open-menus="openMenus"
            @toggle-menu="toggleMenu"
          />
        </ul>
      </div>
    </nav>

    <!-- Secondary Navigation (Management) -->
    <div class="border-t border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-4 py-4" 
         :class="store.isSidebarCollapsed ? 'px-2' : ''">
      <div v-if="!store.isSidebarCollapsed" class="mb-3">
        <span class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          Yönetim
        </span>
      </div>
      <ul class="space-y-1">
        <SidebarMenuItem
          v-for="item in managementNavigationItems"
          :key="item.name"
          :item="item"
          :is-collapsed="store.isSidebarCollapsed"
          :open-menus="openMenus"
          @toggle-menu="toggleMenu"
        />
      </ul>
    </div>

    <!-- Help Card -->
    <div v-if="!store.isSidebarCollapsed" class="mx-4 mb-6">
      <div class="bg-gradient-to-br from-brand-400 to-brand-600 rounded-xl p-4 text-white">
        <h4 class="font-semibold mb-2">Yardıma mı ihtiyacınız var?</h4>
        <p class="text-sm text-blue-100 mb-3">Dokümantasyonu inceleyin</p>
        <button class="w-full bg-white text-brand-500 text-sm font-medium py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors">
          Dokümantasyon
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ChartPieIcon, WrenchScrewdriverIcon, ShoppingBagIcon, SparklesIcon,
  TicketIcon, BellIcon, DocumentChartBarIcon, Cog6ToothIcon, UserCircleIcon, HomeIcon,
  BuildingStorefrontIcon, QueueListIcon, TagIcon, EyeIcon, ListBulletIcon, UsersIcon,
  CalendarDaysIcon, ClipboardDocumentListIcon, DocumentTextIcon, CogIcon, UserIcon
} from '@heroicons/vue/24/outline'
import SidebarMenuItem from './SidebarMenuItem.vue'
import { useDashboardStore } from '@/stores/dashboardStore'

const store = useDashboardStore()

// State for tracking which menus are open
const openMenus = ref<Record<string, boolean>>({})

// Primary Navigation Items
const primaryNavigationItems = ref([
  { 
    name: 'Dashboard', 
    path: '/', 
    icon: HomeIcon,
    exact: true
  },
  {
    name: 'Operasyonlar',
    icon: WrenchScrewdriverIcon,
    children: [
      { 
        name: 'Özet Görünüm', 
        path: '/hotel/operations-overview', 
        icon: ChartPieIcon 
      },
      {
        name: 'Housekeeping',
        icon: SparklesIcon,
        children: [
          { name: 'Özet Görünüm', path: '/hotel/housekeeping/overview', icon: ChartPieIcon },
          { name: 'Görev Atama/Değiştirme', path: '/hotel/housekeeping/tasks', icon: QueueListIcon },
          { name: 'Personel İş Talepleri', path: '/hotel/housekeeping/requests', icon: ClipboardDocumentListIcon },
          { name: 'Kat Yönetimi', path: '/hotel/housekeeping/floors', icon: BuildingStorefrontIcon },
          { name: 'Oda Yönetimi', path: '/hotel/housekeeping/rooms', icon: HomeIcon },
          { name: 'Personel Yönetimi', path: '/hotel/housekeeping/staff', icon: UsersIcon },
          { name: 'Personel İletişim', path: '/hotel/housekeeping/communication', icon: UserIcon },
          { name: 'Performans Analiz', path: '/hotel/housekeeping/performance', icon: DocumentChartBarIcon },
        ],
      },
      {
        name: 'Bakım/Onarım',
        icon: CogIcon,
        children: [
          { name: 'Özet Görünüm', path: '/hotel/maintenance/overview', icon: ChartPieIcon },
          { name: 'Kat Yönetimi', path: '/hotel/maintenance/floors', icon: BuildingStorefrontIcon },
          { name: 'Oda Yönetimi', path: '/hotel/maintenance/rooms', icon: HomeIcon },
          { name: 'Personel Yönetimi', path: '/hotel/maintenance/staff', icon: UsersIcon },
          { name: 'Personel İletişim', path: '/hotel/maintenance/communication', icon: UserIcon },
          { name: 'Performans Analiz', path: '/hotel/maintenance/performance', icon: DocumentChartBarIcon },
        ],
      },
    ],
  },
  {
    name: 'İçerik Yönetimi',
    icon: BuildingStorefrontIcon,
    children: [
      { 
        name: 'Özet Görünüm', 
        path: '/hotel/content-overview', 
        icon: ChartPieIcon 
      },
      { 
        name: 'Promo Yönetimi', 
        path: '/hotel/content/promo-management', 
        icon: TicketIcon 
      },
      {
        name: 'Oda Servisi Yönetimi',
        icon: ShoppingBagIcon,
        children: [
          { name: 'Sipariş İzleme/Yönetim', path: '/hotel/content/room-service/orders', icon: EyeIcon },
          { name: 'Ürün ve Kategori Yönetimi', path: '/hotel/content/room-service/management', icon: ListBulletIcon },
          { name: 'Promo Yönetimi', path: '/hotel/content/room-service/promo', icon: TicketIcon },
        ],
      },
      {
        name: 'Hizmet Yönetimi',
        icon: SparklesIcon,
        children: [
          { name: 'Hizmet İzleme/Yönetme', path: '/hotel/content/guest-services/tracking', icon: EyeIcon },
          { name: 'Hizmet ve Kategori Yönetimi', path: '/hotel/content/guest-services/management', icon: ListBulletIcon },
          { name: 'Promo Yönetimi', path: '/hotel/content/guest-services/promo', icon: TicketIcon },
        ],
      },
      {
        name: 'Aktivite Yönetimi',
        icon: CalendarDaysIcon,
        children: [
          { name: 'İzleme/Yönetme', path: '/hotel/content/activities/tracking', icon: EyeIcon },
          { name: 'Liste Yönetimi', path: '/manage-activities', icon: ListBulletIcon },
          { name: 'Kategori Yönetimi', path: '/hotel/content/activities/categories', icon: TagIcon },
        ],
      },
    ],
  },
  { 
    name: 'Bildirim Merkezi', 
    path: '/notifications', 
    icon: BellIcon 
  },
  {
    name: 'Raporlar',
    icon: DocumentChartBarIcon,
    children: [
      { name: 'Müşteri', path: '/hotel/reports/customer', icon: UserIcon },
      { name: 'Personel', path: '/hotel/reports/staff', icon: UsersIcon },
      { name: 'Faaliyet', path: '/hotel/reports/operational', icon: DocumentTextIcon },
    ],
  },
])

// Management Navigation Items (Secondary Navigation)
const managementNavigationItems = ref([
  { 
    name: 'Ayarlar', 
    path: '/hotel/management/settings', 
    icon: Cog6ToothIcon 
  },
  { 
    name: 'Profil', 
    path: '/hotel/management/profile', 
    icon: UserCircleIcon 
  },
])

// Toggle menu open/closed state
const toggleMenu = (menuName: string) => {
  openMenus.value[menuName] = !openMenus.value[menuName]
}
</script>

<style scoped>
/* Custom scrollbar for sidebar */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}
</style> 