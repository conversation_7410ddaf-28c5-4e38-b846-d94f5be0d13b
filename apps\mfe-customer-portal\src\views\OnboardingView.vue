<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex flex-col">
    <!-- Onboarding Slides -->
    <div class="flex-1 flex flex-col items-center justify-center px-6">
      
      <!-- Slide 1: Culinary Adventures -->
      <div v-if="currentSlide === 0" class="flex flex-col items-center text-center max-w-sm mx-auto">
        <!-- Phone Mockup with Food -->
        <div class="relative mb-12">
          <div class="w-64 h-[500px] bg-gradient-to-br from-orange-100 to-teal-100 rounded-[3rem] flex items-center justify-center relative overflow-hidden">
            <!-- Phone Frame -->
            <div class="w-56 h-[480px] bg-white rounded-[2.5rem] shadow-2xl flex flex-col relative">
              <!-- Status Bar -->
              <div class="h-6 bg-transparent"></div>
              
              <!-- Food Content -->
              <div class="flex-1 flex flex-col items-center justify-center p-6">
                <!-- Food Illustration -->
                <div class="relative mb-8">
                  <div class="w-32 h-32 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg">
                    <span class="text-4xl">🍔</span>
                  </div>
                  <!-- Decorative elements -->
                  <div class="absolute -top-2 -right-2 w-6 h-6 bg-orange-300 rounded-full opacity-70"></div>
                  <div class="absolute -bottom-1 -left-1 w-4 h-4 bg-teal-300 rounded-full opacity-70"></div>
                </div>
                
                <!-- Action Buttons -->
                <div class="space-y-3 w-full">
                  <button class="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 rounded-xl font-semibold shadow-lg">
                    Order
                  </button>
                  <div class="flex space-x-2">
                    <button class="flex-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white py-2.5 rounded-lg text-sm font-medium">
                      Menu
                    </button>
                    <button class="flex-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white py-2.5 rounded-lg text-sm font-medium">
                      Reserve
                    </button>
                    <button class="flex-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white py-2.5 rounded-lg text-sm font-medium">
                      More
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Floating Elements -->
          <div class="absolute -top-4 -left-4 w-8 h-8 bg-orange-300 rounded-full opacity-60 animate-bounce"></div>
          <div class="absolute -bottom-2 -right-6 w-6 h-6 bg-teal-300 rounded-full opacity-60 animate-pulse"></div>
          <div class="absolute top-1/2 -left-8 w-4 h-4 bg-orange-200 rounded-full opacity-50"></div>
        </div>

        <!-- Content -->
        <h1 class="text-3xl font-bold text-gray-900 mb-4 leading-tight">
          Lezzetli Yolculuğa<br>Başlayın
        </h1>
        <p class="text-gray-600 text-lg leading-relaxed">
          Uygulamamızla heyecan verici bir lezzet yolculuğuna çıkın.
        </p>
      </div>

      <!-- Slide 2: Perfect Order -->
      <div v-if="currentSlide === 1" class="flex flex-col items-center text-center max-w-sm mx-auto">
        <!-- 3D Phone with Restaurant -->
        <div class="relative mb-12">
          <div class="w-64 h-[500px] bg-gradient-to-br from-orange-100 to-teal-100 rounded-[3rem] flex items-center justify-center relative overflow-hidden">
            <!-- 3D Restaurant Building -->
            <div class="absolute top-8 left-1/2 transform -translate-x-1/2">
              <div class="w-24 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-t-lg relative">
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-16 h-6 bg-orange-400 rounded-full"></div>
                <div class="absolute inset-x-2 top-2 h-3 bg-white bg-opacity-30 rounded"></div>
                <div class="absolute bottom-0 left-2 right-2 h-8 bg-orange-600 rounded-b"></div>
                <span class="absolute bottom-1 left-1/2 transform -translate-x-1/2 text-white text-xs font-bold">RESTO</span>
              </div>
            </div>
            
            <!-- Phone Frame -->
            <div class="w-56 h-[480px] bg-white rounded-[2.5rem] shadow-2xl flex flex-col relative mt-8">
              <!-- Status Bar -->
              <div class="h-6 bg-transparent"></div>
              
              <!-- Menu Content -->
              <div class="flex-1 p-4">
                <!-- Menu Items -->
                <div class="space-y-2">
                  <div class="flex justify-between items-center p-2 bg-orange-50 rounded-lg">
                    <div class="flex-1">
                      <div class="h-2 bg-orange-300 rounded w-20 mb-1"></div>
                      <div class="h-1.5 bg-orange-200 rounded w-16"></div>
                    </div>
                    <div class="w-8 h-8 bg-orange-400 rounded"></div>
                  </div>
                  <div class="flex justify-between items-center p-2 bg-orange-50 rounded-lg">
                    <div class="flex-1">
                      <div class="h-2 bg-orange-300 rounded w-24 mb-1"></div>
                      <div class="h-1.5 bg-orange-200 rounded w-20"></div>
                    </div>
                    <div class="w-8 h-8 bg-orange-400 rounded"></div>
                  </div>
                </div>
                
                <!-- Categories -->
                <div class="mt-6 space-y-2">
                  <div class="h-2 bg-teal-300 rounded w-16"></div>
                  <div class="h-2 bg-teal-300 rounded w-20"></div>
                  <div class="h-2 bg-teal-300 rounded w-14"></div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Floating Elements -->
          <div class="absolute -top-2 -right-4 w-6 h-6 bg-pink-300 rounded-full opacity-60 animate-bounce"></div>
          <div class="absolute top-1/4 -left-6 w-8 h-8 bg-orange-300 rounded-full opacity-60 animate-pulse"></div>
          <div class="absolute -bottom-4 left-4 w-5 h-5 bg-teal-300 rounded-full opacity-50"></div>
          
          <!-- Drink -->
          <div class="absolute top-16 -right-8">
            <div class="w-12 h-16 bg-gradient-to-b from-orange-400 to-orange-600 rounded-lg relative">
              <div class="absolute top-1 left-1 right-1 h-3 bg-orange-200 rounded-t-lg"></div>
              <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-2 h-4 bg-orange-800 rounded-full"></div>
            </div>
          </div>
        </div>

        <!-- Content -->
        <h1 class="text-3xl font-bold text-gray-900 mb-4 leading-tight">
          Mükemmel Siparişinizi<br>Oluşturun
        </h1>
        <p class="text-gray-600 text-lg leading-relaxed">
          İsteklerinizi özelleştirin ve kolayca sipariş verin.
        </p>
      </div>

      <!-- Slide 3: Delivery Magic -->
      <div v-if="currentSlide === 2" class="flex flex-col items-center text-center max-w-sm mx-auto">
        <!-- Delivery Truck -->
        <div class="relative mb-12">
          <div class="w-64 h-[400px] bg-gradient-to-br from-orange-100 to-teal-100 rounded-[2rem] flex items-center justify-center relative overflow-hidden">
            <!-- Main Truck -->
            <div class="relative">
              <!-- Truck Body -->
              <div class="w-48 h-24 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg relative">
                <!-- Cargo Area -->
                <div class="absolute left-0 top-0 w-32 h-24 bg-orange-500 rounded-l-lg"></div>
                <!-- Cab -->
                <div class="absolute right-0 top-0 w-16 h-24 bg-gradient-to-r from-teal-500 to-teal-600 rounded-r-lg">
                  <!-- Window -->
                  <div class="absolute top-2 left-2 right-2 h-8 bg-teal-300 rounded"></div>
                </div>
                
                <!-- Wheels -->
                <div class="absolute -bottom-3 left-6 w-8 h-8 bg-gray-700 rounded-full">
                  <div class="absolute inset-1 bg-gray-500 rounded-full"></div>
                </div>
                <div class="absolute -bottom-3 right-20 w-8 h-8 bg-gray-700 rounded-full">
                  <div class="absolute inset-1 bg-gray-500 rounded-full"></div>
                </div>
                
                <!-- Packages -->
                <div class="absolute top-2 left-2 w-6 h-6 bg-orange-300 rounded transform rotate-12"></div>
                <div class="absolute top-8 left-8 w-8 h-8 bg-orange-400 rounded"></div>
                <div class="absolute top-4 left-16 w-5 h-7 bg-orange-300 rounded"></div>
              </div>
            </div>
          </div>
          
          <!-- Floating Elements -->
          <div class="absolute -top-4 left-8 w-6 h-6 bg-orange-300 rounded opacity-60 animate-bounce"></div>
          <div class="absolute top-8 -right-4 w-8 h-8 bg-teal-300 rounded-full opacity-60 animate-pulse"></div>
          <div class="absolute -bottom-2 -left-4 w-5 h-5 bg-orange-200 rounded-full opacity-50"></div>
          <div class="absolute top-1/2 right-8 w-4 h-4 bg-teal-200 rounded opacity-60"></div>
          
          <!-- Motion Lines -->
          <div class="absolute top-1/2 -left-8 space-y-1">
            <div class="w-6 h-0.5 bg-teal-300 rounded opacity-60"></div>
            <div class="w-4 h-0.5 bg-teal-300 rounded opacity-40"></div>
            <div class="w-8 h-0.5 bg-teal-300 rounded opacity-60"></div>
          </div>
        </div>

        <!-- Content -->
        <h1 class="text-3xl font-bold text-gray-900 mb-4 leading-tight">
          Kapınıza Kadar<br>Lezzet Tadın
        </h1>
        <p class="text-gray-600 text-lg leading-relaxed">
          Kapınıza kadar gelen lezzetli yemeklerin keyfini çıkarın.
        </p>
      </div>
    </div>

    <!-- Bottom Section -->
    <div class="pb-8 px-6">
      <!-- Page Indicators -->
      <div class="flex justify-center space-x-3 mb-8">
        <div 
          v-for="(_slide, index) in 3" 
          :key="index"
          class="transition-all duration-300 rounded-full"
          :class="currentSlide === index ? 'w-8 h-3 bg-teal-500' : 'w-3 h-3 bg-gray-300'"
        ></div>
      </div>

      <!-- Get Started Button -->
      <button 
        @click="nextSlide"
        class="w-full bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white text-lg font-semibold py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
      >
        {{ currentSlide === 2 ? 'Başlayalım' : 'Devam Et' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentSlide = ref(0)

const nextSlide = () => {
  if (currentSlide.value < 2) {
    currentSlide.value++
  } else {
    // Mark onboarding as completed
    localStorage.setItem('hotelexia_onboarding_completed', 'true')
    router.push('/')
  }
}

onMounted(() => {
  // If user has already seen onboarding, redirect to login
  if (localStorage.getItem('hotelexia_onboarding_completed')) {
    router.push('/')
  }
})
</script>

<style scoped>
/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}
</style> 