<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-white shadow-sm px-4 py-3">
      <div class="max-w-md mx-auto">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <img 
              :src="getUserAvatar()" 
              :alt="getUserName()" 
              class="w-10 h-10 rounded-full object-cover shadow-sm"
            >
            <div>
              <h1 class="text-lg font-bold text-textDark">{{ getUserName() }}</h1>
              <p class="text-xs text-textMedium">Room Service</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="text-right">
              <p class="text-xl font-bold text-primary">₺{{ cartStore.cartTotal.toFixed(2) }}</p>
            </div>
            <router-link to="/cart" class="relative p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <span v-if="cartStore.cartItemCount > 0" class="absolute -top-1 -right-1 bg-primary text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
                {{ cartStore.cartItemCount }}
              </span>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Hero Section -->
    <div class="relative bg-gradient-to-br from-teal-500 via-teal-600 to-teal-700 text-white px-4 py-8 overflow-hidden">
      <!-- Background Image -->
      <div class="absolute inset-0 z-0">
        <img
          :src="heroImageUrl"
          alt="Oda Servisi"
          class="w-full h-full object-cover opacity-20"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-teal-500/90 via-teal-600/90 to-teal-700/90"></div>
      </div>
      
      <div class="relative z-10 max-w-md mx-auto">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <p class="text-teal-100 text-sm font-medium">Bugünün Özel Menüsü</p>
              <p class="text-white text-xs">Sınırlı Süre</p>
            </div>
          </div>
          <div class="bg-red-500 text-white rounded-full px-3 py-1 text-sm font-bold animate-pulse">
            %35 İndirim
          </div>
        </div>
        
        <h1 class="text-2xl font-bold mb-2">Bugün Somon Steak Sipariş Et</h1>
        <p class="text-teal-100 mb-4 leading-relaxed">Özel sosumuz ile marine edilmiş taze somon balık steaki ve özel fiyat avantajı</p>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="text-white">
              <span class="text-2xl font-bold">₺89.99</span>
              <span class="text-teal-200 text-lg line-through ml-2">₺138.99</span>
            </div>
            <div class="flex items-center text-teal-100 text-sm">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span>4.8 (120 değerlendirme)</span>
            </div>
          </div>
          <button 
            class="bg-white text-teal-600 px-6 py-3 rounded-full font-semibold hover:bg-teal-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            @click="orderSpecialDish"
          >
            Şimdi Sipariş Ver
          </button>
        </div>
      </div>
      
      <!-- Background decoration -->
      <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full"></div>
      <div class="absolute bottom-4 left-4 w-16 h-16 bg-white/10 rounded-full"></div>
      <div class="absolute top-1/2 right-12 w-3 h-3 bg-white/20 rounded-full"></div>
      <div class="absolute top-12 right-20 w-2 h-2 bg-white/30 rounded-full"></div>
    </div>

    <!-- Food Categories -->
    <div class="max-w-md mx-auto px-4 py-6">
      <h2 class="text-xl font-bold text-textDark mb-6">Kategoriler</h2>
      
      <!-- Loading State -->
      <div v-if="menuStore.isLoading" class="grid grid-cols-2 gap-4 mb-8">
        <div v-for="n in 6" :key="n" class="bg-white rounded-3xl p-6 shadow-sm animate-pulse">
          <div class="aspect-square rounded-2xl mb-4 bg-gray-200"></div>
          <div class="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="menuStore.error" class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
        <div class="flex items-center mb-3">
          <svg class="w-6 h-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-red-800 font-semibold">Menü Yüklenemedi</h3>
        </div>
        <p class="text-red-700 text-sm mb-4">{{ menuStore.error }}</p>
        <button 
          @click="retryLoadMenu"
          class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-600 transition-colors"
        >
          Tekrar Dene
        </button>
      </div>
      
      <!-- Category Grid -->
      <div v-else class="grid grid-cols-2 gap-4 mb-8">
        <div 
          v-for="category in foodCategories" 
          :key="category.id"
          @click="selectCategory(category)"
          class="bg-white rounded-3xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer"
        >
          <div class="aspect-square rounded-2xl mb-4 overflow-hidden shadow-sm">
            <img 
              :src="category.image" 
              :alt="category.name" 
              class="w-full h-full object-cover"
            >
          </div>
          <h3 class="text-base font-bold text-textDark text-center">{{ category.name }}</h3>
        </div>
      </div>

      <!-- Recommended Section -->
      <div class="mb-8">
        <h2 class="text-xl font-bold text-textDark mb-4">Size Önerilerimiz</h2>
        
        <!-- Loading State for Recommendations -->
        <div v-if="menuStore.isLoading" class="space-y-4">
          <div v-for="n in 3" :key="n" class="bg-white rounded-2xl p-5 shadow-sm animate-pulse">
            <div class="flex justify-between items-start mb-4">
              <div class="flex-1">
                <div class="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-5/6 mb-3"></div>
                <div class="h-5 bg-gray-200 rounded w-1/4"></div>
              </div>
              <div class="w-20 h-20 bg-gray-200 rounded-2xl ml-4"></div>
            </div>
            <div class="h-12 bg-gray-200 rounded-xl"></div>
          </div>
        </div>

        <!-- Recommendations -->
        <div v-else-if="recommendedItems.length > 0" class="space-y-4">
          <div 
            v-for="item in recommendedItems" 
            :key="item.id"
            @click="goToItemDetail(item.id)"
            class="bg-white rounded-2xl p-5 shadow-sm hover:shadow-lg transition-all duration-200 cursor-pointer"
          >
            <div class="flex justify-between items-start mb-4">
              <div class="flex-1">
                <div class="flex items-center mb-2">
                  <h3 class="font-bold text-textDark text-lg">{{ item.name }}</h3>
                  <span v-if="item.isNew" class="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    YENİ
                  </span>
                </div>
                <p class="text-textMedium text-sm mb-3 leading-relaxed">{{ item.description }}</p>
                <div class="flex items-center justify-between">
                  <div>
                    <span class="text-primary font-bold text-xl">${{ item.price }}</span>
                    <span v-if="item.weight" class="text-textLight text-sm ml-2">{{ item.weight }}g</span>
                  </div>
                </div>
              </div>
              <div class="w-20 h-20 bg-gray-100 rounded-2xl ml-4 overflow-hidden shadow-sm">
                <img :src="item.image" :alt="item.name" class="w-full h-full object-cover">
              </div>
            </div>

            <!-- Add to Cart Button -->
            <button 
              @click="addToCart(item)"
              class="w-full bg-gradient-to-r from-primary to-coral-600 hover:from-primary/90 hover:to-coral-700 text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 shadow-lg"
            >
              Sepete Ekle
            </button>
          </div>
        </div>

        <!-- Empty State for Recommendations -->
        <div v-else class="bg-gray-50 rounded-2xl p-8 text-center">
          <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-600 mb-2">Henüz Menü Öğesi Yok</h3>
          <p class="text-gray-500">Şu anda önerilecek menü öğesi bulunmuyor.</p>
        </div>
      </div>

      <!-- Customer Reviews -->
      <div class="mb-8">
        <h2 class="text-xl font-bold text-textDark mb-4">Müşteri Yorumları</h2>
        <div class="space-y-4">
          <div 
            v-for="review in customerReviews" 
            :key="review.id"
            class="bg-white rounded-2xl p-5 shadow-sm"
          >
            <div class="flex items-center mb-3">
              <img :src="review.avatar" :alt="review.name" class="w-12 h-12 rounded-full object-cover shadow-sm">
              <div class="ml-3 flex-1">
                <h4 class="font-bold text-textDark">{{ review.name }}</h4>
                <div class="flex items-center">
                  <div class="flex space-x-1">
                    <svg v-for="i in 5" :key="i" class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <span class="text-textLight text-sm ml-2">{{ review.rating }}/5</span>
                </div>
              </div>
              <button class="text-primary text-sm font-medium hover:underline">Yanıtla</button>
            </div>
            <p class="text-textMedium leading-relaxed">{{ review.comment }}</p>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cartStore'
import { useMenuStore } from '../stores/menuStore'
import { useGuestAuthStore } from '../stores/guestAuthStore'
import { getSafeImageUrl } from '@/utils/imageUtils'

const router = useRouter()

// Stores
const cartStore = useCartStore()
const menuStore = useMenuStore()
const guestAuthStore = useGuestAuthStore()

// User data (from auth store)
const getUserName = () => guestAuthStore.currentGuest?.guest_name || 'Jordan Hebert'
const getUserAvatar = () => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'

// Hero image
const heroImageUrl = computed(() => getSafeImageUrl(undefined, 'food'))

// Food Categories (dynamic from menu store)
const foodCategories = computed(() => {
  return menuStore.categories.map(category => ({
    id: category.id,
    name: category.name,
    image: getSafeImageUrl(category.image_url, 'food')
  }))
})

// Recommended Items (dynamic from menu store)
const recommendedItems = computed(() => {
  return menuStore.recommendedItems.map(item => ({
    id: item.id,
    name: item.name,
    description: item.description,
    price: item.price,
    weight: 250, // Default weight
    isNew: Math.random() > 0.7, // Randomly mark some as new
    image: menuStore.getItemImageUrl(item)
  }))
})

// Customer Reviews (static for now)
const customerReviews = ref([
  {
    id: 1,
    name: 'Ayşe Kaya',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face',
    rating: 5,
    comment: 'Somon steak harika! Çok lezzetli ve sunumu mükemmeldi. Kesinlikle tekrar sipariş vereceğim.'
  },
  {
    id: 2,
    name: 'Mehmet Demir',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    rating: 5,
    comment: 'Oda servisi çok hızlı geldi ve yemekler sıcaktı. Risotto muhteşemdi!'
  }
])

// Methods
const selectCategory = (category: any) => {
  // Create URL-friendly slug from category name
  const slug = category.name.toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/ş/g, 's')
    .replace(/ç/g, 'c')
    .replace(/ğ/g, 'g')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ü/g, 'u')
  
  router.push(`/category/${slug}`)
}

const addToCart = (item: any) => {
  cartStore.addToCart({
    id: item.id.toString(),
    name: item.name,
    price: item.price,
    calories: 185, // Default calories
    weight: item.weight || 200,
    image: item.image,
    isNew: item.isNew || false
  })
}

const orderSpecialDish = () => {
  // Get the first recommended item as special dish
  const specialItem = recommendedItems.value[0]
  if (specialItem) {
  cartStore.addToCart({
      id: specialItem.id,
      name: `Özel ${specialItem.name}`,
      price: 89.99, // Special price
    calories: 250,
    weight: 400,
      image: specialItem.image,
    isNew: true
  })
  }
}

const goToItemDetail = (itemId: string) => {
  router.push(`/item/${itemId}`)
}

const retryLoadMenu = async () => {
  if (guestAuthStore.currentHotelId) {
    await menuStore.refreshMenu(guestAuthStore.currentHotelId)
  }
}

// Load menu data when component mounts
onMounted(async () => {
  if (guestAuthStore.currentHotelId) {
    await menuStore.fetchFullMenu(guestAuthStore.currentHotelId)
  } else {
    console.warn('No hotel ID available, cannot fetch menu')
  }
})
</script>

<style scoped>
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

.coral-600 {
  background-color: #ff5656;
}

.coral-700 {
  background-color: #e74c3c;
}
</style> 