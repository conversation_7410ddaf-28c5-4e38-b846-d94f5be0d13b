<template>
  <div 
    v-if="show" 
    :class="[
      'error-display',
      `error-display--${variant}`,
      `error-display--${severity}`
    ]"
  >
    <!-- Icon -->
    <div class="error-icon">
      <div v-if="severity === 'warning'" class="icon warning-icon">⚠️</div>
      <div v-else-if="severity === 'error'" class="icon error-icon">❌</div>
      <div v-else class="icon info-icon">ℹ️</div>
    </div>

    <!-- Content -->
    <div class="error-content">
      <h3 v-if="title" class="error-title">{{ title }}</h3>
      <p class="error-message">{{ message }}</p>
      <p v-if="details" class="error-details">{{ details }}</p>
      
      <!-- Error Code -->
      <div v-if="errorCode" class="error-code">
        <span class="code-label">Error Code:</span>
        <code class="code-value">{{ errorCode }}</code>
      </div>

      <!-- Timestamp -->
      <div v-if="showTimestamp" class="error-timestamp">
        <span class="timestamp-label">Occurred at:</span>
        <span class="timestamp-value">{{ formattedTimestamp }}</span>
      </div>
    </div>

    <!-- Actions -->
    <div v-if="showActions" class="error-actions">
      <button
        v-if="retryable"
        @click="handleRetry"
        :disabled="retrying"
        class="btn btn--primary"
      >
        <span v-if="retrying" class="w-4 h-4 animate-spin">🔄</span>
        <span>{{ retrying ? 'Retrying...' : 'Retry' }}</span>
      </button>
      
      <button
        v-if="dismissible"
        @click="handleDismiss"
        class="btn btn--secondary"
      >
        Dismiss
      </button>
      
      <button
        v-if="reportable"
        @click="handleReport"
        class="btn btn--outline"
      >
        Report Issue
      </button>
    </div>

    <!-- Collapse/Expand for details -->
    <div v-if="expandableDetails" class="error-expandable">
      <button
        @click="expanded = !expanded"
        class="expand-toggle"
      >
        <span
          :class="['w-4 h-4 transition-transform', { 'rotate-180': expanded }]"
        >▼</span>
        <span>{{ expanded ? 'Hide' : 'Show' }} Details</span>
      </button>
      
      <div v-if="expanded" class="expanded-details">
        <pre class="details-content">{{ expandableDetails }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
// Note: Icons will be provided by the consuming application
// import {
//   ExclamationTriangleIcon,
//   XCircleIcon,
//   InformationCircleIcon,
//   ArrowPathIcon,
//   ChevronDownIcon
// } from '@heroicons/vue/24/outline'

export interface ErrorDisplayProps {
  show?: boolean
  variant?: 'card' | 'banner' | 'inline' | 'modal'
  severity?: 'info' | 'warning' | 'error' | 'critical'
  title?: string
  message: string
  details?: string
  errorCode?: string
  timestamp?: Date | string | number
  showTimestamp?: boolean
  retryable?: boolean
  dismissible?: boolean
  reportable?: boolean
  showActions?: boolean
  expandableDetails?: string
}

const props = withDefaults(defineProps<ErrorDisplayProps>(), {
  show: true,
  variant: 'card',
  severity: 'error',
  showTimestamp: false,
  retryable: false,
  dismissible: true,
  reportable: false,
  showActions: true
})

const emit = defineEmits<{
  retry: []
  dismiss: []
  report: [error: { message: string; details?: string; code?: string }]
}>()

// Local state
const retrying = ref(false)
const expanded = ref(false)

// Computed
const formattedTimestamp = computed(() => {
  if (!props.timestamp) return ''
  
  const date = new Date(props.timestamp)
  return date.toLocaleString()
})

// Methods
const handleRetry = async () => {
  retrying.value = true
  try {
    emit('retry')
  } finally {
    // Reset after a delay to show feedback
    setTimeout(() => {
      retrying.value = false
    }, 1000)
  }
}

const handleDismiss = () => {
  emit('dismiss')
}

const handleReport = () => {
  emit('report', {
    message: props.message,
    details: props.details,
    code: props.errorCode
  })
}
</script>

<style scoped>
.error-display {
  @apply flex flex-col space-y-4 p-4 rounded-lg border;
}

.error-display--card {
  @apply bg-white dark:bg-gray-800 shadow-sm;
}

.error-display--banner {
  @apply bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700;
}

.error-display--inline {
  @apply bg-transparent border-none p-2 space-y-2;
}

.error-display--modal {
  @apply bg-white dark:bg-gray-800 shadow-lg;
}

/* Severity variants */
.error-display--info {
  @apply border-blue-200 dark:border-blue-700;
}

.error-display--info .error-icon {
  @apply text-blue-500;
}

.error-display--warning {
  @apply border-yellow-200 dark:border-yellow-700;
}

.error-display--warning .error-icon {
  @apply text-yellow-500;
}

.error-display--error {
  @apply border-red-200 dark:border-red-700;
}

.error-display--error .error-icon {
  @apply text-red-500;
}

.error-display--critical {
  @apply border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900;
}

.error-display--critical .error-icon {
  @apply text-red-600;
}

/* Components */
.error-icon {
  @apply flex-shrink-0;
}

.error-icon .icon {
  @apply w-6 h-6;
}

.error-content {
  @apply flex-1 space-y-2;
}

.error-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.error-message {
  @apply text-gray-800 dark:text-gray-200;
}

.error-details {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.error-code {
  @apply flex items-center space-x-2 text-sm;
}

.code-label {
  @apply text-gray-500 dark:text-gray-400;
}

.code-value {
  @apply bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono text-xs;
}

.error-timestamp {
  @apply flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400;
}

.error-actions {
  @apply flex flex-wrap gap-2;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn--primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn--secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn--outline {
  @apply border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.error-expandable {
  @apply border-t border-gray-200 dark:border-gray-700 pt-4;
}

.expand-toggle {
  @apply flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors;
}

.expanded-details {
  @apply mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md;
}

.details-content {
  @apply text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap overflow-x-auto;
}
</style>
