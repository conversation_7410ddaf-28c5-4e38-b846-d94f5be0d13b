<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Platform Analitikleri</h1>
      <p class="text-gray-600 dark:text-gray-400">Hotelexia platformunun kapsamlı analiz raporları</p>
    </div>

    <!-- Analytics Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Kullanıcı</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">
              {{ isLoading ? '...' : (platformStats?.totalUsers?.toLocaleString() || '0') }}
            </p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <UsersIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
          <span class="text-green-600 font-medium">+{{ platformStats?.userGrowthRate || '0' }}%</span>
          <span class="text-gray-500 dark:text-gray-400 ml-1">geçen aya göre</span>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Otel</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">
              {{ isLoading ? '...' : (platformStats?.totalHotels?.toLocaleString() || '0') }}
            </p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <HomeIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
          <span class="text-green-600 font-medium">+{{ platformStats?.hotelGrowthRate || '0' }}%</span>
          <span class="text-gray-500 dark:text-gray-400 ml-1">geçen aya göre</span>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Aktif Abonelik</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">
              {{ isLoading ? '...' : (platformStats?.activeSubscriptions?.toLocaleString() || '0') }}
            </p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
            <CheckCircleIcon class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
          <span class="text-purple-600 font-medium">+{{ platformStats?.subscriptionGrowthRate || '0' }}%</span>
          <span class="text-gray-500 dark:text-gray-400 ml-1">geçen aya göre</span>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Gelir</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">
              {{ isLoading ? '...' : (platformStats?.totalRevenue ? `₺${platformStats.totalRevenue.toLocaleString()}` : '₺0') }}
            </p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <ChartBarIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
          <span class="text-green-600 font-medium">+{{ platformStats?.revenueGrowthRate || '0' }}%</span>
          <span class="text-gray-500 dark:text-gray-400 ml-1">geçen aya göre</span>
        </div>
      </div>
    </div>

    <!-- Chart Placeholders -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Kullanıcı Büyümesi (Son 6 Ay)</h2>
        <div class="h-64 bg-gray-100 dark:bg-navy-700 rounded-lg flex items-center justify-center">
          <div class="text-center">
            <ChartBarIcon class="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 dark:text-gray-400">Grafik verileri yüklenecek</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">MFE Modül Kullanımı</h2>
        <div class="h-64 bg-gray-100 dark:bg-navy-700 rounded-lg flex items-center justify-center">
          <div class="text-center">
            <ChartPieIcon class="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 dark:text-gray-400">Pasta grafik verileri yüklenecek</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Performing Hotels -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">En Performanslı Oteller</h2>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Otel</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Aktif Kullanıcı</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Sipariş Sayısı</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Gelir</th>
              <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Memnuniyet</th>
            </tr>
          </thead>
          <tbody>
            <tr class="border-b border-gray-100 dark:border-gray-800">
              <td class="py-3 px-4 text-navy-700 dark:text-white font-medium">Antalya Sahil Resort</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">847</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">1,245</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">₺18,750</td>
              <td class="py-3 px-4">
                <div class="flex items-center">
                  <span class="text-yellow-500">★★★★★</span>
                  <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">4.8</span>
                </div>
              </td>
            </tr>
            <tr class="border-b border-gray-100 dark:border-gray-800">
              <td class="py-3 px-4 text-navy-700 dark:text-white font-medium">Ege Palas İzmir</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">623</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">892</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">₺14,250</td>
              <td class="py-3 px-4">
                <div class="flex items-center">
                  <span class="text-yellow-500">★★★★☆</span>
                  <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">4.6</span>
                </div>
              </td>
            </tr>
            <tr class="border-b border-gray-100 dark:border-gray-800">
              <td class="py-3 px-4 text-navy-700 dark:text-white font-medium">Bosphorus Palace İstanbul</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">781</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">1,156</td>
              <td class="py-3 px-4 text-gray-600 dark:text-gray-400">₺22,890</td>
              <td class="py-3 px-4">
                <div class="flex items-center">
                  <span class="text-yellow-500">★★★★★</span>
                  <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">4.9</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Feature Usage Statistics -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">MFE Modül Kullanım İstatistikleri</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
            <HomeIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 class="font-semibold text-navy-700 dark:text-white">Kat Hizmetleri</h3>
          <p class="text-2xl font-bold text-blue-600 mt-1">94%</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Kullanım oranı</p>
        </div>
        
        <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
            <ShoppingCartIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 class="font-semibold text-navy-700 dark:text-white">Dijital Sipariş</h3>
          <p class="text-2xl font-bold text-green-600 mt-1">87%</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Kullanım oranı</p>
        </div>
        
        <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
            <WrenchScrewdriverIcon class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 class="font-semibold text-navy-700 dark:text-white">Bakım Yönetimi</h3>
          <p class="text-2xl font-bold text-purple-600 mt-1">72%</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Kullanım oranı</p>
        </div>
        
        <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
            <CalendarIcon class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <h3 class="font-semibold text-navy-700 dark:text-white">Aktivite Yönetimi</h3>
          <p class="text-2xl font-bold text-orange-600 mt-1">65%</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Kullanım oranı</p>
        </div>
        
        <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
            <SparklesIcon class="h-6 w-6 text-pink-600 dark:text-pink-400" />
          </div>
          <h3 class="font-semibold text-navy-700 dark:text-white">SPA & Wellness</h3>
          <p class="text-2xl font-bold text-pink-600 mt-1">23%</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Kullanım oranı</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import {
  UsersIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ChartPieIcon,
  HomeIcon,
  ShoppingCartIcon,
  WrenchScrewdriverIcon,
  CalendarIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline'
import { useManagementStore } from '@/stores/managementStore'

const store = useManagementStore()

// Computed properties for live data
const platformStats = computed(() => store.platformStats)
const hotelComparison = computed(() => store.hotelComparison)
const isLoading = computed(() => store.platformStatsLoading || store.hotelComparisonLoading)

onMounted(() => {
  // Initialize analytics data
  store.fetchPlatformStats()
  store.fetchHotelComparison()
})
</script>