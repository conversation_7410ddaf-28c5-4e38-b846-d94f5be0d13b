// Components
export { default as But<PERSON> } from './components/Button.vue'
export { default as Card } from './components/Card.vue'
export { default as Input } from './components/Input.vue'
export { default as ErrorBoundary } from './components/ErrorBoundary.vue'
export { default as ErrorDisplay } from './components/ErrorDisplay.vue'
export { default as LoadingState } from './components/LoadingState.vue'
export { default as LoadingPage } from './components/LoadingPage.vue'

// Utilities
export * from './lib/utils'

// Services
// export * from './services/CrossMFEDataSync' // Temporarily disabled due to build issues
export * from './services/ErrorHandlingService'

// Composables
export * from './composables/useAsyncData'
export * from './composables/useAuthGuard'
export * from './composables/useCrossMFESync'
export * from './composables/useCrossMFERefresh'
export * from './composables/useErrorHandling'
export * from './composables/useLoadingState'
export * from './composables/useCacheManagement'
export * from './composables/useDataFetching'
export * from './composables/useCrossMFEDataConsistency'

// I18n
export { default as i18n, setLocale, getLocale, initializeLocale, SUPPORTED_LOCALES, DEFAULT_LOCALE } from './i18n'
export type { SupportedLocale, MessageSchema } from './i18n'

// Types
export type { PortalTheme } from './lib/utils'

// Re-export common types from class-variance-authority
export type { VariantProps } from 'class-variance-authority'