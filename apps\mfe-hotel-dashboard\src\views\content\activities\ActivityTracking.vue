<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Aktivite <PERSON>ı<PERSON><PERSON><PERSON></h1>
      <p class="text-gray-600 dark:text-gray-300">Mi<PERSON><PERSON>r aktivite kayıtlarını ve katılım durumlarını yönetin.</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ totalRegistrations }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Toplam Kayıt</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ attendedCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Katılan</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ registeredCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">Kayıtlı</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ cancelledCount }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">İptal</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-4 shadow-card mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Durum Filtresi</label>
          <select v-model="selectedStatus" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-navy-600 focus:outline-none focus:ring-brand-500 focus:border-brand-500 sm:text-sm rounded-md dark:bg-navy-700 dark:text-white">
            <option value="">Tüm Durumlar</option>
            <option value="REGISTERED">Kayıtlı</option>
            <option value="ATTENDED">Katıldı</option>
            <option value="CANCELLED">İptal Edildi</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Aktivite Filtresi</label>
          <select v-model="selectedActivity" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-navy-600 focus:outline-none focus:ring-brand-500 focus:border-brand-500 sm:text-sm rounded-md dark:bg-navy-700 dark:text-white">
            <option value="">Tüm Aktiviteler</option>
            <option v-for="activity in uniqueActivities" :key="activity" :value="activity">
              {{ activity }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Arama</label>
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="Misafir adı veya oda numarası..."
            class="block w-full px-3 py-2 border border-gray-300 dark:border-navy-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-brand-500 focus:border-brand-500 sm:text-sm dark:bg-navy-700 dark:text-white"
          />
        </div>

        <div class="flex items-end">
          <button 
            @click="clearFilters"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-navy-700 border border-gray-300 dark:border-navy-600 rounded-md hover:bg-gray-200 dark:hover:bg-navy-600 transition-colors duration-200"
          >
            Filtreleri Temizle
          </button>
        </div>
      </div>
    </div>

    <!-- Registration List -->
    <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-600">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
          Aktivite Kayıtları ({{ filteredRegistrations.length }})
        </h3>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-navy-600">
          <thead class="bg-gray-50 dark:bg-navy-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Misafir Bilgileri
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Aktivite
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Kayıt Tarihi
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Durum
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                İşlemler
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-800 divide-y divide-gray-200 dark:divide-navy-600">
            <tr v-for="registration in paginatedRegistrations" :key="registration.id" class="hover:bg-gray-50 dark:hover:bg-navy-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-navy-600 flex items-center justify-center">
                      <svg class="h-6 w-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ registration.guestName }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Oda {{ registration.roomNumber }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-white">{{ registration.activityTitle }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">#{{ registration.activityId }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(registration.registeredAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(registration.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  <svg :class="getStatusIconClass(registration.status)" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getStatusIconPath(registration.status)"></path>
                  </svg>
                  {{ getStatusText(registration.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    v-if="registration.status === 'REGISTERED'"
                    @click="updateRegistrationStatus(registration.id, 'ATTENDED')"
                    class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 transition-colors duration-200"
                  >
                    Katıldı Olarak İşaretle
                  </button>
                  <button
                    v-if="registration.status === 'REGISTERED'"
                    @click="updateRegistrationStatus(registration.id, 'CANCELLED')"
                    class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200"
                  >
                    İptal Et
                  </button>
                  <button
                    v-if="registration.status === 'CANCELLED'"
                    @click="updateRegistrationStatus(registration.id, 'REGISTERED')"
                    class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"
                  >
                    Yeniden Kaydet
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="filteredRegistrations.length > itemsPerPage" class="bg-white dark:bg-navy-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-navy-600 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="currentPage--"
            :disabled="currentPage <= 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-navy-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Önceki
          </button>
          <button
            @click="currentPage++"
            :disabled="currentPage >= totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-navy-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Sonraki
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700 dark:text-gray-300">
              <span class="font-medium">{{ startItem }}</span>
              -
              <span class="font-medium">{{ endItem }}</span>
              arası, toplam
              <span class="font-medium">{{ filteredRegistrations.length }}</span>
              sonuç
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="currentPage--"
                :disabled="currentPage <= 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-navy-600 bg-white dark:bg-navy-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-navy-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="currentPage = page"
                :class="[
                  page === currentPage
                    ? 'z-10 bg-brand-50 dark:bg-brand-900 border-brand-500 dark:border-brand-400 text-brand-600 dark:text-brand-400'
                    : 'bg-white dark:bg-navy-700 border-gray-300 dark:border-navy-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-navy-600',
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                ]"
              >
                {{ page }}
              </button>
              <button
                @click="currentPage++"
                :disabled="currentPage >= totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-navy-600 bg-white dark:bg-navy-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-navy-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredRegistrations.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200">Kayıt bulunamadı</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Seçilen filtrelere uygun aktivite kaydı bulunamadı.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import type { ActivityRegistration } from '@/types/management'

const managementStore = useManagementStore()

// Filters
const selectedStatus = ref('')
const selectedActivity = ref('')
const searchQuery = ref('')

// Pagination
const currentPage = ref(1)
const itemsPerPage = 10

// Statistics computed properties
const totalRegistrations = computed(() => managementStore.activityRegistrations.length)
const attendedCount = computed(() => managementStore.activityRegistrations.filter(r => r.status === 'ATTENDED').length)
const registeredCount = computed(() => managementStore.activityRegistrations.filter(r => r.status === 'REGISTERED').length)
const cancelledCount = computed(() => managementStore.activityRegistrations.filter(r => r.status === 'CANCELLED').length)

// Unique activities for filter
const uniqueActivities = computed(() => {
  const activities = managementStore.activityRegistrations.map(r => r.activityTitle)
  return [...new Set(activities)]
})

// Filtered registrations
const filteredRegistrations = computed(() => {
  let filtered = managementStore.activityRegistrations

  if (selectedStatus.value) {
    filtered = filtered.filter(registration => registration.status === selectedStatus.value)
  }

  if (selectedActivity.value) {
    filtered = filtered.filter(registration => registration.activityTitle === selectedActivity.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(registration =>
      registration.guestName.toLowerCase().includes(query) ||
      registration.roomNumber.toLowerCase().includes(query) ||
      registration.activityTitle.toLowerCase().includes(query)
    )
  }

  return filtered.sort((a, b) => b.registeredAt.getTime() - a.registeredAt.getTime())
})

// Pagination computed properties
const totalPages = computed(() => Math.ceil(filteredRegistrations.value.length / itemsPerPage))
const startItem = computed(() => (currentPage.value - 1) * itemsPerPage + 1)
const endItem = computed(() => Math.min(currentPage.value * itemsPerPage, filteredRegistrations.value.length))

const paginatedRegistrations = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredRegistrations.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages: number[] = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)
  
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const updateRegistrationStatus = (registrationId: string, newStatus: ActivityRegistration['status']) => {
  managementStore.updateActivityRegistrationStatus(registrationId, newStatus)
}

const clearFilters = () => {
  selectedStatus.value = ''
  selectedActivity.value = ''
  searchQuery.value = ''
  currentPage.value = 1
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const getStatusClass = (status: ActivityRegistration['status']) => {
  switch (status) {
    case 'REGISTERED':
      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
    case 'ATTENDED':
      return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
    case 'CANCELLED':
      return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
    default:
      return 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'
  }
}

const getStatusIconPath = (status: ActivityRegistration['status']) => {
  switch (status) {
    case 'REGISTERED':
      return 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
    case 'ATTENDED':
      return 'M5 13l4 4L19 7'
    case 'CANCELLED':
      return 'M6 18L18 6M6 6l12 12'
    default:
      return 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
  }
}

const getStatusIconClass = (status: ActivityRegistration['status']) => {
  switch (status) {
    case 'REGISTERED':
      return 'text-yellow-600 dark:text-yellow-400'
    case 'ATTENDED':
      return 'text-green-600 dark:text-green-400'
    case 'CANCELLED':
      return 'text-red-600 dark:text-red-400'
    default:
      return 'text-gray-600 dark:text-gray-400'
  }
}

const getStatusText = (status: ActivityRegistration['status']) => {
  switch (status) {
    case 'REGISTERED':
      return 'Kayıtlı'
    case 'ATTENDED':
      return 'Katıldı'
    case 'CANCELLED':
      return 'İptal Edildi'
    default:
      return 'Bilinmeyen'
  }
}
</script> 