<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <div class="max-w-6xl mx-auto">
      <!-- Header -->
      <div class="bg-gradient-to-r from-brand-500 to-purple-600 p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold">Yeni Otel Kurulumu</h1>
            <p class="text-brand-100 mt-1">Hotelexia platformuna yeni bir otel ekleyin</p>
          </div>
          <router-link 
            to="/management/hotels"
            class="text-white hover:text-gray-200 transition-colors flex items-center space-x-2"
          >
            <ArrowLeftIcon class="h-5 w-5" />
            <span>Geri <PERSON></span>
          </router-link>
        </div>
        
        <!-- Progress Stepper -->
        <div class="mt-6">
          <div class="flex items-center justify-between">
            <div 
              v-for="(step, index) in steps" 
              :key="index"
              class="flex items-center"
              :class="{ 'flex-1': index < steps.length - 1 }"
            >
              <div class="flex items-center">
                <div 
                  class="w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all"
                  :class="index < currentStep ? 'bg-white text-brand-500 border-white' : 
                          index === currentStep ? 'bg-brand-300 text-brand-800 border-brand-300' : 
                          'bg-transparent text-brand-200 border-brand-300'"
                >
                  <CheckIcon v-if="index < currentStep" class="h-5 w-5" />
                  <span v-else class="font-semibold">{{ index + 1 }}</span>
                </div>
                <div class="ml-3 text-left">
                  <div class="text-sm font-medium" :class="index <= currentStep ? 'text-white' : 'text-brand-200'">
                    {{ step.title }}
                  </div>
                  <div class="text-xs" :class="index <= currentStep ? 'text-brand-100' : 'text-brand-300'">
                    {{ step.subtitle }}
                  </div>
                </div>
              </div>
              
              <!-- Progress Line -->
              <div 
                v-if="index < steps.length - 1" 
                class="flex-1 h-0.5 mx-4"
                :class="index < currentStep ? 'bg-white' : 'bg-brand-300'"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="bg-white dark:bg-navy-800 p-8 min-h-[calc(100vh-200px)]">
        <!-- Step 1: Otel Bilgileri -->
        <div v-if="currentStep === 0" class="space-y-6">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-navy-700 dark:text-white">Otel Bilgilerini Girin</h3>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Yeni otelin temel bilgilerini tanımlayın</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Otel Adı *
                </label>
                <input
                  v-model="hotelData.name"
                  type="text"
                  placeholder="Örn: Grand Otel İzmir"
                  class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  required
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Otel Adresi *
                </label>
                <textarea
                  v-model="hotelData.address"
                  rows="3"
                  placeholder="Tam adres bilgisini girin"
                  class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  required
                ></textarea>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Yetkili Adı Soyadı *
                </label>
                <input
                  v-model="hotelData.contactPerson"
                  type="text"
                  placeholder="Örn: Ahmet Yılmaz"
                  class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  required
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Yetkili E-posta *
                </label>
                <input
                  v-model="hotelData.email"
                  type="email"
                  placeholder="Örn: <EMAIL>"
                  class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  required
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Telefon *
                </label>
                <input
                  v-model="hotelData.phone"
                  type="tel"
                  placeholder="Örn: +90 232 123 45 67"
                  class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  required
                >
              </div>
            </div>

            <!-- Map Section -->
            <div class="bg-gray-100 dark:bg-navy-700 rounded-lg p-4 flex items-center justify-center">
              <div class="text-center">
                <div class="relative bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg p-8 mb-4">
                  <MapPinIcon class="h-16 w-16 text-white mx-auto mb-2" />
                  <div class="text-white text-sm font-medium">İzmir, Türkiye</div>
                  <div class="absolute top-2 right-2 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  Otel konumu haritada gösterilecek
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: Plan ve Modüller -->
        <div v-if="currentStep === 1" class="space-y-6">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-navy-700 dark:text-white">Abonelik Planı ve Modüleri Seçin</h3>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Otel için en uygun planı ve modülleri belirleyin</p>
          </div>

          <!-- Plan Selection -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div 
              v-for="plan in plans" 
              :key="plan.id"
              @click="selectedPlan = plan.id"
              class="border-2 rounded-xl p-6 cursor-pointer transition-all hover:shadow-lg"
              :class="selectedPlan === plan.id ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20' : 'border-gray-200 dark:border-gray-600 hover:border-brand-300'"
            >
              <div class="text-center">
                <div 
                  class="w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center"
                  :class="plan.iconBg"
                >
                  <component :is="plan.icon" class="h-6 w-6" :class="plan.iconColor" />
                </div>
                <h4 class="text-lg font-semibold text-navy-700 dark:text-white mb-2">{{ plan.name }}</h4>
                <div class="text-2xl font-bold text-brand-600 mb-3">{{ plan.price }}</div>
                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                  <li v-for="feature in plan.features" :key="feature" class="flex items-center">
                    <CheckIcon class="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {{ feature }}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- MFE Modules -->
          <div>
            <h4 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Modül Seçimi</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                v-for="module in mfeModules" 
                :key="module.id"
                class="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
              >
                <div class="flex items-start space-x-3">
                  <div class="relative">
                    <input
                      :id="module.id"
                      v-model="selectedModules[module.id]"
                      type="checkbox"
                      :disabled="!isModuleAvailable(module)"
                      class="h-5 w-5 text-brand-600 rounded border-gray-300 focus:ring-brand-500"
                    >
                    <LockClosedIcon 
                      v-if="!isModuleAvailable(module)"
                      class="absolute -top-1 -right-1 h-3 w-3 text-gray-400"
                    />
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <component :is="module.icon" class="h-5 w-5 text-brand-500" />
                      <label 
                        :for="module.id" 
                        class="font-medium cursor-pointer"
                        :class="isModuleAvailable(module) ? 'text-navy-700 dark:text-white' : 'text-gray-400 dark:text-gray-500'"
                      >
                        {{ module.name }}
                      </label>
                      <span 
                        v-if="module.isPremium && selectedPlan === 'basic'"
                        class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full"
                      >
                        Premium
                      </span>
                    </div>
                    <p 
                      class="text-sm mt-1"
                      :class="isModuleAvailable(module) ? 'text-gray-600 dark:text-gray-400' : 'text-gray-400 dark:text-gray-500'"
                    >
                      {{ module.description }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Yönetici Hesabı -->
        <div v-if="currentStep === 2" class="space-y-6">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-navy-700 dark:text-white">Yönetici Hesabı Oluşturun</h3>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Otel için birincil yönetici hesabını tanımlayın</p>
          </div>

          <div class="max-w-md mx-auto space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Adı Soyadı *
              </label>
              <input
                v-model="adminData.fullName"
                type="text"
                placeholder="Yönetici adı soyadı"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                required
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                E-posta Adresi *
              </label>
              <input
                v-model="adminData.email"
                type="email"
                placeholder="<EMAIL>"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                required
              >
            </div>

            <div>
              <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Geçici Şifre
                </label>
                <button
                  @click="generatePassword"
                  class="text-sm text-brand-600 hover:text-brand-700 font-medium"
                >
                  Şifre Oluştur
                </button>
              </div>
              <input
                v-model="adminData.tempPassword"
                type="text"
                readonly
                placeholder="Geçici şifre otomatik oluşturulacak"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-navy-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none"
              >
            </div>

            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div class="flex items-start space-x-2">
                <InformationCircleIcon class="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div class="text-sm text-blue-700 dark:text-blue-300">
                  <strong>Bilgi:</strong> Yönetici hesabı oluşturulduktan sonra aktivasyon e-postası gönderilecek.
                  İlk girişte şifre değiştirme zorunludur.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Özet ve Onay -->
        <div v-if="currentStep === 3" class="space-y-6">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-navy-700 dark:text-white">Kurulum Özeti</h3>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Tüm bilgileri kontrol edin ve kurulumu tamamlayın</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Otel Bilgileri -->
            <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-4">
              <h4 class="font-semibold text-navy-700 dark:text-white mb-3 flex items-center">
                <BuildingOfficeIcon class="h-5 w-5 mr-2" />
                Otel Bilgileri
              </h4>
              <div class="space-y-2 text-sm">
                <div><span class="text-gray-600 dark:text-gray-400">Otel Adı:</span> <span class="font-medium">{{ hotelData.name }}</span></div>
                <div><span class="text-gray-600 dark:text-gray-400">Adres:</span> <span class="font-medium">{{ hotelData.address }}</span></div>
                <div><span class="text-gray-600 dark:text-gray-400">Yetkili:</span> <span class="font-medium">{{ hotelData.contactPerson }}</span></div>
                <div><span class="text-gray-600 dark:text-gray-400">E-posta:</span> <span class="font-medium">{{ hotelData.email }}</span></div>
                <div><span class="text-gray-600 dark:text-gray-400">Telefon:</span> <span class="font-medium">{{ hotelData.phone }}</span></div>
              </div>
            </div>

            <!-- Plan ve Modüller -->
            <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-4">
              <h4 class="font-semibold text-navy-700 dark:text-white mb-3 flex items-center">
                <CogIcon class="h-5 w-5 mr-2" />
                Plan ve Modüller
              </h4>
              <div class="space-y-2 text-sm">
                <div><span class="text-gray-600 dark:text-gray-400">Seçilen Plan:</span> <span class="font-medium">{{ getSelectedPlanName() }}</span></div>
                <div class="text-gray-600 dark:text-gray-400">Aktif Modüller:</div>
                <div class="ml-4 space-y-1">
                  <div v-for="moduleId in Object.keys(selectedModules).filter(id => selectedModules[id])" :key="moduleId" class="flex items-center">
                    <CheckIcon class="h-3 w-3 text-green-500 mr-2" />
                    <span class="text-xs">{{ getModuleName(moduleId) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Yönetici Hesabı -->
            <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-4">
              <h4 class="font-semibold text-navy-700 dark:text-white mb-3 flex items-center">
                <UserIcon class="h-5 w-5 mr-2" />
                Yönetici Hesabı
              </h4>
              <div class="space-y-2 text-sm">
                <div><span class="text-gray-600 dark:text-gray-400">Ad Soyad:</span> <span class="font-medium">{{ adminData.fullName }}</span></div>
                <div><span class="text-gray-600 dark:text-gray-400">E-posta:</span> <span class="font-medium">{{ adminData.email }}</span></div>
                <div><span class="text-gray-600 dark:text-gray-400">Geçici Şifre:</span> <span class="font-mono text-xs bg-gray-200 dark:bg-navy-600 px-2 py-1 rounded">{{ adminData.tempPassword }}</span></div>
              </div>
            </div>

            <!-- Sonraki Adımlar -->
            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <h4 class="font-semibold text-navy-700 dark:text-white mb-3 flex items-center">
                <ClockIcon class="h-5 w-5 mr-2" />
                Sonraki Adımlar
              </h4>
              <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                <li>✓ Aktivasyon e-postası gönderilecek</li>
                <li>✓ Otel dashboardı hazırlanacak</li>
                <li>✓ Seçilen modüller etkinleştirilecek</li>
                <li>✓ Test verileri oluşturulacak</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="bg-gray-50 dark:bg-navy-700 px-6 py-4 flex items-center justify-between">
        <button
          v-if="currentStep > 0"
          @click="previousStep"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-600 transition-colors"
        >
          <ChevronLeftIcon class="h-4 w-4 inline mr-1" />
          Geri
        </button>
        <div v-else></div>

        <div class="flex items-center space-x-3">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            İptal
          </button>
          
          <button
            v-if="currentStep < steps.length - 1"
            @click="nextStep"
            :disabled="!canProceed"
            class="px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Devam Et
            <ChevronRightIcon class="h-4 w-4 inline ml-1" />
          </button>
          
          <button
            v-else
            @click="completeSetup"
            :disabled="!canComplete"
            class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            <RocketLaunchIcon class="h-4 w-4 mr-2" />
            Kurulumu Tamamla ve Aktivasyon E-postası Gönder
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  XMarkIcon,
  CheckIcon,
  MapPinIcon,
  StarIcon,
  CubeIcon,
  RocketLaunchIcon,
  LockClosedIcon,
  InformationCircleIcon,
  BuildingOfficeIcon,
  UserIcon,
  CogIcon,
  ClockIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ArrowLeftIcon
} from '@heroicons/vue/24/outline'
import {
  ShoppingBagIcon,
  HomeIcon,
  BellIcon,
  ChartBarIcon,
  SparklesIcon,
  WrenchScrewdriverIcon
} from '@heroicons/vue/24/solid'

// Steps
const steps = [
  { title: 'Otel Bilgileri', subtitle: 'Temel bilgiler' },
  { title: 'Plan & Modüller', subtitle: 'Paket seçimi' },
  { title: 'Yönetici Hesabı', subtitle: 'Admin kullanıcı' },
  { title: 'Özet & Onay', subtitle: 'Son kontrol' }
]

const currentStep = ref(0)

// Hotel Data
const hotelData = ref({
  name: '',
  address: '',
  contactPerson: '',
  email: '',
  phone: ''
})

// Plans
const plans = [
  {
    id: 'basic',
    name: 'Basic',
    price: '₺499/ay',
    icon: StarIcon,
    iconBg: 'bg-blue-100 dark:bg-blue-900/30',
    iconColor: 'text-blue-600',
    features: [
      'Temel oda servisi',
      'Misafir istekleri',
      '1 lokasyon',
      'Email desteği'
    ]
  },
  {
    id: 'premium',
    name: 'Premium',
    price: '₺999/ay',
    icon: CubeIcon,
    iconBg: 'bg-purple-100 dark:bg-purple-900/30',
    iconColor: 'text-purple-600',
    features: [
      'Tüm Basic özellikler',
      'Spa & Wellness',
      'Event yönetimi',
      '3 lokasyon',
      'Telefon desteği'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: '₺1999/ay',
    icon: RocketLaunchIcon,
    iconBg: 'bg-orange-100 dark:bg-orange-900/30',
    iconColor: 'text-orange-600',
    features: [
      'Tüm Premium özellikler',
      'Özel entegrasyonlar',
      'Sınırsız lokasyon',
      'Öncelikli destek',
      'Özel eğitim'
    ]
  }
]

const selectedPlan = ref('premium')

// MFE Modules
const mfeModules = [
  {
    id: 'digitalOrdering',
    name: 'Dijital Sipariş',
    description: 'Oda servisi ve menü yönetimi',
    icon: ShoppingBagIcon,
    isPremium: false
  },
  {
    id: 'housekeeping',
    name: 'Akıllı Kat Hizmetleri',
    description: 'Oda durumu ve temizlik yönetimi',
    icon: HomeIcon,
    isPremium: false
  },
  {
    id: 'notifications',
    name: 'Akıllı Bildirimler',
    description: 'WhatsApp, SMS ve email bildirimleri',
    icon: BellIcon,
    isPremium: false
  },
  {
    id: 'analytics',
    name: 'Analitik & Raporlama',
    description: 'Detaylı istatistikler ve raporlar',
    icon: ChartBarIcon,
    isPremium: false
  },
  {
    id: 'spaWellness',
    name: 'SPA & Wellness',
    description: 'Spa randevu ve wellness yönetimi',
    icon: SparklesIcon,
    isPremium: true
  },
  {
    id: 'maintenance',
    name: 'Bakım Yönetimi',
    description: 'Teknik servis ve bakım takibi',
    icon: WrenchScrewdriverIcon,
    isPremium: true
  }
]

const selectedModules = ref({
  digitalOrdering: true,
  housekeeping: true,
  notifications: true,
  analytics: false,
  spaWellness: false,
  maintenance: false
})

// Admin Data
const adminData = ref({
  fullName: '',
  email: '',
  tempPassword: ''
})

// Computed
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return hotelData.value.name && hotelData.value.address && hotelData.value.contactPerson && hotelData.value.email && hotelData.value.phone
    case 1:
      return selectedPlan.value && Object.values(selectedModules.value).some(Boolean)
    case 2:
      return adminData.value.fullName && adminData.value.email && adminData.value.tempPassword
    default:
      return true
  }
})

const canComplete = computed(() => {
  return canProceed.value
})

// Methods
const nextStep = () => {
  if (currentStep.value < steps.length - 1 && canProceed.value) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const isModuleAvailable = (module: any) => {
  if (!module.isPremium) return true
  return selectedPlan.value !== 'basic'
}

const generatePassword = () => {
  const adjectives = ['Güçlü', 'Hızlı', 'Akıllı', 'Güvenli', 'Modern']
  const nouns = ['Otel', 'Portal', 'Sistem', 'Platform', 'Yönetim']
  const randomAdj = adjectives[Math.floor(Math.random() * adjectives.length)]
  const randomNoun = nouns[Math.floor(Math.random() * nouns.length)]
  const randomNum = Math.floor(Math.random() * 9999) + 1000
  
  adminData.value.tempPassword = `${randomAdj}${randomNoun}${randomNum}!`
}

const getSelectedPlanName = () => {
  const plan = plans.find(p => p.id === selectedPlan.value)
  return plan ? plan.name : ''
}

const getModuleName = (moduleId: string) => {
  const module = mfeModules.find(m => m.id === moduleId)
  return module ? module.name : ''
}

const completeSetup = () => {
  // Here would be the API call to create the hotel
  console.log('Creating hotel with data:', {
    hotel: hotelData.value,
    plan: selectedPlan.value,
    modules: selectedModules.value,
    admin: adminData.value
  })
  
  // For now, show success message and redirect to hotel list
  alert('Otel kurulumu başarıyla tamamlandı! Aktivasyon e-postası gönderildi.')
  // Redirect to hotel list
  window.location.href = '/management/hotels'
}

// Auto-generate password when step changes to admin setup
import { watch } from 'vue'
watch(currentStep, (newStep) => {
  if (newStep === 2 && !adminData.value.tempPassword) {
    generatePassword()
  }
})
</script> 