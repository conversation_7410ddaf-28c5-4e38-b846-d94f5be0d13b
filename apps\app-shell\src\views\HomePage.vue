<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Hero Section -->
    <section class="relative overflow-hidden">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            <span class="text-indigo-600">Hotelexia</span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Modern otel işletmeleri için tasarlanmış kapsamlı SaaS platformu
          </p>
          <p class="text-lg text-gray-500 mb-12 max-w-2xl mx-auto">
            Mikro frontend mimarisi ile geliştirilmiş, ölçeklenebilir ve modüler yapıya sahip otel yönetim sistemi
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="http://localhost:5002" target="_blank" 
               class="bg-indigo-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-indigo-700 transition-colors">
              🏨 Hotel Dashboard
            </a>
            <a href="http://localhost:3001" target="_blank"
               class="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              👥 Customer Portal
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Aktif MFE'ler</h2>
          <p class="text-lg text-gray-600">Şu anda geliştirilen mikro frontend uygulamaları</p>
        </div>
        
        <div class="grid md:grid-cols-2 gap-8">
          <!-- Hotel Dashboard -->
          <div class="bg-gradient-to-br from-indigo-50 to-blue-100 p-8 rounded-xl">
            <div class="text-4xl mb-4">🏨</div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Hotel Dashboard MFE</h3>
            <p class="text-gray-600 mb-4">
              Otel yönetimi için kapsamlı dashboard. Content management, bildirimler ve operasyonel takip.
            </p>
            <ul class="text-sm text-gray-500 mb-6 space-y-2">
              <li>✅ Vue 3 + TypeScript + Supabase</li>
              <li>✅ Horizon UI teması</li>
              <li>✅ Port: 5002</li>
              <li>✅ Tam fonksiyonel</li>
            </ul>
            <a href="http://localhost:5002" target="_blank"
               class="inline-block bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
              Dashboard'a Git →
            </a>
          </div>

          <!-- Customer Portal -->
          <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-8 rounded-xl">
            <div class="text-4xl mb-4">👥</div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Customer Portal MFE</h3>
            <p class="text-gray-600 mb-4">
              Misafir deneyimi platformu. Oda servisi, aktiviteler ve bildirimler.
            </p>
            <ul class="text-sm text-gray-500 mb-6 space-y-2">
              <li>✅ Vue 3 + TypeScript + Supabase</li>
              <li>✅ Mobile-first design</li>
              <li>✅ Port: 3001</li>
              <li>✅ Tam fonksiyonel</li>
            </ul>
            <a href="http://localhost:3001" target="_blank"
               class="inline-block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
              Portal'a Git →
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Development Info -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Geliştirme Bilgileri</h2>
        
        <div class="bg-white p-8 rounded-xl shadow-lg">
          <h3 class="text-xl font-semibold mb-4">Başlatma Komutları</h3>
          <div class="bg-gray-100 p-4 rounded-lg font-mono text-left">
            <p class="mb-2"># Tüm MFE'leri başlat</p>
            <p class="text-indigo-600 font-bold">npm run dev</p>
            <br>
            <p class="mb-2"># Sadece App Shell</p>
            <p class="text-green-600">npm run dev:shell</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Simple promotional homepage
</script> 