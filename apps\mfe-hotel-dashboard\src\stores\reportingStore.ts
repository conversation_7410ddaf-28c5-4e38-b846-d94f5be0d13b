import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase, HotelDashboardService } from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'

// Types for reporting data
export interface OperationalMetrics {
  hotel_id: string
  hotel_name: string
  total_rooms: number
  occupied_rooms: number
  clean_rooms: number
  dirty_rooms: number
  maintenance_rooms: number
  out_of_order_rooms: number
  occupancy_rate: number
  pending_maintenance_tasks: number
  completed_maintenance_tasks: number
  new_general_tasks: number
  in_progress_general_tasks: number
  completed_general_tasks: number
  pending_service_requests: number
  completed_service_requests: number
  daily_room_service_orders: number
  daily_room_service_revenue: number
  daily_activity_registrations: number
  total_staff: number
  active_staff: number
}

export interface FinancialMetrics {
  hotel_id: string
  hotel_name: string
  daily_reservation_revenue: number
  daily_room_service_revenue: number
  weekly_reservation_revenue: number
  weekly_room_service_revenue: number
  monthly_reservation_revenue: number
  monthly_room_service_revenue: number
  average_daily_rate: number
  revenue_per_available_room: number
  daily_orders_count: number
  weekly_orders_count: number
  average_order_value: number
}

export interface TaskPerformance {
  hotel_id: string
  hotel_name: string
  total_maintenance_tasks: number
  completed_maintenance_tasks: number
  maintenance_completion_rate: number
  total_general_tasks: number
  completed_general_tasks: number
  general_task_completion_rate: number
  total_service_requests: number
  completed_service_requests: number
  service_request_resolution_rate: number
  avg_task_duration_minutes: number
  high_priority_tasks: number
  urgent_priority_tasks: number
  tasks_created_last_week: number
  tasks_completed_last_week: number
  tasks_per_staff_member: number
}

export interface GuestAnalytics {
  hotel_id: string
  hotel_name: string
  total_activity_registrations: number
  weekly_activity_registrations: number
  monthly_activity_registrations: number
  activity_participation_rate: number
  total_guest_service_requests: number
  weekly_guest_service_requests: number
  avg_service_response_time_minutes: number
  total_room_service_orders: number
  weekly_room_service_orders: number
  avg_room_service_rating: number
  avg_activity_rating: number
  avg_service_request_rating: number
  peak_room_service_hour: number
  most_popular_activity: string
  unique_service_types_requested: number
  returning_guests_count: number
}

export interface DailySummary {
  hotel_id: string
  hotel_name: string
  report_date: string
  total_rooms: number
  occupied_rooms: number
  clean_rooms: number
  dirty_rooms: number
  maintenance_rooms: number
  occupancy_rate: number
  daily_reservation_revenue: number
  daily_room_service_revenue: number
  new_tasks: number
  in_progress_tasks: number
  tasks_completed_today: number
  pending_maintenance: number
  maintenance_completed_today: number
  pending_service_requests: number
  service_requests_today: number
  activity_registrations_today: number
  room_service_orders_today: number
  active_staff_count: number
  avg_room_service_rating_today: number
  reservation_growth_rate: number
  room_service_revenue_growth_rate: number
}

export const useReportingStore = defineStore('reporting', () => {
  // Get auth store for user context
  const authStore = useAuthStore()

  // State
  const operationalMetrics = ref<OperationalMetrics | null>(null)
  const financialMetrics = ref<FinancialMetrics | null>(null)
  const taskPerformance = ref<TaskPerformance | null>(null)
  const guestAnalytics = ref<GuestAnalytics | null>(null)
  const dailySummary = ref<DailySummary | null>(null)

  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Revenue trend data
  const revenueTrend = ref<Array<{
    date_period: string
    reservation_revenue: number
    room_service_revenue: number
    total_revenue: number
  }>>([])
  
  // Task completion trend data
  const taskTrend = ref<Array<{
    date_period: string
    tasks_created: number
    tasks_completed: number
    completion_rate: number
  }>>([])

  // Computed values for dashboard KPIs
  const occupancyRate = computed(() => operationalMetrics.value?.occupancy_rate || 0)
  const totalRevenue = computed(() => {
    if (!financialMetrics.value) return 0
    return financialMetrics.value.daily_reservation_revenue + financialMetrics.value.daily_room_service_revenue
  })
  const taskCompletionRate = computed(() => taskPerformance.value?.general_task_completion_rate || 0)
  const guestSatisfaction = computed(() => guestAnalytics.value?.avg_room_service_rating || 0)

  // Actions
  const fetchOperationalMetrics = async (hotelId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      // Use provided hotelId or get from auth store (for hotel staff) or null (for super admin to see all)
      const targetHotelId = hotelId || (authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : null)

      const { data, error: supabaseError } = await (supabase as any).rpc('get_hotel_operational_metrics_secure', {
        p_hotel_id: targetHotelId
      })
      
      if (supabaseError) throw supabaseError
      
      if (data && data.length > 0) {
        operationalMetrics.value = data[0]
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch operational metrics'
      console.error('Error fetching operational metrics:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchFinancialMetrics = async (hotelId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      const targetHotelId = hotelId || (authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : null)

      const { data, error: supabaseError } = await (supabase as any).rpc('get_hotel_financial_metrics_secure', {
        p_hotel_id: targetHotelId
      })
      
      if (supabaseError) throw supabaseError
      
      if (data && data.length > 0) {
        financialMetrics.value = data[0]
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch financial metrics'
      console.error('Error fetching financial metrics:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchTaskPerformance = async (hotelId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      const targetHotelId = hotelId || (authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : null)

      const { data, error: supabaseError } = await (supabase as any).rpc('get_hotel_task_performance_secure', {
        p_hotel_id: targetHotelId
      })
      
      if (supabaseError) throw supabaseError
      
      if (data && data.length > 0) {
        taskPerformance.value = data[0]
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch task performance'
      console.error('Error fetching task performance:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchGuestAnalytics = async (hotelId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      const targetHotelId = hotelId || (authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : null)

      const { data, error: supabaseError } = await (supabase as any).rpc('get_hotel_guest_analytics_secure', {
        p_hotel_id: targetHotelId
      })
      
      if (supabaseError) throw supabaseError
      
      if (data && data.length > 0) {
        guestAnalytics.value = data[0]
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch guest analytics'
      console.error('Error fetching guest analytics:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchDailySummary = async (hotelId?: string) => {
    isLoading.value = true
    error.value = null

    try {
      const targetHotelId = hotelId || (authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : null)

      const { data, error: supabaseError } = await (supabase as any).rpc('get_daily_hotel_summary_secure', {
        p_hotel_id: targetHotelId
      })
      
      if (supabaseError) throw supabaseError
      
      if (data && data.length > 0) {
        dailySummary.value = data[0]
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch daily summary'
      console.error('Error fetching daily summary:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchRevenueTrend = async (hotelId: string, days: number = 30) => {
    isLoading.value = true
    error.value = null
    
    try {
      const { data, error: supabaseError } = await (supabase as any).rpc('get_hotel_revenue_trend', {
        p_hotel_id: hotelId,
        p_days: days
      })
      
      if (supabaseError) throw supabaseError
      
      revenueTrend.value = (data as any) || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch revenue trend'
      console.error('Error fetching revenue trend:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchTaskTrend = async (hotelId: string, days: number = 30) => {
    isLoading.value = true
    error.value = null
    
    try {
      const { data, error: supabaseError } = await (supabase as any).rpc('get_task_completion_trends', {
        p_hotel_id: hotelId,
        p_days: days
      })
      
      if (supabaseError) throw supabaseError
      
      taskTrend.value = (data as any) || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch task trend'
      console.error('Error fetching task trend:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchAllMetrics = async (hotelId?: string) => {
    await Promise.all([
      fetchOperationalMetrics(hotelId),
      fetchFinancialMetrics(hotelId),
      fetchTaskPerformance(hotelId),
      fetchGuestAnalytics(hotelId),
      fetchDailySummary(hotelId)
    ])
  }

  return {
    // State
    operationalMetrics,
    financialMetrics,
    taskPerformance,
    guestAnalytics,
    dailySummary,
    revenueTrend,
    taskTrend,
    isLoading,
    error,
    
    // Computed
    occupancyRate,
    totalRevenue,
    taskCompletionRate,
    guestSatisfaction,
    
    // Actions
    fetchOperationalMetrics,
    fetchFinancialMetrics,
    fetchTaskPerformance,
    fetchGuestAnalytics,
    fetchDailySummary,
    fetchRevenueTrend,
    fetchTaskTrend,
    fetchAllMetrics
  }
})
