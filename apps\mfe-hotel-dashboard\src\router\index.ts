import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '@/views/Dashboard.vue'
import { useAuthStore } from '@/stores/authStore'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    // Auth Layout Routes (No Sidebar/Navbar)
    {
      path: '/auth',
      component: () => import('../layouts/AuthLayout.vue'),
      children: [
        {
          path: 'login',
          name: 'login',
          component: () => import('../views/LoginView.vue'),
          meta: {
            title: 'Otel Personeli Girişi - Hotelexia',
            public: true
          }
        }
      ]
    },
    // Legacy login route redirect
    {
      path: '/login',
      redirect: '/auth/login'
    },
    // Dashboard Layout Routes (With Sidebar/Navbar)
    {
      path: '/dashboard',
      component: () => import('../layouts/DashboardLayout.vue'),
      children: [
        {
          path: '',
          name: 'dashboard',
          component: Dashboard,
          meta: {
            title: 'Dashboard - Hotelexia',
            requiresAuth: true
          }
        },
        // Operations Routes
        {
          path: 'operations-overview',
          name: 'OperationsOverview',
          component: () => import('@/views/operations/OperationsOverview.vue'),
          meta: {
            title: 'Operasyonlar Özet - Hotelexia',
            requiresAuth: true
          }
        },
        // Housekeeping Routes
        {
          path: 'housekeeping',
          name: 'HousekeepingDashboard',
          redirect: '/dashboard/housekeeping/overview'
        },
        {
          path: 'housekeeping/overview',
          name: 'HousekeepingOverview',
          component: () => import('@/views/operations/housekeeping/HousekeepingOverview.vue'),
          meta: {
            title: 'Kat Hizmetleri Özet - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/tasks',
          name: 'TaskAssignment',
          component: () => import('@/views/operations/housekeeping/TaskAssignment.vue'),
          meta: {
            title: 'Görev Atama - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/task-management',
          name: 'HousekeepingTaskManagement',
          component: () => import('@/views/operations/housekeeping/TaskManagement.vue'),
          meta: {
            title: 'Kat Hizmetleri - Görev Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/requests',
          name: 'StaffRequests',
          component: () => import('@/views/operations/housekeeping/StaffRequests.vue'),
          meta: {
            title: 'Personel İş Talepleri - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/floors',
          name: 'HousekeepingFloorManagement',
          component: () => import('@/views/operations/housekeeping/FloorManagement.vue'),
          meta: {
            title: 'Kat Hizmetleri - Kat Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/rooms',
          name: 'HousekeepingRoomManagement',
          component: () => import('@/views/operations/housekeeping/RoomManagement.vue'),
          meta: {
            title: 'Kat Hizmetleri - Oda Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/staff',
          name: 'HousekeepingStaffManagement',
          component: () => import('@/views/operations/housekeeping/StaffManagement.vue'),
          meta: {
            title: 'Kat Hizmetleri - Personel Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/communication',
          name: 'HousekeepingStaffCommunication',
          component: () => import('@/views/operations/housekeeping/StaffCommunication.vue'),
          meta: {
            title: 'Kat Hizmetleri - Personel İletişim - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'housekeeping/performance',
          name: 'HousekeepingPerformanceAnalysis',
          component: () => import('@/views/operations/housekeeping/PerformanceAnalysis.vue'),
          meta: {
            title: 'Kat Hizmetleri - Performans Analiz - Hotelexia',
            requiresAuth: true
          }
        },

        // Maintenance Routes
        {
          path: 'maintenance/overview',
          name: 'MaintenanceOverview',
          component: () => import('@/views/operations/maintenance/MaintenanceOverview.vue'),
          meta: {
            title: 'Bakım Onarım - Özet - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'maintenance/floors',
          name: 'MaintenanceFloorManagement',
          component: () => import('@/views/operations/maintenance/FloorManagement.vue'),
          meta: {
            title: 'Bakım - Kat Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'maintenance/rooms',
          name: 'MaintenanceRoomManagement',
          component: () => import('@/views/operations/maintenance/RoomManagement.vue'),
          meta: {
            title: 'Bakım - Oda Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'maintenance/staff',
          name: 'MaintenanceStaffManagement',
          component: () => import('@/views/operations/maintenance/StaffManagement.vue'),
          meta: {
            title: 'Bakım - Personel Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'maintenance/communication',
          name: 'MaintenanceStaffCommunication',
          component: () => import('@/views/operations/maintenance/StaffCommunication.vue'),
          meta: {
            title: 'Bakım - Personel İletişim - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'maintenance/performance',
          name: 'MaintenancePerformanceAnalysis',
          component: () => import('@/views/operations/maintenance/PerformanceAnalysis.vue'),
          meta: {
            title: 'Bakım - Performans Analiz - Hotelexia',
            requiresAuth: true
          }
        },

        // Content Management Routes
        {
          path: 'content-overview',
          name: 'ContentOverview',
          component: () => import('@/views/content/ContentOverview.vue'),
          meta: {
            title: 'İçerik Yönetimi Özet - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/promo-management',
          name: 'PromoManagement',
          component: () => import('@/views/content/PromoManagement.vue'),
          meta: {
            title: 'Promo Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },

        // Room Service Routes
        {
          path: 'content/room-service/orders',
          name: 'OrderTracking',
          component: () => import('@/views/content/room-service/OrderTracking.vue'),
          meta: {
            title: 'Sipariş İzleme - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/room-service/management',
          name: 'RoomServiceManagement',
          component: () => import('@/views/content/room-service/RoomServiceManagement.vue'),
          meta: {
            title: 'Oda Servisi Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/room-service/promo',
          name: 'RoomServicePromo',
          component: () => import('@/views/content/room-service/RoomServicePromo.vue'),
          meta: {
            title: 'Oda Servisi Promo Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },

        // Guest Services Routes
        {
          path: 'content/guest-services/tracking',
          name: 'ServiceTracking',
          component: () => import('@/views/content/guest-services/ServiceTracking.vue'),
          meta: {
            title: 'Hizmet Takibi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/guest-services/management',
          name: 'GuestServiceManagement',
          component: () => import('@/views/content/guest-services/GuestServiceManagement.vue'),
          meta: {
            title: 'Misafir Hizmet Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/guest-services/promo',
          name: 'GuestServicePromo',
          component: () => import('@/views/content/guest-services/GuestServicePromo.vue'),
          meta: {
            title: 'Misafir Hizmet Promo Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },

        // Activities Routes
        {
          path: 'content/activities/tracking',
          name: 'ActivityTracking',
          component: () => import('@/views/content/activities/ActivityTracking.vue'),
          meta: {
            title: 'Aktivite Takibi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/activities/categories',
          name: 'ActivityCategoryManagement',
          component: () => import('@/views/content/activities/ActivityCategoryManagement.vue'),
          meta: {
            title: 'Aktivite Kategori Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },

        // Existing Management Routes (Legacy - for existing functionality)
        {
          path: 'manage-customer-portal',
          component: () => import('@/views/management/ManagementLayout.vue'),
          meta: {
            title: 'Customer Portal Management - Hotelexia',
            requiresAuth: true
          },
          children: [
            { path: '', redirect: '/dashboard/manage-customer-portal/digital-ordering' },
            {
              path: 'digital-ordering',
              name: 'ManageDigitalOrdering',
              component: () => import('@/views/management/DigitalOrdering.vue'),
              meta: {
                title: 'Digital Ordering Management - Hotelexia',
                requiresAuth: true
              }
            },
            {
              path: 'events',
              name: 'ManageEvents',
              component: () => import('@/views/management/Events.vue'),
              meta: {
                title: 'Events Management - Hotelexia',
                requiresAuth: true
              }
            },
            {
              path: 'room-service-categories',
              name: 'ManageRoomServiceCategories',
              component: () => import('@/views/management/RoomServiceCategories.vue'),
              meta: {
                title: 'Room Service Categories - Hotelexia',
                requiresAuth: true
              }
            },
            {
              path: 'guest-service-categories',
              name: 'ManageGuestServiceCategories',
              component: () => import('@/views/management/GuestServiceCategories.vue'),
              meta: {
                title: 'Guest Service Categories - Hotelexia',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: 'manage-activities',
          name: 'ManageActivities',
          component: () => import('@/views/management/ActivityManagement.vue'),
          meta: {
            title: 'Activity Management - Hotelexia',
            requiresAuth: true
          }
        },

        // Notification Center
        {
          path: 'notifications',
          name: 'NotificationCenter',
          component: () => import('@/views/notifications/NotificationCenter.vue'),
          meta: {
            title: 'Bildirim Merkezi - Hotelexia',
            requiresAuth: true
          }
        },

        // Reports Routes
        {
          path: 'reports/customer',
          name: 'CustomerReports',
          component: () => import('@/views/reports/CustomerReports.vue'),
          meta: {
            title: 'Müşteri Raporları - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'reports/staff',
          name: 'StaffReports',
          component: () => import('@/views/reports/StaffReports.vue'),
          meta: {
            title: 'Personel Raporları - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'reports/operational',
          name: 'OperationalReports',
          component: () => import('@/views/reports/OperationalReports.vue'),
          meta: {
            title: 'Faaliyet Raporları - Hotelexia',
            requiresAuth: true
          }
        },
      ]
    },

    // Hotel Routes with DashboardLayout
    {
      path: '/hotel',
      component: () => import('../layouts/DashboardLayout.vue'),
      children: [
        {
          path: 'operations-overview',
          name: 'HotelOperationsOverview',
          component: () => import('@/views/operations/OperationsOverview.vue'),
          meta: {
            title: 'Operasyonlar Özet - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content-overview',
          name: 'HotelContentOverview',
          component: () => import('@/views/content/ContentOverview.vue'),
          meta: {
            title: 'İçerik Yönetimi Özet - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'content/promo-management',
          name: 'HotelPromoManagement',
          component: () => import('@/views/content/PromoManagement.vue'),
          meta: {
            title: 'Promo Yönetimi - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'management/settings',
          name: 'HotelManagementSettings',
          component: () => import('@/views/management/Settings.vue'),
          meta: {
            title: 'Sistem Ayarları - Hotelexia',
            requiresAuth: true
          }
        },
        {
          path: 'management/profile',
          name: 'HotelManagementProfile',
          component: () => import('@/views/management/Profile.vue'),
          meta: {
            title: 'Profil Yönetimi - Hotelexia',
            requiresAuth: true
          }
        }
      ]
    },

    // Legacy Redirects for Unified Management Pages
    {
      path: '/hotel/content/room-service/products',
      redirect: '/dashboard/content/room-service/management'
    },
    {
      path: '/hotel/content/guest-services/list',
      redirect: '/dashboard/content/guest-services/management'
    },



    // Legacy Redirects
    {
      path: '/hotel/maintenance',
      redirect: '/dashboard/maintenance/overview'
    },
    {
      path: '/order-tracking',
      redirect: '/dashboard/content/room-service/orders'
    },
    {
      path: '/service-list',
      redirect: '/dashboard/content/guest-services/management'
    },
    {
      path: '/service-tracking',
      redirect: '/dashboard/content/guest-services/tracking'
    },
    {
      path: '/hotel/guest-services/management',
      redirect: '/dashboard/content/guest-services/management'
    },
    {
      path: '/hotel/activities/tracking',
      redirect: '/dashboard/content/activities/tracking'
    },
    {
      path: '/hotel/reports/operations',
      redirect: '/dashboard/reports/operational'
    },
    {
      path: '/hotel/settings',
      redirect: '/hotel/management/settings'
    },
    {
      path: '/hotel/profile',
      redirect: '/hotel/management/profile'
    },
    {
      path: '/reports',
      redirect: '/dashboard/reports/customer'
    },
    {
      path: '/settings',
      redirect: '/hotel/management/settings'
    },
    {
      path: '/profile',
      redirect: '/hotel/management/profile'
    }
  ]
})

// Authentication guard with page title update
router.beforeEach(async (to, from, next) => {
  // Update page title
  document.title = to.meta.title as string || 'Hotelexia - Otel Dashboard'

  const authStore = useAuthStore()

  // Try to restore session if not authenticated
  if (!authStore.isAuthenticated) {
    await authStore.restoreSession()
  }

  // Check if route requires authentication
  const requiresAuth = to.meta.requiresAuth !== false && to.name !== 'login'
  const isPublicRoute = to.meta.public === true

  // If route requires auth, check authentication
  if (requiresAuth && !isPublicRoute) {
    if (!authStore.isAuthenticated || !authStore.hasHotelAccess) {
      // Store the intended destination for post-login redirect
      if (to.path !== '/dashboard') {
        sessionStorage.setItem('hotelexia_hotel_redirect', to.fullPath)
      }
      // Redirect to login
      return next('/auth/login')
    }
  }

  // If user is authenticated and trying to access login page
  if (to.name === 'login') {
    if (authStore.isAuthenticated && authStore.hasHotelAccess) {
      // Check for stored redirect path
      const redirectPath = sessionStorage.getItem('hotelexia_hotel_redirect')
      if (redirectPath) {
        sessionStorage.removeItem('hotelexia_hotel_redirect')
        return next(redirectPath)
      }
      // Default redirect to dashboard
      return next('/dashboard')
    }
  }

  next()
})

export default router 