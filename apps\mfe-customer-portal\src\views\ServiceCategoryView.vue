<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-6">
        <div class="flex items-center justify-between">
          <button @click="goBack" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <svg class="w-6 h-6 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          </button>
          <h1 class="text-xl font-bold text-textDark">{{ category?.name || 'Hizmetler' }}</h1>
          <div class="w-10"></div> <!-- Spacer for centering -->
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
      <div class="text-center">
        <div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p class="text-textMedium">Hizmetler yükleniyor...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="px-4 py-8">
      <div class="bg-red-50 border border-red-200 rounded-2xl p-6 text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-textDark mb-2">Kategori Bulunamadı</h3>
        <p class="text-textMedium mb-4">{{ error }}</p>
        <button 
          @click="goBack"
          class="bg-primary text-white px-6 py-3 rounded-xl font-medium hover:bg-primary/90 transition-colors"
        >
          Geri Dön
        </button>
      </div>
    </div>

    <!-- Category Content -->
    <div v-else-if="category" class="pb-20">
      <!-- Category Hero -->
      <div class="relative h-48 overflow-hidden">
        <img 
          :src="category.image" 
          :alt="category.name" 
          class="w-full h-full object-cover"
        >
        <div class="absolute inset-0 bg-black/40"></div>
        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
          <h2 class="text-2xl font-bold mb-2">{{ category.name }}</h2>
          <p class="text-white/90 text-sm leading-relaxed">{{ category.description }}</p>
          <div class="flex items-center mt-3 space-x-4">
            <div class="flex items-center text-white/80 text-sm">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              <span>{{ category.services.length }} Hizmet Mevcut</span>
            </div>
            <div class="flex items-center text-white/80 text-sm">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span>{{ getMinDuration() }}-{{ getMaxDuration() }} dakika</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Services List -->
      <div class="max-w-md mx-auto px-4 py-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold text-textDark">Mevcut Hizmetler</h3>
          <div class="text-sm text-textMedium">
            {{ category.services.length }} sonuç
          </div>
        </div>

        <div class="space-y-4">
          <div 
            v-for="service in category.services" 
            :key="service.id"
            class="bg-white rounded-2xl p-5 shadow-sm hover:shadow-lg transition-all duration-200 cursor-pointer"
            @click="goToServiceDetail(service.id)"
          >
            <div class="flex items-start space-x-4">
              <!-- Service Image -->
              <div class="flex-shrink-0 w-20 h-20 bg-gray-100 rounded-2xl overflow-hidden">
                <img 
                  :src="service.image" 
                  :alt="service.name" 
                  class="w-full h-full object-cover"
                >
              </div>

              <!-- Service Info -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between mb-2">
                  <h4 class="font-bold text-textDark text-lg leading-tight">{{ service.name }}</h4>
                  <div class="flex-shrink-0 ml-2">
                    <div v-if="service.price > 0" class="text-primary font-bold text-lg">
                      ₺{{ service.price }}
                    </div>
                    <div v-else class="text-green-600 font-bold text-sm">
                      Ücretsiz
                    </div>
                  </div>
                </div>
                
                <p class="text-textMedium text-sm mb-3 leading-relaxed">{{ service.description }}</p>
                
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div v-if="service.duration_minutes > 0" class="flex items-center text-textLight text-xs">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <span>{{ service.duration_minutes }} dakika</span>
                    </div>
                    <div class="flex items-center text-textLight text-xs">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                      </svg>
                      <span>Otel İçi</span>
                    </div>
                  </div>
                  
                  <button 
                    @click.stop="goToServiceDetail(service.id)"
                    class="bg-primary/10 text-primary px-4 py-2 rounded-xl text-sm font-medium hover:bg-primary/20 transition-colors"
                  >
                    Detayları Gör
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="category.services.length === 0" class="text-center py-12">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-textDark mb-2">Henüz Hizmet Bulunmuyor</h3>
          <p class="text-textMedium">Bu kategoride henüz herhangi bir hizmet bulunmamaktadır.</p>
        </div>

        <!-- Call to Action -->
        <div class="mt-8 bg-gradient-to-r from-primary/10 to-teal-600/10 rounded-2xl p-6 text-center">
          <h3 class="text-lg font-bold text-textDark mb-2">Rezervasyon Yapmak İster misiniz?</h3>
          <p class="text-textMedium text-sm mb-4">
            Hizmetlerimiz hakkında daha fazla bilgi almak veya rezervasyon yapmak için bizimle iletişime geçin.
          </p>
          <div class="flex space-x-3">
            <button 
              @click="callConcierge"
              class="flex-1 bg-primary text-white px-4 py-3 rounded-xl font-medium hover:bg-primary/90 transition-colors"
            >
              Concierge Ara
            </button>
            <button 
              @click="sendWhatsApp"
              class="flex-1 bg-green-500 text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              WhatsApp
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { serviceCategories, type ServiceCategory } from '@/data/servicesData'

const router = useRouter()
const route = useRoute()

const loading = ref(true)
const error = ref('')
const category = ref<ServiceCategory | null>(null)

onMounted(() => {
  loadCategory()
})

const loadCategory = () => {
  loading.value = true
  error.value = ''
  
  try {
    const slug = route.params.slug as string
    const foundCategory = serviceCategories.find(cat => cat.slug === slug)
    
    if (!foundCategory) {
      error.value = `"${slug}" kategorisi bulunamadı. Lütfen geçerli bir kategori seçin.`
      category.value = null
    } else {
      category.value = foundCategory
    }
  } catch (err) {
    error.value = 'Kategori yüklenirken bir hata oluştu.'
    category.value = null
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

const goToServiceDetail = (serviceId: number) => {
  router.push(`/service/${serviceId}`)
}

const getMinDuration = (): number => {
  if (!category.value) return 0
  const durations = category.value.services.map(s => s.duration_minutes).filter(d => d > 0)
  return durations.length > 0 ? Math.min(...durations) : 0
}

const getMaxDuration = (): number => {
  if (!category.value) return 0
  const durations = category.value.services.map(s => s.duration_minutes).filter(d => d > 0)
  return durations.length > 0 ? Math.max(...durations) : 0
}

const callConcierge = () => {
  // Simulate calling concierge
  alert('Concierge aranıyor... (Demo)')
}

const sendWhatsApp = () => {
  // Simulate WhatsApp integration
  alert('WhatsApp açılıyor... (Demo)')
}
</script>

<style scoped>
/* Component-specific styles */
</style> 