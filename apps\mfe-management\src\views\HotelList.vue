<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Otel Yönetimi</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Platform'daki tüm otelleri yönetin</p>
      </div>
      <div class="flex items-center space-x-3">
        <router-link
          to="/hotels/new"
          class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors flex items-center space-x-2"
        >
          <PlusIcon class="h-4 w-4" />
          <span>Yeni Otel Ekle</span>
        </router-link>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div class="flex items-center space-x-4">
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Otel ara..."
              class="pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
            >
          </div>
          <select 
            v-model="statusFilter"
            class="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500"
          >
            <option value="">Tüm Durumlar</option>
            <option :value="true">Aktif</option>
            <option :value="false">Pasif</option>
          </select>
        </div>
        <div class="text-sm text-gray-600 dark:text-gray-400">
          Toplam {{ filteredHotels.length }} otel
        </div>
      </div>
    </div>

    <!-- Loading and Error States -->
    <div v-if="loading" class="text-center py-10">Yükleniyor...</div>
    <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
      <strong class="font-bold">Hata!</strong>
      <span class="block sm:inline">{{ error }}</span>
    </div>

    <!-- Hotels Table -->
    <div v-if="!loading && !error" class="bg-white dark:bg-navy-800 rounded-20 shadow-card overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-700">
            <tr>
              <th class="text-left py-4 px-6 text-sm font-semibold text-gray-600 dark:text-gray-400">Otel Adı</th>
              <th class="text-left py-4 px-6 text-sm font-semibold text-gray-600 dark:text-gray-400">Statü</th>
              <th class="text-left py-4 px-6 text-sm font-semibold text-gray-600 dark:text-gray-400">Kayıt Tarihi</th>
              <th class="text-left py-4 px-6 text-sm font-semibold text-gray-600 dark:text-gray-400">İşlemler</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
            <tr 
              v-for="hotel in filteredHotels" 
              :key="hotel.id" 
              class="hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors"
            >
              <td class="py-4 px-6">
                <div>
                  <div class="font-medium text-navy-700 dark:text-white">{{ hotel.name }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ hotel.address }}</div>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center space-x-2">
                  <span
                    class="px-2 py-1 text-xs font-medium rounded-full"
                    :class="hotel.status === 'Aktif' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'"
                  >
                    {{ hotel.status }}
                  </span>
                  <button
                    @click="toggleHotelStatus(hotel)"
                    class="relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
                    :class="hotel.status === 'Aktif' ? 'bg-green-500' : 'bg-gray-300'"
                  >
                    <span
                      class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform"
                      :class="hotel.status === 'Aktif' ? 'translate-x-5' : 'translate-x-1'"
                    />
                  </button>
                </div>
              </td>
              <td class="py-4 px-6 text-sm text-gray-600 dark:text-gray-400">
                {{ hotel.registrationDate }}
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center space-x-2">
                  <button
                    @click="editHotel(hotel.id)"
                    class="p-2 text-gray-400 hover:text-brand-500 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-600 transition-colors"
                    title="Düzenle"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </button>
                  <button
                    @click="manageMfes(hotel.id)"
                    class="p-2 text-gray-400 hover:text-purple-500 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-600 transition-colors"
                    title="MFE Yönetimi"
                  >
                    <CogIcon class="h-4 w-4" />
                  </button>
                  <button
                    class="p-2 text-gray-400 hover:text-red-500 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-600 transition-colors"
                    title="Sil"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  CogIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import { supabase } from '@hotelexia/shared-supabase-client'
import { useManagementStore } from '@/stores/managementStore'

const router = useRouter()
const store = useManagementStore()

const searchQuery = ref('')
const statusFilter = ref<boolean | string>('')

// Use store data instead of local state
const hotels = computed(() => store.hotels)
const loading = computed(() => store.hotelsLoading)
const error = computed(() => store.hotelsError)



onMounted(() => {
  // Fetch hotels using the store
  store.fetchHotels()
})

const filteredHotels = computed(() => {
  return hotels.value.filter(hotel => {
    const searchMatch = !searchQuery.value ||
      hotel.name.toLowerCase().includes(searchQuery.value.toLowerCase());

    const statusMatch = statusFilter.value === '' ||
      (statusFilter.value === true && hotel.status === 'Aktif') ||
      (statusFilter.value === false && hotel.status === 'Pasif');

    return searchMatch && statusMatch;
  });
})

const toggleHotelStatus = async (hotel: any) => {
  try {
    const isCurrentlyActive = hotel.status === 'Aktif'
    const newStatus = !isCurrentlyActive
    const statusText = newStatus ? 'aktif' : 'pasif'

    const confirmed = confirm(
      `"${hotel.name}" otelini ${statusText} yapmak istediğinizden emin misiniz?`
    )

    if (!confirmed) return

    const { error: updateError } = await supabase
      .from('hotels')
      .update({
        is_active: newStatus
      } as any)
      .eq('id', hotel.id)

    if (updateError) throw updateError

    // Refresh the store data to get updated information
    await store.fetchHotels()

    console.log(`Hotel "${hotel.name}" status updated to ${statusText}`)

  } catch (e: any) {
    console.error("Durum güncellenirken hata oluştu:", e.message)
    // Use store error instead of local error
    store.hotelsError = `Otel durumu güncellenirken bir hata oluştu: ${e.message}`

    // Reset error after 5 seconds
    setTimeout(() => {
      store.hotelsError = null
    }, 5000)
  }
}

const formatDate = (dateString: string | null) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('tr-TR')
}

const editHotel = (hotelId: string) => {
  store.handleEditHotelClick(hotelId)
  router.push(`/management/hotels/edit/${hotelId}`)
}

const manageMfes = (hotelId: string) => {
  store.handleMfeManagementClick(hotelId)
  router.push(`/management/hotels/mfe-config/${hotelId}`)
}
</script> 