<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h4 class="text-lg font-bold text-navy-700 dark:text-white">{{ title }}</h4>
        <p v-if="subtitle" class="text-sm text-gray-600 dark:text-gray-400">{{ subtitle }}</p>
      </div>
      <div v-if="actions" class="flex items-center space-x-2">
        <slot name="actions" />
      </div>
    </div>

    <!-- Content -->
    <div class="space-y-4">
      <slot />
    </div>

    <!-- Footer -->
    <div v-if="$slots.footer" class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  subtitle?: string
  actions?: boolean
}

defineProps<Props>()
</script>

<style scoped>
/* Report card specific styles */
</style>
