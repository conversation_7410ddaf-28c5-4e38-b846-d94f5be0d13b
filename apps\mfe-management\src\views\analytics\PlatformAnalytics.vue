<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-navy-700 dark:text-white mb-2">Platform Analitikleri</h1>
      <p class="text-gray-600 dark:text-gray-400">Tüm otellerin performans analizi ve platform geneli raporlar</p>
    </div>

    <!-- Error State -->
    <div v-if="reportingStore.error" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
      <p class="text-red-600 dark:text-red-400">{{ reportingStore.error }}</p>
    </div>

    <!-- Platform Overview KPIs -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <MetricCard
        label="Toplam Otel"
        :value="platformOverview?.total_hotels || 0"
        format="number"
        :icon="BuildingOfficeIcon"
        icon-color="text-blue-600 dark:text-blue-400"
        icon-bg-color="bg-blue-100 dark:bg-blue-900/30"
        :trend="reportingStore.hotelGrowthRate"
        description="Aktif ve pasif oteller"
        :additional-info="{ label: 'Aktif Otel', value: platformOverview?.active_hotels || 0 }"
      />
      <MetricCard
        label="Toplam Kullanıcı"
        :value="platformOverview?.total_users || 0"
        format="number"
        :icon="UsersIcon"
        icon-color="text-green-600 dark:text-green-400"
        icon-bg-color="bg-green-100 dark:bg-green-900/30"
        :trend="reportingStore.userGrowthRate"
        description="Tüm roller dahil"
        :additional-info="{ label: 'Bu ay eklenen', value: platformOverview?.users_added_this_month || 0 }"
      />
      <MetricCard
        label="Aylık Gelir"
        :value="reportingStore.totalRevenue"
        format="currency"
        :icon="CurrencyDollarIcon"
        icon-color="text-purple-600 dark:text-purple-400"
        icon-bg-color="bg-purple-100 dark:bg-purple-900/30"
        description="Bu ay toplam gelir"
      />
      <MetricCard
        label="Ortalama Doluluk"
        :value="reportingStore.averageOccupancyRate"
        format="percentage"
        :icon="ChartBarIcon"
        icon-color="text-orange-600 dark:text-orange-400"
        icon-bg-color="bg-orange-100 dark:bg-orange-900/30"
        description="Tüm oteller ortalaması"
      />
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
      <!-- Hotel Performance Chart -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h4 class="text-lg font-bold text-navy-700 dark:text-white">En İyi Performans Gösteren Oteller</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">Oda başına gelir bazında sıralama</p>
          </div>
        </div>
        <div class="space-y-4">
          <div
            v-for="(hotel, index) in reportingStore.topPerformingHotels"
            :key="hotel.hotel_id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-navy-900 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                index === 0 ? 'bg-yellow-100 text-yellow-800' :
                index === 1 ? 'bg-gray-100 text-gray-800' :
                index === 2 ? 'bg-orange-100 text-orange-800' :
                'bg-blue-100 text-blue-800'
              ]">
                {{ index + 1 }}
              </div>
              <div>
                <div class="font-medium text-navy-700 dark:text-white">{{ hotel.hotel_name }}</div>
                <div class="text-sm text-gray-500">{{ hotel.city }}, {{ hotel.country }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="font-bold text-navy-700 dark:text-white">
                {{ formatCurrency(hotel.monthly_revenue_per_room) }}
              </div>
              <div class="text-sm text-gray-500">oda/ay</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Regional Distribution -->
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h4 class="text-lg font-bold text-navy-700 dark:text-white">Bölgesel Dağılım</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">Ülke bazında otel sayıları</p>
          </div>
        </div>
        <div class="space-y-3">
          <div
            v-for="region in reportingStore.hotelsByRegion"
            :key="region.region"
            class="flex items-center justify-between"
          >
            <span class="text-navy-700 dark:text-white font-medium">{{ region.region }}</span>
            <div class="flex items-center space-x-2">
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-blue-500 h-2 rounded-full"
                  :style="{ width: `${(region.count / (platformOverview?.total_hotels || 1)) * 100}%` }"
                ></div>
              </div>
              <span class="text-sm font-bold text-navy-700 dark:text-white w-8 text-right">{{ region.count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Hotel Comparison Table -->
    <DataTable
      title="Otel Performans Karşılaştırması"
      subtitle="Tüm otellerin detaylı performans analizi"
      :columns="hotelColumns"
      :data="hotelTableData"
      empty-message="Otel verileri bulunamadı"
    >
      <template #actions>
        <button 
          @click="refreshData"
          :disabled="reportingStore.isLoading"
          class="text-sm bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white px-3 py-1 rounded-lg font-medium transition-colors"
        >
          {{ reportingStore.isLoading ? 'Yükleniyor...' : 'Yenile' }}
        </button>
      </template>
    </DataTable>

    <!-- System Activity Summary -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Haftalık Aktivite</h4>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Bakım Görevleri</span>
            <span class="font-bold text-navy-700 dark:text-white">{{ platformOverview?.weekly_maintenance_tasks || 0 }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Servis Talepleri</span>
            <span class="font-bold text-navy-700 dark:text-white">{{ platformOverview?.weekly_service_requests || 0 }}</span>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Kullanıcı Dağılımı</h4>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Otel Yöneticileri</span>
            <span class="font-bold text-navy-700 dark:text-white">{{ platformOverview?.hotel_admin_count || 0 }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Personel</span>
            <span class="font-bold text-navy-700 dark:text-white">{{ platformOverview?.staff_count || 0 }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Misafirler</span>
            <span class="font-bold text-navy-700 dark:text-white">{{ platformOverview?.guest_count || 0 }}</span>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-lg font-bold text-navy-700 dark:text-white mb-4">Büyüme Metrikleri</h4>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Bu Ay Eklenen Otel</span>
            <span class="font-bold text-green-600">+{{ platformOverview?.hotels_added_this_month || 0 }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Bu Ay Eklenen Kullanıcı</span>
            <span class="font-bold text-green-600">+{{ platformOverview?.users_added_this_month || 0 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useManagementReportingStore } from '@/stores/reportingStore'
import {
  BuildingOfficeIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

// Components
import MetricCard from '@/components/reports/MetricCard.vue'
import DataTable from '@/components/reports/DataTable.vue'

// Store
const reportingStore = useManagementReportingStore()

// Computed values
const platformOverview = computed(() => reportingStore.platformOverview)

// Table configuration
const hotelColumns = [
  { key: 'hotel_name', label: 'Otel Adı', type: 'text' as const },
  { key: 'location', label: 'Konum', type: 'text' as const },
  { key: 'status', label: 'Durum', type: 'status' as const, align: 'center' as const },
  { key: 'occupancy_rate', label: 'Doluluk', type: 'percentage' as const, align: 'center' as const },
  { key: 'monthly_revenue', label: 'Aylık Gelir', type: 'currency' as const, align: 'right' as const },
  { key: 'revenue_per_room', label: 'Oda/Gelir', type: 'currency' as const, align: 'right' as const },
  { key: 'guest_rating', label: 'Puan', type: 'text' as const, align: 'center' as const }
]

const hotelTableData = computed(() => {
  return reportingStore.hotelComparisons.map(hotel => ({
    hotel_name: hotel.hotel_name,
    location: `${hotel.city}, ${hotel.country}`,
    status: hotel.is_active ? 'active' : 'inactive',
    occupancy_rate: hotel.current_occupancy_rate,
    monthly_revenue: hotel.monthly_reservation_revenue + hotel.monthly_room_service_revenue,
    revenue_per_room: hotel.monthly_revenue_per_room,
    guest_rating: hotel.avg_guest_rating ? `${hotel.avg_guest_rating.toFixed(1)}/5` : '-'
  }))
})

// Utility functions
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(value)
}

const refreshData = async () => {
  await reportingStore.fetchAllReports()
}

// Lifecycle
onMounted(async () => {
  await reportingStore.fetchAllReports()
})
</script>

<style scoped>
/* Platform analytics specific styles */
</style>
