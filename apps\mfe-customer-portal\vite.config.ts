import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import federation from '@originjs/vite-plugin-federation'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    federation({
      name: 'mfe_customer_portal',
      filename: 'remoteEntry.js',
      exposes: {
        './CustomerPortal': './src/App.vue',
        './Bootstrap': './src/bootstrap.ts',
        './OnboardingView': './src/views/OnboardingView.vue',
        './GuestLoginView': './src/views/GuestLoginView.vue',
        './DashboardView': './src/views/GuestDashboardView.vue',
        './RoomServiceMenuView': './src/views/RoomServiceMenuView.vue',
        './RoomServiceCategoryView': './src/views/RoomServiceCategoryView.vue',
        './RoomServiceView': './src/views/RoomServiceView.vue',
        './RoomServiceItemDetailView': './src/views/RoomServiceItemDetailView.vue',
        './ActivitiesView': './src/views/ActivitiesView.vue',
        './ActivityDetailView': './src/views/ActivityDetailView.vue',
        './ServicesHomeView': './src/views/ServicesHomeView.vue',
        './ServiceCategoryView': './src/views/ServiceCategoryView.vue',
        './ServiceDetailView': './src/views/ServiceDetailView.vue',
        './ServiceRequestView': './src/views/ServiceRequestView.vue',
        './MyRequestsView': './src/views/MyRequestsView.vue',
        './NotificationsView': './src/views/NotificationsView.vue',
        './CartView': './src/views/CartView.vue',
        './OrderSuccessView': './src/views/OrderSuccessView.vue',
        './OrderFailedView': './src/views/OrderFailedView.vue',
        './MyOrdersView': './src/views/MyOrdersView.vue',
        './HomeView': './src/views/HomeView.vue',
        './BookingView': './src/views/BookingView.vue',
        './MyReservationsView': './src/views/MyReservationsView.vue'
      },
      shared: {
        vue: { singleton: true, requiredVersion: '^3.4.15' },
        'vue-router': { singleton: true, requiredVersion: '^4.2.5' },
        pinia: { singleton: true, requiredVersion: '^2.1.7' },
        '@hotelexia/shared-supabase-client': { singleton: true },
        '@hotelexia/shared-components': { singleton: true }
      }
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@hotelexia/shared-components': resolve(__dirname, '../../packages/shared-components/src/index.ts'),
      '@hotelexia/shared-supabase-client': resolve(__dirname, '../../packages/shared-supabase-client/src/index.ts')
    }
  },
  server: {
    port: 3001,
    host: true,
    strictPort: true,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    },
    fs: {
      allow: ['..']
    }
  },
  preview: {
    port: 3001,
    strictPort: true
  },
  build: {
    modulePreload: false,
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  },
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
}) 