import { test, expect } from '@playwright/test'

test.describe('Cross-MFE Data Consistency & Real-time Features', () => {
  test('should maintain hotel data consistency across Management and Hotel Dashboard', async ({ context }) => {
    // Create two pages for different MFEs
    const managementPage = await context.newPage()
    const hotelPage = await context.newPage()
    
    // Navigate to Management Portal hotels list
    await managementPage.goto('http://localhost:3000/management/hotels')
    await managementPage.waitForTimeout(3000)
    
    // Get hotel data from Management Portal
    const managementHotels = await managementPage.locator('table tbody tr')
    const managementHotelCount = await managementHotels.count()
    
    if (managementHotelCount > 0) {
      const firstHotelName = await managementHotels.first().locator('td').first().textContent()
      console.log('Management Portal - First hotel:', firstHotelName)
      
      // Navigate to Hotel Dashboard
      await hotelPage.goto('http://localhost:5002')
      await hotelPage.waitForTimeout(3000)
      
      // Check if hotel context is consistent
      const hotelContext = hotelPage.locator('[data-testid="hotel-name"], .hotel-name, h1, h2')
      if (await hotelContext.count() > 0) {
        const dashboardHotelContext = await hotelContext.first().textContent()
        console.log('Hotel Dashboard - Hotel context:', dashboardHotelContext)
        
        // Should have some hotel context
        expect(dashboardHotelContext).toBeTruthy()
      }
    }
    
    await managementPage.close()
    await hotelPage.close()
  })

  test('should synchronize room status updates across views', async ({ context }) => {
    const page1 = await context.newPage()
    const page2 = await context.newPage()
    
    // Navigate both pages to room management
    await page1.goto('http://localhost:5002/housekeeping/rooms')
    await page2.goto('http://localhost:5002/maintenance/rooms')
    
    await page1.waitForTimeout(3000)
    await page2.waitForTimeout(3000)
    
    // Get initial room counts from both views
    const housekeepingRooms = await page1.locator('[data-testid="room-card"], .room-card, table tbody tr').count()
    const maintenanceRooms = await page2.locator('[data-testid="room-card"], .room-card, table tbody tr').count()
    
    console.log(`Housekeeping rooms: ${housekeepingRooms}, Maintenance rooms: ${maintenanceRooms}`)
    
    // Both views should show consistent room data
    if (housekeepingRooms > 0 && maintenanceRooms > 0) {
      // Allow for some variance due to different filtering
      expect(Math.abs(housekeepingRooms - maintenanceRooms)).toBeLessThanOrEqual(5)
    }
    
    await page1.close()
    await page2.close()
  })

  test('should handle real-time notifications across MFEs', async ({ context }) => {
    const hotelPage = await context.newPage()
    const managementPage = await context.newPage()
    
    // Navigate to different MFEs
    await hotelPage.goto('http://localhost:5002/housekeeping/rooms')
    await managementPage.goto('http://localhost:3000/management/dashboard')
    
    await hotelPage.waitForTimeout(3000)
    await managementPage.waitForTimeout(3000)
    
    // Check for notification systems
    const hotelNotifications = hotelPage.locator('[data-testid="notifications"], .notifications, .notification-bell')
    const managementNotifications = managementPage.locator('[data-testid="notifications"], .notifications, .notification-bell')
    
    // Should have notification systems in place
    const hotelHasNotifications = await hotelNotifications.count() > 0
    const managementHasNotifications = await managementNotifications.count() > 0
    
    console.log(`Hotel notifications: ${hotelHasNotifications}, Management notifications: ${managementHasNotifications}`)
    
    // At least one MFE should have notification system
    expect(hotelHasNotifications || managementHasNotifications).toBe(true)
    
    await hotelPage.close()
    await managementPage.close()
  })

  test('should maintain user session consistency across MFEs', async ({ context }) => {
    const page1 = await context.newPage()
    const page2 = await context.newPage()
    
    // Navigate to different MFEs
    await page1.goto('http://localhost:5002')
    await page2.goto('http://localhost:3000/management/dashboard')
    
    await page1.waitForTimeout(3000)
    await page2.waitForTimeout(3000)
    
    // Check authentication state consistency
    const page1HasLogin = await page1.locator('input[type="email"], input[type="password"]').count() > 0
    const page2HasLogin = await page2.locator('input[type="email"], input[type="password"]').count() > 0
    
    const page1HasContent = await page1.locator('main, .dashboard, [data-testid="content"]').count() > 0
    const page2HasContent = await page2.locator('main, .dashboard, [data-testid="content"]').count() > 0
    
    console.log(`Page1 (Hotel): login=${page1HasLogin}, content=${page1HasContent}`)
    console.log(`Page2 (Management): login=${page2HasLogin}, content=${page2HasContent}`)
    
    // Both should handle auth consistently
    expect(page1HasLogin || page1HasContent).toBe(true)
    expect(page2HasLogin || page2HasContent).toBe(true)
    
    await page1.close()
    await page2.close()
  })

  test('should handle data caching consistency', async ({ page }) => {
    // Test data caching by navigating between pages
    await page.goto('http://localhost:3000/management/hotels')
    await page.waitForTimeout(3000)
    
    // Get initial load time and data
    const startTime = Date.now()
    const initialHotelCount = await page.locator('table tbody tr').count()
    const firstLoadTime = Date.now() - startTime
    
    // Navigate away and back
    await page.goto('http://localhost:3000/management/dashboard')
    await page.waitForTimeout(2000)
    
    await page.goto('http://localhost:3000/management/hotels')
    const cacheStartTime = Date.now()
    await page.waitForTimeout(3000)
    
    const cachedHotelCount = await page.locator('table tbody tr').count()
    const cacheLoadTime = Date.now() - cacheStartTime
    
    // Data should be consistent
    expect(cachedHotelCount).toBe(initialHotelCount)
    
    // Cached load should be faster (if caching is working)
    console.log(`First load: ${firstLoadTime}ms, Cached load: ${cacheLoadTime}ms`)
    
    // Allow for some variance in load times
    if (firstLoadTime > 1000 && cacheLoadTime > 0) {
      expect(cacheLoadTime).toBeLessThanOrEqual(firstLoadTime * 1.5)
    }
  })

  test('should handle error propagation across MFEs', async ({ context }) => {
    const errors: { [key: string]: string[] } = {}
    
    const testPages = [
      { name: 'Hotel Dashboard', url: 'http://localhost:5002' },
      { name: 'Management Portal', url: 'http://localhost:3000/management/dashboard' },
      { name: 'Customer Portal', url: 'http://localhost:3001' }
    ]
    
    for (const testPage of testPages) {
      const page = await context.newPage()
      errors[testPage.name] = []
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors[testPage.name].push(msg.text())
        }
      })
      
      await page.goto(testPage.url)
      await page.waitForTimeout(3000)
      
      // Check for error boundaries
      const hasErrorBoundary = await page.locator('.error-boundary, [data-testid="error-boundary"]').count() > 0
      const hasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
      const hasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
      
      // Should handle errors gracefully
      expect(hasErrorBoundary || hasContent || hasLogin).toBe(true)
      
      await page.close()
    }
    
    // Log errors for debugging
    for (const [pageName, pageErrors] of Object.entries(errors)) {
      const criticalErrors = pageErrors.filter(error => 
        !error.includes('favicon') && 
        !error.includes('404') &&
        !error.includes('WebSocket') &&
        !error.includes('net::ERR_FAILED')
      )
      
      if (criticalErrors.length > 0) {
        console.warn(`${pageName} critical errors:`, criticalErrors)
      }
    }
  })

  test('should maintain data integrity during concurrent operations', async ({ context }) => {
    // Test concurrent data operations
    const page1 = await context.newPage()
    const page2 = await context.newPage()
    
    // Navigate to same data view from different pages
    await page1.goto('http://localhost:3000/management/hotels')
    await page2.goto('http://localhost:3000/management/hotels')
    
    await page1.waitForTimeout(3000)
    await page2.waitForTimeout(3000)
    
    // Get data from both pages
    const page1HotelCount = await page1.locator('table tbody tr').count()
    const page2HotelCount = await page2.locator('table tbody tr').count()
    
    // Should show consistent data
    expect(page1HotelCount).toBe(page2HotelCount)
    
    // Refresh both pages simultaneously
    await Promise.all([
      page1.reload(),
      page2.reload()
    ])
    
    await page1.waitForTimeout(3000)
    await page2.waitForTimeout(3000)
    
    // Data should still be consistent
    const refreshedPage1Count = await page1.locator('table tbody tr').count()
    const refreshedPage2Count = await page2.locator('table tbody tr').count()
    
    expect(refreshedPage1Count).toBe(refreshedPage2Count)
    
    await page1.close()
    await page2.close()
  })

  test('should handle offline/online state transitions', async ({ page, context }) => {
    // Test offline handling
    await page.goto('http://localhost:3000/management/hotels')
    await page.waitForTimeout(3000)
    
    // Get initial data
    const onlineHotelCount = await page.locator('table tbody tr').count()
    
    // Simulate offline state
    await context.setOffline(true)
    await page.reload()
    await page.waitForTimeout(3000)
    
    // Should handle offline state gracefully
    const hasOfflineMessage = await page.locator('text=/offline/i, text=/connection/i, .offline').count() > 0
    const hasErrorBoundary = await page.locator('.error-boundary, [data-testid="error-boundary"]').count() > 0
    const hasCachedData = await page.locator('table tbody tr').count() > 0
    
    // Should show some indication of offline state or cached data
    expect(hasOfflineMessage || hasErrorBoundary || hasCachedData).toBe(true)
    
    // Go back online
    await context.setOffline(false)
    await page.reload()
    await page.waitForTimeout(3000)
    
    // Should restore functionality
    const onlineAgainCount = await page.locator('table tbody tr').count()
    
    // Data should be restored
    if (onlineHotelCount > 0) {
      expect(onlineAgainCount).toBeGreaterThan(0)
    }
  })

  test('should validate cross-MFE navigation consistency', async ({ page }) => {
    // Test navigation between MFEs
    const navigationTests = [
      { from: 'http://localhost:3000', to: 'http://localhost:5002', description: 'App Shell to Hotel Dashboard' },
      { from: 'http://localhost:3000', to: 'http://localhost:3001', description: 'App Shell to Customer Portal' },
      { from: 'http://localhost:5002', to: 'http://localhost:3000/management/dashboard', description: 'Hotel Dashboard to Management' }
    ]
    
    for (const nav of navigationTests) {
      await page.goto(nav.from)
      await page.waitForTimeout(2000)
      
      await page.goto(nav.to)
      await page.waitForTimeout(3000)
      
      // Should load successfully
      const hasContent = await page.locator('main, .dashboard, [data-testid="content"]').count() > 0
      const hasLogin = await page.locator('input[type="email"], input[type="password"]').count() > 0
      const hasError = await page.locator('.error-boundary, [data-testid="error"]').count() > 0
      
      // Should either show content, login, or handle errors gracefully
      expect(hasContent || hasLogin || hasError).toBe(true)
      
      console.log(`${nav.description}: content=${hasContent}, login=${hasLogin}, error=${hasError}`)
    }
  })
})
