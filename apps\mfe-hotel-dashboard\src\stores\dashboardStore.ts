import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { HotelDashboardService, dataCacheService } from '@hotelexia/shared-supabase-client'
import {
  errorHandlingService,
  useLoadingState,
  useErrorHandling,
  useCacheManagement,
  useDataFetching
} from '@hotelexia/shared-components'
import { useAuthStore } from './authStore'

export interface StatisticsData {
  earnings: string
  spendThisMonth: string
  sales: string
  salesGrowth: string
  balance: string
  newTasks: string
  totalProjects: string
}

export interface TaskData {
  id: string
  name: string
  checked: boolean
}

export const useDashboardStore = defineStore('dashboard', () => {
  // Get auth store for user context
  const authStore = useAuthStore()

  // Initialize enhanced composables
  const loadingState = useLoadingState({
    minLoadingTime: 500,
    maxLoadingTime: 30000,
    enableTimeout: true,
    timeoutMessage: 'Dashboard is taking longer than expected to load...'
  })

  const errorHandling = useErrorHandling({
    component: 'HotelDashboard',
    userId: authStore.user?.id,
    hotelId: authStore.userHotelId,
    maxRetries: 3,
    retryDelay: 1000,
    enableRecovery: true
  })

  const cacheManagement = useCacheManagement({
    keyPrefix: 'hotel-dashboard',
    defaultTTL: 2 * 60 * 1000, // 2 minutes
    enableStats: true,
    maxCacheSize: 100
  })

  // State
  const userName = ref('Hotel Admin')
  const hotelName = ref('Grand Palace Hotel')
  const darkMode = ref(false)
  const isSidebarCollapsed = ref(false)

  // Use enhanced loading and error states
  const isLoading = computed(() => loadingState.isLoading.value)
  const error = computed(() => errorHandling.lastError.value?.message || null)

  const statistics = ref<StatisticsData>({
    earnings: '₺0',
    spendThisMonth: '₺0',
    sales: '₺0',
    salesGrowth: '+0%',
    balance: '₺0',
    newTasks: '0',
    totalProjects: '0'
  })

  const tasks = ref<TaskData[]>([])

  // Fetch dashboard statistics from real data
  const fetchDashboardStats = async (forceRefresh = false) => {
    // Don't fetch if auth is not ready
    if (!authStore.isAuthenticated) {
      console.warn('Dashboard: Skipping data fetch - user not authenticated')
      return
    }

    try {
      loadingState.startLoading('Loading dashboard data...', 'dashboard-stats')

      const hotelId = authStore.user?.role !== 'SUPER_ADMIN' ? (authStore.user as any)?.hotel_id : undefined
      const cacheKey = `dashboard-stats:${hotelId || 'global'}`

      // Use enhanced cache management
      const cachedData = await cacheManagement.get(
        cacheKey,
        async () => {
          // Use Promise.allSettled to prevent one failure from breaking everything
          const [tasksResponse, roomsResponse, staffResponse, ordersResponse] = await Promise.allSettled([
            HotelDashboardService.getTasks(hotelId),
            HotelDashboardService.getRooms(hotelId),
            HotelDashboardService.getStaffMembers(hotelId),
            HotelDashboardService.getRoomServiceOrders(hotelId)
          ])

          return {
            tasksResponse,
            roomsResponse,
            staffResponse,
            ordersResponse
          }
        },
        {
          ttl: 2 * 60 * 1000, // 2 minutes cache for dashboard data
          tags: ['dashboard', 'hotel-data', hotelId || 'global'],
          forceRefresh,
          staleWhileRevalidate: true
        }
      )

      const { tasksResponse, roomsResponse, staffResponse, ordersResponse } = cachedData

      // Extract successful responses and log failures
      const extractResponse = (result: PromiseSettledResult<any>, name: string) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          console.error(`Dashboard: Failed to fetch ${name}:`, result.reason)
          return { data: null, error: result.reason }
        }
      }

      const tasksData = extractResponse(tasksResponse, 'tasks')
      const roomsData = extractResponse(roomsResponse, 'rooms')
      const staffData = extractResponse(staffResponse, 'staff')
      const ordersData = extractResponse(ordersResponse, 'orders')

      // Process tasks data with fallback
      let newTasks = 0
      let completedTasks = 0
      if (tasksData.data) {
        const allTasks = tasksData.data
        newTasks = allTasks.filter(t => t.status === 'NEW').length
        completedTasks = allTasks.filter(t => t.status === 'COMPLETED').length
      }

      // Calculate revenue from room service orders with fallback
      let totalRevenue = 0
      let todayRevenue = 0
      const today = new Date().toDateString()

      if (ordersData.data) {
        const deliveredOrders = ordersData.data.filter(order => order.status === 'DELIVERED')
        totalRevenue = deliveredOrders.reduce((sum, order) => sum + order.totalPrice, 0)

        const todayOrders = deliveredOrders.filter(order =>
          new Date(order.createdAt).toDateString() === today
        )
        todayRevenue = todayOrders.reduce((sum, order) => sum + order.totalPrice, 0)
      }

      // Update statistics with real data
      statistics.value = {
        earnings: `₺${todayRevenue.toFixed(2)}`,
        spendThisMonth: '₺0', // Will be calculated from expenses
        sales: `₺${totalRevenue.toFixed(2)}`,
        salesGrowth: '+0%', // Will be calculated from historical data
        balance: `₺${(totalRevenue * 0.8).toFixed(2)}`, // Estimated balance (80% of revenue)
        newTasks: newTasks.toString(),
        totalProjects: (roomsData.data?.length || 0).toString()
      }

      // Convert recent tasks to dashboard format
      if (tasksData.data) {
        tasks.value = tasksData.data
          .slice(0, 5)
          .map(task => ({
            id: task.id,
            name: `${task.title} - ${task.roomNumber}`,
            checked: task.status === 'COMPLETED'
          }))
      }

      // Update user info from auth store
      if (authStore.user) {
        userName.value = (authStore.user as any).full_name || 'Hotel Admin'
        // hotelName will be updated when hotel data is available
      }

    } catch (err) {
      // Use enhanced error handling service
      await errorHandlingService.handleError(
        err instanceof Error ? err : new Error(String(err)),
        {
          component: 'DashboardStore',
          action: 'fetchDashboardStats',
          userId: authStore.user?.id,
          hotelId: authStore.userHotelId
        },
        {
          showToast: true,
          retryable: true,
          customMessage: 'Dashboard verileri yüklenirken hata oluştu'
        }
      )

      error.value = err instanceof Error ? err.message : 'Failed to fetch dashboard statistics'

      // Set fallback values on error to prevent blank pages
      statistics.value = {
        earnings: '₺0',
        spendThisMonth: '₺0',
        sales: '₺0',
        salesGrowth: '+0%',
        balance: '₺0',
        newTasks: '0',
        totalProjects: '0'
      }
      tasks.value = []
    } finally {
      isLoading.value = false
    }
  }

  // Retry mechanism for failed data fetches
  const retryFetchDashboardStats = async (maxRetries = 3) => {
    let retryCount = 0

    while (retryCount < maxRetries) {
      try {
        await fetchDashboardStats()
        if (!error.value) {
          break // Success, exit retry loop
        }
      } catch (err) {
        console.warn(`Dashboard stats fetch attempt ${retryCount + 1} failed:`, err)
      }

      retryCount++
      if (retryCount < maxRetries) {
        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000))
      }
    }

    if (retryCount === maxRetries && error.value) {
      console.error('Dashboard stats fetch failed after all retries')
    }
  }

  // Initialize dashboard data
  const initializeDashboard = async () => {
    await fetchDashboardStats()
  }

  // Note: Automatic initialization removed to prevent blank pages
  // Components should call initializeDashboard() when ready

  // Computed
  const completedTasksCount = computed(() => 
    tasks.value.filter(task => task.checked).length
  )

  const pendingTasksCount = computed(() => 
    tasks.value.filter(task => !task.checked).length
  )

  // Actions
  const toggleTask = (taskId: string) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.checked = !task.checked
    }
  }

  const toggleDarkMode = () => {
    darkMode.value = !darkMode.value
    document.documentElement.classList.toggle('dark', darkMode.value)
  }

  const toggleSidebar = () => {
    isSidebarCollapsed.value = !isSidebarCollapsed.value
  }

  return {
    userName,
    hotelName,
    darkMode,
    isSidebarCollapsed,
    statistics,
    tasks,
    isLoading,
    error,
    completedTasksCount,
    pendingTasksCount,
    toggleTask,
    toggleDarkMode,
    toggleSidebar,
    fetchDashboardStats,
    retryFetchDashboardStats,
    initializeDashboard
  }
}) 