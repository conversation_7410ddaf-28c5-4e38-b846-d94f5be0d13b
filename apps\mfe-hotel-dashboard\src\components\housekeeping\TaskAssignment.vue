<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">Görev Yönetimi</h1>
          <p class="text-gray-600 dark:text-gray-400">Kat hizmetleri görev atama ve takip sistemi</p>
        </div>
        <button
          @click="openCreateModal"
          class="flex items-center space-x-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <span><PERSON><PERSON></span>
        </button>
      </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <KpiCard
        title="Toplam Görev"
        :value="tasks.length"
        format="number"
        subtitle="aktif görevler"
        icon="clipboard-list"
        color="blue"
      />
      <KpiCard
        title="Atanmamış"
        :value="tasksByStatus.unassigned.length"
        format="number"
        subtitle="bekleyen"
        icon="clock"
        color="gray"
      />
      <KpiCard
        title="Devam Eden"
        :value="tasksByStatus.inProgress.length"
        format="number"
        subtitle="işlemde"
        icon="cog"
        color="yellow"
      />
      <KpiCard
        title="Kalite Kontrolü"
        :value="tasksByStatus.qualityCheck.length"
        format="number"
        subtitle="kontrol"
        icon="check-circle"
        color="purple"
      />
      <KpiCard
        title="Tamamlanan"
        :value="tasksByStatus.completed.length"
        format="number"
        subtitle="bugün"
        icon="check"
        color="green"
      />
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg p-6 mb-8">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Öncelik:</label>
          <select
            v-model="filters.priority"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
          >
            <option value="">Tümü</option>
            <option value="URGENT">Acil</option>
            <option value="HIGH">Yüksek</option>
            <option value="MEDIUM">Orta</option>
            <option value="LOW">Düşük</option>
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Personel:</label>
          <select
            v-model="filters.assignedTo"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
          >
            <option value="">Tümü</option>
            <option value="unassigned">Atanmamış</option>
            <option v-for="staffMember in staff" :key="staffMember.id" :value="staffMember.id">
                              {{ staffMember.name }} {{ staffMember.surname }}
            </option>
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Kat:</label>
          <select
            v-model="filters.floor"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
          >
            <option value="">Tümü</option>
            <option v-for="floor in floors" :key="floor.id" :value="floor.number">
              {{ floor.number }}. Kat
            </option>
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <input
            v-model="filters.search"
            type="text"
            placeholder="Görev ara..."
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm"
          />
          <button
            @click="clearFilters"
            class="px-3 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
      <!-- Yeni Görevler -->
      <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Yeni Görevler</h3>
            <span class="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full text-sm font-medium">
              {{ filteredTasksByStatus.unassigned.length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-[600px] overflow-y-auto">
          <TaskCard
            v-for="task in filteredTasksByStatus.unassigned"
            :key="task.id"
            :task="task"
            @edit="openEditModal"
            @status-change="handleStatusChange"
            @assign="handleAssign"
          />
          <div v-if="filteredTasksByStatus.unassigned.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <p class="text-sm">Yeni görev bulunmuyor</p>
          </div>
        </div>
      </div>

      <!-- Atanmış -->
      <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Atanmış</h3>
            <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full text-sm font-medium">
              {{ getAssignedTasks().length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-[600px] overflow-y-auto">
          <TaskCard
            v-for="task in getAssignedTasks()"
            :key="task.id"
            :task="task"
            @edit="openEditModal"
            @status-change="handleStatusChange"
            @assign="handleAssign"
          />
          <div v-if="getAssignedTasks().length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <p class="text-sm">Atanmış görev bulunmuyor</p>
          </div>
        </div>
      </div>

      <!-- Devam Ediyor -->
      <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Devam Ediyor</h3>
            <span class="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 px-2 py-1 rounded-full text-sm font-medium">
              {{ filteredTasksByStatus.inProgress.length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-[600px] overflow-y-auto">
          <TaskCard
            v-for="task in filteredTasksByStatus.inProgress"
            :key="task.id"
            :task="task"
            @edit="openEditModal"
            @status-change="handleStatusChange"
            @assign="handleAssign"
          />
          <div v-if="filteredTasksByStatus.inProgress.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-sm">Devam eden görev bulunmuyor</p>
          </div>
        </div>
      </div>

      <!-- Kalite Kontrolü -->
      <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Kalite Kontrolü</h3>
            <span class="bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 px-2 py-1 rounded-full text-sm font-medium">
              {{ filteredTasksByStatus.qualityCheck.length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-[600px] overflow-y-auto">
          <TaskCard
            v-for="task in filteredTasksByStatus.qualityCheck"
            :key="task.id"
            :task="task"
            @edit="openEditModal"
            @status-change="handleStatusChange"
            @assign="handleAssign"
          />
          <div v-if="filteredTasksByStatus.qualityCheck.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-sm">Kalite kontrolünde görev bulunmuyor</p>
          </div>
        </div>
      </div>

      <!-- Tamamlandı -->
      <div class="bg-white dark:bg-navy-900 rounded-2xl shadow-lg">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-800 dark:text-white">Tamamlandı</h3>
            <span class="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-sm font-medium">
              {{ filteredTasksByStatus.completed.length }}
            </span>
          </div>
        </div>
        <div class="p-4 space-y-3 max-h-[600px] overflow-y-auto">
          <TaskCard
            v-for="task in filteredTasksByStatus.completed"
            :key="task.id"
            :task="task"
            @edit="openEditModal"
            @status-change="handleStatusChange"
            @assign="handleAssign"
          />
          <div v-if="filteredTasksByStatus.completed.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <p class="text-sm">Tamamlanmış görev bulunmuyor</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Task Modal -->
    <TaskAssignmentModal
      :is-open="modal.isOpen"
      :mode="modal.mode"
      :task="modal.task"
      @close="closeModal"
      @submit="handleModalSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useHousekeepingStore } from '@/stores/housekeepingStore'
import type { HousekeepingTask, TaskStatus } from '@/types/housekeeping'
import TaskCard from './TaskCard.vue'
import KpiCard from './KpiCard.vue'
import TaskAssignmentModal from './TaskAssignmentModal.vue'

const housekeepingStore = useHousekeepingStore()

// State
const filters = ref({
  priority: '',
  assignedTo: '',
  floor: '',
  search: ''
})

const modal = ref({
  isOpen: false,
  mode: 'create' as 'create' | 'edit',
  task: undefined as HousekeepingTask | undefined
})

// Computed
const tasks = computed(() => housekeepingStore.tasks)
const staff = computed(() => housekeepingStore.staff)
const floors = computed(() => housekeepingStore.floors)
const tasksByStatus = computed(() => housekeepingStore.tasksByStatus)

const filteredTasks = computed(() => {
  let filtered = tasks.value

  // Priority filter
  if (filters.value.priority) {
    filtered = filtered.filter(task => task.priority === filters.value.priority)
  }

  // Assigned staff filter
  if (filters.value.assignedTo) {
    if (filters.value.assignedTo === 'unassigned') {
      filtered = filtered.filter(task => !task.assignedTo)
    } else {
      filtered = filtered.filter(task => task.assignedTo === filters.value.assignedTo)
    }
  }

  // Floor filter
  if (filters.value.floor) {
    const floorNumber = parseInt(filters.value.floor)
    filtered = filtered.filter(task => {
      const roomNumber = task.roomNumber
      // Simple logic: extract floor from room number (e.g., "205" -> floor 2)
      const taskFloor = Math.floor(parseInt(roomNumber) / 100)
      return taskFloor === floorNumber
    })
  }

  // Search filter
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase()
    filtered = filtered.filter(task => 
      task.title.toLowerCase().includes(searchTerm) ||
      task.description.toLowerCase().includes(searchTerm) ||
      task.roomNumber.includes(searchTerm) ||
      (task.notes && task.notes.toLowerCase().includes(searchTerm))
    )
  }

  return filtered
})

const filteredTasksByStatus = computed(() => {
  const filtered = filteredTasks.value
  
  return {
    unassigned: filtered.filter(t => t.status === 'NEW'),
    inProgress: filtered.filter(t => t.status === 'IN_PROGRESS'),
    qualityCheck: filtered.filter(t => t.status === 'QUALITY_CHECK'),
    completed: filtered.filter(t => t.status === 'COMPLETED'),
    cancelled: filtered.filter(t => t.status === 'CANCELLED')
  }
})

// Methods
const getAssignedTasks = () => {
  return filteredTasks.value.filter(task => 
    task.assignedTo && task.status === 'NEW'
  )
}

const clearFilters = () => {
  filters.value = {
    priority: '',
    assignedTo: '',
    floor: '',
    search: ''
  }
}

const openCreateModal = () => {
  modal.value = {
    isOpen: true,
    mode: 'create',
    task: undefined
  }
}

const openEditModal = (task: HousekeepingTask) => {
  modal.value = {
    isOpen: true,
    mode: 'edit',
    task: task
  }
}

const closeModal = () => {
  modal.value.isOpen = false
  modal.value.task = undefined
}

const handleModalSubmit = (taskData: any) => {
  if (modal.value.mode === 'create') {
    housekeepingStore.addNewTask(taskData)
  } else if (modal.value.task) {
    // Update existing task logic would go here
    // For now, we'll update the task directly in the store
    const task = housekeepingStore.tasks.find(t => t.id === modal.value.task?.id)
    if (task) {
      Object.assign(task, taskData)
    }
  }
}

const handleStatusChange = (taskId: string, newStatus: TaskStatus) => {
  housekeepingStore.updateTaskStatus(taskId, newStatus)
}

const handleAssign = (task: HousekeepingTask) => {
  // For now, this would open an assignment modal
  // In a real implementation, you'd show a staff selection modal
  console.log('Assigning task:', task.id)
}
</script> 