<template>
  <div v-if="show" :class="containerClasses">
    <!-- Skeleton Loading -->
    <div v-if="variant === 'skeleton'" class="space-y-4">
      <div v-for="i in skeletonLines" :key="i" class="skeleton-line" :style="getSkeletonWidth(i)"></div>
    </div>
    
    <!-- Spinner Loading -->
    <div v-else-if="variant === 'spinner'" class="spinner-container">
      <div class="spinner"></div>
      <p v-if="message" class="spinner-message">{{ message }}</p>
    </div>
    
    <!-- Card Skeleton -->
    <div v-else-if="variant === 'card'" class="card-skeleton">
      <div class="card-skeleton-header"></div>
      <div class="card-skeleton-content">
        <div class="skeleton-line w-full"></div>
        <div class="skeleton-line w-3/4"></div>
        <div class="skeleton-line w-1/2"></div>
      </div>
    </div>
    
    <!-- Table Skeleton -->
    <div v-else-if="variant === 'table'" class="table-skeleton">
      <div class="table-skeleton-header">
        <div v-for="i in tableColumns" :key="i" class="skeleton-line h-4"></div>
      </div>
      <div v-for="row in tableRows" :key="row" class="table-skeleton-row">
        <div v-for="col in tableColumns" :key="col" class="skeleton-line h-4"></div>
      </div>
    </div>
    
    <!-- Dashboard Skeleton -->
    <div v-else-if="variant === 'dashboard'" class="dashboard-skeleton">
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div v-for="i in 4" :key="i" class="stat-card-skeleton">
          <div class="skeleton-line h-6 w-1/2 mb-2"></div>
          <div class="skeleton-line h-8 w-3/4"></div>
        </div>
      </div>
      
      <!-- Charts -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="chart-skeleton">
          <div class="skeleton-line h-6 w-1/3 mb-4"></div>
          <div class="skeleton-chart"></div>
        </div>
        <div class="chart-skeleton">
          <div class="skeleton-line h-6 w-1/3 mb-4"></div>
          <div class="skeleton-chart"></div>
        </div>
      </div>
    </div>
    
    <!-- List Skeleton -->
    <div v-else-if="variant === 'list'" class="list-skeleton">
      <div v-for="i in listItems" :key="i" class="list-item-skeleton">
        <div class="list-item-avatar"></div>
        <div class="list-item-content">
          <div class="skeleton-line h-4 w-3/4 mb-2"></div>
          <div class="skeleton-line h-3 w-1/2"></div>
        </div>
      </div>
    </div>
    
    <!-- Default Spinner -->
    <div v-else class="default-loading">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
      </div>
      <p v-if="message" class="loading-message">{{ message }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  show: boolean
  variant?: 'spinner' | 'skeleton' | 'card' | 'table' | 'dashboard' | 'list' | 'default'
  message?: string
  size?: 'sm' | 'md' | 'lg'
  fullscreen?: boolean
  overlay?: boolean
  skeletonLines?: number
  tableColumns?: number
  tableRows?: number
  listItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  message: '',
  size: 'md',
  fullscreen: false,
  overlay: false,
  skeletonLines: 3,
  tableColumns: 4,
  tableRows: 5,
  listItems: 5
})

const containerClasses = computed(() => {
  const classes = ['loading-state']
  
  if (props.fullscreen) {
    classes.push('loading-fullscreen')
  }
  
  if (props.overlay) {
    classes.push('loading-overlay')
  }
  
  classes.push(`loading-${props.size}`)
  
  return classes.join(' ')
})

const getSkeletonWidth = (index: number) => {
  const widths = ['100%', '85%', '70%', '90%', '60%']
  return { width: widths[index % widths.length] }
}
</script>

<style scoped>
.loading-state {
  @apply flex items-center justify-center;
}

.loading-fullscreen {
  @apply fixed inset-0 bg-white dark:bg-navy-900 z-50;
}

.loading-overlay {
  @apply absolute inset-0 bg-white/80 dark:bg-navy-900/80 backdrop-blur-sm z-40;
}

.loading-sm {
  @apply p-2;
}

.loading-md {
  @apply p-4;
}

.loading-lg {
  @apply p-8;
}

/* Skeleton Animations */
@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.skeleton-line {
  @apply bg-gray-200 dark:bg-navy-700 rounded h-4 mb-3;
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Spinner Styles */
.spinner-container {
  @apply flex flex-col items-center;
}

.spinner {
  @apply w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
}

.spinner-message {
  @apply mt-4 text-sm text-gray-600 dark:text-gray-300;
}

/* Card Skeleton */
.card-skeleton {
  @apply bg-white dark:bg-navy-800 rounded-lg shadow p-6;
}

.card-skeleton-header {
  @apply h-6 bg-gray-200 dark:bg-navy-700 rounded mb-4;
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.card-skeleton-content {
  @apply space-y-3;
}

/* Table Skeleton */
.table-skeleton {
  @apply bg-white dark:bg-navy-800 rounded-lg shadow overflow-hidden;
}

.table-skeleton-header {
  @apply grid gap-4 p-4 bg-gray-50 dark:bg-navy-700;
  grid-template-columns: repeat(var(--columns, 4), 1fr);
}

.table-skeleton-row {
  @apply grid gap-4 p-4 border-t border-gray-200 dark:border-navy-600;
  grid-template-columns: repeat(var(--columns, 4), 1fr);
}

/* Dashboard Skeleton */
.dashboard-skeleton {
  @apply w-full;
}

.stat-card-skeleton {
  @apply bg-white dark:bg-navy-800 rounded-lg shadow p-6;
}

.chart-skeleton {
  @apply bg-white dark:bg-navy-800 rounded-lg shadow p-6;
}

.skeleton-chart {
  @apply h-64 bg-gray-200 dark:bg-navy-700 rounded;
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* List Skeleton */
.list-skeleton {
  @apply space-y-4;
}

.list-item-skeleton {
  @apply flex items-center space-x-4 p-4 bg-white dark:bg-navy-800 rounded-lg shadow;
}

.list-item-avatar {
  @apply w-12 h-12 bg-gray-200 dark:bg-navy-700 rounded-full;
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.list-item-content {
  @apply flex-1;
}

/* Default Loading */
.default-loading {
  @apply flex flex-col items-center;
}

.loading-spinner {
  @apply relative;
}

.spinner-ring {
  @apply w-12 h-12 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
}

.loading-message {
  @apply mt-4 text-sm text-gray-600 dark:text-gray-300 text-center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-skeleton .grid {
    @apply grid-cols-1;
  }
  
  .table-skeleton-header,
  .table-skeleton-row {
    @apply grid-cols-2;
  }
}
</style>
