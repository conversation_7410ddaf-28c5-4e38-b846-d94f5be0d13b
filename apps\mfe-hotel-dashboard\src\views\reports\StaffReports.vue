<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Personel Raporları</h1>
        <p class="text-gray-600 dark:text-gray-400">Personel performansı ve iş gücü analizleri</p>
      </div>
      <div class="flex items-center space-x-4">
        <!-- Department Filter -->
        <select 
          v-model="selectedDepartment"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="all">Tüm Departmanlar</option>
          <option value="housekeeping">Kat Hizmetleri</option>
          <option value="maintenance">Bakım Onarım</option>
          <option value="front-desk"><PERSON><PERSON>ü<PERSON></option>
          <option value="restaurant">Restoran</option>
          <option value="security">Güvenlik</option>
        </select>
        <!-- Period Filter -->
        <select 
          v-model="selectedPeriod"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="week">Bu Hafta</option>
          <option value="month">Bu Ay</option>
          <option value="quarter">Bu Çeyrek</option>
          <option value="year">Bu Yıl</option>
        </select>
        <!-- Export Button -->
        <button
          @click="exportReports"
          class="px-6 py-2 bg-brand-500 hover:bg-brand-600 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
        >
          <ArrowDownTrayIcon class="w-5 h-5" />
          <span>Raporu İndir</span>
        </button>
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="metric in keyMetrics"
        :key="metric.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ metric.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ metric.value }}</p>
            <p v-if="metric.change" :class="[
              'text-sm mt-1 flex items-center',
              metric.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
            ]">
              <component :is="metric.change.startsWith('+') ? ArrowTrendingUpIcon : ArrowTrendingDownIcon" class="w-4 h-4 mr-1" />
              {{ metric.change }}
            </p>
          </div>
          <div :class="[
            'p-3 rounded-xl',
            metric.iconBg
          ]">
            <component :is="metric.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Department Performance Chart -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Departman Performansı</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">Ortalama Puan</span>
        </div>
        <div class="space-y-4">
          <div v-for="dept in departmentPerformance" :key="dept.name" class="flex items-center justify-between">
            <div class="flex items-center">
              <div :class="['w-3 h-3 rounded-full mr-3', dept.color]"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ dept.name }}</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div
                  :class="['h-2 rounded-full', dept.color.replace('bg-', 'bg-')]"
                  :style="{ width: (dept.score / 100) * 100 + '%' }"
                ></div>
              </div>
              <span class="text-sm font-semibold text-navy-700 dark:text-white w-12 text-right">{{ dept.score }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Attendance Overview -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Devam Durumu</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">{{ selectedPeriod === 'week' ? 'Bu Hafta' : selectedPeriod === 'month' ? 'Bu Ay' : 'Bu Yıl' }}</span>
        </div>
        <div class="h-48 flex items-center justify-center">
          <!-- Chart Placeholder -->
          <div class="text-center">
            <ChartBarIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">Devam durumu chart burada gösterilecek</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Performers Table -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">En İyi Performans Gösteren Personel</h3>
          <div class="flex items-center space-x-4">
            <!-- Search Input -->
            <div class="relative">
              <MagnifyingGlassIcon class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Personel ara..."
                class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Sıra
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Personel
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Departman
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tamamlanan Görev
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Performans Puanı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Devam Oranı
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Müşteri Puanı
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr v-for="(staff, index) in filteredTopPerformers" :key="staff.id" class="hover:bg-gray-50 dark:hover:bg-navy-600">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div :class="[
                    'w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold text-white',
                    index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : index === 2 ? 'bg-orange-500' : 'bg-gray-300'
                  ]">
                    {{ index + 1 }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gradient-to-r from-brand-400 to-brand-600 flex items-center justify-center text-white font-semibold">
                      {{ staff.name.split(' ').map(n => n[0]).join('') }}
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ staff.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ staff.position }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  getDepartmentBadgeColor(staff.department)
                ]">
                  {{ getDepartmentName(staff.department) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ staff.completedTasks }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="text-sm text-gray-900 dark:text-white mr-2">{{ staff.performanceScore }}%</div>
                  <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-green-500 h-2 rounded-full"
                      :style="{ width: staff.performanceScore + '%' }"
                    ></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="text-sm text-gray-900 dark:text-white mr-2">{{ staff.attendanceRate }}%</div>
                  <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-blue-500 h-2 rounded-full"
                      :style="{ width: staff.attendanceRate + '%' }"
                    ></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <StarIcon class="w-4 h-4 text-yellow-400 mr-1" />
                  <span class="text-sm text-gray-900 dark:text-white">{{ staff.customerRating }}/5</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Department Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Training Completion -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Eğitim Tamamlama Oranları</h3>
        <div class="space-y-4">
          <div v-for="training in trainingCompletion" :key="training.name" class="flex items-center justify-between">
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ training.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ training.participants }} katılımcı</div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div
                  class="bg-purple-500 h-2 rounded-full"
                  :style="{ width: training.completion + '%' }"
                ></div>
              </div>
              <span class="text-sm font-semibold text-navy-700 dark:text-white w-10 text-right">{{ training.completion }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Shift Distribution -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Vardiya Dağılımı</h3>
        <div class="space-y-4">
          <div v-for="shift in shiftDistribution" :key="shift.name" class="flex items-center justify-between">
            <div class="flex items-center">
              <div :class="['w-3 h-3 rounded-full mr-3', shift.color]"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ shift.name }}</span>
            </div>
            <div class="text-right">
              <span class="text-sm font-semibold text-navy-700 dark:text-white">{{ shift.count }}</span>
              <div class="text-xs text-gray-500 dark:text-gray-400">personel</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Overtime Statistics -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Fazla Mesai İstatistikleri</h3>
        <div class="space-y-4">
          <div class="text-center">
            <div class="text-3xl font-bold text-navy-700 dark:text-white">248</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Bu ay toplam fazla mesai saati</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-navy-700 dark:text-white">₺18,650</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Fazla mesai maliyeti</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-navy-700 dark:text-white">32</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Fazla mesai yapan personel</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ArrowDownTrayIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  UsersIcon,
  ClockIcon,
  TrophyIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'

// Reactive refs
const selectedDepartment = ref('all')
const selectedPeriod = ref('month')
const searchQuery = ref('')

// Key Metrics Data
const keyMetrics = computed(() => [
  {
    title: 'Toplam Personel',
    value: '127',
    change: '+3.2%',
    icon: UsersIcon,
    iconBg: 'bg-blue-500'
  },
  {
    title: 'Ortalama Performans',
    value: '87.5%',
    change: '+2.8%',
    icon: TrophyIcon,
    iconBg: 'bg-green-500'
  },
  {
    title: 'Devam Oranı',
    value: '94.1%',
    change: '+1.5%',
    icon: CheckCircleIcon,
    iconBg: 'bg-yellow-500'
  },
  {
    title: 'Ortalama Çalışma Saati',
    value: '8.2h',
    change: '-0.3h',
    icon: ClockIcon,
    iconBg: 'bg-purple-500'
  }
])

// Department Performance Data
const departmentPerformance = ref([
  { name: 'Ön Büro', score: 95, color: 'bg-green-500' },
  { name: 'Kat Hizmetleri', score: 89, color: 'bg-blue-500' },
  { name: 'Restoran', score: 92, color: 'bg-purple-500' },
  { name: 'Bakım Onarım', score: 86, color: 'bg-yellow-500' },
  { name: 'Güvenlik', score: 91, color: 'bg-indigo-500' }
])

// Top Performers Data
const topPerformers = ref([
  {
    id: '1',
    name: 'Ayşe Demir',
    position: 'Kat Sorumlusu',
    department: 'housekeeping',
    completedTasks: 156,
    performanceScore: 98,
    attendanceRate: 100,
    customerRating: 4.9
  },
  {
    id: '2',
    name: 'Mehmet Yılmaz',
    position: 'Resepsiyon Sorumlusu',
    department: 'front-desk',
    completedTasks: 142,
    performanceScore: 96,
    attendanceRate: 98,
    customerRating: 4.8
  },
  {
    id: '3',
    name: 'Fatma Özkan',
    position: 'Servis Sorumlusu',
    department: 'restaurant',
    completedTasks: 134,
    performanceScore: 94,
    attendanceRate: 97,
    customerRating: 4.7
  },
  {
    id: '4',
    name: 'Ali Kaya',
    position: 'Bakım Teknisyeni',
    department: 'maintenance',
    completedTasks: 128,
    performanceScore: 92,
    attendanceRate: 95,
    customerRating: 4.6
  },
  {
    id: '5',
    name: 'Zeynep Arslan',
    position: 'Güvenlik Görevlisi',
    department: 'security',
    completedTasks: 121,
    performanceScore: 90,
    attendanceRate: 99,
    customerRating: 4.5
  }
])

// Training Completion Data
const trainingCompletion = ref([
  { name: 'Müşteri Hizmetleri', completion: 95, participants: 45 },
  { name: 'İş Güvenliği', completion: 88, participants: 67 },
  { name: 'Hijyen Eğitimi', completion: 92, participants: 52 },
  { name: 'Teknoloji Kullanımı', completion: 78, participants: 38 },
  { name: 'İletişim Becerileri', completion: 85, participants: 41 }
])

// Shift Distribution Data
const shiftDistribution = ref([
  { name: 'Sabah Vardiyası (06:00-14:00)', count: 45, color: 'bg-blue-500' },
  { name: 'Öğlen Vardiyası (14:00-22:00)', count: 42, color: 'bg-green-500' },
  { name: 'Gece Vardiyası (22:00-06:00)', count: 25, color: 'bg-purple-500' },
  { name: 'Esnek Vardiya', count: 15, color: 'bg-yellow-500' }
])

// Computed Properties
const filteredTopPerformers = computed(() => {
  let filtered = topPerformers.value

  // Filter by department
  if (selectedDepartment.value !== 'all') {
    filtered = filtered.filter(staff => staff.department === selectedDepartment.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(staff =>
      staff.name.toLowerCase().includes(query) ||
      staff.position.toLowerCase().includes(query)
    )
  }

  return filtered
})

// Methods
const getDepartmentName = (department: string) => {
  const names: Record<string, string> = {
    'housekeeping': 'Kat Hizmetleri',
    'maintenance': 'Bakım Onarım',
    'front-desk': 'Ön Büro',
    'restaurant': 'Restoran',
    'security': 'Güvenlik'
  }
  return names[department] || department
}

const getDepartmentBadgeColor = (department: string) => {
  const colors: Record<string, string> = {
    'housekeeping': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    'maintenance': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    'front-desk': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    'restaurant': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
    'security': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
  }
  return colors[department] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
}

const exportReports = () => {
  alert('Personel raporu indirme özelliği backend entegrasyonu ile aktif hale gelecek.')
}
</script> 