/**
 * Cross-MFE Realtime Service
 * Provides consistent real-time data synchronization across all micro-frontends
 */

import { supabase } from './index'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'

// Event types for cross-MFE communication
export interface CrossMFEEvent {
  type: 'TASK_UPDATE' | 'NOTIFICATION_UPDATE' | 'ORDER_UPDATE' | 'RESERVATION_UPDATE' | 'USER_UPDATE' | 'HOTEL_UPDATE'
  payload: any
  source: 'hotel-dashboard' | 'management-portal' | 'customer-portal' | 'app-shell'
  timestamp: string
}

// Subscription callback type
export type RealtimeCallback = (event: CrossMFEEvent) => void

class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map()
  private callbacks: Map<string, Set<RealtimeCallback>> = new Map()
  private isInitialized = false

  /**
   * Initialize the realtime service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // Set up critical table subscriptions
      await this.setupCriticalSubscriptions()
      this.isInitialized = true
      console.log('RealtimeService initialized successfully')
    } catch (error) {
      console.error('Failed to initialize RealtimeService:', error)
      throw error
    }
  }

  /**
   * Set up subscriptions for critical tables that affect multiple MFEs
   */
  private async setupCriticalSubscriptions(): Promise<void> {
    // Tasks - affects Hotel Dashboard and Management Portal
    this.subscribeToTable('maintenance_tasks', 'TASK_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'TASK_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })

    // Notifications - affects all MFEs
    this.subscribeToTable('staff_notifications', 'NOTIFICATION_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'NOTIFICATION_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })

    this.subscribeToTable('guest_notifications', 'NOTIFICATION_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'NOTIFICATION_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })

    // Orders - affects Hotel Dashboard and Customer Portal
    this.subscribeToTable('orders', 'ORDER_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'ORDER_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })

    // Reservations - affects Hotel Dashboard and Customer Portal
    this.subscribeToTable('reservations', 'RESERVATION_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'RESERVATION_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })

    // User profiles - affects all MFEs
    this.subscribeToTable('user_profiles', 'USER_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'USER_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })

    // Hotels - affects Management Portal and App Shell
    this.subscribeToTable('hotels', 'HOTEL_UPDATE', (payload) => {
      this.broadcastEvent({
        type: 'HOTEL_UPDATE',
        payload: payload,
        source: this.detectSource(),
        timestamp: new Date().toISOString()
      })
    })
  }

  /**
   * Subscribe to a specific table for real-time updates
   */
  private subscribeToTable(
    tableName: string, 
    eventType: CrossMFEEvent['type'],
    callback: (payload: RealtimePostgresChangesPayload<any>) => void
  ): void {
    const channelName = `realtime:${tableName}`
    
    if (this.channels.has(channelName)) {
      console.warn(`Already subscribed to ${tableName}`)
      return
    }

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: tableName
        },
        callback
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Successfully subscribed to ${tableName}`)
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`Failed to subscribe to ${tableName}`)
        }
      })

    this.channels.set(channelName, channel)
  }

  /**
   * Subscribe to cross-MFE events
   */
  subscribe(eventType: CrossMFEEvent['type'], callback: RealtimeCallback): () => void {
    if (!this.callbacks.has(eventType)) {
      this.callbacks.set(eventType, new Set())
    }
    
    this.callbacks.get(eventType)!.add(callback)

    // Return unsubscribe function
    return () => {
      this.callbacks.get(eventType)?.delete(callback)
    }
  }

  /**
   * Broadcast event to all subscribers
   */
  private broadcastEvent(event: CrossMFEEvent): void {
    const subscribers = this.callbacks.get(event.type)
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('Error in realtime callback:', error)
        }
      })
    }
  }

  /**
   * Detect the source MFE based on current URL or context
   */
  private detectSource(): CrossMFEEvent['source'] {
    if (typeof window === 'undefined') return 'app-shell'
    
    const hostname = window.location.hostname
    const port = window.location.port
    
    // Detect based on port (development) or subdomain (production)
    if (port === '5002' || hostname.includes('hotel-dashboard')) {
      return 'hotel-dashboard'
    } else if (port === '5001' || hostname.includes('management')) {
      return 'management-portal'
    } else if (port === '3001' || hostname.includes('customer')) {
      return 'customer-portal'
    } else {
      return 'app-shell'
    }
  }

  /**
   * Manually trigger a cross-MFE event (for testing or manual synchronization)
   */
  triggerEvent(type: CrossMFEEvent['type'], payload: any): void {
    this.broadcastEvent({
      type,
      payload,
      source: this.detectSource(),
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Get subscription status
   */
  getSubscriptionStatus(): Record<string, string> {
    const status: Record<string, string> = {}
    this.channels.forEach((channel, name) => {
      status[name] = channel.state
    })
    return status
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup(): void {
    this.channels.forEach((channel, name) => {
      channel.unsubscribe()
      console.log(`Unsubscribed from ${name}`)
    })
    this.channels.clear()
    this.callbacks.clear()
    this.isInitialized = false
  }

  /**
   * Reconnect all subscriptions (useful for handling connection issues)
   */
  async reconnect(): Promise<void> {
    this.cleanup()
    await this.initialize()
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService()
