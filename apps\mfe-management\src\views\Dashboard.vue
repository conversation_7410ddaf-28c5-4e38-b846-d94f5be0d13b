<template>
  <ErrorBoundary
    fallback-message="Dashboard yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin."
    @retry="handleRetry"
  >
    <!-- Loading State -->
    <LoadingState
      v-if="isLoading"
      variant="dashboard"
      :show="true"
      message="Dashboard verileri yükleniyor..."
    />

    <!-- Error State -->
    <div v-else-if="hasError" class="text-center py-12">
      <ExclamationTriangleIcon class="w-16 h-16 text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Veri Yükleme Hatası
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        Dashboard verileri yüklenirken bir hata oluştu.
      </p>
      <button
        @click="handleRetry"
        class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors"
      >
        Tekrar Dene
      </button>
    </div>

    <!-- Main Content -->
    <div v-else class="space-y-6">
      <!-- Page Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Platform Dashboard</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">Hotelexia platform genel durumu ve istatistikleri</p>
        </div>
        <div class="flex items-center space-x-3">
          <router-link
            to="/hotels/new"
            class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors inline-block"
          >
            Yeni Otel Ekle
          </router-link>
        </div>
      </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <MiniStatistics
        name="Toplam Otel Sayısı"
        :value="store.platformStats.totalHotels.toString()"
        growth="+8%"
        :start-content="BuildingOfficeIcon"
      />
      <MiniStatistics
        name="Aktif Abonelikler"
        :value="store.platformStats.activeSubscriptions.toString()"
        growth="+12%"
        :start-content="CheckCircleIcon"
      />
      <MiniStatistics
        name="Bekleyen Talepler"
        :value="store.platformStats.pendingRequests.toString()"
        :start-content="ClockIcon"
      />
      <MiniStatistics
        name="Sistem Sağlığı"
        :value="store.platformStats.systemHealth"
        growth="100%"
        :start-content="ShieldCheckIcon"
      />
    </div>

    <!-- Charts and Tables Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Revenue Chart -->
      <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">Platform Geliri</h3>
          <select class="text-sm border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-1 bg-white dark:bg-navy-700 text-gray-700 dark:text-gray-300">
            <option>Son 7 Gün</option>
            <option>Son 30 Gün</option>
            <option>Son 3 Ay</option>
          </select>
        </div>
        <!-- Chart Placeholder -->
        <div class="h-64 bg-gray-50 dark:bg-navy-700 rounded-lg flex items-center justify-center">
          <div class="text-center">
            <ChartBarIcon class="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 dark:text-gray-400">Gelir grafiği yakında eklenecek</p>
          </div>
        </div>
      </div>

      <!-- Recent Activities -->
      <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">Son Aktiviteler</h3>
          <button class="text-brand-500 text-sm font-medium hover:text-brand-600">Tümünü Gör</button>
        </div>
        <div class="space-y-4">
          <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="activity.bgColor">
              <component :is="activity.icon" class="h-4 w-4" :class="activity.iconColor" />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-navy-700 dark:text-white">{{ activity.title }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Hotels Overview Table -->
    <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-bold text-navy-700 dark:text-white">Son Eklenen Oteller</h3>
        <router-link 
          to="/hotels"
          class="text-brand-500 text-sm font-medium hover:text-brand-600"
        >
          Tüm Otelleri Gör
        </router-link>
      </div>
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">Otel Adı</th>
              <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">Statü</th>
              <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">Plan</th>
              <th class="text-left py-3 px-4 text-sm font-semibold text-gray-600 dark:text-gray-400">Kayıt Tarihi</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="hotel in recentHotels" :key="hotel.id" class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-navy-700">
              <td class="py-3 px-4">
                <div class="font-medium text-navy-700 dark:text-white">{{ hotel.name }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ hotel.contactPerson }}</div>
              </td>
              <td class="py-3 px-4">
                <span 
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="hotel.status === 'Aktif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ hotel.status }}
                </span>
              </td>
              <td class="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">{{ hotel.plan }}</td>
              <td class="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">{{ formatDate(hotel.registrationDate) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import {
  BuildingOfficeIcon,
  CheckCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  PlusIcon,
  UserPlusIcon,
  CogIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import MiniStatistics from '@/components/cards/MiniStatistics.vue'
import { ErrorBoundary, LoadingState, useManagementPortalRefresh } from '@hotelexia/shared-components'
import { useManagementStore } from '@/stores/managementStore'

const store = useManagementStore()

// Computed states for loading and error handling
const isLoading = computed(() => {
  return store.platformStatsLoading || store.hotelsLoading
})

const hasError = computed(() => {
  return !!(store.platformStatsError || store.hotelsError)
})

// Retry function
const handleRetry = async () => {
  try {
    await store.initializeData()
  } catch (error) {
    console.error('Failed to retry dashboard data loading:', error)
  }
}

// Set up cross-MFE refresh listening
const refreshListener = useManagementPortalRefresh(
  ['hotels', 'users', 'stats'],
  (event) => {
    console.log('Management Portal refresh triggered:', event.component)
    // Refresh management data when cross-MFE events occur
    store.initializeData()
  }
)

// Initialize data on mount
onMounted(() => {
  if (!store.platformStats.totalHotels && !isLoading.value) {
    store.initializeData()
  }
})

// Recent activities dummy data
const recentActivities = [
  {
    id: 1,
    title: 'Yeni otel kaydı: Ege Palas İzmir',
    time: '2 saat önce',
    icon: PlusIcon,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    id: 2,
    title: 'Sistem güncellemesi tamamlandı',
    time: '4 saat önce',
    icon: CogIcon,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    id: 3,
    title: 'Yeni kullanıcı kaydı',
    time: '6 saat önce',
    icon: UserPlusIcon,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  {
    id: 4,
    title: 'Güvenlik uyarısı çözüldü',
    time: '1 gün önce',
    icon: ExclamationTriangleIcon,
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600'
  }
]

// Recent hotels (last 5)
const recentHotels = computed(() => {
  return store.hotels.slice(-5).reverse()
})

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script> 