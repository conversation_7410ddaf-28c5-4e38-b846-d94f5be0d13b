// Image utility functions for Customer Portal MFE
// Replaces external images with local placeholders to avoid CORS issues

export interface ImagePlaceholder {
  url: string
  alt: string
  category?: string
}

// Local placeholder images (using data URLs or local assets)
const PLACEHOLDER_IMAGES = {
  // Food categories
  food: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MS4wNDU3IDE3MCA4MEM1NyA2OC45NTQzIDE2MS4wNDYgNjAgMTUwIDYwQzEzOC45NTQgNjAgMTMwIDY4Ljk1NDMgMTMwIDgwQzEzMCA5MS4wNDU3IDEzOC45NTQgMTAwIDE1MCAxMDBaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNTAgMjQwQzE4My4xMzcgMjQwIDIxMCAyMTMuMTM3IDIxMCAxODBDMjEwIDE0Ni44NjMgMTgzLjEzNyAxMjAgMTUwIDEyMEM5Ni44NjI5IDEyMCA5MCA5Ni44NjI5IDkwIDEzMEM5MCAyMTMuMTM3IDExNi44NjMgMjQwIDE1MCAyNDBaIiBmaWxsPSIjOUNBM0FGIi8+CjwvZz4KPC9zdmc+',
  
  // Service categories
  service: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRUZGNkZGIi8+CjxwYXRoIGQ9Ik0xNTAgNzBDMTcyLjA5MSA3MCAyMDAgOTcuOTA5IDIwMCAxMjBDMjAwIDE0Mi4wOTEgMTcyLjA5MSAxNzAgMTUwIDE3MEM5Ny45MDg2IDE3MCA5MCA5Ny45MDg2IDkwIDEyMEM5MCA5Ny45MDg2IDEyNy45MDkgNzAgMTUwIDcwWiIgZmlsbD0iIzM5OEVGNyIvPgo8L3N2Zz4=',
  
  // Hotel/room images
  hotel: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI1MCIgdmlld0JveD0iMCAwIDQwMCAyNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjUwIiBmaWxsPSIjRjlGQUZCIi8+CjxyZWN0IHg9IjUwIiB5PSI1MCIgd2lkdGg9IjMwMCIgaGVpZ2h0PSIxNTAiIGZpbGw9IiNFNUU3RUIiLz4KPHJlY3QgeD0iMTAwIiB5PSIxMDAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgZmlsbD0iIzlDQTNBRiIvPgo8cmVjdCB4PSIyNTAiIHk9IjEwMCIgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjOUNBM0FGIi8+CjwvZz4KPC9zdmc+',
  
  // Default placeholder
  default: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MS4wNDU3IDE3MCA4MEM1NyA2OC45NTQzIDE2MS4wNDYgNjAgMTUwIDYwQzEzOC45NTQgNjAgMTMwIDY4Ljk1NDMgMTMwIDgwQzEzMCA5MS4wNDU3IDEzOC45NTQgMTAwIDE1MCAxMDBaIiBmaWxsPSIjOUNBM0FGIi8+CjwvZz4KPC9zdmc+'
}

// Category-specific placeholder mappings
const CATEGORY_PLACEHOLDERS: Record<string, keyof typeof PLACEHOLDER_IMAGES> = {
  // Food categories
  'salatalar': 'food',
  'et yemekleri': 'food',
  'makarna': 'food',
  'çorbalar': 'food',
  'sıcak yemekler': 'food',
  'balık yemekleri': 'food',
  'tatlılar': 'food',
  'içecekler': 'food',
  'ana yemekler': 'food',
  'appetizer': 'food',
  'main': 'food',
  'dessert': 'food',
  'beverage': 'food',
  
  // Service categories
  'housekeeping': 'service',
  'maintenance': 'service',
  'amenities': 'service',
  'other': 'service',
  
  // Hotel/room
  'hotel': 'hotel',
  'room': 'hotel'
}

/**
 * Get a safe placeholder image URL for a given category
 */
export function getPlaceholderImage(category?: string): string {
  if (!category) return PLACEHOLDER_IMAGES.default
  
  const normalizedCategory = category.toLowerCase()
  const placeholderKey = CATEGORY_PLACEHOLDERS[normalizedCategory] || 'default'
  
  return PLACEHOLDER_IMAGES[placeholderKey]
}

/**
 * Get a safe image URL, falling back to placeholder if external URL
 */
export function getSafeImageUrl(imageUrl?: string, category?: string): string {
  // If no image URL provided, return placeholder
  if (!imageUrl) {
    return getPlaceholderImage(category)
  }
  
  // If it's already a data URL or local asset, return as-is
  if (imageUrl.startsWith('data:') || imageUrl.startsWith('/') || imageUrl.startsWith('./')) {
    return imageUrl
  }
  
  // If it's an external URL (Unsplash, Picsum, etc.), replace with placeholder
  if (imageUrl.includes('unsplash.com') || 
      imageUrl.includes('picsum.photos') || 
      imageUrl.includes('via.placeholder.com') ||
      imageUrl.startsWith('http')) {
    return getPlaceholderImage(category)
  }
  
  // For any other case, return the original URL
  return imageUrl
}

/**
 * Generate a color-based placeholder for text initials
 */
export function getInitialPlaceholder(text: string, size: number = 32): string {
  const initial = text.charAt(0).toUpperCase()
  const colors = [
    '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
    '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
    '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
    '#EC4899', '#F43F5E'
  ]
  
  const colorIndex = text.charCodeAt(0) % colors.length
  const color = colors[colorIndex]
  
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" fill="${color}"/>
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.4}" font-weight="bold">${initial}</text>
    </svg>
  `)}`
}

/**
 * Handle image loading errors by replacing with placeholder
 */
export function handleImageError(event: Event, category?: string): void {
  const img = event.target as HTMLImageElement
  if (img) {
    img.src = getPlaceholderImage(category)
  }
}

/**
 * Create an image element with safe loading
 */
export function createSafeImage(src: string, alt: string, category?: string): HTMLImageElement {
  const img = new Image()
  img.src = getSafeImageUrl(src, category)
  img.alt = alt
  img.onerror = (e) => handleImageError(e as Event, category)
  return img
}

// Export placeholder images for direct use
export { PLACEHOLDER_IMAGES }
