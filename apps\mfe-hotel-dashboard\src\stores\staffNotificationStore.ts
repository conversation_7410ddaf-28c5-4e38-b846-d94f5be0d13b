import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'
import type { RealtimeChannel } from '@supabase/supabase-js'

export interface StaffNotification {
  id: string
  hotel_id: string
  recipient_id: string | null
  title: string
  message: string
  notification_type: string | null
  priority: 'low' | 'medium' | 'high' | 'urgent' | null
  category: string | null
  related_task_id: string | null
  related_order_id: string | null
  related_request_id: string | null
  is_read: boolean
  read_at: string | null
  is_urgent: boolean
  requires_action: boolean
  action_taken: boolean
  action_taken_at: string | null
  metadata: any
  sent_by: string | null
  created_at: string
  updated_at: string | null
}

export const useStaffNotificationStore = defineStore('staffNotification', () => {
  // State
  const notifications = ref<StaffNotification[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isConnected = ref(false)
  
  // Realtime channel
  let notificationsChannel: RealtimeChannel | null = null
  
  // Get auth store for user context
  const authStore = useAuthStore()
  
  // Computed
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.is_read)
  )
  
  const unreadCount = computed(() => unreadNotifications.value.length)
  
  const urgentNotifications = computed(() => 
    notifications.value.filter(n => n.is_urgent && !n.is_read)
  )
  
  const actionRequiredNotifications = computed(() => 
    notifications.value.filter(n => n.requires_action && !n.action_taken)
  )
  
  const notificationsByCategory = computed(() => {
    const categories = ['maintenance', 'housekeeping', 'service', 'order', 'system']
    return categories.map(category => ({
      category,
      notifications: notifications.value.filter(n => n.category === category),
      unreadCount: notifications.value.filter(n => n.category === category && !n.is_read).length
    }))
  })
  
  // Actions
  const fetchNotifications = async () => {
    if (!authStore.userHotelId) {
      console.warn('Cannot fetch notifications: no hotel ID')
      return
    }
    
    isLoading.value = true
    error.value = null
    
    try {
      const { data, error: fetchError } = await supabase
        .from('staff_notifications' as any)
        .select('*')
        .eq('hotel_id', authStore.userHotelId)
        .or(`recipient_id.is.null,recipient_id.eq.${authStore.user?.id}`)
        .order('created_at', { ascending: false })
        .limit(100)
      
      if (fetchError) {
        throw fetchError
      }
      
      notifications.value = (data as any as StaffNotification[]) || []
    } catch (err: any) {
      console.error('Error fetching staff notifications:', err)
      error.value = err.message || 'Failed to fetch notifications'
    } finally {
      isLoading.value = false
    }
  }
  
  const markAsRead = async (notificationId: string) => {
    try {
      const { error: updateError } = await supabase
        .from('staff_notifications' as any)
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId)
      
      if (updateError) {
        throw updateError
      }
      
      // Update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.is_read = true
        notification.read_at = new Date().toISOString()
      }
    } catch (err: any) {
      console.error('Error marking notification as read:', err)
      error.value = err.message || 'Failed to mark notification as read'
    }
  }
  
  const markAllAsRead = async () => {
    if (!authStore.userHotelId || !authStore.user?.id) return
    
    try {
      const { error: updateError } = await supabase
        .from('staff_notifications' as any)
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('hotel_id', authStore.userHotelId)
        .or(`recipient_id.is.null,recipient_id.eq.${authStore.user.id}`)
        .eq('is_read', false)
      
      if (updateError) {
        throw updateError
      }
      
      // Update local state
      const now = new Date().toISOString()
      notifications.value.forEach(notification => {
        if (!notification.is_read) {
          notification.is_read = true
          notification.read_at = now
        }
      })
    } catch (err: any) {
      console.error('Error marking all notifications as read:', err)
      error.value = err.message || 'Failed to mark all notifications as read'
    }
  }
  
  const markActionTaken = async (notificationId: string) => {
    try {
      const { error: updateError } = await supabase
        .from('staff_notifications' as any)
        .update({
          action_taken: true,
          action_taken_at: new Date().toISOString()
        })
        .eq('id', notificationId)
      
      if (updateError) {
        throw updateError
      }
      
      // Update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.action_taken = true
        notification.action_taken_at = new Date().toISOString()
      }
    } catch (err: any) {
      console.error('Error marking action as taken:', err)
      error.value = err.message || 'Failed to mark action as taken'
    }
  }
  
  const createNotification = async (notificationData: Partial<StaffNotification>) => {
    if (!authStore.userHotelId) {
      throw new Error('Hotel ID is required')
    }
    
    try {
      const { data, error: insertError } = await supabase
        .from('staff_notifications' as any)
        .insert({
          hotel_id: authStore.userHotelId,
          sent_by: authStore.user?.id,
          ...notificationData
        })
        .select()
        .single()
      
      if (insertError) {
        throw insertError
      }
      
      return data
    } catch (err: any) {
      console.error('Error creating notification:', err)
      error.value = err.message || 'Failed to create notification'
      throw err
    }
  }
  
  // Real-time subscriptions
  const setupRealtimeSubscription = () => {
    if (!authStore.userHotelId) {
      console.warn('Cannot setup staff notifications subscription: no hotel ID')
      return
    }
    
    try {
      notificationsChannel = supabase
        .channel('staff_notifications_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'staff_notifications',
            filter: `hotel_id=eq.${authStore.userHotelId}`
          },
          (payload) => {
            console.log('Staff notification change:', payload)
            handleNotificationChange(payload)
          }
        )
        .subscribe((status) => {
          console.log('Staff notifications subscription status:', status)
          if (status === 'SUBSCRIBED') {
            isConnected.value = true
          }
        })
    } catch (err) {
      console.error('Error setting up staff notifications subscription:', err)
      error.value = 'Failed to setup staff notifications subscription'
    }
  }
  
  const handleNotificationChange = (payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload
    
    // Filter by user - only process notifications for current user or broadcast notifications
    const userId = authStore.user?.id
    const isForUser = !newRecord?.recipient_id || newRecord?.recipient_id === userId
    const isForUserOld = !oldRecord?.recipient_id || oldRecord?.recipient_id === userId
    
    switch (eventType) {
      case 'INSERT':
        if (isForUser && newRecord) {
          notifications.value.unshift(newRecord)
        }
        break
      case 'UPDATE':
        if (isForUser && newRecord) {
          const index = notifications.value.findIndex(n => n.id === newRecord.id)
          if (index !== -1) {
            notifications.value[index] = newRecord
          }
        }
        break
      case 'DELETE':
        if (isForUserOld && oldRecord) {
          const index = notifications.value.findIndex(n => n.id === oldRecord.id)
          if (index !== -1) {
            notifications.value.splice(index, 1)
          }
        }
        break
    }
  }
  
  const cleanup = () => {
    if (notificationsChannel) {
      try {
        supabase.removeChannel(notificationsChannel)
        console.log('Staff notifications channel removed')
      } catch (err) {
        console.error('Error removing staff notifications channel:', err)
      }
      notificationsChannel = null
    }
    
    isConnected.value = false
    error.value = null
  }
  
  const initializeSubscriptions = () => {
    cleanup()
    setupRealtimeSubscription()
  }
  
  return {
    // State
    notifications,
    isLoading,
    error,
    isConnected,
    
    // Computed
    unreadNotifications,
    unreadCount,
    urgentNotifications,
    actionRequiredNotifications,
    notificationsByCategory,
    
    // Actions
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    markActionTaken,
    createNotification,
    initializeSubscriptions,
    cleanup
  }
})
