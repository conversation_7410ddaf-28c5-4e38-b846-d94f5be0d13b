import { ref } from 'vue'

// Mock types and services for build compatibility
enum CrossMFEEventType {
  DATA_CONSISTENCY_UPDATE = 'DATA_CONSISTENCY_UPDATE',
  DATA_STALE = 'DATA_STALE',
  SYSTEM_ERROR = 'SYSTEM_ERROR'
}

interface CrossMFEEvent {
  type: CrossMFEEventType
  payload: any
  source: string
  timestamp: number
}

const crossMFERealtimeService = {
  subscribe: (type: CrossMFEEventType, callback: (event: CrossMFEEvent) => void) => () => {},
  emit: (type: CrossMFEEventType, data: any) => {},
  updateDataConsistency: (key: string, data: any, version?: number) => {},
  validateDataConsistency: (key: string, data: any, version: number) => true,
  getConsistentData: (key: string) => ({ data: null, version: 0, timestamp: Date.now() })
}

export interface DataConsistencyOptions {
  /**
   * Unique key for the data being tracked
   */
  dataKey: string
  
  /**
   * Function to fetch fresh data when inconsistency is detected
   */
  refreshData: () => Promise<any>
  
  /**
   * Function to validate if local data is consistent
   */
  validateData?: (localData: any, remoteData: any) => boolean
  
  /**
   * Auto-refresh interval in milliseconds (default: 5 minutes)
   */
  autoRefreshInterval?: number
  
  /**
   * Enable automatic conflict resolution
   */
  autoResolveConflicts?: boolean
}

export function useCrossMFEDataConsistency(options: DataConsistencyOptions) {
  const {
    dataKey,
    refreshData,
    validateData,
    autoRefreshInterval = 5 * 60 * 1000, // 5 minutes
    autoResolveConflicts = true
  } = options

  // State
  const isConsistent = ref(true)
  const lastSyncTime = ref<number | null>(null)
  const conflictCount = ref(0)
  const isRefreshing = ref(false)
  const error = ref<string | null>(null)

  // Internal state
  let autoRefreshTimer: NodeJS.Timeout | null = null
  let unsubscribeFromEvents: (() => void) | null = null

  /**
   * Update local data and broadcast consistency update
   */
  const updateData = async (data: any, version?: number) => {
    try {
      // Update consistency cache
      crossMFERealtimeService.updateDataConsistency(dataKey, data, version)
      
      lastSyncTime.value = Date.now()
      isConsistent.value = true
      error.value = null
      
      console.log(`Data consistency updated for ${dataKey}`)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update data consistency'
      console.error(`Failed to update data consistency for ${dataKey}:`, err)
    }
  }

  /**
   * Validate data consistency
   */
  const validateConsistency = async (data: any, version: number): Promise<boolean> => {
    try {
      const isValid = crossMFERealtimeService.validateDataConsistency(dataKey, data, version)
      
      if (!isValid) {
        isConsistent.value = false
        conflictCount.value++
        
        if (autoResolveConflicts) {
          console.log(`Auto-resolving data conflict for ${dataKey}`)
          await handleDataRefresh()
        }
      }
      
      return isValid
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to validate data consistency'
      console.error(`Failed to validate data consistency for ${dataKey}:`, err)
      return false
    }
  }

  /**
   * Get consistent data from cache
   */
  const getConsistentData = () => {
    return crossMFERealtimeService.getConsistentData(dataKey)
  }

  /**
   * Handle data refresh
   */
  const handleDataRefresh = async () => {
    if (isRefreshing.value) {
      return
    }

    isRefreshing.value = true
    error.value = null

    try {
      console.log(`Refreshing data for ${dataKey}`)
      const freshData = await refreshData()
      
      // Update consistency cache with fresh data
      await updateData(freshData)
      
      console.log(`Data refreshed successfully for ${dataKey}`)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to refresh data'
      console.error(`Failed to refresh data for ${dataKey}:`, err)
    } finally {
      isRefreshing.value = false
    }
  }

  /**
   * Handle cross-MFE events
   */
  const handleCrossMFEEvent = (event: CrossMFEEvent) => {
    switch (event.type) {
      case CrossMFEEventType.DATA_CONSISTENCY_UPDATE:
        if (event.payload.key === dataKey) {
          console.log(`Data consistency update received for ${dataKey}`)
          
          // Check if we need to validate our local data
          const cachedData = getConsistentData()
          if (cachedData && validateData) {
            const isValid = validateData(cachedData.data, event.payload.data)
            if (!isValid) {
              isConsistent.value = false
              conflictCount.value++
              
              if (autoResolveConflicts) {
                handleDataRefresh()
              }
            }
          }
        }
        break

      case CrossMFEEventType.DATA_STALE:
        if (event.payload.key === dataKey) {
          console.log(`Stale data detected for ${dataKey}`)
          isConsistent.value = false
          
          if (autoResolveConflicts) {
            handleDataRefresh()
          }
        }
        break

      case CrossMFEEventType.SYSTEM_ERROR:
        if (event.payload.eventType?.includes(dataKey)) {
          console.warn(`System error related to ${dataKey}:`, event.payload.error)
          error.value = event.payload.error
        }
        break
    }
  }

  /**
   * Start consistency monitoring
   */
  const startMonitoring = () => {
    // Subscribe to cross-MFE events
    unsubscribeFromEvents = crossMFERealtimeService.subscribe(
      CrossMFEEventType.DATA_CONSISTENCY_UPDATE,
      handleCrossMFEEvent
    )

    // Also subscribe to stale data events
    const unsubscribeStale = crossMFERealtimeService.subscribe(
      CrossMFEEventType.DATA_STALE,
      handleCrossMFEEvent
    )

    // And system error events
    const unsubscribeError = crossMFERealtimeService.subscribe(
      CrossMFEEventType.SYSTEM_ERROR,
      handleCrossMFEEvent
    )

    // Combine unsubscribe functions
    const originalUnsubscribe = unsubscribeFromEvents
    unsubscribeFromEvents = () => {
      originalUnsubscribe()
      unsubscribeStale()
      unsubscribeError()
    }

    // Start auto-refresh timer
    if (autoRefreshInterval > 0) {
      autoRefreshTimer = setInterval(() => {
        const cached = getConsistentData()
        if (cached) {
          const age = Date.now() - cached.timestamp
          if (age > autoRefreshInterval) {
            console.log(`Auto-refreshing stale data for ${dataKey} (age: ${age}ms)`)
            handleDataRefresh()
          }
        }
      }, autoRefreshInterval)
    }

    console.log(`Started consistency monitoring for ${dataKey}`)
  }

  /**
   * Stop consistency monitoring
   */
  const stopMonitoring = () => {
    if (unsubscribeFromEvents) {
      unsubscribeFromEvents()
      unsubscribeFromEvents = null
    }

    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
      autoRefreshTimer = null
    }

    console.log(`Stopped consistency monitoring for ${dataKey}`)
  }

  /**
   * Force refresh data
   */
  const forceRefresh = () => {
    return handleDataRefresh()
  }

  /**
   * Reset consistency state
   */
  const resetConsistency = () => {
    isConsistent.value = true
    conflictCount.value = 0
    error.value = null
    lastSyncTime.value = null
  }

  /**
   * Get consistency status
   */
  const getStatus = () => {
    const cached = getConsistentData()
    return {
      isConsistent: isConsistent.value,
      lastSyncTime: lastSyncTime.value,
      conflictCount: conflictCount.value,
      isRefreshing: isRefreshing.value,
      error: error.value,
      cachedData: cached,
      dataAge: cached ? Date.now() - cached.timestamp : null
    }
  }

  // Lifecycle yönetimi build için devre dışı - component'lar startMonitoring() çağırmalı

  return {
    // State
    isConsistent,
    lastSyncTime,
    conflictCount,
    isRefreshing,
    error,

    // Methods
    updateData,
    validateConsistency,
    getConsistentData,
    forceRefresh,
    resetConsistency,
    getStatus,
    startMonitoring,
    stopMonitoring
  }
}

/**
 * Specialized hook for hotel data consistency
 */
export function useHotelDataConsistency(refreshHotels: () => Promise<any>) {
  return useCrossMFEDataConsistency({
    dataKey: 'hotels',
    refreshData: refreshHotels,
    validateData: (local, remote) => {
      // Simple validation - check if hotel counts match
      return Array.isArray(local) && Array.isArray(remote) && local.length === remote.length
    },
    autoRefreshInterval: 3 * 60 * 1000, // 3 minutes for hotels
    autoResolveConflicts: true
  })
}

/**
 * Specialized hook for room data consistency
 */
export function useRoomDataConsistency(refreshRooms: () => Promise<any>) {
  return useCrossMFEDataConsistency({
    dataKey: 'rooms',
    refreshData: refreshRooms,
    validateData: (local, remote) => {
      // Validate room status consistency
      if (!Array.isArray(local) || !Array.isArray(remote)) return false
      
      // Check if any room statuses have changed
      const localStatusMap = new Map(local.map(room => [room.id, room.status]))
      const remoteStatusMap = new Map(remote.map(room => [room.id, room.status]))
      
      for (const [id, status] of localStatusMap) {
        if (remoteStatusMap.get(id) !== status) {
          return false
        }
      }
      
      return true
    },
    autoRefreshInterval: 1 * 60 * 1000, // 1 minute for rooms (more frequent)
    autoResolveConflicts: true
  })
}
