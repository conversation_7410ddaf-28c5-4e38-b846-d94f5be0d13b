<template>
  <div class="relative">
    <button
      @click="toggleDropdown"
      data-testid="notification-bell"
      class="relative p-2 text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 rounded-full"
    >
      <span class="sr-only">View notifications</span>
      <BellIcon class="h-6 w-6" aria-hidden="true" />
      
      <!-- Notification badge -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs font-medium rounded-full flex items-center justify-center"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
      
      <!-- Connection status indicator -->
      <span
        :class="[
          'absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white dark:border-navy-800',
          isConnected ? 'bg-green-400' : 'bg-red-400'
        ]"
        :title="isConnected ? 'Gerçek zamanlı bildirimler aktif' : 'Gerçek zamanlı bağlantı kesildi'"
      ></span>
    </button>

    <!-- Dropdown -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <div
        v-if="showDropdown"
        class="absolute right-0 mt-2 w-80 bg-white dark:bg-navy-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
      >
        <div class="py-1">
          <!-- Header -->
          <div class="px-4 py-3 border-b border-gray-200 dark:border-navy-600">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                  Bildirimler
                </h3>
                <div class="flex items-center space-x-1">
                  <div
                    :class="[
                      'h-2 w-2 rounded-full',
                      isConnected ? 'bg-green-400' : 'bg-red-400'
                    ]"
                  ></div>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ isConnected ? 'Canlı' : 'Bağlantı Yok' }}
                  </span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  v-if="unreadCount > 0"
                  @click="markAllAsRead"
                  class="text-xs text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  Tümünü okundu işaretle
                </button>
                <button
                  @click="testNotifications"
                  class="text-xs text-green-600 hover:text-green-500 dark:text-green-400"
                  title="Test bildirimlerini gönder"
                >
                  Test
                </button>
              </div>
            </div>
          </div>

          <!-- Notifications list -->
          <div class="max-h-96 overflow-y-auto">
            <div
              v-if="notifications.length === 0"
              class="px-4 py-8 text-center text-gray-500 dark:text-gray-400"
            >
              <BellIcon class="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
              <p class="mt-2 text-sm">Henüz bildirim yok</p>
            </div>
            
            <div
              v-for="notification in notifications"
              :key="notification.id"
              @click="handleNotificationClick(notification)"
              :class="[
                'px-4 py-3 hover:bg-gray-50 dark:hover:bg-navy-700 cursor-pointer border-b border-gray-100 dark:border-navy-600 last:border-b-0',
                !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              ]"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <component
                    :is="getNotificationIcon(notification.type)"
                    :class="getNotificationIconClass(notification.type)"
                    class="h-5 w-5"
                  />
                </div>
                <div class="ml-3 flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {{ notification.title }}
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-300 line-clamp-2">
                    {{ notification.message }}
                  </p>
                  <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    {{ formatTime(notification.timestamp) }}
                  </p>
                </div>
                <div v-if="!notification.read" class="flex-shrink-0 ml-2">
                  <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="px-4 py-3 border-t border-gray-200 dark:border-navy-600">
            <button
              @click="clearAllNotifications"
              class="w-full text-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Tüm bildirimleri temizle
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRealtimeStore } from '@/stores/realtimeStore'
import { useStaffNotificationStore } from '@/stores/staffNotificationStore'
import {
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline'

const realtimeStore = useRealtimeStore()
const staffNotificationStore = useStaffNotificationStore()
const showDropdown = ref(false)

const notifications = computed(() => realtimeStore.recentNotifications)
const unreadCount = computed(() =>
  realtimeStore.unreadCount + staffNotificationStore.unreadCount
)
const isConnected = computed(() => realtimeStore.isConnected)

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const markAllAsRead = () => {
  realtimeStore.markAllAsRead()
}

const clearAllNotifications = () => {
  realtimeStore.clearAllNotifications()
  showDropdown.value = false
}

const testNotifications = () => {
  realtimeStore.testNotificationSystem()
}

const handleNotificationClick = (notification: any) => {
  realtimeStore.markAsRead(notification.id)
  
  // Handle navigation based on notification data
  if (notification.data?.type === 'maintenance_task' && notification.data?.taskId) {
    console.log('Navigate to maintenance task:', notification.data.taskId)
  } else if (notification.data?.type === 'service_request' && notification.data?.requestId) {
    console.log('Navigate to service request:', notification.data.requestId)
  }
  
  showDropdown.value = false
}

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'success':
      return CheckCircleIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'error':
      return XCircleIcon
    case 'info':
    default:
      return InformationCircleIcon
  }
}

const getNotificationIconClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-green-400'
    case 'warning':
      return 'text-yellow-400'
    case 'error':
      return 'text-red-400'
    case 'info':
    default:
      return 'text-blue-400'
  }
}

const formatTime = (timestamp: Date): string => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  
  if (diff < 60000) {
    return 'Şimdi'
  } else if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes}d`
  } else if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}s`
  } else {
    return timestamp.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short'
    })
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    showDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
