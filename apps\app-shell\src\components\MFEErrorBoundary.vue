<template>
  <div v-if="hasError" class="mfe-error-boundary">
    <div class="error-container">
      <div class="error-icon">
        <ExclamationTriangleIcon class="w-16 h-16 text-red-500" />
      </div>
      
      <div class="error-content">
        <h2 class="error-title">Modül Yükleme Hatası</h2>
        <p class="error-message">
          {{ mfeName }} modülü yüklenirken bir hata oluştu.
        </p>
        
        <div class="error-actions">
          <button 
            @click="retry" 
            class="btn-primary"
            :disabled="isRetrying"
          >
            <ArrowPathIcon v-if="isRetrying" class="w-4 h-4 animate-spin mr-2" />
            {{ isRetrying ? 'Yeniden Deneniyor...' : 'Tekrar Dene' }}
          </button>
          
          <button 
            @click="goHome" 
            class="btn-secondary"
          >
            Ana Sayfaya Dön
          </button>
        </div>
        
        <!-- Development error details -->
        <details v-if="isDevelopment && errorDetails" class="error-details">
          <summary class="error-details-summary">
            Teknik Detaylar
          </summary>
          <div class="error-stack">
            <p><strong>MFE:</strong> {{ mfeName }}</p>
            <p><strong>Component:</strong> {{ componentName }}</p>
            <p><strong>Time:</strong> {{ new Date(errorTime).toLocaleString() }}</p>
            <pre>{{ errorDetails }}</pre>
          </div>
        </details>
      </div>
    </div>
  </div>
  
  <Suspense v-else>
    <template #default>
      <component 
        :is="mfeComponent" 
        v-bind="componentProps"
        @error="handleComponentError"
      />
    </template>
    
    <template #fallback>
      <div class="loading-container">
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
        <p class="loading-message">{{ mfeName }} modülü yükleniyor...</p>
      </div>
    </template>
  </Suspense>
</template>

<script setup lang="ts">
import { ref, computed, onErrorCaptured, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/vue/24/outline'
import { globalErrorHandler } from '../utils/errorHandling'

interface Props {
  mfeName: string
  componentName: string
  componentLoader: () => Promise<any>
  componentProps?: Record<string, any>
  maxRetries?: number
}

const props = withDefaults(defineProps<Props>(), {
  componentProps: () => ({}),
  maxRetries: 3
})

const emit = defineEmits<{
  error: [error: Error]
  retry: []
  recovered: []
}>()

const router = useRouter()

// State
const hasError = ref(false)
const errorDetails = ref('')
const errorTime = ref(0)
const isRetrying = ref(false)
const retryCount = ref(0)

// Computed
const isDevelopment = computed(() => import.meta.env.DEV)

// Create the MFE component with error handling
const mfeComponent = defineAsyncComponent({
  loader: async () => {
    try {
      const component = await props.componentLoader()
      
      // Reset error state on successful load
      if (hasError.value) {
        hasError.value = false
        errorDetails.value = ''
        retryCount.value = 0
        emit('recovered')
      }
      
      return component
    } catch (error) {
      console.error(`Failed to load MFE component ${props.mfeName}/${props.componentName}:`, error)
      
      // Handle the error
      handleLoadError(error instanceof Error ? error : new Error(String(error)))
      
      // Return a fallback component
      return {
        template: '<div></div>'
      }
    }
  },
  errorComponent: {
    template: '<div></div>'
  },
  loadingComponent: {
    template: `
      <div class="loading-container">
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
        <p class="loading-message">{{ mfeName }} modülü yükleniyor...</p>
      </div>
    `
  },
  delay: 200,
  timeout: 10000
})

// Error handlers
const handleLoadError = (error: Error) => {
  hasError.value = true
  errorDetails.value = error.stack || error.message
  errorTime.value = Date.now()
  
  // Report to global error handler
  globalErrorHandler.handleModuleFederationError(error, props.mfeName, props.componentName)
  
  // Emit error event
  emit('error', error)
}

const handleComponentError = (error: Error) => {
  console.error(`Runtime error in MFE component ${props.mfeName}/${props.componentName}:`, error)
  handleLoadError(error)
}

// Error capture for runtime errors
onErrorCaptured((error: Error, instance, errorInfo) => {
  console.error('MFE Error Boundary caught an error:', error)
  console.error('Error info:', errorInfo)
  console.error('Component instance:', instance)
  
  handleLoadError(error)
  
  // Prevent the error from propagating further
  return false
})

// Actions
const retry = async () => {
  if (retryCount.value >= props.maxRetries) {
    console.warn(`Max retries (${props.maxRetries}) reached for ${props.mfeName}/${props.componentName}`)
    return
  }
  
  isRetrying.value = true
  retryCount.value++
  
  try {
    // Wait before retrying (exponential backoff)
    await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount.value) * 1000))
    
    // Reset error state
    hasError.value = false
    errorDetails.value = ''
    
    // Emit retry event
    emit('retry')
    
    // Force component reload by changing key
    await router.go(0)
  } catch (error) {
    console.error('Retry failed:', error)
  } finally {
    isRetrying.value = false
  }
}

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.mfe-error-boundary {
  @apply min-h-screen flex items-center justify-center bg-gray-50 dark:bg-navy-900 px-4;
}

.error-container {
  @apply max-w-md w-full text-center;
}

.error-icon {
  @apply flex justify-center mb-6;
}

.error-content {
  @apply bg-white dark:bg-navy-800 rounded-lg shadow-lg p-8;
}

.error-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-4;
}

.error-message {
  @apply text-gray-600 dark:text-gray-300 mb-6 leading-relaxed;
}

.error-actions {
  @apply flex flex-col sm:flex-row justify-center items-center gap-3 mb-6;
}

.btn-primary {
  @apply px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center;
}

.btn-secondary {
  @apply px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors;
}

.error-details {
  @apply mt-6 text-left;
}

.error-details-summary {
  @apply cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.error-stack {
  @apply bg-gray-100 dark:bg-navy-700 p-4 rounded text-xs font-mono text-gray-800 dark:text-gray-200 overflow-auto max-h-40;
}

.loading-container {
  @apply flex flex-col items-center justify-center min-h-64 p-8;
}

.loading-spinner {
  @apply mb-4;
}

.spinner {
  @apply w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin;
}

.loading-message {
  @apply text-gray-600 dark:text-gray-300 text-sm;
}
</style>
