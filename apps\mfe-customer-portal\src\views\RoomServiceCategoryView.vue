<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="sticky top-0 z-10 bg-white shadow-sm">
      <div class="flex items-center justify-between px-4 py-3">
        <!-- Back Button -->
        <button 
          @click="goBack"
          class="flex items-center justify-center w-10 h-10 bg-backgroundLight rounded-lg"
        >
          <svg class="w-5 h-5 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- Page Title -->
        <h1 class="text-lg font-semibold text-textDark">{{ categoryTitle }}</h1>

        <!-- Cart Button -->
        <div class="relative">
          <button 
            @click="$router.push('/cart')"
            class="flex items-center bg-primary text-white px-3 py-2 rounded-lg font-medium"
          >
            <span class="mr-2">₺{{ cartStore.cartTotal.toFixed(2) }}</span>
            <div class="relative">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H3m4 10v6a1 1 0 001 1h9a1 1 0 001-1v-6M7 13v6a1 1 0 001 1h2M17 13v2"/>
              </svg>
              <span 
                v-if="cartStore.cartItemCount > 0"
                class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
              >
                {{ cartStore.cartItemCount }}
              </span>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="px-4 py-4">
      <div class="flex space-x-3">
        <!-- Search Input -->
        <div class="flex-1 relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
            <button class="bg-primary text-white p-2 rounded-lg">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Ara ..."
            class="w-full pl-16 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-textDark placeholder-textMedium focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
          />
        </div>
        
        <!-- Filter Button -->
        <button 
          @click="toggleFilters"
          class="p-3 bg-white border border-gray-200 rounded-xl"
        >
          <svg class="w-5 h-5 text-textMedium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707v4.172a1 1 0 01-.556.894l-2 1A1 1 0 0110 19.25v-5.836a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Recommendations Section -->
    <div v-if="recommendedItems.length > 0" class="px-4 mb-6">
      <h2 class="text-lg font-bold text-textDark mb-4">Size Önerilerimiz</h2>
      <div class="bg-white rounded-xl p-4 shadow-sm">
        <div 
          v-for="item in recommendedItems" 
          :key="item.id"
          @click="goToItemDetail(item.id)"
          class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
        >
          <div class="w-16 h-16 rounded-xl overflow-hidden flex-shrink-0">
            <img
              :src="getSafeImageUrl(item.image_url || undefined, 'food')"
              :alt="item.name"
              class="w-full h-full object-cover"
            />
          </div>
          <div class="flex-1">
            <h3 class="font-semibold text-textDark">{{ item.name }}</h3>
            <p class="text-textMedium text-sm">{{ item.description || 'Lezzetli bir seçenek' }}</p>
          </div>
          <div class="text-right">
            <div class="text-lg font-bold text-primary">₺{{ item.price.toFixed(2) }}</div>
            <div v-if="item.is_available" class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">MEVCİT</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Menu Items List -->
    <div class="px-4 pb-20">
      <div class="space-y-4">
        <div 
          v-for="item in filteredItems" 
          :key="item.id"
          class="bg-white rounded-xl p-4 shadow-sm relative"
        >
          <!-- Available Badge -->
          <div 
            v-if="item.is_available"
            class="absolute top-3 left-3 z-10"
          >
            <div class="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-lg">
              MEVCİT
            </div>
          </div>

          <div class="flex space-x-4">
            <!-- Food Image -->
            <div class="w-20 h-20 flex-shrink-0 cursor-pointer" @click="goToItemDetail(item.id)">
              <img
                :src="getSafeImageUrl(item.image_url || undefined, 'food')"
                :alt="item.name"
                class="w-full h-full object-cover rounded-xl hover:opacity-80 transition-opacity"
              />
            </div>

            <!-- Item Content -->
            <div class="flex-1">
              <div class="flex justify-between items-start mb-2">
                <!-- Item Name -->
                <h3 
                  @click="goToItemDetail(item.id)"
                  class="font-semibold text-textDark text-lg mb-1 cursor-pointer hover:text-primary transition-colors"
                >
                  {{ item.name }}
                </h3>
                
                <!-- Favorite Button -->
                <button 
                  @click="toggleFavorite(item.id)"
                  class="p-1"
                >
                  <svg 
                    class="w-6 h-6 text-textMedium"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </button>
              </div>

              <!-- Description -->
              <p class="text-textMedium text-sm mb-2 line-clamp-2">{{ item.description || 'Lezzetli bir seçenek' }}</p>

              <!-- Dietary Info and Preparation Time -->
              <div class="flex items-center space-x-2 mb-3">
                <span v-if="item.is_vegetarian" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700">
                  🌱
                </span>
                <span v-if="item.is_vegan" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700">
                  🌿
                </span>
                <span v-if="item.is_gluten_free" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">
                  🌾
                </span>
                <span v-if="item.preparation_time_minutes" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                  ⏱️ {{ item.preparation_time_minutes }}dk
                </span>
              </div>

              <!-- Price and Add Button -->
              <div class="flex justify-between items-center">
                <span class="text-2xl font-bold text-textDark">₺{{ item.price.toFixed(2) }}</span>
                
                <!-- Add to Cart Button or Quantity Control -->
                <div v-if="getItemQuantity(item.id) === 0">
                  <button 
                    @click="addToCart(item)"
                    class="w-10 h-10 bg-primary text-white rounded-xl flex items-center justify-center hover:bg-primary/90 transition-colors"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
                
                <!-- Quantity Control -->
                <div v-else class="flex items-center space-x-2">
                  <button 
                    @click="decrementQuantity(item.id)"
                    class="w-8 h-8 bg-gray-200 text-textDark rounded-lg flex items-center justify-center"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                    </svg>
                  </button>
                  
                  <div class="w-8 h-8 bg-primary text-white rounded-lg flex items-center justify-center font-semibold text-sm">
                    {{ getItemQuantity(item.id) }}
                  </div>
                  
                  <button 
                    @click="incrementQuantity(item.id)"
                    class="w-8 h-8 bg-primary text-white rounded-lg flex items-center justify-center"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="menuStore.isLoading" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl p-6 flex flex-col items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-3"></div>
        <p class="text-textDark">Menü yükleniyor...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="menuStore.error && !menuStore.isLoading" class="px-4 py-8">
      <div class="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
        <svg class="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-lg font-semibold text-red-800 mb-2">Menü Yüklenemedi</h3>
        <p class="text-red-600 mb-4">{{ menuStore.error }}</p>
        <button 
          @click="retryLoadMenu"
          class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Tekrar Dene
        </button>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCartStore } from '../stores/cartStore'
import { useMenuStore } from '../stores/menuStore'
import { useGuestAuthStore } from '../stores/guestAuthStore'
import { getSafeImageUrl } from '@/utils/imageUtils'

// Router
const route = useRoute()
const router = useRouter()

// Stores
const cartStore = useCartStore()
const menuStore = useMenuStore()
const guestAuthStore = useGuestAuthStore()

// Reactive data
const searchQuery = ref('')
const showFilters = ref(false)

// Category from route parameter
const category = computed(() => route.params.category as string)

// Category title from menu store
const categoryTitle = computed(() => {
  const currentCategory = menuStore.categories.find(cat => 
    cat.name.toLowerCase().replace(/\s+/g, '-').replace(/ı/g, 'i') === category.value
  )
  return currentCategory?.name || 'Menü'
})

// Computed properties
const filteredItems = computed(() => {
  // Find the current category
  const currentCategory = menuStore.categories.find(cat => 
    cat.name.toLowerCase().replace(/\s+/g, '-').replace(/ı/g, 'i') === category.value
  )
  
  if (!currentCategory || !currentCategory.items) {
    return []
  }
  
  let items = currentCategory.items
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    items = items.filter(item => 
      item.name.toLowerCase().includes(query) ||
      (item.description && item.description.toLowerCase().includes(query))
    )
  }
  
  return items
})

const recommendedItems = computed(() => {
  // Get all items from other categories and show 3 random recommendations
  const allItems = menuStore.categories
    .filter(cat => cat.name.toLowerCase().replace(/\s+/g, '-').replace(/ı/g, 'i') !== category.value)
    .flatMap(cat => cat.items || [])
  
  return allItems.slice(0, 3)
})

// Methods
const goBack = () => {
  router.go(-1)
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const addToCart = (item: any) => {
  cartStore.addToCart({
    id: item.id,
    name: item.name,
    price: item.price,
    description: item.description,
    image_url: item.image_url
  })
}

const incrementQuantity = (itemId: string) => {
  cartStore.incrementQuantity(itemId)
}

const decrementQuantity = (itemId: string) => {
  cartStore.decrementQuantity(itemId)
}

const getItemQuantity = (itemId: string) => {
  const item = cartStore.cartItems.find(cartItem => cartItem.id === itemId)
  return item ? item.quantity : 0
}

const toggleFavorite = (itemId: string) => {
  // TODO: Implement favorites functionality with backend
  console.log('Toggle favorite for item:', itemId)
}

const retryLoadMenu = async () => {
  if (guestAuthStore.currentHotelId) {
    await menuStore.refreshMenu(guestAuthStore.currentHotelId)
  }
}

const goToItemDetail = (itemId: string) => {
  router.push(`/item/${itemId}`)
}

// Lifecycle
onMounted(async () => {
  // Load menu data if not already loaded
  if (guestAuthStore.currentHotelId && menuStore.categories.length === 0) {
    await menuStore.fetchFullMenu(guestAuthStore.currentHotelId)
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 