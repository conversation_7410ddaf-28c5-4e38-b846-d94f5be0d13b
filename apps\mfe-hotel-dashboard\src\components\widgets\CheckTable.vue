<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <h4 class="text-lg font-bold text-navy-700 dark:text-white">Günlük Kontrol Listesi</h4>
      <button class="text-brand-500 text-sm font-medium hover:text-brand-600">
        Tü<PERSON>ü<PERSON><PERSON>
      </button>
    </div>

    <!-- Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-200 dark:border-gray-700">
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">
              <input type="checkbox" class="rounded border-gray-300 text-brand-500 focus:ring-brand-500" @change="toggleAll">
            </th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">ODA</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">GÖREV</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">DURUM</th>
            <th class="text-left pb-3 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wider">BİTİŞ TARİHİ</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="task in tasks" :key="task.id" class="hover:bg-gray-50 dark:hover:bg-navy-700">
            <td class="py-4">
              <input 
                type="checkbox" 
                :checked="task.completed"
                @change="toggleTask(task.id)"
                class="rounded border-gray-300 text-brand-500 focus:ring-brand-500"
              >
            </td>
            <td class="py-4">
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ task.room }}</span>
            </td>
            <td class="py-4">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ task.task }}</span>
            </td>
            <td class="py-4">
              <span 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusClass(task.status)"
              >
                {{ task.status }}
              </span>
            </td>
            <td class="py-4">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ task.dueDate }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Task {
  id: string
  room: string
  task: string
  status: 'Tamamlandı' | 'Bekliyor' | 'İptal'
  dueDate: string
  completed: boolean
}

const tasks = ref<Task[]>([
  {
    id: '1',
    room: '201',
    task: 'Genel Temizlik',
    status: 'Tamamlandı',
    dueDate: '14:30',
    completed: true
  },
  {
    id: '2',
    room: '305',
    task: 'Bakım Kontrolü',
    status: 'Bekliyor',
    dueDate: '16:00',
    completed: false
  },
  {
    id: '3',
    room: '128',
    task: 'Minibar Dolum',
    status: 'Tamamlandı',
    dueDate: '12:15',
    completed: true
  },
  {
    id: '4',
    room: '412',
    task: 'Teknik Kontrol',
    status: 'Bekliyor',
    dueDate: '17:30',
    completed: false
  },
  {
    id: '5',
    room: '209',
    task: 'Özel İstek',
    status: 'İptal',
    dueDate: '15:45',
    completed: false
  }
])

const getStatusClass = (status: string) => {
  switch (status) {
    case 'Tamamlandı':
      return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
    case 'Bekliyor':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
    case 'İptal':
      return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
  }
}

const toggleTask = (taskId: string) => {
  const task = tasks.value.find(t => t.id === taskId)
  if (task) {
    task.completed = !task.completed
    task.status = task.completed ? 'Tamamlandı' : 'Bekliyor'
  }
}

const toggleAll = (event: Event) => {
  const target = event.target as HTMLInputElement
  tasks.value.forEach(task => {
    task.completed = target.checked
    task.status = task.completed ? 'Tamamlandı' : 'Bekliyor'
  })
}
</script>

<style scoped>
/* Table specific styles */
</style> 