<template>
  <div class="fixed top-5 right-5 z-50 w-full max-w-sm">
    <transition
      enter-active-class="transform ease-out duration-300 transition"
      enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
      leave-active-class="transition ease-in duration-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div 
        v-if="isToastVisible && toastQueue.length > 0" 
        class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 cursor-pointer hover:shadow-xl transition-shadow"
        @click="handleToastClick"
      >
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div :class="[
              'w-6 h-6 rounded-full flex items-center justify-center text-sm',
              getToastIconColor(toastQueue[0]?.type)
            ]">
              {{ notificationStore.getNotificationIcon(toastQueue[0]?.type) }}
            </div>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ toastQueue[0]?.title }}</p>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ toastQueue[0]?.message }}</p>
            <p class="mt-1 text-xs text-blue-500">Dokunarak görüntüle</p>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore, type Notification } from '@/stores/notificationStore'

const router = useRouter()
const notificationStore = useNotificationStore()
const toastQueue = ref<Notification[]>([])
const isToastVisible = ref(false)

// Define click handler function BEFORE other functions
const handleToastClick = () => {
  const currentToast = toastQueue.value[0]
  if (currentToast) {
    // Mark as read
    if (!currentToast.isRead) {
      notificationStore.markAsRead(currentToast.id)
    }
    
    // Navigate if linkTo exists
    if (currentToast.linkTo) {
      router.push(currentToast.linkTo)
    }
    
    // Remove toast
    isToastVisible.value = false
    toastQueue.value.shift()
    setTimeout(showNextToast, 500)
  }
}

// Define showNextToast function
const showNextToast = () => {
  if (toastQueue.value.length === 0 || isToastVisible.value) return

  isToastVisible.value = true
  
  // Auto-hide after 5 seconds
  setTimeout(() => {
    if (isToastVisible.value) {
      isToastVisible.value = false
      toastQueue.value.shift()
      setTimeout(showNextToast, 500)
    }
  }, 5000)
}

// Helper functions defined BEFORE the watcher
const getToastIconColor = (type?: Notification['type']): string => {
  switch (type) {
    case 'order':
      return 'bg-orange-100 text-orange-600'
    case 'service':
      return 'bg-blue-100 text-blue-600'
    case 'activity':
      return 'bg-green-100 text-green-600'
    case 'alert':
      return 'bg-red-100 text-red-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

// Watch for new notifications being added to the store
watch(
  () => notificationStore.notifications,
  (newNotifications, oldNotifications) => {
    // Only proceed if we have both arrays and new length is greater
    if (oldNotifications && newNotifications.length > oldNotifications.length) {
      // A new notification was added, find it and add to our queue
      const newNotification = newNotifications[0] // The newest is always at the start
      if (!newNotification.isRead) { // Only show for unread notifications
         toastQueue.value.push(newNotification)
         showNextToast() // Attempt to show the toast
      }
    }
  },
  { deep: true } // deep watch is needed to detect changes in the array of objects
)
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}
</style> 