<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Operasyonel Raporlar</h1>
        <p class="text-gray-600 dark:text-gray-400">Otel operasyonlarınızı analiz edin ve verimliliği artırın</p>
      </div>
      <div class="flex items-center space-x-4">
        <!-- Category Filter -->
        <select 
          v-model="selectedCategory"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="all">Tüm Kategoriler</option>
          <option value="housekeeping">Kat Hizmetleri</option>
          <option value="maintenance">Bakım Onarım</option>
          <option value="room-service">Oda Servisi</option>
          <option value="guest-services">Misa<PERSON><PERSON>zmetleri</option>
          <option value="activities">Aktiviteler</option>
        </select>
        <!-- Period Filter -->
        <select 
          v-model="selectedPeriod"
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white"
        >
          <option value="today">Bugün</option>
          <option value="week">Bu Hafta</option>
          <option value="month">Bu Ay</option>
          <option value="quarter">Bu Çeyrek</option>
          <option value="year">Bu Yıl</option>
        </select>
        <!-- Export Button -->
        <button
          @click="exportReports"
          class="px-6 py-2 bg-brand-500 hover:bg-brand-600 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
        >
          <ArrowDownTrayIcon class="w-5 h-5" />
          <span>Raporu İndir</span>
        </button>
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="metric in keyMetrics"
        :key="metric.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ metric.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ metric.value }}</p>
            <p v-if="metric.change" :class="[
              'text-sm mt-1 flex items-center',
              metric.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
            ]">
              <component :is="metric.change.startsWith('+') ? ArrowTrendingUpIcon : ArrowTrendingDownIcon" class="w-4 h-4 mr-1" />
              {{ metric.change }}
            </p>
          </div>
          <div :class="[
            'p-3 rounded-xl',
            metric.iconBg
          ]">
            <component :is="metric.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Service Request Trends -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Hizmet Talepleri Trendi</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">Son 30 Gün</span>
        </div>
        <div class="h-64 flex items-center justify-center">
          <!-- Chart Placeholder -->
          <div class="text-center">
            <ChartBarIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500 dark:text-gray-400">Hizmet talepleri trendi chart burada gösterilecek</p>
          </div>
        </div>
      </div>

      <!-- Revenue by Category -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Kategoriye Göre Gelir</h3>
          <span class="text-sm text-gray-500 dark:text-gray-400">{{ selectedPeriod === 'today' ? 'Bugün' : selectedPeriod === 'week' ? 'Bu Hafta' : selectedPeriod === 'month' ? 'Bu Ay' : 'Bu Yıl' }}</span>
        </div>
        <div class="space-y-4">
          <div v-for="category in revenueByCategory" :key="category.name" class="flex items-center justify-between">
            <div class="flex items-center">
              <div :class="['w-3 h-3 rounded-full mr-3', category.color]"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ category.name }}</span>
            </div>
            <div class="text-right">
              <span class="text-sm font-semibold text-navy-700 dark:text-white">₺{{ category.amount.toLocaleString('tr-TR') }}</span>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ category.percentage }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Operational Efficiency Table -->
    <div class="bg-white dark:bg-navy-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Operasyonel Verimlilik</h3>
          <div class="flex items-center space-x-4">
            <!-- Search Input -->
            <div class="relative">
              <MagnifyingGlassIcon class="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Departman ara..."
                class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-navy-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Departman/Hizmet
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Toplam Talep
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tamamlanan
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Ortalama Süre
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Verimlilik
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Müşteri Memnuniyeti
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Trend
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-navy-700 divide-y divide-gray-200 dark:divide-gray-600">
            <tr v-for="efficiency in filteredEfficiency" :key="efficiency.department" class="hover:bg-gray-50 dark:hover:bg-navy-600">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-brand-400 to-brand-600 flex items-center justify-center mr-3">
                    <component :is="efficiency.icon" class="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ efficiency.department }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ efficiency.category }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ efficiency.totalRequests }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ efficiency.completed }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ efficiency.averageTime }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="text-sm text-gray-900 dark:text-white mr-2">{{ efficiency.efficiency }}%</div>
                  <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      :class="[
                        'h-2 rounded-full',
                        efficiency.efficiency >= 90 ? 'bg-green-500' : 
                        efficiency.efficiency >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                      ]"
                      :style="{ width: efficiency.efficiency + '%' }"
                    ></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <StarIcon class="w-4 h-4 text-yellow-400 mr-1" />
                  <span class="text-sm text-gray-900 dark:text-white">{{ efficiency.satisfaction }}/5</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div :class="[
                  'flex items-center text-sm',
                  efficiency.trend === 'up' ? 'text-green-500' : 
                  efficiency.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                ]">
                  <component :is="efficiency.trend === 'up' ? ArrowTrendingUpIcon : efficiency.trend === 'down' ? ArrowTrendingDownIcon : MinusIcon" class="w-4 h-4 mr-1" />
                  {{ efficiency.trendValue }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Real-time Dashboard Widgets -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Active Tasks -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Aktif Görevler</h3>
        <div class="space-y-4">
          <div v-for="task in activeTasks" :key="task.id" class="flex items-center justify-between">
            <div class="flex items-center">
              <div :class="[
                'w-3 h-3 rounded-full mr-3',
                task.priority === 'HIGH' ? 'bg-red-500' : 
                task.priority === 'MEDIUM' ? 'bg-yellow-500' : 'bg-green-500'
              ]"></div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ task.title }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ task.room }} - {{ task.assignee }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ task.timeRemaining }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Alerts -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Sistem Uyarıları</h3>
        <div class="space-y-4">
          <div v-for="alert in systemAlerts" :key="alert.id" class="flex items-start">
            <div :class="[
              'w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0',
              alert.type === 'ERROR' ? 'bg-red-500' : 
              alert.type === 'WARNING' ? 'bg-yellow-500' : 'bg-blue-500'
            ]"></div>
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ alert.title }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ alert.message }}</div>
              <div class="text-xs text-gray-400 mt-1">{{ alert.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-6">Anlık İstatistikler</h3>
        <div class="space-y-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-navy-700 dark:text-white">{{ getCurrentActiveOrders() }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Aktif Sipariş</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-navy-700 dark:text-white">{{ getPendingMaintenanceCount() }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Bekleyen Bakım</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-navy-700 dark:text-white">{{ getOnlineStaffCount() }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Aktif Personel</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-navy-700 dark:text-white">{{ getTodaysRevenue() }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Bugünün Geliri</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ArrowDownTrayIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ClockIcon,
  StarIcon,
  BanknotesIcon,
  WrenchScrewdriverIcon,
  GiftIcon,
  SparklesIcon,
  HomeIcon,
  MinusIcon,
  UsersIcon,
  CalendarDaysIcon
} from '@heroicons/vue/24/outline'

// Reactive refs
const selectedCategory = ref('all')
const selectedPeriod = ref('month')
const searchQuery = ref('')

// Key Metrics Data
const keyMetrics = computed(() => [
  {
    title: 'Toplam Talep',
    value: '1,247',
    change: '+15.3%',
    icon: ClipboardDocumentListIcon,
    iconBg: 'bg-blue-500'
  },
  {
    title: 'Ortalama Yanıt Süresi',
    value: '12.4dk',
    change: '-2.1dk',
    icon: ClockIcon,
    iconBg: 'bg-green-500'
  },
  {
    title: 'Verimlilik Oranı',
    value: '89.7%',
    change: '****%',
    icon: StarIcon,
    iconBg: 'bg-yellow-500'
  },
  {
    title: 'Günlük Gelir',
    value: '₺42,850',
    change: '****%',
    icon: CurrencyDollarIcon,
    iconBg: 'bg-purple-500'
  }
])

// Revenue by Category Data
const revenueByCategory = ref([
  { name: 'Oda Servisi', amount: 156780, percentage: 38, color: 'bg-blue-500' },
  { name: 'Misafir Hizmetleri', amount: 98450, percentage: 24, color: 'bg-green-500' },
  { name: 'Aktiviteler', amount: 67230, percentage: 16, color: 'bg-purple-500' },
  { name: 'Spa & Wellness', amount: 54670, percentage: 13, color: 'bg-yellow-500' },
  { name: 'Diğer', amount: 35870, percentage: 9, color: 'bg-gray-500' }
])

// Operational Efficiency Data
const operationalEfficiency = ref([
  {
    department: 'Oda Servisi',
    category: 'Yiyecek & İçecek',
    totalRequests: 342,
    completed: 325,
    averageTime: '18 dk',
    efficiency: 95,
    satisfaction: 4.6,
    trend: 'up',
    trendValue: '****%',
    icon: GiftIcon
  },
  {
    department: 'Kat Hizmetleri',
    category: 'Temizlik & Bakım',
    totalRequests: 156,
    completed: 142,
    averageTime: '25 dk',
    efficiency: 91,
    satisfaction: 4.4,
    trend: 'up',
    trendValue: '****%',
    icon: SparklesIcon
  },
  {
    department: 'Bakım Onarım',
    category: 'Teknik Hizmetler',
    totalRequests: 89,
    completed: 76,
    averageTime: '45 dk',
    efficiency: 85,
    satisfaction: 4.2,
    trend: 'down',
    trendValue: '-1.2%',
    icon: WrenchScrewdriverIcon
  },
  {
    department: 'Misafir Hizmetleri',
    category: 'Genel Hizmetler',
    totalRequests: 234,
    completed: 218,
    averageTime: '15 dk',
    efficiency: 93,
    satisfaction: 4.7,
    trend: 'up',
    trendValue: '****%',
    icon: UsersIcon
  },
  {
    department: 'Aktiviteler',
    category: 'Eğlence & Etkinlik',
    totalRequests: 67,
    completed: 65,
    averageTime: '120 dk',
    efficiency: 97,
    satisfaction: 4.8,
    trend: 'stable',
    trendValue: '0.0%',
    icon: CalendarDaysIcon
  }
])

// Active Tasks Data
const activeTasks = ref([
  {
    id: '1',
    title: 'Oda Temizliği',
    room: 'Oda 205',
    assignee: 'Ayşe D.',
    priority: 'HIGH',
    timeRemaining: '5 dk'
  },
  {
    id: '2',
    title: 'Kahvaltı Servisi',
    room: 'Oda 312',
    assignee: 'Mehmet Y.',
    priority: 'MEDIUM',
    timeRemaining: '12 dk'
  },
  {
    id: '3',
    title: 'Klima Bakımı',
    room: 'Oda 108',
    assignee: 'Ali K.',
    priority: 'LOW',
    timeRemaining: '30 dk'
  },
  {
    id: '4',
    title: 'Havuz Temizliği',
    room: 'Havuz',
    assignee: 'Fatma Ö.',
    priority: 'MEDIUM',
    timeRemaining: '15 dk'
  }
])

// System Alerts Data
const systemAlerts = ref([
  {
    id: '1',
    type: 'ERROR',
    title: 'Yoğun Talep Uyarısı',
    message: 'Oda servisi taleplerinde %30 artış tespit edildi',
    time: '5 dakika önce'
  },
  {
    id: '2',
    type: 'WARNING',
    title: 'Personel Eksikliği',
    message: 'Kat hizmetlerinde 2 personel eksik',
    time: '15 dakika önce'
  },
  {
    id: '3',
    type: 'INFO',
    title: 'Sistem Güncellemesi',
    message: 'Bakım modülü güncellendi v2.1.3',
    time: '1 saat önce'
  }
])

// Computed Properties
const filteredEfficiency = computed(() => {
  let filtered = operationalEfficiency.value

  // Filter by category
  if (selectedCategory.value !== 'all') {
    const categoryMap: Record<string, string> = {
      'housekeeping': 'Kat Hizmetleri',
      'maintenance': 'Bakım Onarım',
      'room-service': 'Oda Servisi',
      'guest-services': 'Misafir Hizmetleri',
      'activities': 'Aktiviteler'
    }
    const categoryName = categoryMap[selectedCategory.value]
    if (categoryName) {
      filtered = filtered.filter(item => item.department === categoryName)
    }
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.department.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query)
    )
  }

  return filtered
})

// Methods
const getCurrentActiveOrders = () => {
  return Math.floor(Math.random() * 50) + 20 // 20-70 range
}

const getPendingMaintenanceCount = () => {
  return Math.floor(Math.random() * 15) + 5 // 5-20 range
}

const getOnlineStaffCount = () => {
  return Math.floor(Math.random() * 30) + 85 // 85-115 range
}

const getTodaysRevenue = () => {
  const revenue = Math.floor(Math.random() * 20000) + 35000 // 35k-55k range
  return '₺' + revenue.toLocaleString('tr-TR')
}

const exportReports = () => {
  alert('Operasyonel rapor indirme özelliği backend entegrasyonu ile aktif hale gelecek.')
}
</script> 