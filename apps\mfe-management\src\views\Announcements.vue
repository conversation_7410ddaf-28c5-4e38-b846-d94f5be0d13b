<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-navy-700 dark:text-white">Global Duyurular</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Tüm otellere gönderilecek duyuruları yönetin</p>
      </div>
      <button 
        @click="showCreateModal = true"
        class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 transition-colors flex items-center space-x-2"
      >
        <PlusIcon class="h-4 w-4" />
        <span>Yeni Duyuru O<PERSON>ştur</span>
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Duyuru</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ announcements.length }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <SpeakerWaveIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Bu ay oluşturuldu
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Ortalama Görüntüleme</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ averageViews }}%</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <EyeIcon class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Duyuru görüntüleme oranı
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Aktif Duyuru</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ activeAnnouncements }}</p>
          </div>
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
            <BellIcon class="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Şu anda yayında
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Ortalama Tıklanma</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">{{ averageClickRate }}%</p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
            <CursorArrowRaysIcon class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Tıklanma oranı
        </div>
      </div>
    </div>

    <!-- Announcements Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div 
        v-for="announcement in announcements" 
        :key="announcement.id"
        class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow"
      >
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-start space-x-3">
            <div 
              class="w-10 h-10 rounded-full flex items-center justify-center"
              :class="getStatusBadgeClass(announcement.status).bg"
            >
              <component 
                :is="getStatusIcon(announcement.status)" 
                class="h-5 w-5"
                :class="getStatusBadgeClass(announcement.status).text"
              />
            </div>
            <div>
              <h3 class="font-semibold text-navy-700 dark:text-white">{{ announcement.title }}</h3>
              <div class="flex items-center space-x-2 mt-1">
                <span 
                  class="text-xs px-2 py-1 rounded-full"
                  :class="getStatusBadgeClass(announcement.status).badge"
                >
                  {{ announcement.status }}
                </span>
                <span 
                  class="text-xs px-2 py-1 rounded-full"
                  :class="getTargetBadgeClass(announcement.targetAudience)"
                >
                  {{ announcement.targetAudience }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="editAnnouncement(announcement)"
              class="p-2 text-gray-400 hover:text-brand-500 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-600 transition-colors"
              title="Düzenle"
            >
              <PencilIcon class="h-4 w-4" />
            </button>
            <button
              @click="deleteAnnouncement(announcement.id)"
              class="p-2 text-gray-400 hover:text-red-500 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-600 transition-colors"
              title="Sil"
            >
              <TrashIcon class="h-4 w-4" />
            </button>
          </div>
        </div>

        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
          {{ announcement.content }}
        </p>

        <div class="space-y-3">
          <!-- Analytics -->
          <div class="grid grid-cols-3 gap-4 p-3 bg-gray-50 dark:bg-navy-700 rounded-lg">
            <div class="text-center">
              <div class="text-lg font-semibold text-navy-700 dark:text-white">{{ announcement.analytics.views }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Görüntüleme</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-semibold text-navy-700 dark:text-white">{{ announcement.analytics.clicks }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Tıklanma</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-semibold text-navy-700 dark:text-white">{{ announcement.analytics.clickRate }}%</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Tıklanma Oranı</div>
            </div>
          </div>

          <!-- Date Info -->
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Yayınlandı: {{ formatDate(announcement.publishDate) }}</span>
            <span v-if="announcement.endDate">Son: {{ formatDate(announcement.endDate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Announcement Modal -->
    <div 
      v-if="showCreateModal || editingAnnouncement"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click.self="closeModal"
    >
      <div class="bg-white dark:bg-navy-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
              {{ editingAnnouncement ? 'Duyuru Düzenle' : 'Yeni Duyuru Oluştur' }}
            </h3>
            <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
              <XMarkIcon class="h-6 w-6" />
            </button>
          </div>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div class="space-y-6">
            <!-- Title -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Duyuru Başlığı *
              </label>
              <input
                v-model="formData.title"
                type="text"
                placeholder="Örn: Platform v2.2 Güncellemesi"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                required
              >
            </div>

            <!-- Content -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Duyuru İçeriği *
              </label>
              <div class="relative">
                <textarea
                  v-model="formData.content"
                  rows="6"
                  placeholder="Duyuru içeriğini buraya yazın..."
                  class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  required
                ></textarea>
                <div class="absolute bottom-2 right-2 text-xs text-gray-400">
                  {{ formData.content.length }}/500 karakter
                </div>
              </div>
            </div>

            <!-- Target Audience -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Hedef Kitle *
              </label>
              <select
                v-model="formData.targetAudience"
                class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500"
                required
              >
                <option value="">Hedef kitle seçin</option>
                <option value="Tüm Oteller">Tüm Oteller</option>
                <option value="Basic Plan">Basic Plan</option>
                <option value="Premium Plan">Premium Plan</option>
                <option value="Enterprise Plan">Enterprise Plan</option>
              </select>
            </div>

            <!-- Publishing Options -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Yayınlama Seçenekleri
              </label>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    v-model="formData.publishOption"
                    type="radio"
                    value="immediate"
                    class="h-4 w-4 text-brand-600 border-gray-300 focus:ring-brand-500"
                  >
                  <span class="ml-2 text-gray-700 dark:text-gray-300">Hemen Yayınla</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="formData.publishOption"
                    type="radio"
                    value="scheduled"
                    class="h-4 w-4 text-brand-600 border-gray-300 focus:ring-brand-500"
                  >
                  <span class="ml-2 text-gray-700 dark:text-gray-300">İleri Tarihte Yayınla</span>
                </label>
              </div>

              <!-- Schedule Date/Time -->
              <div v-if="formData.publishOption === 'scheduled'" class="mt-3 grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Tarih</label>
                  <input
                    v-model="formData.scheduledDate"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500"
                  >
                </div>
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Saat</label>
                  <input
                    v-model="formData.scheduledTime"
                    type="time"
                    class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-500"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            İptal
          </button>
          <button
            @click="saveAnnouncement"
            :disabled="!isFormValid"
            class="px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {{ editingAnnouncement ? 'Güncelle' : 'Yayınla' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  PlusIcon,
  XMarkIcon,
  SpeakerWaveIcon,
  EyeIcon,
  BellIcon,
  CursorArrowRaysIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon
} from '@heroicons/vue/24/outline'

// Types
interface Announcement {
  id: string
  title: string
  content: string
  status: 'Yayında' | 'Zamanlanmış' | 'Taslak' | 'Sona Erdi'
  targetAudience: string
  publishDate: string
  endDate?: string
  analytics: {
    views: string
    clicks: string
    clickRate: string
  }
}

// State
const showCreateModal = ref(false)
const editingAnnouncement = ref<Announcement | null>(null)

const formData = ref({
  title: '',
  content: '',
  targetAudience: '',
  publishOption: 'immediate',
  scheduledDate: '',
  scheduledTime: ''
})

// Dummy data
const announcements = ref<Announcement[]>([
  {
    id: '1',
    title: 'Platform v2.2 Güncellemesi',
    content: 'Sistem bakımı nedeniyle 20 Ocak 2025 saat 02:00-04:00 arası kısa süreli kesinti yaşanabilir. Yeni özellikler ve performans iyileştirmeleri eklenecektir.',
    status: 'Yayında',
    targetAudience: 'Tüm Oteller',
    publishDate: '2025-01-15T14:30:00Z',
    endDate: '2025-01-21T00:00:00Z',
    analytics: {
      views: '4,512',
      clicks: '378',
      clickRate: '8.4'
    }
  },
  {
    id: '2',
    title: 'Akıllı Bildirim Sistemi Aktif',
    content: 'Yeni akıllı bildirim sistemi sayesinde misafirlerinize daha hızlı ve etkili şekilde ulaşabilirsiniz. WhatsApp, SMS ve email entegrasyonu kullanıma hazır.',
    status: 'Yayında',
    targetAudience: 'Premium Plan',
    publishDate: '2025-01-12T09:15:00Z',
    analytics: {
      views: '2,847',
      clicks: '294',
      clickRate: '10.3'
    }
  },
  {
    id: '3',
    title: 'Kış Sezonu Kampanyası',
    content: 'Tüm Basic plan kullanıcıları için özel indirim! 3 aylık Premium plana geçiş yapanlar %30 indirim kazanır.',
    status: 'Zamanlanmış',
    targetAudience: 'Basic Plan',
    publishDate: '2025-01-25T10:00:00Z',
    analytics: {
      views: '0',
      clicks: '0',
      clickRate: '0'
    }
  },
  {
    id: '4',
    title: 'SPA Modülü Beta Testi',
    content: 'Enterprise müşterilerimiz için SPA & Wellness modülünün beta versiyonu test edilmeye hazır. Katılmak isteyenler destek ekibiyle iletişime geçebilir.',
    status: 'Sona Erdi',
    targetAudience: 'Enterprise Plan',
    publishDate: '2025-01-08T15:00:00Z',
    endDate: '2025-01-15T00:00:00Z',
    analytics: {
      views: '1,234',
      clicks: '89',
      clickRate: '7.2'
    }
  }
])

// Computed
const activeAnnouncements = computed(() => {
  return announcements.value.filter(a => a.status === 'Yayında').length
})

const averageViews = computed(() => {
  const total = announcements.value.reduce((sum, a) => sum + parseInt(a.analytics.views.replace(',', '')), 0)
  return Math.round(total / announcements.value.length / 50) // Normalized percentage
})

const averageClickRate = computed(() => {
  const total = announcements.value.reduce((sum, a) => sum + parseFloat(a.analytics.clickRate), 0)
  return (total / announcements.value.length).toFixed(1)
})

const isFormValid = computed(() => {
  const basic = formData.value.title && formData.value.content && formData.value.targetAudience
  if (formData.value.publishOption === 'scheduled') {
    return basic && formData.value.scheduledDate && formData.value.scheduledTime
  }
  return basic
})

// Methods
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'Yayında': return CheckCircleIcon
    case 'Zamanlanmış': return ClockIcon
    case 'Sona Erdi': return ExclamationCircleIcon
    default: return BellIcon
  }
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'Yayında':
      return {
        bg: 'bg-green-100 dark:bg-green-900/30',
        text: 'text-green-600 dark:text-green-400',
        badge: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
      }
    case 'Zamanlanmış':
      return {
        bg: 'bg-blue-100 dark:bg-blue-900/30',
        text: 'text-blue-600 dark:text-blue-400',
        badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
      }
    case 'Sona Erdi':
      return {
        bg: 'bg-gray-100 dark:bg-gray-900/30',
        text: 'text-gray-600 dark:text-gray-400',
        badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
      }
    default:
      return {
        bg: 'bg-orange-100 dark:bg-orange-900/30',
        text: 'text-orange-600 dark:text-orange-400',
        badge: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300'
      }
  }
}

const getTargetBadgeClass = (target: string) => {
  switch (target) {
    case 'Tüm Oteller':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
    case 'Basic Plan':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
    case 'Premium Plan':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
    case 'Enterprise Plan':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const editAnnouncement = (announcement: Announcement) => {
  editingAnnouncement.value = announcement
  formData.value = {
    title: announcement.title,
    content: announcement.content,
    targetAudience: announcement.targetAudience,
    publishOption: announcement.status === 'Zamanlanmış' ? 'scheduled' : 'immediate',
    scheduledDate: '',
    scheduledTime: ''
  }
}

const deleteAnnouncement = (id: string) => {
  if (confirm('Bu duyuruyu silmek istediğinizden emin misiniz?')) {
    const index = announcements.value.findIndex(a => a.id === id)
    if (index > -1) {
      announcements.value.splice(index, 1)
    }
  }
}

const saveAnnouncement = () => {
  // Here would be the API call to save/update announcement
  console.log('Saving announcement:', formData.value)
  
  const newAnnouncement: Announcement = {
    id: editingAnnouncement.value?.id || Date.now().toString(),
    title: formData.value.title,
    content: formData.value.content,
    status: formData.value.publishOption === 'immediate' ? 'Yayında' : 'Zamanlanmış',
    targetAudience: formData.value.targetAudience,
    publishDate: formData.value.publishOption === 'immediate' 
      ? new Date().toISOString() 
      : new Date(`${formData.value.scheduledDate}T${formData.value.scheduledTime}`).toISOString(),
    analytics: {
      views: '0',
      clicks: '0',
      clickRate: '0'
    }
  }

  if (editingAnnouncement.value) {
    const index = announcements.value.findIndex(a => a.id === editingAnnouncement.value!.id)
    if (index > -1) {
      announcements.value[index] = { ...announcements.value[index], ...newAnnouncement }
    }
  } else {
    announcements.value.unshift(newAnnouncement)
  }

  closeModal()
}

const closeModal = () => {
  showCreateModal.value = false
  editingAnnouncement.value = null
  formData.value = {
    title: '',
    content: '',
    targetAudience: '',
    publishOption: 'immediate',
    scheduledDate: '',
    scheduledTime: ''
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 