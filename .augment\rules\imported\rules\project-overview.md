---
type: "agent_requested"
---

# Hotelexia Project Overview

## 1. High-Level Goal
Hotelexia is a **Unicorn-level, enterprise-grade SaaS platform** for the hospitality industry. It provides a comprehensive, multi-tenant solution for hotel management, built on a modern micro-frontend (MFE) architecture.

## 2. Core Architecture
The project is a **pnpm monorepo** containing a central **App Shell** and multiple specialized **Micro-Frontends (MFEs)**. This architecture ensures scalability, independent development, and robust separation of concerns.

## 3. Key Portals (MFEs)
- **`app-shell`**: The main application container and orchestrator.
- **`mfe-customer-portal`**: Guest-facing portal for searching hotels, making reservations, and managing personal bookings.
- **`mfe-hotel-dashboard`**: Comprehensive dashboard for hotel staff and managers to handle daily operations, maintenance, reporting, and activity management.
- **`mfe-management`**: Super-admin portal for platform owners to manage hotels, users, system health, and global settings.

## 4. Current Status
- **Completion:** ~95% (Frontend is feature-complete and build-successful).
- **Next Step:** Full-scale integration with the Supabase backend services.
- **Readiness:** Production-ready pending backend API hookup.