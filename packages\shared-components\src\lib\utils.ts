import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Horizon UI Chakra theme variants for different portals
 */
export const portalThemes = {
  customer: {
    primary: 'bg-gradient-to-r from-blue-500 to-cyan-500',
    secondary: 'bg-gradient-to-r from-blue-50 to-cyan-50',
    accent: 'text-blue-600',
    background: 'bg-gray-50',
  },
  hotel: {
    primary: 'bg-gradient-to-r from-brand-500 to-purple-600',
    secondary: 'bg-gradient-to-r from-brand-50 to-purple-50',
    accent: 'text-brand-600',
    background: 'bg-white',
  },
  manager: {
    primary: 'bg-gradient-to-r from-purple-600 to-indigo-600',
    secondary: 'bg-gradient-to-r from-purple-50 to-indigo-50',
    accent: 'text-purple-600',
    background: 'bg-gray-100',
  },
} as const

export type PortalTheme = keyof typeof portalThemes

/**
 * Color variants for consistent theming
 */
export const colorVariants = {
  brand: {
    50: '#EBF8FF',
    100: '#BEE3F8',
    200: '#90CDF4',
    300: '#63B3ED',
    400: '#4299E1',
    500: '#422AFB', // Primary brand color
    600: '#3182CE',
    700: '#2B77CB',
    800: '#2C5282',
    900: '#2A4365',
  },
  navy: {
    50: '#F7FAFC',
    100: '#EDF2F7',
    200: '#E2E8F0',
    300: '#CBD5E0',
    400: '#A0AEC0',
    500: '#718096',
    600: '#4A5568',
    700: '#2D3748', // Primary navy
    800: '#1A202C',
    900: '#171923',
  },
  secondaryGray: {
    100: '#F7FAFC',
    200: '#EDF2F7',
    300: '#E2E8F0',
    400: '#CBD5E0',
    500: '#A0AEC0',
    600: '#718096',
    700: '#4A5568',
    800: '#2D3748',
    900: '#1A202C',
  }
} as const

/**
 * Responsive breakpoints matching Tailwind defaults
 */
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const

/**
 * Animation presets for consistent motion
 */
export const animations = {
  fadeIn: 'animate-in fade-in duration-200',
  fadeOut: 'animate-out fade-out duration-200',
  slideIn: 'animate-in slide-in-from-bottom-2 duration-300',
  slideOut: 'animate-out slide-out-to-bottom-2 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
  scaleOut: 'animate-out zoom-out-95 duration-200',
} as const

/**
 * Shadow variants for consistent depth
 */
export const shadows = {
  card: 'shadow-sm hover:shadow-md transition-shadow duration-200',
  brand: 'shadow-lg shadow-brand-500/25',
  soft: 'shadow-sm',
  medium: 'shadow-md',
  large: 'shadow-lg',
  xl: 'shadow-xl',
} as const

/**
 * Helper function to get portal-specific styling
 */
export function getPortalStyles(portal: PortalTheme) {
  return portalThemes[portal]
}

/**
 * Generate consistent spacing values
 */
export const spacing = {
  xs: '0.5rem',   // 8px
  sm: '0.75rem',  // 12px
  md: '1rem',     // 16px
  lg: '1.5rem',   // 24px
  xl: '2rem',     // 32px
  '2xl': '3rem',  // 48px
  '3xl': '4rem',  // 64px
} as const

/**
 * Consistent border radius values
 */
export const borderRadius = {
  none: '0',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  full: '9999px',
} as const 