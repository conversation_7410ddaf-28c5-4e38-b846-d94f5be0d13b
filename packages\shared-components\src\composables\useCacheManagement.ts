import { ref, computed, watch } from 'vue'

// Mock types for build compatibility
export interface CacheOptions {
  ttl?: number
  tags?: string[]
  priority?: number
  enableCompression?: boolean
}

// Mock dataCacheService for build compatibility
const dataCacheService = {
  get: async <T>(key: string, fetcher: () => Promise<T>, options?: CacheOptions): Promise<T> => {
    return await fetcher()
  },
  set: (key: string, value: any, options?: CacheOptions): void => {},
  has: (key: string): boolean => false,
  delete: (key: string): boolean => true,
  clear: (): void => {},
  keys: (): string[] => [],
  stats: () => ({ size: 0, hits: 0, misses: 0 }),
  clearByPattern: (pattern: string): void => {},
  invalidateByTags: (tags: string[]): void => {},
  cleanup: (): void => {},
  getStatus: () => ({
    size: 0,
    memoryUsage: 0,
    oldestEntry: null,
    newestEntry: null
  })
}

export interface CacheManagementOptions {
  /**
   * Default TTL for cache entries (ms)
   */
  defaultTTL?: number
  
  /**
   * Enable automatic cache cleanup
   */
  enableAutoCleanup?: boolean
  
  /**
   * Cleanup interval (ms)
   */
  cleanupInterval?: number
  
  /**
   * Maximum cache size (number of entries)
   */
  maxCacheSize?: number
  
  /**
   * Enable cache statistics
   */
  enableStats?: boolean
  
  /**
   * Cache key prefix for this component/context
   */
  keyPrefix?: string
  
  /**
   * Enable cache persistence across sessions
   */
  enablePersistence?: boolean
  
  /**
   * Storage key for persistent cache
   */
  persistenceKey?: string
}

export interface CacheStats {
  totalEntries: number
  hitRate: number
  missRate: number
  totalHits: number
  totalMisses: number
  totalRequests: number
  averageResponseTime: number
  cacheSize: number
  oldestEntry: number | null
  newestEntry: number | null
}

export function useCacheManagement(options: CacheManagementOptions = {}) {
  const {
    defaultTTL = 5 * 60 * 1000, // 5 minutes
    enableAutoCleanup = true,
    cleanupInterval = 60 * 1000, // 1 minute
    maxCacheSize = 1000,
    enableStats = true,
    keyPrefix = '',
    enablePersistence = false,
    persistenceKey = 'cache-management'
  } = options

  // State
  const isInitialized = ref(false)
  const cacheStats = ref<CacheStats>({
    totalEntries: 0,
    hitRate: 0,
    missRate: 0,
    totalHits: 0,
    totalMisses: 0,
    totalRequests: 0,
    averageResponseTime: 0,
    cacheSize: 0,
    oldestEntry: null,
    newestEntry: null
  })
  
  const recentOperations = ref<Array<{
    key: string
    operation: 'get' | 'set' | 'delete' | 'clear'
    timestamp: number
    hit?: boolean
    responseTime?: number
  }>>([])

  // Timers
  let cleanupTimer: NodeJS.Timeout | null = null
  let statsUpdateTimer: NodeJS.Timeout | null = null

  // Computed
  const cacheHealth = computed(() => {
    const stats = cacheStats.value
    const hitRate = stats.hitRate
    const responseTime = stats.averageResponseTime
    
    if (hitRate > 80 && responseTime < 100) return 'excellent'
    if (hitRate > 60 && responseTime < 500) return 'good'
    if (hitRate > 40 && responseTime < 1000) return 'fair'
    return 'poor'
  })

  const cacheEfficiency = computed(() => {
    const stats = cacheStats.value
    if (stats.totalRequests === 0) return 0
    
    // Calculate efficiency based on hit rate and response time
    const hitRateScore = stats.hitRate / 100
    const responseTimeScore = Math.max(0, 1 - (stats.averageResponseTime / 1000))
    
    return Math.round((hitRateScore * 0.7 + responseTimeScore * 0.3) * 100)
  })

  /**
   * Initialize cache management
   */
  const initialize = async () => {
    if (isInitialized.value) return

    // Load persistent cache if enabled
    if (enablePersistence) {
      await loadPersistentCache()
    }

    // Start auto cleanup if enabled
    if (enableAutoCleanup) {
      startAutoCleanup()
    }

    // Start stats updates if enabled
    if (enableStats) {
      startStatsUpdates()
    }

    isInitialized.value = true
    console.log('Cache management initialized')
  }

  /**
   * Get data from cache with enhanced tracking
   */
  const get = async <T>(
    key: string, 
    fetcher: () => Promise<T>, 
    options?: CacheOptions
  ): Promise<T> => {
    const fullKey = keyPrefix ? `${keyPrefix}:${key}` : key
    const startTime = Date.now()
    
    try {
      // Check if data exists in cache
      const hasData = dataCacheService.has(fullKey)
      
      // Get data (will use cache or fetch)
      const result = await dataCacheService.get(fullKey, fetcher, {
        ttl: defaultTTL,
        ...options
      })
      
      const responseTime = Date.now() - startTime
      
      // Track operation
      trackOperation(fullKey, 'get', hasData, responseTime)
      
      return result
    } catch (error) {
      const responseTime = Date.now() - startTime
      trackOperation(fullKey, 'get', false, responseTime)
      throw error
    }
  }

  /**
   * Set data in cache
   */
  const set = <T>(key: string, data: T, options?: CacheOptions): void => {
    const fullKey = keyPrefix ? `${keyPrefix}:${key}` : key
    
    dataCacheService.set(fullKey, data, {
      ttl: defaultTTL,
      ...options
    })
    
    trackOperation(fullKey, 'set')
    
    // Check cache size limits
    if (maxCacheSize > 0) {
      enforceCacheSize()
    }
  }

  /**
   * Delete data from cache
   */
  const remove = (key: string): boolean => {
    const fullKey = keyPrefix ? `${keyPrefix}:${key}` : key
    const result = dataCacheService.delete(fullKey)
    
    trackOperation(fullKey, 'delete')
    
    return result
  }

  /**
   * Clear all cache entries with optional pattern
   */
  const clear = (pattern?: string): void => {
    if (pattern) {
      const fullPattern = keyPrefix ? `${keyPrefix}:${pattern}` : pattern
      dataCacheService.clearByPattern(fullPattern)
    } else if (keyPrefix) {
      dataCacheService.clearByPattern(`${keyPrefix}:*`)
    } else {
      dataCacheService.clear()
    }
    
    trackOperation(pattern || 'all', 'clear')
  }

  /**
   * Invalidate cache entries by tags
   */
  const invalidateByTags = (tags: string[]): void => {
    dataCacheService.invalidateByTags(tags)
    trackOperation(`tags:${tags.join(',')}`, 'delete')
  }

  /**
   * Preload data into cache
   */
  const preload = async <T>(
    key: string, 
    fetcher: () => Promise<T>, 
    options?: CacheOptions
  ): Promise<void> => {
    const fullKey = keyPrefix ? `${keyPrefix}:${key}` : key
    
    if (!dataCacheService.has(fullKey)) {
      try {
        const data = await fetcher()
        set(key, data, options)
      } catch (error) {
        console.warn(`Failed to preload cache for key ${key}:`, error)
      }
    }
  }

  /**
   * Track cache operation for statistics
   */
  const trackOperation = (
    key: string, 
    operation: 'get' | 'set' | 'delete' | 'clear', 
    hit?: boolean, 
    responseTime?: number
  ) => {
    if (!enableStats) return

    const timestamp = Date.now()
    
    // Add to recent operations
    recentOperations.value.unshift({
      key,
      operation,
      timestamp,
      hit,
      responseTime
    })
    
    // Keep only last 100 operations
    if (recentOperations.value.length > 100) {
      recentOperations.value = recentOperations.value.slice(0, 100)
    }
    
    // Update stats for get operations
    if (operation === 'get' && hit !== undefined) {
      cacheStats.value.totalRequests++
      
      if (hit) {
        cacheStats.value.totalHits++
      } else {
        cacheStats.value.totalMisses++
      }
      
      // Recalculate rates
      cacheStats.value.hitRate = (cacheStats.value.totalHits / cacheStats.value.totalRequests) * 100
      cacheStats.value.missRate = (cacheStats.value.totalMisses / cacheStats.value.totalRequests) * 100
      
      // Update average response time
      if (responseTime !== undefined) {
        const totalResponseTime = cacheStats.value.averageResponseTime * (cacheStats.value.totalRequests - 1)
        cacheStats.value.averageResponseTime = (totalResponseTime + responseTime) / cacheStats.value.totalRequests
      }
    }
  }

  /**
   * Update cache statistics
   */
  const updateStats = () => {
    if (!enableStats) return

    const status = dataCacheService.getStatus()
    cacheStats.value.totalEntries = status.size
    cacheStats.value.cacheSize = status.memoryUsage
    cacheStats.value.oldestEntry = status.oldestEntry
    cacheStats.value.newestEntry = status.newestEntry
  }

  /**
   * Start automatic cleanup
   */
  const startAutoCleanup = () => {
    cleanupTimer = setInterval(() => {
      dataCacheService.cleanup()
    }, cleanupInterval)
  }

  /**
   * Start statistics updates
   */
  const startStatsUpdates = () => {
    statsUpdateTimer = setInterval(updateStats, 5000) // Update every 5 seconds
  }

  /**
   * Enforce cache size limits
   */
  const enforceCacheSize = () => {
    const status = dataCacheService.getStatus()
    if (status.size > maxCacheSize) {
      // Remove oldest entries
      const entriesToRemove = status.size - maxCacheSize
      console.log(`Cache size limit exceeded, removing ${entriesToRemove} oldest entries`)
      
      // This would need to be implemented in the cache service
      // dataCacheService.removeOldest(entriesToRemove)
    }
  }

  /**
   * Load persistent cache from storage
   */
  const loadPersistentCache = async () => {
    try {
      const saved = localStorage.getItem(persistenceKey)
      if (saved) {
        const data = JSON.parse(saved)
        // Restore cache entries that haven't expired
        // This would need to be implemented in the cache service
        console.log('Loaded persistent cache data')
      }
    } catch (error) {
      console.warn('Failed to load persistent cache:', error)
    }
  }

  /**
   * Save cache to persistent storage
   */
  const savePersistentCache = () => {
    if (!enablePersistence) return

    try {
      const status = dataCacheService.getStatus()
      localStorage.setItem(persistenceKey, JSON.stringify({
        timestamp: Date.now(),
        stats: cacheStats.value,
        // Add cache entries if needed
      }))
    } catch (error) {
      console.warn('Failed to save persistent cache:', error)
    }
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
      cleanupTimer = null
    }
    
    if (statsUpdateTimer) {
      clearInterval(statsUpdateTimer)
      statsUpdateTimer = null
    }
    
    if (enablePersistence) {
      savePersistentCache()
    }
  }

  // Lifecycle yönetimi build için devre dışı - component'lar initialize() çağırmalı
  // Save persistent cache on stats changes
  if (enablePersistence) {
    watch(cacheStats, savePersistentCache, { deep: true })
  }

  return {
    // State
    isInitialized,
    cacheStats,
    recentOperations,
    cacheHealth,
    cacheEfficiency,

    // Actions
    get,
    set,
    remove,
    clear,
    invalidateByTags,
    preload,
    updateStats,
    cleanup
  }
}
