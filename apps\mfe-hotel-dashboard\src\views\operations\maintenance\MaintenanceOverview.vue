<template>
  <div class="p-8">
    <!-- Header Section -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Bakım - <PERSON><PERSON></h1>
      <p class="text-gray-600 dark:text-gray-400">Bakım departmanının genel durumu ve performans göstergeleri</p>
    </div>

    <!-- Quick Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="stat in quickStats"
        :key="stat.title"
        class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-navy-700 dark:text-white mt-2">{{ stat.value }}</p>
            <p v-if="stat.change" :class="[
              'text-sm mt-1',
              stat.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
            ]">
              {{ stat.change }}
            </p>
          </div>
          <div :class="[
            'w-12 h-12 rounded-xl flex items-center justify-center',
            stat.iconBg
          ]">
            <component :is="stat.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Analytics Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Maintenance Requests Chart -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Bakım Talepleri</h3>
          <select class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-800 text-gray-900 dark:text-white text-sm">
            <option value="week">Bu Hafta</option>
            <option value="month">Bu Ay</option>
            <option value="quarter">Bu Çeyrek</option>
          </select>
        </div>
        <div class="space-y-4">
          <div
            v-for="request in maintenanceRequests"
            :key="request.type"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div :class="[
                'w-3 h-3 rounded-full',
                request.color
              ]"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ request.type }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-navy-700 dark:text-white">{{ request.count }}</span>
              <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                <div 
                  :class="request.color"
                  class="h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${request.percentage}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Staff Performance -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Personel Performansı</h3>
          <button class="text-brand-500 hover:text-brand-600 text-sm font-medium">
            Tümünü Gör
          </button>
        </div>
        <div class="space-y-4">
          <div
            v-for="staff in topPerformers"
            :key="staff.id || staff.name"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-navy-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                {{ (staff as any).name.charAt(0) }}
              </div>
              <div>
                <p class="text-sm font-medium text-navy-700 dark:text-white">{{ (staff as any).name }}</p>
                <p class="text-xs text-gray-600 dark:text-gray-400">{{ (staff as any).specialization }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-bold text-green-500">{{ (staff as any).completedTasks }}</p>
              <p class="text-xs text-gray-600 dark:text-gray-400">Tamamlanan</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Maintenance Activities -->
      <div class="lg:col-span-2 bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Son Bakım Faaliyetleri</h3>
          <button class="text-brand-500 hover:text-brand-600 text-sm font-medium">
            Tümünü Gör
          </button>
        </div>
        <div class="space-y-4">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-navy-800 rounded-lg"
          >
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0',
              activity.statusColor
            ]">
              <component :is="activity.icon" class="w-4 h-4 text-white" />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-navy-700 dark:text-white">{{ activity.title }}</p>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ activity.description }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ activity.room }}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ activity.assignedTo }}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ activity.time }}</span>
              </div>
            </div>
            <div :class="[
              'px-2 py-1 rounded-full text-xs font-medium',
              activity.statusBadge
            ]">
              {{ activity.status }}
            </div>
          </div>
        </div>
      </div>

      <!-- Urgent Items -->
      <div class="bg-white dark:bg-navy-700 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-navy-700 dark:text-white">Acil Durumlar</h3>
          <span class="text-xs bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 px-2 py-1 rounded-full">
            {{ urgentItems.length }} Acil
          </span>
        </div>
        <div class="space-y-3">
          <div
            v-for="item in urgentItems"
            :key="item.id"
            class="p-3 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/20"
          >
            <div class="flex items-start justify-between">
              <div>
                <p class="text-sm font-medium text-red-800 dark:text-red-200">{{ item.title }}</p>
                <p class="text-xs text-red-600 dark:text-red-300 mt-1">{{ item.location }}</p>
              </div>
              <span class="text-xs text-red-600 dark:text-red-300">{{ item.time }}</span>
            </div>
            <p class="text-xs text-red-700 dark:text-red-300 mt-2">{{ item.description }}</p>
            <div class="flex items-center justify-between mt-3">
              <span :class="[
                'px-2 py-1 rounded text-xs font-medium',
                item.priorityClass
              ]">
                {{ item.priority }}
              </span>
              <button class="text-xs text-red-600 dark:text-red-300 hover:text-red-800 dark:hover:text-red-100 font-medium">
                Atama Yap
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { supabase } from '@hotelexia/shared-supabase-client'
import type { Database } from '@hotelexia/shared-supabase-client'
import { 
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  UserGroupIcon,
  BuildingOfficeIcon,
  CogIcon,
  ShieldExclamationIcon
} from '@heroicons/vue/24/outline'

type MaintenanceTask = Database['public']['Tables']['maintenance_tasks']['Row'] & {
  rooms: { room_number: string } | null;
  profiles: { full_name: string } | null;
}

const tasks = ref<MaintenanceTask[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

const fetchTasks = async () => {
  try {
    loading.value = true
    const { data, error: fetchError } = await (supabase as any)
      .from('maintenance_tasks')
      .select(`
        *,
        rooms ( room_number ),
        profiles ( full_name )
      `)
      .order('created_at', { ascending: false })
      
    if (fetchError) throw fetchError
    tasks.value = data || []
  } catch (e: any) {
    error.value = `Bakım görevleri yüklenemedi: ${e.message}`
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchTasks()
})


// Computed properties
const quickStats = computed(() => {
  const totalRequests = tasks.value.length
  const completedToday = tasks.value.filter(t => 
    t.status === 'COMPLETED' && 
    new Date(t.created_at).toDateString() === new Date().toDateString()
  ).length
  const pendingRequests = tasks.value.filter(t => t.status === 'PENDING').length
  
  // This is a placeholder as we don't have live staff status
  const availableStaff = 5 

  return [
    {
      title: 'Toplam Talepler',
      value: totalRequests,
      change: '+8%',
      icon: WrenchScrewdriverIcon,
      iconBg: 'bg-blue-500'
    },
    {
      title: 'Bugün Tamamlanan',
      value: completedToday,
      change: '+12%',
      icon: CheckCircleIcon,
      iconBg: 'bg-green-500'
    },
    {
      title: 'Bekleyen Talepler',
      value: pendingRequests,
      change: '-5%',
      icon: ClockIcon,
      iconBg: 'bg-orange-500'
    },
    {
      title: 'Müsait Personel',
      value: availableStaff,
      change: '+2',
      icon: UserGroupIcon,
      iconBg: 'bg-purple-500'
    }
  ]
})

const maintenanceRequests = computed(() => {
  const requestCounts = tasks.value.reduce((acc, task) => {
    const type = task.priority || 'Unknown' // Using priority as type for visualization
    if (!acc[type]) {
      acc[type] = 0
    }
    acc[type]++
    return acc
  }, {} as Record<string, number>)

  const total = tasks.value.length
  const colors: { [key: string]: string } = {
    'High': 'bg-red-500',
    'Medium': 'bg-yellow-500',
    'Low': 'bg-blue-500',
    'Unknown': 'bg-gray-500',
  }

  return Object.entries(requestCounts).map(([type, count]) => ({
    type,
    count,
    percentage: total > 0 ? ((count as any) / total) * 100 : 0,
    color: colors[type] || 'bg-gray-500'
  }))
})

interface StaffPerformer {
  id: string | null;
  name: string;
  specialization: string;
  completedTasks: number;
}

const topPerformers = computed(() => {
    const staffPerformance = tasks.value
    .filter(task => task.status === 'COMPLETED' && task.profiles?.full_name)
    .reduce((acc, task) => {
        const name = task.profiles!.full_name!
        if (!acc[name]) {
        acc[name] = {
            id: task.assigned_to,
            name: name,
            specialization: 'Teknisyen', // Placeholder
            completedTasks: 0
        };
        }
        acc[name].completedTasks++;
        return acc;
    }, {} as Record<string, StaffPerformer>);

    return Object.values(staffPerformance)
        .sort((a, b) => b.completedTasks - a.completedTasks)
        .slice(0, 3);
});


const recentActivities = computed(() => {
  const statusMap: { [key: string]: any } = {
    'COMPLETED': { icon: CheckCircleIcon, color: 'bg-green-500', badge: 'bg-green-100 text-green-800' },
    'IN_PROGRESS': { icon: CogIcon, color: 'bg-blue-500', badge: 'bg-blue-100 text-blue-800' },
    'PENDING': { icon: ClockIcon, color: 'bg-orange-500', badge: 'bg-orange-100 text-orange-800' },
  }

  return tasks.value.slice(0, 5).map(task => {
    const statusInfo = statusMap[task.status!] || { icon: ExclamationTriangleIcon, color: 'bg-gray-500', badge: 'bg-gray-100 text-gray-800' }
    return {
      id: task.id,
      title: task.title,
      description: task.description || 'No description',
      room: `Oda: ${task.rooms?.room_number || 'N/A'}`,
      assignedTo: `Atanan: ${task.profiles?.full_name || 'N/A'}`,
      time: new Date(task.created_at).toLocaleTimeString('tr-TR'),
      status: task.status,
      icon: statusInfo.icon,
      statusColor: statusInfo.color,
      statusBadge: statusInfo.badge
    }
  })
})

const urgentItems = computed(() => {
  return tasks.value
    .filter(task => task.priority === 'High' && task.status !== 'COMPLETED')
    .slice(0, 3)
    .map(task => ({
      id: task.id,
      title: task.title,
      location: `Oda: ${task.rooms?.room_number || 'N/A'}`,
      time: new Date(task.created_at).toLocaleTimeString('tr-TR'),
      description: task.description || 'No description',
      priority: task.priority,
      priorityClass: 'bg-red-200 text-red-800 dark:bg-red-800/30 dark:text-red-200'
    }))
})

// CRUD Operations for Maintenance Tasks
const updateTaskStatus = async (taskId: string, newStatus: string) => {
  try {
    const { error } = await (supabase as any)
      .from('maintenance_tasks')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)

    if (error) throw error

    // Refresh tasks
    await fetchTasks()
    console.log(`Task ${taskId} status updated to ${newStatus}`)
  } catch (error: any) {
    console.error('Error updating task status:', error.message)
  }
}

const assignTaskToStaff = async (taskId: string, staffId: string) => {
  try {
    const { error } = await (supabase as any)
      .from('maintenance_tasks')
      .update({
        assigned_to: staffId,
        status: 'IN_PROGRESS',
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)

    if (error) throw error

    // Refresh tasks
    await fetchTasks()
    console.log(`Task ${taskId} assigned to staff ${staffId}`)
  } catch (error: any) {
    console.error('Error assigning task:', error.message)
  }
}

const updateTaskPriority = async (taskId: string, newPriority: string) => {
  try {
    const { error } = await (supabase as any)
      .from('maintenance_tasks')
      .update({
        priority: newPriority,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId)

    if (error) throw error

    // Refresh tasks
    await fetchTasks()
    console.log(`Task ${taskId} priority updated to ${newPriority}`)
  } catch (error: any) {
    console.error('Error updating task priority:', error.message)
  }
}
</script> 