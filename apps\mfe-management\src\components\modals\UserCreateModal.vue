<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-navy-700 dark:text-white"><PERSON><PERSON></h2>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <XMarkIcon class="h-6 w-6" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Email -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            E-posta Adresi *
          </label>
          <input
            v-model="form.email"
            type="email"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
            placeholder="<EMAIL>"
          />
        </div>

        <!-- Full Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ad Soyad *
          </label>
          <input
            v-model="form.full_name"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
            placeholder="Kullanıcının tam adı"
          />
        </div>

        <!-- Phone -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Telefon
          </label>
          <input
            v-model="form.phone"
            type="tel"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
            placeholder="+90 555 123 45 67"
          />
        </div>

        <!-- Role -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Rol *
          </label>
          <select
            v-model="form.role"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
          >
            <option value="">Rol seçin</option>
            <option value="SUPER_ADMIN">Süper Admin</option>
            <option value="HOTEL_ADMIN">Otel Yöneticisi</option>
            <option value="MANAGER">Müdür</option>
            <option value="SUPERVISOR">Süpervizör</option>
            <option value="STAFF">Personel</option>
            <option value="GUEST">Misafir</option>
          </select>
        </div>

        <!-- Hotel (only for hotel roles) -->
        <div v-if="form.role === 'HOTEL_ADMIN' || form.role === 'MANAGER' || form.role === 'SUPERVISOR' || form.role === 'STAFF'">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Otel *
          </label>
          <select
            v-model="form.hotel_id"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
          >
            <option value="">Otel seçin</option>
            <option v-for="hotel in hotels" :key="hotel.id" :value="hotel.id">
              {{ hotel.name }}
            </option>
          </select>
        </div>

        <!-- Temporary Password -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Geçici Şifre *
          </label>
          <div class="relative">
            <input
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              required
              minlength="6"
              class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:bg-navy-700 dark:text-white"
              placeholder="En az 6 karakter"
            />
            <button
              type="button"
              @click="showPassword = !showPassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <EyeIcon v-if="!showPassword" class="h-5 w-5 text-gray-400" />
              <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
            </button>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Kullanıcı ilk girişte şifresini değiştirmek zorunda kalacak
          </p>
        </div>

        <!-- Send Welcome Email -->
        <div class="flex items-center">
          <input
            v-model="form.send_welcome_email"
            type="checkbox"
            id="send_welcome_email"
            class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
          />
          <label for="send_welcome_email" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Hoş geldin e-postası gönder
          </label>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
          <p class="text-sm text-red-600 dark:text-red-400">{{ error }}</p>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            İptal
          </button>
          <button
            type="submit"
            :disabled="isLoading"
            class="px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            <span v-if="isLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            <span>{{ isLoading ? 'Oluşturuluyor...' : 'Kullanıcı Oluştur' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { XMarkIcon, EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline'

// Props & Emits
defineEmits<{
  close: []
  created: [user: any]
}>()

// Types
interface Hotel {
  id: string
  name: string
}

interface CreateUserForm {
  email: string
  full_name: string
  phone: string
  role: string
  hotel_id: string
  password: string
  send_welcome_email: boolean
}

// State
const form = ref<CreateUserForm>({
  email: '',
  full_name: '',
  phone: '',
  role: '',
  hotel_id: '',
  password: '',
  send_welcome_email: true
})

const hotels = ref<Hotel[]>([])
const isLoading = ref(false)
const error = ref('')
const showPassword = ref(false)

// Methods
const loadHotels = async () => {
  try {
    // TODO: Implement Supabase integration
    console.log('Loading hotels for user creation...')
  } catch (err) {
    console.error('Error loading hotels:', err)
  }
}

const handleSubmit = async () => {
  isLoading.value = true
  error.value = ''

  try {
    // TODO: Implement Supabase integration
    console.log('Creating user:', form.value)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Emit success
    // $emit('created', newUser)
  } catch (err: any) {
    error.value = err.message || 'Kullanıcı oluşturulurken bir hata oluştu'
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadHotels()
})
</script>
