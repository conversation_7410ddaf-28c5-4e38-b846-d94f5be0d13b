<template>
  <div class="bg-white dark:bg-navy-800 rounded-xl shadow-card p-6 border border-gray-200 dark:border-navy-700">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-bold text-navy-700 dark:text-white">
        <PERSON><PERSON><PERSON><PERSON><PERSON> İşlemler
      </h3>
      <Lightning class="w-5 h-5 text-brand-500" />
    </div>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <button
        v-for="action in quickActions"
        :key="action.id"
        @click="handleAction(action.route)"
        class="group flex items-center p-4 bg-gray-50 dark:bg-navy-700 rounded-lg hover:bg-brand-50 dark:hover:bg-brand-900/20 transition-all duration-200 border border-transparent hover:border-brand-200 dark:hover:border-brand-700"
      >
        <div class="flex items-center justify-center w-10 h-10 rounded-lg mr-3" :class="action.iconBg">
          <component :is="action.icon" class="w-5 h-5" :class="action.iconColor" />
        </div>
        <div class="flex flex-col items-start">
          <span class="text-sm font-semibold text-gray-900 dark:text-white group-hover:text-brand-600 dark:group-hover:text-brand-400">
            {{ action.title }}
          </span>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ action.description }}
          </span>
        </div>
        <ArrowRightIcon class="w-4 h-4 text-gray-400 group-hover:text-brand-500 ml-auto transition-colors duration-200" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  PlusIcon, 
  CalendarIcon, 
  TagIcon, 
  CogIcon,
  ArrowRightIcon,
  BoltIcon as Lightning
} from '@heroicons/vue/24/outline'

const router = useRouter()

interface QuickAction {
  id: string
  title: string
  description: string
  icon: any
  iconBg: string
  iconColor: string
  route: string
}

const quickActions: QuickAction[] = [
  {
    id: 'add-menu-item',
    title: 'Yeni Menü Öğesi',
    description: 'Oda servisi menüsüne ekle',
    icon: PlusIcon,
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600 dark:text-green-400',
    route: '/hotel/content/room-service/management'
  },
  {
    id: 'create-activity',
    title: 'Yeni Aktivite',
    description: 'Otel aktivitesi oluştur',
    icon: CalendarIcon,
    iconBg: 'bg-blue-100 dark:bg-blue-900/20',
    iconColor: 'text-blue-600 dark:text-blue-400',
    route: '/hotel/content/activities/management'
  },
  {
    id: 'add-promotion',
    title: 'Yeni Promosyon',
    description: 'İndirim kampanyası tanımla',
    icon: TagIcon,
    iconBg: 'bg-orange-100 dark:bg-orange-900/20',
    iconColor: 'text-orange-600 dark:text-orange-400',
    route: '/hotel/content/room-service/management'
  },
  {
    id: 'manage-services',
    title: 'Hizmetleri Yönet',
    description: 'Misafir hizmetlerini düzenle',
    icon: CogIcon,
    iconBg: 'bg-purple-100 dark:bg-purple-900/20',
    iconColor: 'text-purple-600 dark:text-purple-400',
    route: '/hotel/content/guest-services/management'
  }
]

const handleAction = (route: string) => {
  router.push(route)
}
</script> 