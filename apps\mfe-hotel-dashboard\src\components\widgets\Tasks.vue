<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <h4 class="text-lg font-bold text-navy-700 dark:text-white"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
      <div class="text-sm text-gray-600 dark:text-gray-400">
        {{ completedTasksCount }}/{{ tasks.length }}
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON>e</span>
        <span class="text-sm font-medium text-navy-700 dark:text-white">{{ progressPercentage }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          class="bg-gradient-to-r from-brand-500 to-brand-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>

    <!-- Tasks List -->
    <div class="space-y-3">
      <div 
        v-for="task in tasks" 
        :key="task.id"
        class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-navy-700 transition-colors group"
      >
        <input 
          type="checkbox" 
          :checked="task.completed"
          @change="toggleTask(task.id)"
          class="rounded border-gray-300 text-brand-500 focus:ring-brand-500"
        >
        <div class="flex-1">
          <p 
            class="text-sm font-medium transition-colors"
            :class="task.completed ? 'text-gray-400 dark:text-gray-500 line-through' : 'text-navy-700 dark:text-white'"
          >
            {{ task.title }}
          </p>
          <p 
            class="text-xs mt-1"
            :class="task.completed ? 'text-gray-300 dark:text-gray-600' : 'text-gray-500 dark:text-gray-400'"
          >
            {{ task.description }}
          </p>
        </div>
        <div class="flex items-center space-x-1">
          <span 
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
            :class="getPriorityClass(task.priority)"
          >
            {{ task.priority }}
          </span>
          <button class="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-all">
            <TrashIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- Add New Task -->
    <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
      <button 
        @click="showAddTask = !showAddTask"
        class="w-full text-left text-sm text-brand-500 hover:text-brand-600 font-medium"
      >
        + Yeni Görev Ekle
      </button>
      
      <!-- Add Task Form -->
      <div v-if="showAddTask" class="mt-3 space-y-2">
        <input 
          v-model="newTaskTitle"
          type="text" 
          placeholder="Görev başlığı..."
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-navy-700 text-navy-700 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500"
        >
        <div class="flex space-x-2">
          <button 
            @click="addTask"
            class="px-3 py-1 bg-brand-500 text-white text-xs rounded-lg hover:bg-brand-600 transition-colors"
          >
            Ekle
          </button>
          <button 
            @click="cancelAddTask"
            class="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
          >
            İptal
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { TrashIcon } from '@heroicons/vue/24/outline'
import { useDashboardStore } from '@/stores/dashboardStore'

const dashboardStore = useDashboardStore()
const showAddTask = ref(false)
const newTaskTitle = ref('')

interface Task {
  id: string
  title: string
  description: string
  priority: 'Yüksek' | 'Orta' | 'Düşük'
  completed: boolean
}

const tasks = ref<Task[]>([
  {
    id: '1',
    title: 'Sabah toplantısına katıl',
    description: 'Departman liderleri ile günlük planlama',
    priority: 'Yüksek',
    completed: true
  },
  {
    id: '2',
    title: 'Kat hizmetleri raporunu gözden geçir',
    description: 'Dün tamamlanan işleri kontrol et',
    priority: 'Orta',
    completed: false
  },
  {
    id: '3',
    title: 'Yeni personel oryantasyonu',
    description: 'Saat 14:00\'te resepsiyon eğitimi',
    priority: 'Yüksek',
    completed: false
  },
  {
    id: '4',
    title: 'Maliyet raporunu hazırla',
    description: 'Bu ay için harcama analizi',
    priority: 'Düşük',
    completed: true
  },
  {
    id: '5',
    title: 'Müşteri geri bildirimlerini incele',
    description: 'Son hafta yorumlarını değerlendir',
    priority: 'Orta',
    completed: false
  }
])

// Computed
const completedTasksCount = computed(() => 
  tasks.value.filter(task => task.completed).length
)

const progressPercentage = computed(() => 
  Math.round((completedTasksCount.value / tasks.value.length) * 100)
)

// Methods
const toggleTask = (taskId: string) => {
  const task = tasks.value.find(t => t.id === taskId)
  if (task) {
    task.completed = !task.completed
  }
}

const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'Yüksek':
      return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
    case 'Orta':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
    case 'Düşük':
      return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
  }
}

const addTask = () => {
  if (newTaskTitle.value.trim()) {
    const newTask: Task = {
      id: Date.now().toString(),
      title: newTaskTitle.value.trim(),
      description: 'Yeni görev',
      priority: 'Orta',
      completed: false
    }
    tasks.value.push(newTask)
    cancelAddTask()
  }
}

const cancelAddTask = () => {
  showAddTask.value = false
  newTaskTitle.value = ''
}
</script>

<style scoped>
/* Task specific styles */
</style> 