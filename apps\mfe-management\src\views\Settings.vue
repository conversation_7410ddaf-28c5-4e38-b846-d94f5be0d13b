<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">Platform Ayarları</h1>
      <p class="text-gray-600 dark:text-gray-400">Hotelexia platformunun genel ayarlarını yönetin</p>
    </div>

    <!-- Settings Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Gene<PERSON> Ayarlar -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4 flex items-center">
          <Cog6ToothIcon class="h-5 w-5 mr-2 text-brand-500" />
          Gene<PERSON> Ayarlar
        </h2>
        
        <div class="space-y-4">
          <!-- Platform Maintenance Mode -->
          <div class="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-white">Platform Bakım Modu</label>
              <p class="text-xs text-gray-500 dark:text-gray-400">Platformu geçici olarak devre dışı bırakır</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="settings.maintenanceMode" class="sr-only peer">
              <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
            </label>
          </div>

          <!-- Auto Hotel Approval -->
          <div class="flex items-center justify-between py-3 border-b border-gray-200 dark:border-gray-700">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-white">Yeni Otel Kayıtlarını Otomatik Onayla</label>
              <p class="text-xs text-gray-500 dark:text-gray-400">Yeni kayıtlar manuel onay gerektirmez</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="settings.autoApproveHotels" class="sr-only peer">
              <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
            </label>
          </div>

          <!-- Email Notifications -->
          <div class="flex items-center justify-between py-3">
            <div>
              <label class="text-sm font-medium text-gray-900 dark:text-white">E-posta Bildirimleri</label>
              <p class="text-xs text-gray-500 dark:text-gray-400">Kritik sistem olayları için e-posta gönder</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="settings.emailNotifications" class="sr-only peer">
              <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- API Entegrasyonları -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4 flex items-center">
          <KeyIcon class="h-5 w-5 mr-2 text-brand-500" />
          API Entegrasyonları
        </h2>
        
        <div class="space-y-4">
          <!-- Stripe API Key -->
          <div>
            <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">Stripe API Key</label>
            <input
              type="password"
              v-model="settings.stripeApiKey"
              placeholder="sk_test_..."
              class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
          </div>

          <!-- SMS Provider API Key -->
          <div>
            <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">SMS Sağlayıcı API Key</label>
            <input
              type="password"
              v-model="settings.smsApiKey"
              placeholder="SMS API anahtarınız"
              class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
          </div>

          <!-- Email Service API Key -->
          <div>
            <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">E-posta Servis API Key</label>
            <input
              type="password"
              v-model="settings.emailApiKey"
              placeholder="SendGrid, Mailgun vb. API key"
              class="w-full px-3 py-2 bg-gray-50 dark:bg-navy-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Bildirimler Section -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4 flex items-center">
        <BellIcon class="h-5 w-5 mr-2 text-brand-500" />
        Bildirimler
      </h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- System Alerts -->
        <div class="flex items-center justify-between py-3 border border-gray-200 dark:border-gray-700 rounded-lg px-4">
          <div>
            <label class="text-sm font-medium text-gray-900 dark:text-white">Sistem Uyarıları</label>
            <p class="text-xs text-gray-500 dark:text-gray-400">Sistem durumu değişiklikleri</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" v-model="settings.systemAlerts" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
          </label>
        </div>

        <!-- Hotel Registration -->
        <div class="flex items-center justify-between py-3 border border-gray-200 dark:border-gray-700 rounded-lg px-4">
          <div>
            <label class="text-sm font-medium text-gray-900 dark:text-white">Yeni Kayıtlar</label>
            <p class="text-xs text-gray-500 dark:text-gray-400">Yeni otel kayıtları</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" v-model="settings.newRegistrations" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
          </label>
        </div>

        <!-- Payment Issues -->
        <div class="flex items-center justify-between py-3 border border-gray-200 dark:border-gray-700 rounded-lg px-4">
          <div>
            <label class="text-sm font-medium text-gray-900 dark:text-white">Ödeme Sorunları</label>
            <p class="text-xs text-gray-500 dark:text-gray-400">Başarısız ödemeler</p>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" v-model="settings.paymentIssues" class="sr-only peer">
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 dark:peer-focus:ring-brand-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-brand-600"></div>
          </label>
        </div>
      </div>
    </div>

    <!-- Save Button -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-sm font-medium text-gray-900 dark:text-white">Ayarları Kaydet</h3>
          <p class="text-xs text-gray-500 dark:text-gray-400">Değişiklikler anında uygulanacaktır</p>
        </div>
        <button
          @click="saveSettings"
          class="bg-brand-500 hover:bg-brand-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
        >
          Kaydet
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Cog6ToothIcon,
  KeyIcon,
  BellIcon
} from '@heroicons/vue/24/outline'

// Settings reactive state
const settings = ref({
  maintenanceMode: false,
  autoApproveHotels: true,
  emailNotifications: true,
  stripeApiKey: '',
  smsApiKey: '',
  emailApiKey: '',
  systemAlerts: true,
  newRegistrations: true,
  paymentIssues: true
})

// Methods
const saveSettings = () => {
  // TODO: Implement save functionality
  console.log('Saving settings:', settings.value)
  // Here you would typically call an API to save the settings
}
</script> 