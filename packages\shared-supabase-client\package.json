{"name": "@hotelexia/shared-supabase-client", "version": "1.0.0", "description": "Shared Supabase client for Hotelexia MFEs", "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./package.json": "./package.json", "./types": "./src/types.ts", "./dataServices": "./src/dataServices.ts", "./dataTransformers": "./src/dataTransformers.ts", "./CrossMFERealtimeService": "./src/services/CrossMFERealtimeService.ts", "./DataCacheService": "./src/services/DataCacheService.ts"}, "scripts": {"dev": "vite build --watch", "build": "tsc --noEmit && vite build", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/supabase-js": "^2.45.4"}, "peerDependencies": {"vue": "^3.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "vite": "^5.0.0"}, "keywords": ["supabase", "hotelexia", "shared", "client"], "author": "Hotelexia Team", "license": "MIT"}