<template>
  <div class="bg-white rounded-xl shadow-lg p-6">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-semibold text-navy-700">Tale<PERSON></h2>
      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-500">Toplam:</span>
        <span class="bg-brand-100 text-brand-700 px-2 py-1 rounded-full text-sm font-medium">
          {{ serviceRequestStore.statusCounts.total }}
        </span>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="serviceRequestStore.isLoading" class="flex items-center justify-center py-8">
      <div class="flex items-center gap-3 text-gray-500">
        <svg class="animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>Talepler yükleniyor...</span>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="serviceRequestStore.requests.length === 0" class="text-center py-8">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Henüz talep bulunmuyor</h3>
      <p class="text-gray-500">Yukarıdaki formu kullanarak ilk servis talebinizi oluşturabilirsiniz.</p>
    </div>

    <!-- Status Summary -->
    <div v-else class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <div class="text-sm font-medium text-yellow-700">Bekleyen</div>
        <div class="text-2xl font-bold text-yellow-600">{{ serviceRequestStore.statusCounts.pending }}</div>
      </div>
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div class="text-sm font-medium text-blue-700">İşlemde</div>
        <div class="text-2xl font-bold text-blue-600">{{ serviceRequestStore.statusCounts.in_progress }}</div>
      </div>
      <div class="bg-green-50 border border-green-200 rounded-lg p-3">
        <div class="text-sm font-medium text-green-700">Tamamlanan</div>
        <div class="text-2xl font-bold text-green-600">{{ serviceRequestStore.statusCounts.completed }}</div>
      </div>
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
        <div class="text-sm font-medium text-gray-700">Toplam</div>
        <div class="text-2xl font-bold text-gray-600">{{ serviceRequestStore.statusCounts.total }}</div>
      </div>
    </div>

    <!-- Request List -->
    <div v-if="serviceRequestStore.requests.length > 0" class="space-y-4">
      <div 
        v-for="request in serviceRequestStore.requests" 
        :key="request.id"
        class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center gap-3">
            <!-- Request Type Icon -->
            <div class="p-2 rounded-lg" :class="getTypeIconClass(request.request_type)">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path v-if="request.request_type === 'Housekeeping'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                <path v-else-if="request.request_type === 'Maintenance'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path v-else-if="request.request_type === 'Amenities'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            
            <div>
              <h3 class="font-medium text-gray-900">{{ getTypeLabel(request.request_type) }}</h3>
              <p class="text-sm text-gray-500">Oda {{ request.room_number }}</p>
            </div>
          </div>

          <!-- Status Badge -->
          <span class="px-3 py-1 rounded-full text-xs font-medium" :class="getStatusClass(request.status)">
            {{ getStatusLabel(request.status) }}
          </span>
        </div>

        <!-- Description -->
        <div class="mb-3">
          <p class="text-gray-700 text-sm leading-relaxed">{{ request.description }}</p>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-between pt-3 border-t border-gray-100">
          <span class="text-xs text-gray-500">
            {{ formatDate(request.created_at) }}
          </span>
          <span v-if="request.status === 'in_progress'" class="flex items-center gap-1 text-xs text-blue-600">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            İşleniyor
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useServiceRequestStore } from '../stores/serviceRequestStore'

const serviceRequestStore = useServiceRequestStore()

// Helper functions
function getTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    'Housekeeping': 'Kat Hizmetleri',
    'Maintenance': 'Bakım & Tamir',
    'Amenities': 'Olanaklar & Minibar',
    'Other': 'Diğer'
  }
  return labels[type] || type
}

function getTypeIconClass(type: string): string {
  const classes: Record<string, string> = {
    'Housekeeping': 'bg-purple-100 text-purple-600',
    'Maintenance': 'bg-orange-100 text-orange-600',
    'Amenities': 'bg-green-100 text-green-600',
    'Other': 'bg-gray-100 text-gray-600'
  }
  return classes[type] || 'bg-gray-100 text-gray-600'
}

function getStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    'pending': 'Bekliyor',
    'in_progress': 'İşlemde',
    'completed': 'Tamamlandı',
    'canceled': 'İptal'
  }
  return labels[status] || status
}

function getStatusClass(status: string): string {
  const classes: Record<string, string> = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'in_progress': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'canceled': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffDays > 0) {
    return `${diffDays} gün önce`
  } else if (diffHours > 0) {
    return `${diffHours} saat önce`
  } else if (diffMinutes > 0) {
    return `${diffMinutes} dakika önce`
  } else {
    return 'Şimdi'
  }
}
</script> 