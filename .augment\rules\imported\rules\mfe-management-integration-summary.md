---
type: "agent_requested"
---

# MFE: Management Portal Integration Summary
**Latest Plan Reference: `backend-tasklist.mdc` - Phase 2**

## 1. Core Purpose
This MFE is the central command center for platform administrators (`system_admin` role). It provides the tools to manage the entire Hotelexia ecosystem, with a primary focus on hotel lifecycle management.

## 2. Key Backend Integration Tasks (Phase 2)

### 2.1. Hotel Data Management
- **Primary Table:** `public.hotels`
- **Permissions:** Full `ALL` access (Create, Read, Update, Delete) for users with the `system_admin` role, enforced by RLS.
- **Data Seeding:** The backend plan includes seeding initial sample hotels to populate the dashboard on first load.

### 2.2. View & Component Integration
- **Hotel List/Dashboard (`Dashboard.vue`):**
    - **Action:** This view must be connected to the `hotels` table.
    - **Query:** It will perform a `select * from hotels;` to display all hotels in the system.
    - **Access Control:** The entire view will be protected by the App Shell's router guard, ensuring only `system_admin` users can access it.
- **New Hotel Wizard (`HotelSetupWizard.vue`):**
    - **Action:** This component's form will be connected to an `insert` function for the `hotels` table.
    - **Data Mapping:** All form fields (e.g., Hotel Name, Address, City) must map directly to their corresponding columns in the `hotels` table.

## 3. User & Profile Management
- **Primary Table:** `public.profiles`
- **Task:** This portal is responsible for assigning `hotel_manager` and `hotel_staff` roles to specific hotels by updating the `hotel_id` foreign key in the `profiles` table for a given user.

## 4. Security & Access
- **Authentication:** Relies entirely on the centralized `authStore` from the App Shell.
- **Authorization:** All backend operations are protected by Supabase RLS policies that strictly check for the `system_admin` role.

This summary provides a focused overview of the integration tasks for the Management Portal MFE as detailed in the main backend plan.



