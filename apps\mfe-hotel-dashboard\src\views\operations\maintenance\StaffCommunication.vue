<template>
  <div class="min-h-screen bg-gray-50 dark:bg-navy-900 p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        Bakım - Personel İletişim
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Bakım departmanı gerçek zamanlı iletişim merkezi
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-140px)]">
      <!-- Sidebar - Channels & Direct Messages -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm h-full p-4">
          <!-- Online Status -->
          <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-semibold text-navy-700 dark:text-white">
                Çevrimiçi Durum
              </h3>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-green-600 dark:text-green-400">Aktif</span>
              </div>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ onlineCount }} kişi çevrimiçi
            </div>
          </div>

          <!-- Search -->
          <div class="mb-4">
            <input
              v-model="searchTerm"
              type="text"
              placeholder="Kanal veya kişi ara..."
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            />
          </div>

          <!-- Channel List -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Bakım Kanalları
            </h4>
            <div class="space-y-2">
              <div
                v-for="channel in filteredMaintenanceChannels"
                :key="channel.id"
                @click="selectChannel(channel)"
                :class="[
                  'flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors',
                  selectedChannel?.id === channel.id
                    ? 'bg-brand-500 text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-navy-700'
                ]"
              >
                <div class="flex items-center space-x-3">
                  <div class="text-lg">{{ channel.icon }}</div>
                  <div>
                    <div class="font-medium text-sm">{{ channel.name }}</div>
                    <div class="text-xs opacity-75">{{ channel.memberCount }} üye</div>
                  </div>
                </div>
                <div
                  v-if="channel.unreadCount > 0"
                  class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                >
                  {{ channel.unreadCount }}
                </div>
              </div>
            </div>
          </div>

          <!-- Direct Messages -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Bakım Personeli
            </h4>
            <div class="space-y-2">
              <div
                v-for="dm in filteredMaintenanceDirectMessages"
                :key="dm.id"
                @click="selectDirectMessage(dm)"
                :class="[
                  'flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors',
                  selectedDirectMessage?.id === dm.id
                    ? 'bg-brand-500 text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-navy-700'
                ]"
              >
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      {{ dm.name.charAt(0) }}
                    </div>
                    <div
                      v-if="dm.isOnline"
                      class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                    ></div>
                  </div>
                  <div>
                    <div class="font-medium text-sm">{{ dm.name }}</div>
                    <div class="text-xs opacity-75 truncate max-w-20">{{ dm.lastMessage }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-xs opacity-75">{{ dm.lastMessageTime }}</div>
                  <div
                    v-if="dm.unreadCount > 0"
                    class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mt-1"
                  >
                    {{ dm.unreadCount }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Area -->
      <div class="lg:col-span-3">
        <div class="bg-white dark:bg-navy-800 rounded-xl shadow-sm h-full flex flex-col">
          <!-- Chat Header -->
          <div class="p-4 border-b border-gray-200 dark:border-navy-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="text-2xl">
                  {{ selectedChannel?.icon || '🔧' }}
                </div>
                <div>
                  <h3 class="font-semibold text-navy-700 dark:text-white">
                    {{ selectedChannel?.name || selectedDirectMessage?.name || 'Sohbet Seçin' }}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ selectedChannel ? `${selectedChannel.memberCount} üye` : 
                        selectedDirectMessage ? (selectedDirectMessage.isOnline ? 'Çevrimiçi' : 'Çevrimdışı') : 
                        'Bir kanal veya kişi seçin' }}
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
                  title="Arama"
                >
                  <MagnifyingGlassIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
                <button
                  class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-navy-700 transition-colors"
                  title="Ayarlar"
                >
                  <Cog6ToothIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </button>
              </div>
            </div>
          </div>

          <!-- Messages Area -->
          <div class="flex-1 overflow-y-auto p-4 space-y-4" ref="messagesContainer">
            <div v-if="!selectedChannel && !selectedDirectMessage" class="flex items-center justify-center h-full">
              <div class="text-center">
                <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <ChatBubbleLeftRightIcon class="w-12 h-12 text-gray-400" />
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Bir sohbet seçin</h3>
                <p class="text-gray-500 dark:text-gray-400">Sol taraftan bir kanal veya kişi seçerek sohbete başlayın.</p>
              </div>
            </div>
            <div
              v-for="message in currentMessages"
              :key="message.id"
              :class="[
                'flex items-start space-x-3',
                message.isOwn ? 'justify-end' : 'justify-start'
              ]"
            >
              <div v-if="!message.isOwn" class="flex-shrink-0">
                <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  {{ message.sender.charAt(0) }}
                </div>
              </div>
              <div
                :class="[
                  'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                  message.isOwn
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 dark:bg-navy-700 text-gray-900 dark:text-white'
                ]"
              >
                <div v-if="!message.isOwn" class="text-sm font-medium mb-1">
                  {{ message.sender }}
                </div>
                <div class="text-sm">{{ message.text }}</div>
                <div
                  :class="[
                    'text-xs mt-1',
                    message.isOwn ? 'text-white/70' : 'text-gray-500 dark:text-gray-400'
                  ]"
                >
                  {{ message.timestamp }}
                </div>
              </div>
              <div v-if="message.isOwn" class="flex-shrink-0">
                <div class="w-8 h-8 bg-gradient-to-r from-brand-400 to-brand-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  S
                </div>
              </div>
            </div>
          </div>

          <!-- Message Input -->
          <div v-if="selectedChannel || selectedDirectMessage" class="p-4 border-t border-gray-200 dark:border-navy-700">
            <div class="flex items-center space-x-2">
              <div class="flex-1 relative">
                <input
                  v-model="newMessage"
                  @keypress.enter="sendMessage"
                  type="text"
                  placeholder="Mesajınızı yazın..."
                  class="w-full px-4 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-navy-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                />
                <button
                  @click="toggleEmojiPicker"
                  class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  😊
                </button>
              </div>
              <button
                @click="attachFile"
                class="p-2 text-gray-600 dark:text-gray-400 hover:text-brand-600 dark:hover:text-brand-400 transition-colors"
                title="Dosya Ekle"
              >
                <PaperClipIcon class="w-5 h-5" />
              </button>
              <button
                @click="sendMessage"
                :disabled="!newMessage.trim()"
                class="px-4 py-2 bg-brand-500 hover:bg-brand-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg font-medium transition-colors disabled:cursor-not-allowed"
              >
                Gönder
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useManagementStore } from '@/stores/managementStore'
import {
  MagnifyingGlassIcon,
  Cog6ToothIcon,
  ChatBubbleLeftRightIcon,
  PaperClipIcon
} from '@heroicons/vue/24/outline'

const managementStore = useManagementStore()

// Reactive data
const searchTerm = ref('')
const newMessage = ref('')
const selectedChannel = ref<any>(null)
const selectedDirectMessage = ref<any>(null)
const messagesContainer = ref<HTMLElement | null>(null)

// Computed properties
const filteredMaintenanceChannels = computed(() => {
  let channels = managementStore.communicationChannels.filter(
    channel => channel.department === 'MAINTENANCE'
  )
  
  if (searchTerm.value) {
    channels = channels.filter(channel =>
      channel.name.toLowerCase().includes(searchTerm.value.toLowerCase())
    )
  }
  
  return channels
})

const filteredMaintenanceDirectMessages = computed(() => {
  let messages = managementStore.directMessages.filter(
    dm => dm.department === 'MAINTENANCE'
  )
  
  if (searchTerm.value) {
    messages = messages.filter(dm =>
      dm.name.toLowerCase().includes(searchTerm.value.toLowerCase())
    )
  }
  
  return messages
})

const onlineCount = computed(() => {
  return filteredMaintenanceDirectMessages.value.filter(dm => dm.isOnline).length
})

const currentMessages = computed(() => {
  let messages = managementStore.communicationMessages.filter(
    msg => msg.department === 'MAINTENANCE'
  )

  if (selectedChannel.value) {
    messages = messages.filter(msg => msg.channelId === selectedChannel.value.id)
  } else if (selectedDirectMessage.value) {
    messages = messages.filter(msg => msg.directMessageId === selectedDirectMessage.value.id)
  } else {
    return []
  }

  return messages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
})

// Methods
const selectChannel = (channel: any) => {
  selectedChannel.value = channel
  selectedDirectMessage.value = null
  scrollToBottom()
}

const selectDirectMessage = (dm: any) => {
  selectedDirectMessage.value = dm
  selectedChannel.value = null
  scrollToBottom()
}

const sendMessage = () => {
  if (!newMessage.value.trim()) return

  const messageId = Date.now().toString()
  const timestamp = new Date().toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit'
  })

  const message = {
    id: messageId,
    sender: 'Siz',
    senderName: 'Siz',
    text: newMessage.value.trim(),
    message: newMessage.value.trim(),
    timestamp,
    isOwn: true,
    isRead: true,
    department: 'MAINTENANCE' as const,
    channelId: selectedChannel.value?.id,
    directMessageId: selectedDirectMessage.value?.id,
    status: 'sent' as const
  }

  // Add message to store
  managementStore.communicationMessages.push(message)

  // Clear input
  newMessage.value = ''

  // Scroll to bottom
  nextTick(() => {
    scrollToBottom()
  })

  // Simulate auto-response for demonstration
  if (selectedDirectMessage.value) {
    setTimeout(() => {
      const responseText = getAutoResponse()
      const autoResponse = {
        id: (Date.now() + 1).toString(),
        sender: selectedDirectMessage.value.name,
        senderName: selectedDirectMessage.value.name,
        text: responseText,
        message: responseText,
        timestamp: new Date().toLocaleTimeString('tr-TR', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        isOwn: false,
        isRead: false,
        department: 'MAINTENANCE' as const,
        directMessageId: selectedDirectMessage.value.id,
        status: 'delivered' as const
      }
      
      managementStore.communicationMessages.push(autoResponse)
      
      nextTick(() => {
        scrollToBottom()
      })
    }, 1000 + Math.random() * 2000)
  }
}

const getAutoResponse = () => {
  const responses = [
    'Anladım, hemen kontrol ediyorum.',
    'Tamam, 15 dakika içinde orada olacağım.',
    'Bu iş için gerekli malzemeler var mı?',
    'Öncelik derecesi nedir bu işin?',
    'Başka bir yerde de aynı sorun var mı?',
    'İş tamamlandı, kontrol edebilirsiniz.',
    'Ek bilgiye ihtiyacım var bu konuda.',
    'Bu hafta programıma alıyorum.',
    'Anlaşıldı, takibini yapacağım.'
  ]
  return responses[Math.floor(Math.random() * responses.length)]
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const toggleEmojiPicker = () => {
  // TODO: Implement emoji picker
  console.log('Emoji picker toggled')
}

const attachFile = () => {
  // TODO: Implement file attachment
  console.log('File attachment clicked')
}

// Lifecycle
onMounted(() => {
  // Auto-select first channel if available
  if (filteredMaintenanceChannels.value.length > 0) {
    selectChannel(filteredMaintenanceChannels.value[0])
  }
})
</script> 