<template>
  <div class="min-h-screen bg-backgroundLight">
    <!-- Header -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-6">
        <div class="flex items-center justify-between">
          <button @click="goBack" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <svg class="w-6 h-6 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          </button>
          <h1 class="text-2xl font-bold text-textDark">Hizmetler</h1>
          <div class="w-10"></div> <!-- Spacer for centering -->
        </div>
      </div>
    </div>

    <!-- Hero Section -->
    <div class="relative bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 text-white px-4 py-8 overflow-hidden">
      <!-- Background Image -->
      <div class="absolute inset-0 z-0">
        <img 
          src="https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=800&h=400&fit=crop&crop=center" 
          alt="Spa Services"
          class="w-full h-full object-cover opacity-20"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/90 via-purple-600/90 to-pink-600/90"></div>
      </div>
      
      <div class="relative z-10 max-w-md mx-auto">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
              </svg>
            </div>
            <div>
              <p class="text-purple-100 text-sm font-medium">Size Özel</p>
              <p class="text-white text-xs">Rezervasyon Gerekli</p>
            </div>
          </div>
          <div class="bg-pink-500 text-white rounded-full px-3 py-1 text-sm font-bold">
            Premium
          </div>
        </div>
        
        <h1 class="text-2xl font-bold mb-2">SPA & Wellness Deneyimi</h1>
        <p class="text-purple-100 mb-4 leading-relaxed">Vücudunuzu ve ruhunuzu şımartın, stresi geride bırakın</p>
        
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="text-white">
              <span class="text-lg font-bold">Randevu Al</span>
            </div>
            <div class="flex items-center text-purple-100 text-sm">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span>4.9 (245 değerlendirme)</span>
            </div>
          </div>
          <button 
            class="bg-white text-purple-600 px-6 py-3 rounded-full font-semibold hover:bg-purple-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            @click="goToCategory('spa-wellness')"
          >
            Keşfet
          </button>
        </div>
      </div>
      
      <!-- Background decoration -->
      <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full"></div>
      <div class="absolute bottom-4 left-4 w-16 h-16 bg-white/10 rounded-full"></div>
      <div class="absolute top-1/2 right-12 w-3 h-3 bg-white/20 rounded-full"></div>
      <div class="absolute top-12 right-20 w-2 h-2 bg-white/30 rounded-full"></div>
    </div>

    <!-- Service Categories -->
    <div class="max-w-md mx-auto px-4 py-6">
      <h2 class="text-xl font-bold text-textDark mb-6">Hizmet Kategorileri</h2>
      
      <!-- Categories Grid -->
      <div class="space-y-6 mb-8">
        <div 
          v-for="category in serviceCategories" 
          :key="category.id"
          @click="goToCategory(category.slug)"
          class="relative bg-white rounded-3xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02]"
        >
          <!-- Category Image -->
          <div class="relative h-48 overflow-hidden">
            <img 
              :src="category.image" 
              :alt="category.name" 
              class="w-full h-full object-cover"
            >
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
            
            <!-- Category Content Overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
              <h3 class="text-xl font-bold mb-2">{{ category.name }}</h3>
              <p class="text-white/90 text-sm leading-relaxed">{{ category.description }}</p>
            </div>
            
            <!-- Service Count Badge -->
            <div class="absolute top-4 right-4 bg-white/20 backdrop-blur-sm text-white rounded-full px-3 py-1 text-sm font-medium">
              {{ category.services.length }} Hizmet
            </div>
          </div>
          
          <!-- Quick Info -->
          <div class="p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-textMedium text-sm">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span>{{ getMinDuration(category) }}+ dk</span>
                </div>
                <div class="flex items-center text-textMedium text-sm">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                  </svg>
                  <span>₺{{ getMinPrice(category) }}+</span>
                </div>
              </div>
              
              <div class="flex items-center text-primary">
                <span class="text-sm font-medium mr-1">Görüntüle</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Why Choose Our Services -->
      <div class="mb-8">
        <h2 class="text-xl font-bold text-textDark mb-4">Neden Bizimle?</h2>
        <div class="space-y-3">
          <div class="flex items-center text-textMedium">
            <svg class="w-5 h-5 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <span>Profesyonel ve deneyimli personel</span>
          </div>
          <div class="flex items-center text-textMedium">
            <svg class="w-5 h-5 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <span>Modern ve hijyenik tesisler</span>
          </div>
          <div class="flex items-center text-textMedium">
            <svg class="w-5 h-5 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <span>Kolay online rezervasyon sistemi</span>
          </div>
          <div class="flex items-center text-textMedium">
            <svg class="w-5 h-5 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <span>Misafirlerimize özel fiyat avantajları</span>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">

import { useRouter } from 'vue-router'
import { serviceCategories, type ServiceCategory } from '@/data/servicesData'

const router = useRouter()

const goBack = () => {
  router.back()
}

const goToCategory = (slug: string) => {
  router.push(`/service-category/${slug}`)
}

// Helper functions
const getMinPrice = (category: ServiceCategory): number => {
  const prices = category.services.map(service => service.price).filter(price => price > 0)
  return prices.length > 0 ? Math.min(...prices) : 0
}

const getMinDuration = (category: ServiceCategory): number => {
  const durations = category.services.map(service => service.duration_minutes).filter(duration => duration > 0)
  return durations.length > 0 ? Math.min(...durations) : 0
}
</script>

<style scoped>
/* Component-specific styles */
</style> 