/// <reference types="./env.d.ts" />
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

// Export createClient for use in MFEs
export { createClient } from "@supabase/supabase-js";

// Enhanced environment variable checking with better error handling
let supabaseUrl: string;
let supabaseAnonKey: string;

try {
  // Vite exposes variables prefixed with VITE_ to the import.meta.env object.
  supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
  supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

  // Enhanced validation with specific error messages
  if (!supabaseUrl) {
    throw new Error(
      "❌ VITE_SUPABASE_URL is missing. Please add it to your .env.local file.",
    );
  }

  if (!supabaseAnonKey) {
    throw new Error(
      "❌ VITE_SUPABASE_ANON_KEY is missing. Please add it to your .env.local file.",
    );
  }

  // Validate URL format
  if (
    !supabaseUrl.startsWith("https://") ||
    !supabaseUrl.includes("supabase.co")
  ) {
    throw new Error(
      "❌ VITE_SUPABASE_URL must be a valid Supabase URL (https://your-project.supabase.co)",
    );
  }

  // Debug environment variables in development
  if (import.meta.env.DEV) {
    console.log("🔧 Shared Supabase Client - Environment check:", {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseAnonKey,
      urlPreview: supabaseUrl.substring(0, 30) + "...",
      keyPreview: supabaseAnonKey.substring(0, 20) + "...",
    });
  }
} catch (error) {
  console.error("🚨 Supabase Configuration Error:", error);

  // Show user-friendly error message in development
  if (import.meta.env.DEV) {
    const errorMessage = `
🚨 SUPABASE CONFIGURATION ERROR 🚨

 ${(error as Error).message}

📋 TO FIX THIS:
1. Create a .env.local file in your project root
2. Add the following lines:

VITE_SUPABASE_URL="https://your-project-id.supabase.co"
VITE_SUPABASE_ANON_KEY="your-anon-key-here"

3. Restart your development server
4. Refresh the page

📁 Current working directory: ${window?.location?.href || "Unknown"}
    `;

    // Create a visible error overlay in development
    if (typeof document !== "undefined") {
      const errorOverlay = document.createElement("div");
      errorOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        padding: 20px;
        box-sizing: border-box;
        z-index: 9999;
        overflow: auto;
        white-space: pre-wrap;
      `;
      errorOverlay.textContent = errorMessage;

      // Add close button
      const closeButton = document.createElement("button");
      closeButton.textContent = "✕ Close (but please fix the error!)";
      closeButton.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
      `;
      closeButton.onclick = () => errorOverlay.remove();
      errorOverlay.appendChild(closeButton);

      document.body.appendChild(errorOverlay);
    }
  }

  // Rethrow the error to prevent further initialization
  throw error;
}

// Create the singleton Supabase client with enhanced error handling
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Helper functions for common operations with error handling
export const getCurrentUser = async () => {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  } catch (error) {
    console.error("Error getting current user:", error);
    throw error;
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  } catch (error) {
    console.error("Error signing out:", error);
    throw error;
  }
};

export const signInWithEmail = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error signing in:", error);
    throw error;
  }
};

export const signUpWithEmail = async (
  email: string,
  password: string,
  metadata?: Record<string, any>,
) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error signing up:", error);
    throw error;
  }
};

// Get user profile including hotel_id with enhanced error handling
export const getUserProfile = async (userId?: string) => {
  try {
    const targetUserId = userId || (await getCurrentUser())?.id;
    if (!targetUserId) throw new Error("User not authenticated");

    const { data, error } = await supabase
      .from("user_profiles")
      .select("*")
      .eq("id", targetUserId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw error;
  }
};

// Get user's hotel_id specifically with enhanced error handling
export const getUserHotelId = async (
  userId?: string,
): Promise<string | null> => {
  try {
    const profile = await getUserProfile(userId);
    return profile?.hotel_id || null;
  } catch (error) {
    console.error("Error fetching user hotel ID:", error);
    return null;
  }
};

// Get co-workers (staff members from the same hotel) with enhanced error handling
export const getHotelStaffMembers = async (userId?: string) => {
  try {
    const targetUserId = userId || (await getCurrentUser())?.id;
    if (!targetUserId) throw new Error("User not authenticated");

    // Get all staff members from the same hotel using RLS policy
    // The RLS policy will automatically filter by hotel_id and roles
    const { data, error } = await supabase
      .from("user_profiles")
      .select("id, full_name, avatar_url, role, created_at")
      .order("full_name", { ascending: true });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error getting hotel staff members:", error);
    throw error;
  }
};

// Get specific user profile by ID (useful for task assignment display)
export const getUserProfileById = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .select("id, full_name, avatar_url, role")
      .eq("id", userId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error getting user profile by ID:", error);
    throw error;
  }
};

// Export types for convenience
export * from "./types";
export * from "./dataTransformers";
export * from "./dataServices";
export * from "./services/CrossMFERealtimeService";
export * from "./services/DataCacheService";
// export * from "./services/CrossMFEDataSynchronizer"; // Temporarily disabled due to build issues
export type { User, Session } from "@supabase/supabase-js";

// Re-export specific types that are commonly used
export type {
  CrossMFEEventType,
  CrossMFEEvent,
  HotelDataChangeEvent,
  UserProfileChangeEvent,
  TaskStatusChangeEvent,
  ServiceRequestChangeEvent,
  NotificationChangeEvent,
  RoomStatusChangeEvent,
  CrossMFEEventListener
} from "./services/CrossMFERealtimeService";

// Default export for backward compatibility
export default supabase;

// Log successful initialization
if (import.meta.env.DEV) {
  console.log(
    "✅ Shared Supabase Client - Singleton instance created successfully",
  );
}
