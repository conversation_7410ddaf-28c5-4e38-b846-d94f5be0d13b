import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@hotelexia/shared-supabase-client'
import { useAuthStore } from './authStore'

// Types for management reporting data
export interface PlatformOverview {
  total_hotels: number
  active_hotels: number
  inactive_hotels: number
  total_users: number
  super_admin_count: number
  hotel_admin_count: number
  staff_count: number
  guest_count: number
  monthly_room_service_revenue: number
  monthly_reservation_revenue: number
  weekly_maintenance_tasks: number
  weekly_service_requests: number
  hotels_added_this_month: number
  users_added_this_month: number
}

export interface HotelComparison {
  hotel_id: string
  hotel_name: string
  city: string
  country: string
  is_active: boolean
  hotel_created_at: string
  total_rooms: number
  total_staff: number
  current_occupancy_rate: number
  monthly_reservation_revenue: number
  monthly_room_service_revenue: number
  pending_maintenance_tasks: number
  pending_service_requests: number
  maintenance_completion_rate: number
  monthly_activity_registrations: number
  monthly_room_service_orders: number
  avg_guest_rating: number
  monthly_tasks_per_staff: number
  monthly_new_reservations: number
  monthly_new_staff: number
  weekly_system_activity: number
  monthly_revenue_per_room: number
}

export const useManagementReportingStore = defineStore('managementReporting', () => {
  // Get auth store for user context
  const authStore = useAuthStore()

  // State
  const platformOverview = ref<PlatformOverview | null>(null)
  const hotelComparisons = ref<HotelComparison[]>([])

  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed values for dashboard KPIs
  const totalRevenue = computed(() => {
    if (!platformOverview.value) return 0
    return platformOverview.value.monthly_reservation_revenue + platformOverview.value.monthly_room_service_revenue
  })

  const hotelGrowthRate = computed(() => {
    if (!platformOverview.value) return 0
    const currentMonth = platformOverview.value.hotels_added_this_month
    const totalHotels = platformOverview.value.total_hotels
    if (totalHotels === 0) return 0
    return Math.round((currentMonth / totalHotels) * 100)
  })

  const userGrowthRate = computed(() => {
    if (!platformOverview.value) return 0
    const currentMonth = platformOverview.value.users_added_this_month
    const totalUsers = platformOverview.value.total_users
    if (totalUsers === 0) return 0
    return Math.round((currentMonth / totalUsers) * 100)
  })

  const averageOccupancyRate = computed(() => {
    if (hotelComparisons.value.length === 0) return 0
    const totalOccupancy = hotelComparisons.value.reduce((sum, hotel) => sum + hotel.current_occupancy_rate, 0)
    return Math.round(totalOccupancy / hotelComparisons.value.length)
  })

  const topPerformingHotels = computed(() => {
    return hotelComparisons.value
      .filter(hotel => hotel.is_active)
      .sort((a, b) => b.monthly_revenue_per_room - a.monthly_revenue_per_room)
      .slice(0, 5)
  })

  const hotelsByRegion = computed(() => {
    const regions: Record<string, number> = {}
    hotelComparisons.value.forEach(hotel => {
      const region = hotel.country || 'Unknown'
      regions[region] = (regions[region] || 0) + 1
    })
    return Object.entries(regions).map(([region, count]) => ({ region, count }))
  })

  // Actions
  const fetchPlatformOverview = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Verify user has SUPER_ADMIN role
      if (authStore.user?.role !== 'SUPER_ADMIN') {
        throw new Error('Access denied: SUPER_ADMIN role required for platform overview')
      }

      const { data, error: supabaseError } = await (supabase as any).rpc('get_management_platform_overview_secure')

      if (supabaseError) throw supabaseError

      if (data && Array.isArray(data) && data.length > 0) {
        platformOverview.value = data[0] as any
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch platform overview'
      console.error('Error fetching platform overview:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchHotelComparisons = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Verify user has SUPER_ADMIN role
      if (authStore.user?.role !== 'SUPER_ADMIN') {
        throw new Error('Access denied: SUPER_ADMIN role required for hotel comparison')
      }

      const { data, error: supabaseError } = await (supabase as any).rpc('get_management_hotel_comparison_secure')

      if (supabaseError) throw supabaseError

      hotelComparisons.value = (data as any) || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch hotel comparisons'
      console.error('Error fetching hotel comparisons:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchAllReports = async () => {
    await Promise.all([
      fetchPlatformOverview(),
      fetchHotelComparisons()
    ])
  }

  const getHotelPerformanceMetrics = (hotelId: string) => {
    const hotel = hotelComparisons.value.find(h => h.hotel_id === hotelId)
    if (!hotel) return null

    return {
      occupancyRate: hotel.current_occupancy_rate,
      revenuePerRoom: hotel.monthly_revenue_per_room,
      guestRating: hotel.avg_guest_rating,
      taskEfficiency: hotel.monthly_tasks_per_staff,
      maintenanceCompletion: hotel.maintenance_completion_rate
    }
  }

  const getRevenueComparison = () => {
    return hotelComparisons.value.map(hotel => ({
      hotelName: hotel.hotel_name,
      reservationRevenue: hotel.monthly_reservation_revenue,
      roomServiceRevenue: hotel.monthly_room_service_revenue,
      totalRevenue: hotel.monthly_reservation_revenue + hotel.monthly_room_service_revenue,
      revenuePerRoom: hotel.monthly_revenue_per_room
    }))
  }

  const getOperationalComparison = () => {
    return hotelComparisons.value.map(hotel => ({
      hotelName: hotel.hotel_name,
      occupancyRate: hotel.current_occupancy_rate,
      totalRooms: hotel.total_rooms,
      totalStaff: hotel.total_staff,
      pendingMaintenance: hotel.pending_maintenance_tasks,
      pendingServiceRequests: hotel.pending_service_requests,
      maintenanceCompletion: hotel.maintenance_completion_rate
    }))
  }

  return {
    // State
    platformOverview,
    hotelComparisons,
    isLoading,
    error,
    
    // Computed
    totalRevenue,
    hotelGrowthRate,
    userGrowthRate,
    averageOccupancyRate,
    topPerformingHotels,
    hotelsByRegion,
    
    // Actions
    fetchPlatformOverview,
    fetchHotelComparisons,
    fetchAllReports,
    getHotelPerformanceMetrics,
    getRevenueComparison,
    getOperationalComparison
  }
})
