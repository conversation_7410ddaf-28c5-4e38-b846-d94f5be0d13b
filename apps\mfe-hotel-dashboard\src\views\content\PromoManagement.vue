<template>
  <div class="p-4 md:p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-2">
        Promo Yönetimi
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Genel promosyon ve kampanya yönetimi
      </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Aktif Promolar</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">12</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
            <TicketIcon class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Bekleyen Promolar</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">5</p>
          </div>
          <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
            <ClockIcon class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Toplam İndirim</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">₺2,450</p>
          </div>
          <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
            <CurrencyDollarIcon class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">Kullanım Oranı</p>
            <p class="text-2xl font-bold text-navy-700 dark:text-white">78%</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
            <ChartBarIcon class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm mb-8">
      <h3 class="text-lg font-bold text-navy-700 dark:text-white mb-4">
        Hızlı İşlemler
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <PlusIcon class="w-5 h-5 text-brand-500 mr-3" />
          <span class="text-navy-700 dark:text-white font-medium">Yeni Promo Oluştur</span>
        </button>
        <button class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <DocumentDuplicateIcon class="w-5 h-5 text-brand-500 mr-3" />
          <span class="text-navy-700 dark:text-white font-medium">Promo Şablonları</span>
        </button>
        <button class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <ChartPieIcon class="w-5 h-5 text-brand-500 mr-3" />
          <span class="text-navy-700 dark:text-white font-medium">Promo Raporları</span>
        </button>
      </div>
    </div>

    <!-- Promo Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Room Service Promos -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Oda Servisi Promoları
          </h3>
          <span class="bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
            8 Aktif
          </span>
        </div>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Kahvaltı İndirimi</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%20 indirim</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
              <button class="text-brand-500 hover:text-brand-600">
                <PencilIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Gece Yarısı Menü</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">₺50 indirim</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
              <button class="text-brand-500 hover:text-brand-600">
                <PencilIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Guest Service Promos -->
      <div class="bg-white dark:bg-navy-800 rounded-xl p-6 shadow-sm">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-navy-700 dark:text-white">
            Misafir Hizmet Promoları
          </h3>
          <span class="bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium">
            4 Aktif
          </span>
        </div>
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Spa Paketi</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">%30 indirim</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
              <button class="text-brand-500 hover:text-brand-600">
                <PencilIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div>
              <p class="font-medium text-navy-700 dark:text-white">Çamaşır Hizmeti</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">2. parça ücretsiz</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-600 dark:text-green-400 text-sm">Aktif</span>
              <button class="text-brand-500 hover:text-brand-600">
                <PencilIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Development Notice -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
      <div class="flex items-start">
        <InformationCircleIcon class="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" />
        <div>
          <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">
            Geliştirme Aşamasında
          </h3>
          <p class="text-blue-700 dark:text-blue-300 mb-3">
            Bu sayfa şu anda geliştirme aşamasındadır. Gelecekte aşağıdaki özellikler eklenecektir:
          </p>
          <ul class="list-disc list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm">
            <li>Detaylı promo oluşturma ve düzenleme</li>
            <li>Promo şablonları ve öneri sistemi</li>
            <li>Kullanım analytics ve performans raporları</li>
            <li>Otomatik promo aktivasyonu</li>
            <li>A/B test functionality</li>
            <li>Müşteri segmentasyonu</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  TicketIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  PlusIcon,
  DocumentDuplicateIcon,
  ChartPieIcon,
  PencilIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline'
</script> 