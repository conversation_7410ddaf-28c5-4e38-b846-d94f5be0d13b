import { ref, computed, onMounted, onUnmounted } from 'vue'
import { errorHandlingService, type ErrorContext, type ErrorHandlingOptions } from '../services/ErrorHandlingService'

export interface UseErrorHandlingOptions {
  component?: string
  userId?: string
  hotelId?: string
  showToasts?: boolean
  logErrors?: boolean
  maxRetries?: number
  retryDelay?: number
  autoClearAfter?: number
  enableRecovery?: boolean
  persistent?: boolean
  persistentKey?: string
}

export function useErrorHandling(options: UseErrorHandlingOptions = {}) {
  const {
    component = 'Unknown',
    userId,
    hotelId,
    showToasts = true,
    logErrors = true,
    maxRetries = 3,
    retryDelay = 1000,
    autoClearAfter = 10000,
    enableRecovery = true,
    persistent = false,
    persistentKey = 'default'
  } = options

  // State
  const lastError = ref<Error | null>(null)
  const errorCount = ref(0)
  const isHandlingError = ref(false)
  const retryCount = ref(0)
  const errorHistory = ref<Array<{ error: Error; timestamp: number; context?: any }>>([])
  const isRecovering = ref(false)
  const recoveryActions = ref<Array<{ name: string; action: () => Promise<void> }>>([])

  // Timers
  let autoClearTimer: NodeJS.Timeout | null = null

  // Base context for all errors in this component
  const baseContext: Partial<ErrorContext> = {
    component,
    userId,
    hotelId
  }

  // Computed
  const hasErrors = computed(() => errorCount.value > 0)

  /**
   * Handle a general error
   */
  const handleError = async (
    error: Error,
    context: Partial<ErrorContext> = {},
    options: ErrorHandlingOptions = {}
  ) => {
    isHandlingError.value = true
    lastError.value = error
    errorCount.value++

    try {
      await errorHandlingService.handleError(
        error,
        { ...baseContext, ...context },
        {
          showToast: showToasts,
          logToConsole: logErrors,
          ...options
        }
      )
    } catch (handlingError) {
      console.error('Error while handling error:', handlingError)
    } finally {
      isHandlingError.value = false
    }
  }

  /**
   * Handle Supabase-specific errors
   */
  const handleSupabaseError = async (
    error: any,
    context: Partial<ErrorContext> = {}
  ) => {
    isHandlingError.value = true
    lastError.value = error instanceof Error ? error : new Error(String(error))
    errorCount.value++

    try {
      await errorHandlingService.handleSupabaseError(
        error,
        { ...baseContext, ...context }
      )
    } catch (handlingError) {
      console.error('Error while handling Supabase error:', handlingError)
    } finally {
      isHandlingError.value = false
    }
  }

  /**
   * Handle network errors
   */
  const handleNetworkError = async (
    error: Error,
    context: Partial<ErrorContext> = {}
  ) => {
    isHandlingError.value = true
    lastError.value = error
    errorCount.value++

    try {
      await errorHandlingService.handleNetworkError(
        error,
        { ...baseContext, ...context }
      )
    } catch (handlingError) {
      console.error('Error while handling network error:', handlingError)
    } finally {
      isHandlingError.value = false
    }
  }

  /**
   * Handle authentication errors
   */
  const handleAuthError = async (
    error: Error,
    context: Partial<ErrorContext> = {}
  ) => {
    isHandlingError.value = true
    lastError.value = error
    errorCount.value++

    try {
      await errorHandlingService.handleAuthError(
        error,
        { ...baseContext, ...context }
      )
    } catch (handlingError) {
      console.error('Error while handling auth error:', handlingError)
    } finally {
      isHandlingError.value = false
    }
  }

  /**
   * Wrap an async function with error handling
   */
  const withErrorHandling = <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context: Partial<ErrorContext> = {},
    options: ErrorHandlingOptions = {}
  ) => {
    return async (...args: T): Promise<R | null> => {
      try {
        return await fn(...args)
      } catch (error) {
        await handleError(
          error instanceof Error ? error : new Error(String(error)),
          context,
          options
        )
        return null
      }
    }
  }

  /**
   * Wrap a Supabase query with error handling
   */
  const withSupabaseErrorHandling = <T extends any[], R>(
    queryFn: (...args: T) => Promise<{ data: R | null; error: any }>,
    context: Partial<ErrorContext> = {}
  ) => {
    return async (...args: T): Promise<R | null> => {
      try {
        const { data, error } = await queryFn(...args)
        
        if (error) {
          await handleSupabaseError(error, context)
          return null
        }
        
        return data
      } catch (error) {
        await handleError(
          error instanceof Error ? error : new Error(String(error)),
          context
        )
        return null
      }
    }
  }

  /**
   * Clear error state
   */
  const clearErrors = () => {
    lastError.value = null
    errorCount.value = 0
  }

  /**
   * Get error statistics for this component
   */
  const getErrorStats = () => {
    return {
      lastError: lastError.value,
      errorCount: errorCount.value,
      hasErrors: hasErrors.value,
      isHandlingError: isHandlingError.value
    }
  }

  return {
    // State
    lastError,
    errorCount,
    hasErrors,
    isHandlingError,

    // Error handling methods
    handleError,
    handleSupabaseError,
    handleNetworkError,
    handleAuthError,

    // Wrapper methods
    withErrorHandling,
    withSupabaseErrorHandling,

    // Utility methods
    clearErrors,
    getErrorStats
  }
}

// Specialized hooks for different contexts

/**
 * Hook for handling errors in data fetching operations
 */
export function useDataErrorHandling(component: string, userId?: string, hotelId?: string) {
  const errorHandler = useErrorHandling({ component, userId, hotelId })

  const handleDataError = (error: any, operation: string) => {
    return errorHandler.handleError(
      error instanceof Error ? error : new Error(String(error)),
      { action: `data_${operation}` },
      { retryable: true }
    )
  }

  const handleQueryError = (error: any, table: string) => {
    return errorHandler.handleSupabaseError(error, { action: `query_${table}` })
  }

  return {
    ...errorHandler,
    handleDataError,
    handleQueryError
  }
}

/**
 * Hook for handling errors in form operations
 */
export function useFormErrorHandling(component: string, userId?: string, hotelId?: string) {
  const errorHandler = useErrorHandling({ component, userId, hotelId })

  const handleValidationError = (error: Error, field?: string) => {
    return errorHandler.handleError(error, { action: `validation_${field || 'form'}` }, {
      severity: 'medium',
      showToast: true
    })
  }

  const handleSubmitError = (error: any) => {
    return errorHandler.handleError(
      error instanceof Error ? error : new Error(String(error)),
      { action: 'form_submit' },
      { retryable: true, showModal: true }
    )
  }

  return {
    ...errorHandler,
    handleValidationError,
    handleSubmitError
  }
}

/**
 * Hook for handling errors in authentication operations
 */
export function useAuthErrorHandling(component: string) {
  const errorHandler = useErrorHandling({ component })

  const handleLoginError = (error: any) => {
    return errorHandler.handleAuthError(
      error instanceof Error ? error : new Error(String(error)),
      { action: 'login' }
    )
  }

  const handleLogoutError = (error: any) => {
    return errorHandler.handleAuthError(
      error instanceof Error ? error : new Error(String(error)),
      { action: 'logout' }
    )
  }

  const handleSessionError = (error: any) => {
    return errorHandler.handleAuthError(
      error instanceof Error ? error : new Error(String(error)),
      { action: 'session_restore' }
    )
  }

  return {
    ...errorHandler,
    handleLoginError,
    handleLogoutError,
    handleSessionError
  }
}
