import { createI18n } from 'vue-i18n'

// Import language files - mock for build compatibility
const en = {
  common: {
    retry: 'Try Again',
    loading: 'Loading...'
  },
  errors: {
    somethingWentWrong: 'Something went wrong',
    technicalDetails: 'Technical Details'
  },
  navigation: {
    goHome: 'Go Home'
  }
}

const tr = {
  common: {
    retry: 'Tekrar Dene',
    loading: 'Yükleniyor...'
  },
  errors: {
    somethingWentWrong: 'Bir hata oluştu',
    technicalDetails: 'Teknik Detaylar'
  },
  navigation: {
    goHome: 'Ana Sayfa'
  }
}

// Type definitions for better TypeScript support
export type MessageSchema = typeof en

// Available locales
export const SUPPORTED_LOCALES = ['en', 'tr'] as const
export type SupportedLocale = typeof SUPPORTED_LOCALES[number]

// Default locale
export const DEFAULT_LOCALE: SupportedLocale = 'tr'

// Create i18n instance
export const i18n = createI18n({
  legacy: false, // Use Composition API mode
  locale: DEFAULT_LOCALE,
  fallbackLocale: 'en',
  messages: {
    en,
    tr
  },
  globalInjection: true, // Enable global $t function
  missingWarn: true,
  fallbackWarn: true
})

// Helper functions
export const setLocale = (locale: SupportedLocale) => {
  if (i18n.global.locale) {
    i18n.global.locale.value = locale as any
  }
  // Store preference in localStorage
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('hotelexia_locale', locale)
  }
}

export const getLocale = (): SupportedLocale => {
  // Get from localStorage or use default
  if (typeof localStorage !== 'undefined') {
    const stored = localStorage.getItem('hotelexia_locale') as SupportedLocale
    return SUPPORTED_LOCALES.includes(stored) ? stored : DEFAULT_LOCALE
  }
  return DEFAULT_LOCALE
}

export const initializeLocale = () => {
  const locale = getLocale()
  setLocale(locale)
}

// Export for use in other packages
export default i18n
