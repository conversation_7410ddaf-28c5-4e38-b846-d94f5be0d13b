<template>
  <div class="min-h-screen bg-backgroundLight pb-24">
    <!-- Header -->
    <div class="bg-backgroundWhite border-b border-borderColor">
      <div class="px-4 py-6">
        <div class="flex items-center justify-between">
          <button @click="goBack" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <svg class="w-6 h-6 text-textDark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          </button>
          <h1 class="text-2xl font-bold text-textDark">Günün Programı</h1>
          <div class="w-10"></div> <!-- Spacer for centering -->
        </div>
      </div>
    </div>

    <!-- Current Time Display -->
    <div class="px-4 py-3 bg-primary text-white">
      <div class="flex items-center justify-center">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
        </svg>
        <span class="text-sm font-medium">Şu an: {{ currentTime }}</span>
      </div>
    </div>

    <!-- Timeline Section -->
    <div class="px-4 py-6">
      <div class="space-y-4">
        <div 
          v-for="item in timelineItems" 
          :key="item.id"
          class="flex items-start space-x-4"
          :class="{
            'opacity-50': item.status === 'past',
            'bg-white p-3 rounded-lg shadow-sm border-l-4 border-primary': item.status === 'ongoing'
          }"
        >
          <!-- Timeline Dot -->
          <div class="flex flex-col items-center">
            <div 
              class="w-3 h-3 rounded-full flex-shrink-0 mt-2"
              :class="{
                'bg-gray-300': item.status === 'past',
                'bg-primary timeline-dot is-ongoing': item.status === 'ongoing',
                'bg-teal-500': item.status === 'future' && item.type === 'activity',
                'bg-amber-500': item.status === 'future' && item.type === 'service'
              }"
            ></div>
            <div class="w-px bg-gray-200 h-8 mt-2" v-if="item.id !== timelineItems[timelineItems.length - 1]?.id"></div>
          </div>

          <!-- Event Content -->
          <div class="flex-1 pb-8">
            <div class="flex items-center justify-between mb-1">
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-textDark">
                  {{ item.startTime }} - {{ item.endTime }}
                </span>
                <!-- Service Badge -->
                <span 
                  v-if="item.type === 'service'" 
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800"
                >
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  Rezervasyonunuz
                </span>
                <!-- Ongoing Badge -->
                <span 
                  v-if="item.status === 'ongoing'" 
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                >
                  <span class="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></span>
                  Şu An Aktif
                </span>
              </div>
            </div>
            
            <h3 class="text-lg font-semibold text-textDark mb-1">{{ item.name }}</h3>
            <p class="text-sm text-textMedium mb-2">📍 {{ item.location }}</p>
            <p class="text-sm text-textMedium">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Separator -->
    <div class="px-4">
      <div class="border-t border-borderColor"></div>
    </div>

    <!-- Promotional Activities Section -->
    <div class="px-4 py-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-textDark">Öne Çıkan Aktiviteler</h2>
        <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
        </svg>
      </div>

      <!-- Promoted Activities Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div 
          v-for="activity in promotedActivities" 
          :key="activity.id"
          class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer"
          @click="viewActivityDetail(activity.id)"
        >
          <!-- Activity Image -->
          <div class="relative h-32 bg-gradient-to-br from-primary/20 to-teal-600/20">
            <img 
              v-if="activity.image"
              :src="activity.image" 
              :alt="activity.name"
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-black/10"></div>
            <!-- Time Badge -->
            <div class="absolute top-3 left-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg">
              <span class="text-xs font-medium text-textDark">
                {{ activity.startTime }} - {{ activity.endTime }}
              </span>
            </div>
          </div>

          <!-- Activity Content -->
          <div class="p-4">
            <h3 class="font-semibold text-textDark mb-2">{{ activity.name }}</h3>
            <p class="text-sm text-textMedium mb-3">📍 {{ activity.location }}</p>
            <button class="w-full bg-primary hover:bg-teal-600 text-white px-4 py-2 rounded-xl text-sm font-medium transition-colors">
              Detayları Gör
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { dailyActivities, userBookedServices } from '@/data/activitiesData'
import { useCustomerDataStore } from '@/stores/customerDataStore'

const router = useRouter()
const customerDataStore = useCustomerDataStore()
const currentTime = ref('')
let timeInterval: number | null = null

// Update current time every minute
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('tr-TR', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  })
}

// Timeline items with status calculation - combining live data with dummy data
const timelineItems = computed(() => {
  // Combine live activities from store with dummy data for now
  const liveActivities = customerDataStore.todayActivities.map(activity => ({
    id: activity.id,
    type: activity.type,
    name: activity.title,
    location: activity.location,
    startTime: activity.startTime,
    endTime: activity.endTime,
    description: activity.description,
    image: activity.imageUrl,
    isPromoted: activity.isPromoted
  }))

  const allEvents = [...liveActivities, ...dailyActivities, ...userBookedServices]

  // Sort by start time
  const sortedEvents = allEvents.sort((a, b) => {
    return a.startTime.localeCompare(b.startTime)
  })

  // Add status to each event
  return sortedEvents.map(event => {
    const now = new Date()
    const currentTimeStr = now.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })

    let status: 'past' | 'ongoing' | 'future' = 'future'

    if (currentTimeStr > event.endTime) {
      status = 'past'
    } else if (currentTimeStr >= event.startTime && currentTimeStr <= event.endTime) {
      status = 'ongoing'
    }

    return {
      ...event,
      status
    }
  })
})

// Promoted activities (combining live and dummy data)
const promotedActivities = computed(() => {
  const livePromoted = customerDataStore.todayActivities.filter(activity => activity.isPromoted)
  const dummyPromoted = dailyActivities.filter(activity => activity.isPromoted)
  return [...livePromoted, ...dummyPromoted]
})

const goBack = () => {
  router.back()
}

const viewActivityDetail = (id: string) => {
  router.push(`/activity-detail/${id}`)
}

onMounted(async () => {
  updateCurrentTime()
  // Update every minute
  timeInterval = window.setInterval(updateCurrentTime, 60000)

  // Fetch live activities data
  await customerDataStore.fetchActivities()
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.3); }
}

.timeline-dot.is-ongoing {
  background-color: #ef4444; /* red-500 */
  animation: pulse 1.5s infinite;
}
</style> 