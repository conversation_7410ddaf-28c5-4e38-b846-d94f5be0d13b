<template>
  <div class="bg-gray-50 dark:bg-navy-700 rounded-lg p-4 border border-gray-200 dark:border-navy-600 hover:shadow-md transition-shadow duration-200">
    <!-- Header with ID and Room -->
    <div class="flex items-center justify-between mb-3">
      <div>
        <p class="text-sm font-semibold text-navy-700 dark:text-white">#{{ request.id.slice(0, 8) }}</p>
        <p class="text-xs text-gray-600 dark:text-gray-300">Oda {{ request.roomNumber }}</p>
      </div>
      <div class="text-right">
        <p class="text-sm font-medium text-navy-700 dark:text-white">{{ request.guestName }}</p>
        <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatTimeAgo(request.createdAt) }}</p>
      </div>
    </div>

    <!-- Service Type -->
    <div class="mb-3">
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
        </svg>
        {{ request.serviceType }}
      </span>
    </div>

    <!-- Description -->
    <div class="mb-3">
      <p class="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">{{ request.description }}</p>
    </div>

    <!-- Assigned Staff (if any) -->
    <div v-if="request.assignedStaffName" class="mb-3">
      <div class="flex items-center text-xs text-gray-600 dark:text-gray-400">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        <span>Atanan: {{ request.assignedStaffName }}</span>
      </div>
    </div>

    <!-- Notes (if any) -->
    <div v-if="request.notes" class="mb-3">
      <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded p-2">
        <p class="text-xs text-yellow-800 dark:text-yellow-200">
          <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          {{ request.notes }}
        </p>
      </div>
    </div>

    <!-- Status Badge for Completed/Cancelled -->
    <div v-if="isCompleted || isCancelled" class="mb-3">
      <span v-if="isCompleted" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Tamamlandı
      </span>
      <span v-if="isCancelled" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        İptal Edildi
      </span>
    </div>

    <!-- Action Buttons -->
    <div v-if="!isCompleted && !isCancelled" class="flex flex-wrap gap-1">
      <!-- NEW -> ASSIGNED -->
      <button
        v-if="request.status === 'NEW'"
        @click="$emit('update-status', request.id, 'ASSIGNED')"
        class="flex-1 min-w-0 px-2 py-1 text-xs font-medium text-yellow-700 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 rounded hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-200"
      >
        Ata
      </button>

      <!-- ASSIGNED -> IN_PROGRESS -->
      <button
        v-if="request.status === 'ASSIGNED'"
        @click="$emit('update-status', request.id, 'IN_PROGRESS')"
        class="flex-1 min-w-0 px-2 py-1 text-xs font-medium text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-900 border border-orange-300 dark:border-orange-700 rounded hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors duration-200"
      >
        Başlat
      </button>

      <!-- IN_PROGRESS -> COMPLETED -->
      <button
        v-if="request.status === 'IN_PROGRESS'"
        @click="$emit('update-status', request.id, 'COMPLETED')"
        class="flex-1 min-w-0 px-2 py-1 text-xs font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 border border-green-300 dark:border-green-700 rounded hover:bg-green-200 dark:hover:bg-green-800 transition-colors duration-200"
      >
        Tamamla
      </button>

      <!-- Cancel Button (for NEW, ASSIGNED, IN_PROGRESS) -->
      <button
        v-if="['NEW', 'ASSIGNED', 'IN_PROGRESS'].includes(request.status)"
        @click="$emit('update-status', request.id, 'CANCELLED')"
        class="px-2 py-1 text-xs font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200"
      >
        İptal
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GuestServiceRequest } from '@/types/management'

interface Props {
  request: GuestServiceRequest
  isCompleted?: boolean
  isCancelled?: boolean
}

defineProps<Props>()

defineEmits<{
  'update-status': [requestId: string, newStatus: GuestServiceRequest['status']]
}>()

// Helper function to format time ago
const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) {
    return 'Az önce'
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} dakika önce`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `${hours} saat önce`
  } else {
    const days = Math.floor(diffInMinutes / 1440)
    return `${days} gün önce`
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 