<template>
  <div class="p-8">
    <div v-if="loading && !hotel">
      <p>Otel bilgileri yükleniyor...</p>
    </div>
    <div v-else-if="error">
      <p class="text-red-500">{{ error }}</p>
    </div>
    <div v-else-if="hotel">
      <h1 class="text-3xl font-bold text-navy-700 dark:text-white">{{ hotel.name }} - Rezervasyon</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Oda seçiminizi ve tarihlerinizi belirtin.</p>

      <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Room List -->
        <div class="md:col-span-2">
          <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-4">Müsait Odalar</h2>
          <div v-if="roomsLoading" class="text-center py-10"><p><PERSON><PERSON>ar yükleniyor...</p></div>
          <div v-else class="space-y-4">
            <div v-for="room in availableRooms" :key="room.id"
                 @click="() => selectRoom(room)"
                 :class="['p-4 border rounded-lg cursor-pointer', selectedRoom?.id === room.id ? 'border-brand-500 bg-brand-50' : 'border-gray-300']">
              <h3 class="font-bold">{{ room.room_type }} - Oda {{ room.room_number }}</h3>
              <p>Kat: {{ room.floor }}</p>
              <p class="font-semibold mt-2">Gecelik 1500 TL</p> <!-- Placeholder price -->
            </div>
          </div>
        </div>

        <!-- Reservation Form -->
        <div>
          <h2 class="text-2xl font-bold text-navy-700 dark:text-white mb-4">Rezervasyon Detayları</h2>
          <div class="bg-white dark:bg-navy-800 p-6 rounded-lg shadow-card">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Giriş Tarihi</label>
              <input type="date" v-model="checkInDate" class="w-full p-2 border rounded-lg">
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Çıkış Tarihi</label>
              <input type="date" v-model="checkOutDate" class="w-full p-2 border rounded-lg">
            </div>
             <div v-if="selectedRoom" class="mt-6 text-center">
                <p>Seçilen Oda: <span class="font-bold">{{ selectedRoom.room_type }} - {{ selectedRoom.room_number }}</span></p>
                <p>Toplam Tutar: <span class="font-bold">{{ totalPrice }} TL</span></p>
                <button @click="createReservation" :disabled="isSubmitting" class="mt-4 w-full px-4 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 disabled:bg-gray-400">
                  {{ isSubmitting ? 'Rezervasyon yapılıyor...' : 'Şimdi Rezerve Et' }}
                </button>
                <p v-if="reservationError" class="text-red-500 mt-2">{{ reservationError }}</p>
             </div>
             <div v-else class="mt-6 text-center">
                <p class="text-gray-500">Lütfen bir oda seçin.</p>
             </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { supabase } from '@hotelexia/shared-supabase-client';
import type { Database } from '@hotelexia/shared-supabase-client';

type Hotel = Database['public']['Tables']['hotels']['Row'];
type Room = Database['public']['Tables']['rooms']['Row'];

const route = useRoute();
const router = useRouter();

const hotelId = route.params.hotelId as string;

const hotel = ref<Hotel | null>(null);
const availableRooms = ref<Room[]>([]);
const selectedRoom = ref<Room | null>(null);

const checkInDate = ref('');
const checkOutDate = ref('');

const loading = ref(true);
const roomsLoading = ref(true);
const isSubmitting = ref(false);

const error = ref<string | null>(null);
const reservationError = ref<string | null>(null);

const fetchHotelDetails = async () => {
  try {
    loading.value = true;
    const { data, error: fetchError } = await supabase.from('hotels').select('*').eq('id', hotelId).single();
    if (fetchError) throw fetchError;
    hotel.value = data;
  } catch (e: any) {
    error.value = 'Otel bilgileri alınamadı.';
  } finally {
    loading.value = false;
  }
};

const fetchAvailableRooms = async () => {
  try {
    roomsLoading.value = true;
    const { data, error: fetchError } = await supabase.from('rooms').select('*').eq('hotel_id', hotelId).eq('is_available', true);
    if (fetchError) throw fetchError;
    availableRooms.value = data || [];
  } catch (e: any) {
    error.value = 'Odalar yüklenemedi.';
  } finally {
    roomsLoading.value = false;
  }
};

const selectRoom = (room: any) => {
    selectedRoom.value = room;
};

const totalPrice = computed(() => {
    if(!checkInDate.value || !checkOutDate.value || !selectedRoom.value) return 0;
    const day_diff = new Date(checkOutDate.value).getTime() - new Date(checkInDate.value).getTime();
    const days = Math.ceil(day_diff / (1000 * 3600 * 24));
    if(days <= 0) return 0;
    return days * 1500; // Placeholder price
});

const createReservation = async () => {
  if (!selectedRoom.value || !checkInDate.value || !checkOutDate.value || totalPrice.value <= 0) {
    reservationError.value = "Lütfen tüm alanları doldurun ve geçerli bir tarih aralığı seçin.";
    return;
  }

  isSubmitting.value = true;
  reservationError.value = null;

  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("Rezervasyon yapmak için giriş yapmalısınız.");

    const reservationData = {
      profile_id: user.id,
      hotel_id: hotelId,
      room_id: selectedRoom.value.id,
      check_in_date: checkInDate.value,
      check_out_date: checkOutDate.value,
      total_price: totalPrice.value,
      guest_email: user.email, // Denormalized for convenience
    };

    const { error: insertError } = await supabase.from('reservations').insert(reservationData as any);
    if (insertError) throw insertError;

    // Reservation successful
    router.push('/guest/dashboard'); // Redirect to a confirmation or dashboard page

  } catch (e: any) {
    reservationError.value = e.message;
  } finally {
    isSubmitting.value = false;
  }
};

onMounted(() => {
  fetchHotelDetails();
  fetchAvailableRooms();
  const today = new Date();
  checkInDate.value = today.toISOString().split('T')[0];
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  checkOutDate.value = tomorrow.toISOString().split('T')[0];
});
</script> 