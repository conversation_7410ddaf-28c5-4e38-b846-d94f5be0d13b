import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import federation from '@originjs/vite-plugin-federation'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    federation({
      name: 'host',
      remotes: {
        mfeCustomerPortal: 'http://localhost:3001/assets/remoteEntry.js',
        hotelDashboard: 'http://localhost:5002/assets/remoteEntry.js',
        mfeHotelDashboard: 'http://localhost:5002/assets/remoteEntry.js',
        mfeManagement: 'http://localhost:5001/assets/remoteEntry.js'
      },
      shared: {
        vue: { singleton: true, requiredVersion: '^3.4.15' },
        'vue-router': { singleton: true, requiredVersion: '^4.2.5' },
        pinia: { singleton: true, requiredVersion: '^2.1.7' },
        '@hotelexia/shared-supabase-client': { singleton: true }
      }
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@hotelexia/shared-components': resolve(__dirname, '../../packages/shared-components/src/index.ts'),
      '@hotelexia/shared-supabase-client': resolve(__dirname, '../../packages/shared-supabase-client/src/index.ts')
    }
  },
  server: {
    port: 5000,
    host: true,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    },
    fs: {
      allow: ['..']
    }
  },
  build: {
    target: 'esnext',
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router']
        }
      }
    }
  }
}) 