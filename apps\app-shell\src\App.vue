<template>
  <div id="app" class="min-h-screen bg-white">
    <!-- Global Auth Loading Overlay -->
    <LoadingOverlay
      :show="authStore.isLoading"
      :title="$t('auth.welcome')"
      :message="$t('common.loading')"
    />

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/" class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">H</span>
            </div>
            <span class="text-xl font-bold text-gray-900">Hotelexia</span>
          </router-link>
          
          <!-- Navigation Links -->
          <div class="hidden md:flex space-x-8">
            <router-link to="/" 
                         class="text-gray-600 hover:text-indigo-600 transition-colors"
                         :class="{ 'text-indigo-600 font-semibold': $route.name === 'home' }">
              Ana Sayfa
            </router-link>
            <router-link to="/about" 
                         class="text-gray-600 hover:text-indigo-600 transition-colors"
                         :class="{ 'text-indigo-600 font-semibold': $route.name === 'about' }">
              Hakkımızda
            </router-link>
            <router-link to="/features" 
                         class="text-gray-600 hover:text-indigo-600 transition-colors"
                         :class="{ 'text-indigo-600 font-semibold': $route.name === 'features' }">
              Özellikler
            </router-link>
            <router-link to="/contact" 
                         class="text-gray-600 hover:text-indigo-600 transition-colors"
                         :class="{ 'text-indigo-600 font-semibold': $route.name === 'contact' }">
              İletişim
            </router-link>
          </div>
          
          <!-- Auth Status & Navigation -->
          <div class="flex items-center space-x-3">
            <!-- User Menu (if authenticated) -->
            <div v-if="authStore.isAuthenticated && authStore.profile" class="flex items-center space-x-3">
              <!-- System Health Indicator -->
              <div v-if="!globalStatsStore.isHealthy" class="flex items-center space-x-2 text-sm">
                <span v-if="globalStatsStore.hasWarning" class="text-yellow-600">⚠️</span>
                <span v-if="globalStatsStore.hasError" class="text-red-600">🔴</span>
                <span v-if="globalStatsStore.hasWarning" class="text-yellow-600">System Warning</span>
                <span v-if="globalStatsStore.hasError" class="text-red-600">System Error</span>
              </div>

              <!-- Hotel Context (if applicable) -->
              <div v-if="hotelStore.currentHotel" class="hidden lg:flex items-center space-x-2 text-sm text-gray-600">
                <span>🏨</span>
                <span>{{ hotelStore.currentHotelName }}</span>
              </div>

              <!-- Notifications -->
              <div v-if="notificationStore.unreadCount > 0" class="relative">
                <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                  <span class="text-red-600 text-xs font-medium">
                    {{ notificationStore.unreadCount > 9 ? '9+' : notificationStore.unreadCount }}
                  </span>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                  <span class="text-indigo-600 text-sm font-medium">
                    {{ authStore.userDisplayName[0]?.toUpperCase() }}
                  </span>
                </div>
                <span class="text-sm text-gray-700 hidden sm:block">
                  {{ authStore.userDisplayName }}
                </span>
                <span class="text-xs text-indigo-600 bg-indigo-50 px-2 py-1 rounded-full">
                  {{ getRoleLabel(authStore.userRole) }}
                </span>
              </div>
              <LanguageSwitcher />

              <button
                @click="handleSignOut"
                class="text-gray-500 hover:text-red-600 transition-colors text-sm"
              >
                {{ $t('auth.signOut') }}
              </button>
            </div>
            
            <!-- MFE Links (if not authenticated) -->
            <div v-else class="flex items-center space-x-3">
              <LanguageSwitcher />

              <a href="http://localhost:5002" target="_blank"
                 class="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors">
                🏨 {{ $t('navigation.hotels') }}
              </a>
              <a href="http://localhost:3001" target="_blank"
                 class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors">
                👥 {{ $t('navigation.guests') }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main>
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 border-t border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <p class="text-gray-600 mb-4">
            Hotelexia - Modern Otel Yönetim SaaS Platformu
          </p>
          <div class="flex justify-center space-x-6 text-sm text-gray-500">
            <span>Vue 3 + TypeScript</span>
            <span>•</span>
            <span>Mikro Frontend Mimarisi</span>
            <span>•</span>
            <span>Supabase Backend</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { watch, onUnmounted } from 'vue'
import { useAuthStore } from './stores/authStore'
import { useNotificationStore } from './stores/notificationStore'
import { useHotelStore } from './stores/hotelStore'
import { useGlobalStatsStore } from './stores/globalStatsStore'
import { useRealtimeStore } from './stores/realtimeStore'
import { useAppShellSync } from '@hotelexia/shared-components'
// import { crossMFEDataSynchronizer } from '@hotelexia/shared-supabase-client' // Temporarily disabled
import LoadingOverlay from './components/LoadingOverlay.vue'
import LanguageSwitcher from './components/LanguageSwitcher.vue'

const authStore = useAuthStore()
const notificationStore = useNotificationStore()
const hotelStore = useHotelStore()
const globalStatsStore = useGlobalStatsStore()
const realtimeStore = useRealtimeStore()

// Initialize Cross-MFE sync for app shell
const crossMFESync = useAppShellSync(
  authStore.user?.id || '',
  authStore.userHotelId || ''
)

// Initialize shared data when user is authenticated
watch(() => authStore.isAuthenticated, async (isAuthenticated) => {
  if (isAuthenticated) {
    // Initialize all shared data stores
    hotelStore.initializeHotels()
    notificationStore.fetchNotifications()
    globalStatsStore.refreshStats()

    // Initialize realtime coordination after authentication
    try {
      await realtimeStore.initialize()
    } catch (error) {
      console.error('Failed to initialize realtime coordination:', error)
    }

    // Initialize Cross-MFE sync
    if (authStore.user?.id) {
      console.log('Initializing Cross-MFE sync for app shell')
      crossMFESync.initialize(authStore.user.id, authStore.userHotelId || undefined)
      crossMFESync.start()

      // Initialize Cross-MFE Data Synchronizer
      // Temporarily disabled due to build issues
      /*
      if (authStore.userHotelId) {
        try {
          await crossMFEDataSynchronizer.initialize({
            tables: ['hotels', 'rooms', 'room_service_orders', 'service_requests', 'maintenance_tasks'],
            hotelId: authStore.userHotelId,
            userId: authStore.user.id,
            syncInterval: 2 * 60 * 1000, // 2 minutes
            enableConflictResolution: true
          })
          console.log('Cross-MFE Data Synchronizer initialized for app shell')
        } catch (error) {
          console.error('Failed to initialize Cross-MFE Data Synchronizer:', error)
        }
      }
      */
    }
  } else {
    // Clear data and disconnect realtime when user logs out
    notificationStore.clearAll()
    globalStatsStore.clearStats()
    await realtimeStore.disconnect()
    crossMFESync.stop()
    // crossMFEDataSynchronizer.stop() // Temporarily disabled
  }
}, { immediate: true })

// Cleanup Cross-MFE sync when app unmounts
onUnmounted(() => {
  console.log('Cleaning up Cross-MFE sync for app shell')
  crossMFESync.stop()
})

const getRoleLabel = (role: string | null) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'Süper Admin'
    case 'HOTEL_ADMIN':
      return 'Otel Müdürü'
    case 'STAFF':
      return 'Otel Personeli'
    case 'GUEST':
      return 'Misafir'
    default:
      return 'Kullanıcı'
  }
}

const handleSignOut = async () => {
  await authStore.signOut()
}
</script>

<style>
/* Global styles for promotional website */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.router-link-active {
  color: #4f46e5;
  font-weight: 600;
}
</style> 