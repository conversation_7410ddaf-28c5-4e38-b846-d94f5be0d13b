import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { i18n, initializeLocale } from '@hotelexia/shared-components'
import { globalErrorHandler } from './utils/errorHandling'
import './assets/css/global.css'

// Initialize locale from localStorage
initializeLocale()

// Pinia store
const pinia = createPinia()

// Vue uygulaması
const app = createApp(App)

// Global error handling for Vue
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue Error:', err, info)
  globalErrorHandler.handleGeneralError(
    err instanceof Error ? err : new Error(String(err)),
    {
      component: instance?.$options.name || 'Unknown',
      route: window.location.pathname
    }
  )
}

app.use(pinia)
app.use(router)
app.use(i18n)

// Mount the app
app.mount('#app')

console.log('🚀 Hotelexia App Shell - Promotional website loaded successfully')