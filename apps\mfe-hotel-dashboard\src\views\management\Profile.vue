<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold text-navy-700 dark:text-white mb-6">Profil <PERSON>netim<PERSON></h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Profile Picture Section -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
          <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Profil <PERSON>ğrafı</h2>
          <div class="flex flex-col items-center">
            <div class="w-32 h-32 rounded-full bg-gray-300 dark:bg-gray-600 overflow-hidden mb-4">
              <img
                v-if="profileImage"
                :src="profileImage"
                alt="Profile"
                class="w-full h-full object-cover"
              />
              <div v-else class="w-full h-full flex items-center justify-center">
                <UserCircleIcon class="w-16 h-16 text-gray-400" />
              </div>
            </div>
            <button
              @click="fileInput?.click()"
              class="bg-brand-500 hover:bg-brand-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Fotoğraf Değiştır
            </button>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileChange"
              class="hidden"
            />
          </div>
        </div>
      </div>

      <!-- Profile Information Section -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
          <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Kişisel Bilgiler</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Ad
              </label>
              <input
                type="text"
                v-model="firstName"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
                placeholder="Adınızı giriniz"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Soyad
              </label>
              <input
                type="text"
                v-model="lastName"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
                placeholder="Soyadınızı giriniz"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                E-posta
              </label>
              <input
                type="email"
                v-model="email"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
                placeholder="E-posta adresiniz"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Telefon
              </label>
              <input
                type="tel"
                v-model="phone"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
                placeholder="Telefon numaranız"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Pozisyon
              </label>
              <select
                v-model="position"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
              >
                <option value="">Pozisyon seçiniz</option>
                <option value="manager">Otel Müdürü</option>
                <option value="front_desk">Resepsiyon</option>
                <option value="housekeeping">Kat Görevlisi</option>
                <option value="maintenance">Teknik Servis</option>
                <option value="admin">Sistem Yöneticisi</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Departman
              </label>
              <select
                v-model="department"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
              >
                <option value="">Departman seçiniz</option>
                <option value="management">Yönetim</option>
                <option value="operations">Operasyonlar</option>
                <option value="housekeeping">Kat Hizmetleri</option>
                <option value="maintenance">Bakım</option>
                <option value="front_office">Ön Büro</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Password Change Section -->
    <div class="mt-6 bg-white dark:bg-navy-700 rounded-lg p-6 shadow-sm">
      <h2 class="text-lg font-semibold text-navy-700 dark:text-white mb-4">Şifre Değiştir</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Mevcut Şifre
          </label>
          <input
            type="password"
            v-model="currentPassword"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
            placeholder="Mevcut şifreniz"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Yeni Şifre
          </label>
          <input
            type="password"
            v-model="newPassword"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
            placeholder="Yeni şifreniz"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Yeni Şifre (Tekrar)
          </label>
          <input
            type="password"
            v-model="confirmPassword"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-brand-500 focus:border-brand-500 dark:bg-navy-800 dark:text-white"
            placeholder="Yeni şifrenizi tekrar giriniz"
          />
        </div>
      </div>
      <div class="mt-4">
        <button
          @click="changePassword"
          class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          Şifreyi Değiştir
        </button>
      </div>
    </div>

    <!-- Save Button -->
    <div class="mt-6 flex justify-end space-x-4">
      <button
        @click="cancelChanges"
        class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        İptal
      </button>
      <button
        @click="saveProfile"
        class="bg-brand-500 hover:bg-brand-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
      >
        Profili Kaydet
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { UserCircleIcon } from '@heroicons/vue/24/outline'

// File input ref
const fileInput = ref<HTMLInputElement>()

// Profile Information
const firstName = ref('Ahmet')
const lastName = ref('Yılmaz')
const email = ref('<EMAIL>')
const phone = ref('+90 ************')
const position = ref('manager')
const department = ref('management')
const profileImage = ref('')

// Password Change
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      profileImage.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const changePassword = () => {
  if (newPassword.value !== confirmPassword.value) {
    alert('Yeni şifreler eşleşmiyor!')
    return
  }
  if (newPassword.value.length < 6) {
    alert('Şifre en az 6 karakter olmalıdır!')
    return
  }
  // TODO: Implement password change with backend
  alert('Şifre değiştirildi! (Backend entegrasyonu yapılacak)')
  currentPassword.value = ''
  newPassword.value = ''
  confirmPassword.value = ''
}

const cancelChanges = () => {
  // Reset to original values
  firstName.value = 'Ahmet'
  lastName.value = 'Yılmaz'
  email.value = '<EMAIL>'
  phone.value = '+90 ************'
  position.value = 'manager'
  department.value = 'management'
}

const saveProfile = () => {
  // TODO: Implement save functionality with backend
  alert('Profil kaydedildi! (Backend entegrasyonu yapılacak)')
}
</script> 