<template>
  <div class="bg-white dark:bg-navy-800 rounded-20 shadow-card overflow-hidden hover:shadow-card-hover transition-shadow">
    <!-- Activity Image -->
    <div class="relative h-48 overflow-hidden">
      <img 
        :src="activity.imageUrl" 
        :alt="activity.title"
        class="w-full h-full object-cover transition-transform hover:scale-105"
      >
      <!-- Status Badge -->
      <div class="absolute top-4 left-4">
        <span 
          class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
          :class="activity.isActive 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'"
        >
          <span 
            class="w-2 h-2 rounded-full mr-2"
            :class="activity.isActive ? 'bg-green-500' : 'bg-gray-400'"
          ></span>
          {{ activity.isActive ? 'Aktif' : 'Pasif' }}
        </span>
      </div>
      
      <!-- Time Badge -->
      <div class="absolute top-4 right-4">
        <span class="bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-xs font-medium">
          {{ formatDateTime(activity.startTimeUtc) }}
        </span>
      </div>
    </div>

    <!-- Card Content -->
    <div class="p-6">
      <!-- Title and Description -->
      <div class="mb-4">
        <h3 class="text-lg font-semibold text-navy-700 dark:text-white mb-2 line-clamp-2">
          {{ activity.title }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
          {{ activity.description }}
        </p>
      </div>

      <!-- Location and Capacity -->
      <div class="flex items-center gap-4 mb-4 text-sm text-gray-600 dark:text-gray-400">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {{ activity.location }}
        </div>
        <div v-if="activity.capacity" class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          {{ activity.capacity }} kişi
        </div>
      </div>

      <!-- Tags -->
      <div v-if="activity.tags && activity.tags.length > 0" class="mb-4">
        <div class="flex flex-wrap gap-2">
          <span 
            v-for="tag in activity.tags.slice(0, 3)"
            :key="tag"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-50 text-brand-700 dark:bg-brand-900 dark:text-brand-300"
          >
            {{ tag }}
          </span>
          <span 
            v-if="activity.tags.length > 3"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
          >
            +{{ activity.tags.length - 3 }}
          </span>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <!-- Status Toggle -->
        <div class="flex items-center">
          <button
            @click="$emit('toggle-status', activity.id)"
            class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
            :class="activity.isActive ? 'bg-brand-600' : 'bg-gray-200 dark:bg-gray-600'"
          >
            <span
              class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
              :class="activity.isActive ? 'translate-x-6' : 'translate-x-1'"
            ></span>
          </button>
          <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
            {{ activity.isActive ? 'Aktif' : 'Pasif' }}
          </span>
        </div>

        <!-- Edit and Delete -->
        <div class="flex items-center space-x-2">
          <button
            @click="$emit('edit', activity)"
            class="p-2 text-gray-400 hover:text-brand-500 transition-colors"
            title="Düzenle"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
          <button
            @click="$emit('delete', activity.id)"
            class="p-2 text-gray-400 hover:text-red-500 transition-colors"
            title="Sil"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { HotelActivity } from '@/types/management'

// Props
interface Props {
  activity: HotelActivity
}

defineProps<Props>()

// Emits
defineEmits<{
  edit: [activity: HotelActivity]
  delete: [activityId: string]
  'toggle-status': [activityId: string]
}>()

// Methods
const formatDateTime = (isoString: string) => {
  const date = new Date(isoString)
  return date.toLocaleString('tr-TR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }) + ' (Yerel Saat)'
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 