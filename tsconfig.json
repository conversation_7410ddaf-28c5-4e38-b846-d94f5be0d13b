{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Project specific */
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["./packages/shared/*"],
      "@customer/*": ["./packages/customer-portal/*"],
      "@hotel/*": ["./packages/hotel-portal/*"],
      "@manager/*": ["./packages/manager-portal/*"]
    },
    "types": ["vite/client"]
  },
  "include": [
    "env.d.ts",
    "src/**/*",
    "packages/**/*",
    "src/**/*.vue",
    "packages/**/*.vue"
, "apps/mfe-customer-portal"  ],
  "exclude": [
    "src/**/__tests__/*",
    "packages/**/__tests__/*"
  ]
}