<template>
  <div class="min-h-screen bg-gradient-to-br from-emerald-900 via-teal-900 to-cyan-900 flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full">
      <!-- Header Section -->
      <div class="text-center mb-8">
        <div class="mx-auto h-16 w-16 bg-white rounded-2xl flex items-center justify-center shadow-2xl mb-6">
          <svg class="h-8 w-8 text-emerald-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </div>
        <h1 class="text-4xl font-bold text-white mb-2">
          Süper <PERSON>
        </h1>
        <p class="text-emerald-200 text-lg">
          Hotelexia Yönetim Paneli
        </p>
      </div>

      <!-- Login Card -->
      <div class="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-white mb-2">
              E-posta Adresi
            </label>
            <div class="relative">
              <input
                id="email"
                v-model="email"
                type="email"
                autocomplete="email"
                required
                :disabled="isLoading"
                class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-transparent transition-all duration-200 disabled:opacity-50"
                placeholder="<EMAIL>"
              />
              <UserIcon class="absolute right-3 top-3 h-5 w-5 text-white/40" />
            </div>
            <p v-if="emailError" class="mt-1 text-sm text-red-600">{{ emailError }}</p>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-white mb-2">
              Şifre
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="current-password"
                required
                :disabled="isLoading"
                class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-transparent transition-all duration-200 disabled:opacity-50 pr-12"
                placeholder="••••••••"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                :disabled="isLoading"
                class="absolute right-3 top-3 text-white/40 hover:text-white/60 transition-colors"
              >
                <EyeIcon v-if="!showPassword" class="h-5 w-5" />
                <EyeSlashIcon v-else class="h-5 w-5" />
              </button>
            </div>
            <p v-if="passwordError" class="mt-1 text-sm text-red-100">{{ passwordError }}</p>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="p-4 bg-red-500/20 border border-red-500/30 rounded-xl backdrop-blur-sm">
            <div class="flex">
              <ExclamationTriangleIcon class="h-5 w-5 text-red-300 mr-2 flex-shrink-0 mt-0.5" />
              <p class="text-sm text-red-100">{{ error }}</p>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="isLoading || !isFormValid"
            class="w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap' }}
          </button>
        </form>

        <!-- Development Helper -->
        <div v-if="isDevelopment" class="mt-6 pt-6 border-t border-white/20">
          <p class="text-xs text-white/60 text-center mb-3">Geliştirme Ortamı - Test Hesabı</p>
          <button
            @click="fillTestCredentials"
            :disabled="isLoading"
            class="w-full px-3 py-2 text-xs text-white/80 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            Test Hesabı ile Doldur
          </button>
        </div>

        <!-- Portal Info -->
        <div class="mt-8 text-center">
          <p class="text-white/60 text-sm">
            Hotelexia Süper Admin Paneli
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center mt-8">
        <p class="text-xs text-emerald-200/60">
          © 2024 Hotelexia. Tüm hakları saklıdır.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { supabase } from '@hotelexia/shared-supabase-client'
import { 
  UserIcon, 
  EyeIcon, 
  EyeSlashIcon, 
  ExclamationTriangleIcon 
} from '@heroicons/vue/24/outline'

// Router
const router = useRouter()

// Reactive state
const email = ref('')
const password = ref('')
const showPassword = ref(false)
const isLoading = ref(false)
const error = ref<string | null>(null)
const emailError = ref<string | null>(null)
const passwordError = ref<string | null>(null)

// Environment check
const isDevelopment = computed(() => import.meta.env.DEV)

// Form validation
const isFormValid = computed(() => {
  return email.value.trim() !== '' && 
         password.value.trim() !== '' && 
         isValidEmail(email.value) &&
         !emailError.value &&
         !passwordError.value
})

// Email validation
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate email on input
const validateEmail = () => {
  emailError.value = null
  if (email.value && !isValidEmail(email.value)) {
    emailError.value = 'Geçerli bir e-posta adresi girin'
  }
}

// Validate password on input
const validatePassword = () => {
  passwordError.value = null
  if (password.value && password.value.length < 6) {
    passwordError.value = 'Şifre en az 6 karakter olmalıdır'
  }
}

// Fill test credentials for development
const fillTestCredentials = () => {
  email.value = '<EMAIL>'
  password.value = 'admin123'
}

// Handle login
const handleLogin = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  error.value = null

  try {
    // Authenticate with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: email.value.trim(),
      password: password.value
    })

    if (authError) {
      throw authError
    }

    if (!authData.user) {
      throw new Error('Kullanıcı bilgileri alınamadı')
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, role, hotel_id, full_name, email')
      .eq('id', authData.user.id)
      .single()

    if (profileError) {
      throw new Error('Kullanıcı profili bulunamadı')
    }

    // Check if user has SUPER_ADMIN role
    if ((profile as any).role !== 'SUPER_ADMIN') {
      await supabase.auth.signOut()
      throw new Error('Bu panele erişim yetkiniz bulunmuyor. Sadece Süper Admin kullanıcıları giriş yapabilir.')
    }

    // Store user context for the session
    sessionStorage.setItem('hotelexia_management_user', JSON.stringify({
      id: (profile as any).id,
      email: (profile as any).email,
      fullName: (profile as any).full_name,
      role: (profile as any).role,
      hotelId: (profile as any).hotel_id
    }))

    // Successful login - redirect to dashboard
    console.log('Management Portal login successful:', profile as any)
    router.push('/dashboard')

  } catch (err: any) {
    console.error('Login error:', err)
    
    // Handle specific error types
    if (err.message?.includes('Invalid login credentials')) {
      error.value = 'E-posta adresi veya şifre hatalı'
    } else if (err.message?.includes('Email not confirmed')) {
      error.value = 'E-posta adresinizi doğrulamanız gerekiyor'
    } else if (err.message?.includes('Too many requests')) {
      error.value = 'Çok fazla deneme yapıldı. Lütfen birkaç dakika sonra tekrar deneyin'
    } else {
      error.value = err.message || 'Giriş yapılırken bir hata oluştu'
    }
  } finally {
    isLoading.value = false
  }
}

// Watch for input changes to validate
const watchEmail = () => {
  validateEmail()
}

const watchPassword = () => {
  validatePassword()
}

// Clear errors when user starts typing
onMounted(() => {
  // Auto-focus email field
  const emailInput = document.getElementById('email')
  if (emailInput) {
    emailInput.focus()
  }
})
</script>

<style scoped>
/* Custom styles for the login form */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
}

/* Focus ring customization */
input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
