@tailwind base;
@tailwind components;
@tailwind utilities;

/* New Mobile Theme Global Styles */
@layer base {
  html, body {
    @apply font-sans text-textDark bg-backgroundLight;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  /* Touch-friendly interactive elements */
  button, a, [role="button"] {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Ensure tap targets are big enough on mobile */
  input, select, textarea {
    @apply min-h-[44px];
  }
}

@layer components {
  /* New Mobile Theme Components */
  .form-input {
    @apply w-full px-4 py-3 text-base border border-borderColor rounded-lg 
           focus:border-primary focus:ring-2 focus:ring-primary/20
           transition-colors duration-200 bg-backgroundWhite text-textDark;
  }

  .btn-primary {
    @apply w-full py-3 px-6 text-base font-medium text-white 
           bg-primary hover:bg-primary/90 active:bg-primary/80
           rounded-lg transition-colors duration-200 
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply w-full py-3 px-6 text-base font-medium text-primary 
           bg-backgroundWhite border border-primary hover:bg-primary/5
           rounded-lg transition-colors duration-200;
  }

  /* Card styles */
  .mobile-card {
    @apply bg-backgroundWhite rounded-lg shadow-sm border border-borderColor p-4;
  }

  .mobile-card-title {
    @apply text-lg font-semibold text-textDark mb-2;
  }

  .mobile-card-subtitle {
    @apply text-sm text-textMedium;
  }

  /* Status badges */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium;
  }

  .status-badge-delivered {
    @apply bg-green-100 text-green-800;
  }

  .status-badge-pending {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-badge-canceled {
    @apply bg-primary/10 text-primary;
  }

  .status-badge-preparing {
    @apply bg-blue-100 text-blue-800;
  }
} 